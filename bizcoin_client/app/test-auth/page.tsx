"use client";

import { useAuth } from "@/contexts/appwrite-auth-context";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import AppwriteEmailVerificationModal from "@/components/AppwriteEmailVerificationModal";
import { useState } from "react";

export default function TestAuthPage() {
  const { user, userProfile, loading, logout } = useAuth();
  const [showModal, setShowModal] = useState(false);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4">
      <div className="max-w-2xl mx-auto">
        <Card>
          <CardHeader>
            <CardTitle>Appwrite Authentication Test</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {user ? (
              <div className="space-y-4">
                <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                  <h3 className="font-semibold text-green-800 mb-2">✅ Authenticated</h3>
                  <div className="space-y-2 text-sm">
                    <p><strong>User ID:</strong> {user.$id}</p>
                    <p><strong>Name:</strong> {user.name}</p>
                    <p><strong>Email:</strong> {user.email}</p>
                    <p><strong>Email Verified:</strong> {user.emailVerification ? "✅ Yes" : "❌ No"}</p>
                    <p><strong>Created:</strong> {new Date(user.$createdAt).toLocaleString()}</p>
                  </div>
                </div>

                {userProfile && (
                  <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <h3 className="font-semibold text-blue-800 mb-2">👤 User Profile</h3>
                    <pre className="text-sm bg-white p-2 rounded border overflow-auto">
                      {JSON.stringify(userProfile, null, 2)}
                    </pre>
                  </div>
                )}

                <Button onClick={logout} variant="destructive">
                  Logout
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <h3 className="font-semibold text-yellow-800 mb-2">🔐 Not Authenticated</h3>
                  <p className="text-sm text-yellow-700">
                    You are not currently logged in. Click the button below to authenticate.
                  </p>
                </div>

                <Button onClick={() => setShowModal(true)}>
                  Login / Sign Up
                </Button>

                <AppwriteEmailVerificationModal
                  isOpen={showModal}
                  onClose={() => setShowModal(false)}
                  onSuccess={() => {
                    setShowModal(false);
                    // Refresh the page to show updated auth state
                    window.location.reload();
                  }}
                />
              </div>
            )}

            <div className="mt-8 p-4 bg-gray-50 border border-gray-200 rounded-lg">
              <h3 className="font-semibold text-gray-800 mb-2">🔧 Configuration</h3>
              <div className="space-y-1 text-sm text-gray-600">
                <p><strong>Appwrite Endpoint:</strong> {process.env.NEXT_PUBLIC_APPWRITE_PUBLIC_ENDPOINT}</p>
                <p><strong>Project ID:</strong> {process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID}</p>
              </div>
            </div>

            <div className="mt-4">
              <Button variant="outline" onClick={() => window.location.href = "/"}>
                ← Back to Home
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
