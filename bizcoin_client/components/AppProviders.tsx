"use client";
import { ThemeProvider } from "@/components/theme-provider";
import { AuthProvider } from "@/contexts/appwrite-auth-context";

import { Suspense } from "react";
import { SidebarProvider } from "./ui/sidebar";
import { Toaster } from "sonner";

export default function AppProviders({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <ThemeProvider
      attribute="class"
      defaultTheme="system"
      enableSystem
      disableTransitionOnChange
    >
      <AuthProvider>
        <SidebarProvider>
          <Suspense fallback={<div className="flex items-center justify-center min-h-screen"><div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900" /></div>}>{children}</Suspense>
          <Toaster />
        </SidebarProvider>
      </AuthProvider>
    </ThemeProvider>
  );
}
