"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./node_modules/next/dist/esm/server/app-render/dynamic-rendering.js":
/*!***************************************************************************!*\
  !*** ./node_modules/next/dist/esm/server/app-render/dynamic-rendering.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Postpone: () => (/* binding */ Postpone),\n/* harmony export */   PreludeState: () => (/* binding */ PreludeState),\n/* harmony export */   abortAndThrowOnSynchronousRequestDataAccess: () => (/* binding */ abortAndThrowOnSynchronousRequestDataAccess),\n/* harmony export */   abortOnSynchronousPlatformIOAccess: () => (/* binding */ abortOnSynchronousPlatformIOAccess),\n/* harmony export */   accessedDynamicData: () => (/* binding */ accessedDynamicData),\n/* harmony export */   annotateDynamicAccess: () => (/* binding */ annotateDynamicAccess),\n/* harmony export */   consumeDynamicAccess: () => (/* binding */ consumeDynamicAccess),\n/* harmony export */   createDynamicTrackingState: () => (/* binding */ createDynamicTrackingState),\n/* harmony export */   createDynamicValidationState: () => (/* binding */ createDynamicValidationState),\n/* harmony export */   createHangingInputAbortSignal: () => (/* binding */ createHangingInputAbortSignal),\n/* harmony export */   createPostponedAbortSignal: () => (/* binding */ createPostponedAbortSignal),\n/* harmony export */   formatDynamicAPIAccesses: () => (/* binding */ formatDynamicAPIAccesses),\n/* harmony export */   getFirstDynamicReason: () => (/* binding */ getFirstDynamicReason),\n/* harmony export */   isDynamicPostpone: () => (/* binding */ isDynamicPostpone),\n/* harmony export */   isPrerenderInterruptedError: () => (/* binding */ isPrerenderInterruptedError),\n/* harmony export */   markCurrentScopeAsDynamic: () => (/* binding */ markCurrentScopeAsDynamic),\n/* harmony export */   postponeWithTracking: () => (/* binding */ postponeWithTracking),\n/* harmony export */   throwIfDisallowedDynamic: () => (/* binding */ throwIfDisallowedDynamic),\n/* harmony export */   throwToInterruptStaticGeneration: () => (/* binding */ throwToInterruptStaticGeneration),\n/* harmony export */   trackAllowedDynamicAccess: () => (/* binding */ trackAllowedDynamicAccess),\n/* harmony export */   trackDynamicDataInDynamicRender: () => (/* binding */ trackDynamicDataInDynamicRender),\n/* harmony export */   trackFallbackParamAccessed: () => (/* binding */ trackFallbackParamAccessed),\n/* harmony export */   trackSynchronousPlatformIOAccessInDev: () => (/* binding */ trackSynchronousPlatformIOAccessInDev),\n/* harmony export */   trackSynchronousRequestDataAccessInDev: () => (/* binding */ trackSynchronousRequestDataAccessInDev),\n/* harmony export */   useDynamicRouteParams: () => (/* binding */ useDynamicRouteParams)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(middleware)/./node_modules/next/dist/compiled/react/react.react-server.js\");\n/* harmony import */ var _client_components_hooks_server_context__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../client/components/hooks-server-context */ \"(middleware)/./node_modules/next/dist/esm/client/components/hooks-server-context.js\");\n/* harmony import */ var _client_components_static_generation_bailout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../client/components/static-generation-bailout */ \"(middleware)/./node_modules/next/dist/esm/client/components/static-generation-bailout.js\");\n/* harmony import */ var _work_unit_async_storage_external__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./work-unit-async-storage.external */ \"(shared)/./node_modules/next/dist/esm/server/app-render/work-unit-async-storage.external.js\");\n/* harmony import */ var _app_render_work_async_storage_external__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../app-render/work-async-storage.external */ \"(shared)/./node_modules/next/dist/esm/server/app-render/work-async-storage.external.js\");\n/* harmony import */ var _dynamic_rendering_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../dynamic-rendering-utils */ \"(middleware)/./node_modules/next/dist/esm/server/dynamic-rendering-utils.js\");\n/* harmony import */ var _lib_metadata_metadata_constants__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../lib/metadata/metadata-constants */ \"(middleware)/./node_modules/next/dist/esm/lib/metadata/metadata-constants.js\");\n/* harmony import */ var _lib_scheduler__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../lib/scheduler */ \"(middleware)/./node_modules/next/dist/esm/lib/scheduler.js\");\n/**\n * The functions provided by this module are used to communicate certain properties\n * about the currently running code so that Next.js can make decisions on how to handle\n * the current execution in different rendering modes such as pre-rendering, resuming, and SSR.\n *\n * Today Next.js treats all code as potentially static. Certain APIs may only make sense when dynamically rendering.\n * Traditionally this meant deopting the entire render to dynamic however with PPR we can now deopt parts\n * of a React tree as dynamic while still keeping other parts static. There are really two different kinds of\n * Dynamic indications.\n *\n * The first is simply an intention to be dynamic. unstable_noStore is an example of this where\n * the currently executing code simply declares that the current scope is dynamic but if you use it\n * inside unstable_cache it can still be cached. This type of indication can be removed if we ever\n * make the default dynamic to begin with because the only way you would ever be static is inside\n * a cache scope which this indication does not affect.\n *\n * The second is an indication that a dynamic data source was read. This is a stronger form of dynamic\n * because it means that it is inappropriate to cache this at all. using a dynamic data source inside\n * unstable_cache should error. If you want to use some dynamic data inside unstable_cache you should\n * read that data outside the cache and pass it in as an argument to the cached function.\n */ // Once postpone is in stable we should switch to importing the postpone export directly\n\n\n\n\n\n\n\n\nconst hasPostpone = typeof react__WEBPACK_IMPORTED_MODULE_0__.unstable_postpone === 'function';\nfunction createDynamicTrackingState(isDebugDynamicAccesses) {\n    return {\n        isDebugDynamicAccesses,\n        dynamicAccesses: [],\n        syncDynamicErrorWithStack: null\n    };\n}\nfunction createDynamicValidationState() {\n    return {\n        hasSuspenseAboveBody: false,\n        hasDynamicMetadata: false,\n        hasDynamicViewport: false,\n        hasAllowedDynamic: false,\n        dynamicErrors: []\n    };\n}\nfunction getFirstDynamicReason(trackingState) {\n    var _trackingState_dynamicAccesses_;\n    return (_trackingState_dynamicAccesses_ = trackingState.dynamicAccesses[0]) == null ? void 0 : _trackingState_dynamicAccesses_.expression;\n}\n/**\n * This function communicates that the current scope should be treated as dynamic.\n *\n * In most cases this function is a no-op but if called during\n * a PPR prerender it will postpone the current sub-tree and calling\n * it during a normal prerender will cause the entire prerender to abort\n */ function markCurrentScopeAsDynamic(store, workUnitStore, expression) {\n    if (workUnitStore) {\n        if (workUnitStore.type === 'cache' || workUnitStore.type === 'unstable-cache') {\n            // inside cache scopes marking a scope as dynamic has no effect because the outer cache scope\n            // creates a cache boundary. This is subtly different from reading a dynamic data source which is\n            // forbidden inside a cache scope.\n            return;\n        }\n    }\n    // If we're forcing dynamic rendering or we're forcing static rendering, we\n    // don't need to do anything here because the entire page is already dynamic\n    // or it's static and it should not throw or postpone here.\n    if (store.forceDynamic || store.forceStatic) return;\n    if (store.dynamicShouldError) {\n        throw Object.defineProperty(new _client_components_static_generation_bailout__WEBPACK_IMPORTED_MODULE_2__.StaticGenBailoutError(`Route ${store.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`), \"__NEXT_ERROR_CODE\", {\n            value: \"E553\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    if (workUnitStore) {\n        if (workUnitStore.type === 'prerender-ppr') {\n            postponeWithTracking(store.route, expression, workUnitStore.dynamicTracking);\n        } else if (workUnitStore.type === 'prerender-legacy') {\n            workUnitStore.revalidate = 0;\n            // We aren't prerendering but we are generating a static page. We need to bail out of static generation\n            const err = Object.defineProperty(new _client_components_hooks_server_context__WEBPACK_IMPORTED_MODULE_1__.DynamicServerError(`Route ${store.route} couldn't be rendered statically because it used ${expression}. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`), \"__NEXT_ERROR_CODE\", {\n                value: \"E550\",\n                enumerable: false,\n                configurable: true\n            });\n            store.dynamicUsageDescription = expression;\n            store.dynamicUsageStack = err.stack;\n            throw err;\n        } else if ( true && workUnitStore && workUnitStore.type === 'request') {\n            workUnitStore.usedDynamic = true;\n        }\n    }\n}\n/**\n * This function communicates that some dynamic path parameter was read. This\n * differs from the more general `trackDynamicDataAccessed` in that it is will\n * not error when `dynamic = \"error\"` is set.\n *\n * @param store The static generation store\n * @param expression The expression that was accessed dynamically\n */ function trackFallbackParamAccessed(store, expression) {\n    const prerenderStore = _work_unit_async_storage_external__WEBPACK_IMPORTED_MODULE_3__.workUnitAsyncStorage.getStore();\n    if (!prerenderStore || prerenderStore.type !== 'prerender-ppr') return;\n    postponeWithTracking(store.route, expression, prerenderStore.dynamicTracking);\n}\n/**\n * This function is meant to be used when prerendering without dynamicIO or PPR.\n * When called during a build it will cause Next.js to consider the route as dynamic.\n *\n * @internal\n */ function throwToInterruptStaticGeneration(expression, store, prerenderStore) {\n    // We aren't prerendering but we are generating a static page. We need to bail out of static generation\n    const err = Object.defineProperty(new _client_components_hooks_server_context__WEBPACK_IMPORTED_MODULE_1__.DynamicServerError(`Route ${store.route} couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`), \"__NEXT_ERROR_CODE\", {\n        value: \"E558\",\n        enumerable: false,\n        configurable: true\n    });\n    prerenderStore.revalidate = 0;\n    store.dynamicUsageDescription = expression;\n    store.dynamicUsageStack = err.stack;\n    throw err;\n}\n/**\n * This function should be used to track whether something dynamic happened even when\n * we are in a dynamic render. This is useful for Dev where all renders are dynamic but\n * we still track whether dynamic APIs were accessed for helpful messaging\n *\n * @internal\n */ function trackDynamicDataInDynamicRender(_store, workUnitStore) {\n    if (workUnitStore) {\n        if (workUnitStore.type === 'cache' || workUnitStore.type === 'unstable-cache') {\n            // inside cache scopes marking a scope as dynamic has no effect because the outer cache scope\n            // creates a cache boundary. This is subtly different from reading a dynamic data source which is\n            // forbidden inside a cache scope.\n            return;\n        }\n        // TODO: it makes no sense to have these work unit store types during a dev render.\n        if (workUnitStore.type === 'prerender' || workUnitStore.type === 'prerender-client' || workUnitStore.type === 'prerender-legacy') {\n            workUnitStore.revalidate = 0;\n        }\n        if ( true && workUnitStore.type === 'request') {\n            workUnitStore.usedDynamic = true;\n        }\n    }\n}\nfunction abortOnSynchronousDynamicDataAccess(route, expression, prerenderStore) {\n    const reason = `Route ${route} needs to bail out of prerendering at this point because it used ${expression}.`;\n    const error = createPrerenderInterruptedError(reason);\n    prerenderStore.controller.abort(error);\n    const dynamicTracking = prerenderStore.dynamicTracking;\n    if (dynamicTracking) {\n        dynamicTracking.dynamicAccesses.push({\n            // When we aren't debugging, we don't need to create another error for the\n            // stack trace.\n            stack: dynamicTracking.isDebugDynamicAccesses ? new Error().stack : undefined,\n            expression\n        });\n    }\n}\nfunction abortOnSynchronousPlatformIOAccess(route, expression, errorWithStack, prerenderStore) {\n    const dynamicTracking = prerenderStore.dynamicTracking;\n    abortOnSynchronousDynamicDataAccess(route, expression, prerenderStore);\n    // It is important that we set this tracking value after aborting. Aborts are executed\n    // synchronously except for the case where you abort during render itself. By setting this\n    // value late we can use it to determine if any of the aborted tasks are the task that\n    // called the sync IO expression in the first place.\n    if (dynamicTracking) {\n        if (dynamicTracking.syncDynamicErrorWithStack === null) {\n            dynamicTracking.syncDynamicErrorWithStack = errorWithStack;\n        }\n    }\n}\nfunction trackSynchronousPlatformIOAccessInDev(requestStore) {\n    // We don't actually have a controller to abort but we do the semantic equivalent by\n    // advancing the request store out of prerender mode\n    requestStore.prerenderPhase = false;\n}\n/**\n * use this function when prerendering with dynamicIO. If we are doing a\n * prospective prerender we don't actually abort because we want to discover\n * all caches for the shell. If this is the actual prerender we do abort.\n *\n * This function accepts a prerenderStore but the caller should ensure we're\n * actually running in dynamicIO mode.\n *\n * @internal\n */ function abortAndThrowOnSynchronousRequestDataAccess(route, expression, errorWithStack, prerenderStore) {\n    const prerenderSignal = prerenderStore.controller.signal;\n    if (prerenderSignal.aborted === false) {\n        // TODO it would be better to move this aborted check into the callsite so we can avoid making\n        // the error object when it isn't relevant to the aborting of the prerender however\n        // since we need the throw semantics regardless of whether we abort it is easier to land\n        // this way. See how this was handled with `abortOnSynchronousPlatformIOAccess` for a closer\n        // to ideal implementation\n        abortOnSynchronousDynamicDataAccess(route, expression, prerenderStore);\n        // It is important that we set this tracking value after aborting. Aborts are executed\n        // synchronously except for the case where you abort during render itself. By setting this\n        // value late we can use it to determine if any of the aborted tasks are the task that\n        // called the sync IO expression in the first place.\n        const dynamicTracking = prerenderStore.dynamicTracking;\n        if (dynamicTracking) {\n            if (dynamicTracking.syncDynamicErrorWithStack === null) {\n                dynamicTracking.syncDynamicErrorWithStack = errorWithStack;\n            }\n        }\n    }\n    throw createPrerenderInterruptedError(`Route ${route} needs to bail out of prerendering at this point because it used ${expression}.`);\n}\n// For now these implementations are the same so we just reexport\nconst trackSynchronousRequestDataAccessInDev = trackSynchronousPlatformIOAccessInDev;\nfunction Postpone({ reason, route }) {\n    const prerenderStore = _work_unit_async_storage_external__WEBPACK_IMPORTED_MODULE_3__.workUnitAsyncStorage.getStore();\n    const dynamicTracking = prerenderStore && prerenderStore.type === 'prerender-ppr' ? prerenderStore.dynamicTracking : null;\n    postponeWithTracking(route, reason, dynamicTracking);\n}\nfunction postponeWithTracking(route, expression, dynamicTracking) {\n    assertPostpone();\n    if (dynamicTracking) {\n        dynamicTracking.dynamicAccesses.push({\n            // When we aren't debugging, we don't need to create another error for the\n            // stack trace.\n            stack: dynamicTracking.isDebugDynamicAccesses ? new Error().stack : undefined,\n            expression\n        });\n    }\n    react__WEBPACK_IMPORTED_MODULE_0__.unstable_postpone(createPostponeReason(route, expression));\n}\nfunction createPostponeReason(route, expression) {\n    return `Route ${route} needs to bail out of prerendering at this point because it used ${expression}. ` + `React throws this special object to indicate where. It should not be caught by ` + `your own try/catch. Learn more: https://nextjs.org/docs/messages/ppr-caught-error`;\n}\nfunction isDynamicPostpone(err) {\n    if (typeof err === 'object' && err !== null && typeof err.message === 'string') {\n        return isDynamicPostponeReason(err.message);\n    }\n    return false;\n}\nfunction isDynamicPostponeReason(reason) {\n    return reason.includes('needs to bail out of prerendering at this point because it used') && reason.includes('Learn more: https://nextjs.org/docs/messages/ppr-caught-error');\n}\nif (isDynamicPostponeReason(createPostponeReason('%%%', '^^^')) === false) {\n    throw Object.defineProperty(new Error('Invariant: isDynamicPostpone misidentified a postpone reason. This is a bug in Next.js'), \"__NEXT_ERROR_CODE\", {\n        value: \"E296\",\n        enumerable: false,\n        configurable: true\n    });\n}\nconst NEXT_PRERENDER_INTERRUPTED = 'NEXT_PRERENDER_INTERRUPTED';\nfunction createPrerenderInterruptedError(message) {\n    const error = Object.defineProperty(new Error(message), \"__NEXT_ERROR_CODE\", {\n        value: \"E394\",\n        enumerable: false,\n        configurable: true\n    });\n    error.digest = NEXT_PRERENDER_INTERRUPTED;\n    return error;\n}\nfunction isPrerenderInterruptedError(error) {\n    return typeof error === 'object' && error !== null && error.digest === NEXT_PRERENDER_INTERRUPTED && 'name' in error && 'message' in error && error instanceof Error;\n}\nfunction accessedDynamicData(dynamicAccesses) {\n    return dynamicAccesses.length > 0;\n}\nfunction consumeDynamicAccess(serverDynamic, clientDynamic) {\n    // We mutate because we only call this once we are no longer writing\n    // to the dynamicTrackingState and it's more efficient than creating a new\n    // array.\n    serverDynamic.dynamicAccesses.push(...clientDynamic.dynamicAccesses);\n    return serverDynamic.dynamicAccesses;\n}\nfunction formatDynamicAPIAccesses(dynamicAccesses) {\n    return dynamicAccesses.filter((access)=>typeof access.stack === 'string' && access.stack.length > 0).map(({ expression, stack })=>{\n        stack = stack.split('\\n') // Remove the \"Error: \" prefix from the first line of the stack trace as\n        // well as the first 4 lines of the stack trace which is the distance\n        // from the user code and the `new Error().stack` call.\n        .slice(4).filter((line)=>{\n            // Exclude Next.js internals from the stack trace.\n            if (line.includes('node_modules/next/')) {\n                return false;\n            }\n            // Exclude anonymous functions from the stack trace.\n            if (line.includes(' (<anonymous>)')) {\n                return false;\n            }\n            // Exclude Node.js internals from the stack trace.\n            if (line.includes(' (node:')) {\n                return false;\n            }\n            return true;\n        }).join('\\n');\n        return `Dynamic API Usage Debug - ${expression}:\\n${stack}`;\n    });\n}\nfunction assertPostpone() {\n    if (!hasPostpone) {\n        throw Object.defineProperty(new Error(`Invariant: React.unstable_postpone is not defined. This suggests the wrong version of React was loaded. This is a bug in Next.js`), \"__NEXT_ERROR_CODE\", {\n            value: \"E224\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n}\n/**\n * This is a bit of a hack to allow us to abort a render using a Postpone instance instead of an Error which changes React's\n * abort semantics slightly.\n */ function createPostponedAbortSignal(reason) {\n    assertPostpone();\n    const controller = new AbortController();\n    // We get our hands on a postpone instance by calling postpone and catching the throw\n    try {\n        react__WEBPACK_IMPORTED_MODULE_0__.unstable_postpone(reason);\n    } catch (x) {\n        controller.abort(x);\n    }\n    return controller.signal;\n}\n/**\n * In a prerender, we may end up with hanging Promises as inputs due them\n * stalling on connection() or because they're loading dynamic data. In that\n * case we need to abort the encoding of arguments since they'll never complete.\n */ function createHangingInputAbortSignal(workUnitStore) {\n    const controller = new AbortController();\n    if (workUnitStore.cacheSignal) {\n        // If we have a cacheSignal it means we're in a prospective render. If the input\n        // we're waiting on is coming from another cache, we do want to wait for it so that\n        // we can resolve this cache entry too.\n        workUnitStore.cacheSignal.inputReady().then(()=>{\n            controller.abort();\n        });\n    } else {\n        // Otherwise we're in the final render and we should already have all our caches\n        // filled. We might still be waiting on some microtasks so we wait one tick before\n        // giving up. When we give up, we still want to render the content of this cache\n        // as deeply as we can so that we can suspend as deeply as possible in the tree\n        // or not at all if we don't end up waiting for the input.\n        (0,_lib_scheduler__WEBPACK_IMPORTED_MODULE_7__.scheduleOnNextTick)(()=>controller.abort());\n    }\n    return controller.signal;\n}\nfunction annotateDynamicAccess(expression, prerenderStore) {\n    const dynamicTracking = prerenderStore.dynamicTracking;\n    if (dynamicTracking) {\n        dynamicTracking.dynamicAccesses.push({\n            stack: dynamicTracking.isDebugDynamicAccesses ? new Error().stack : undefined,\n            expression\n        });\n    }\n}\nfunction useDynamicRouteParams(expression) {\n    const workStore = _app_render_work_async_storage_external__WEBPACK_IMPORTED_MODULE_4__.workAsyncStorage.getStore();\n    if (workStore && workStore.isStaticGeneration && workStore.fallbackRouteParams && workStore.fallbackRouteParams.size > 0) {\n        // There are fallback route params, we should track these as dynamic\n        // accesses.\n        const workUnitStore = _work_unit_async_storage_external__WEBPACK_IMPORTED_MODULE_3__.workUnitAsyncStorage.getStore();\n        if (workUnitStore) {\n            // We're prerendering with dynamicIO or PPR or both\n            if (workUnitStore.type === 'prerender-client') {\n                // We are in a prerender with dynamicIO semantics\n                // We are going to hang here and never resolve. This will cause the currently\n                // rendering component to effectively be a dynamic hole\n                react__WEBPACK_IMPORTED_MODULE_0__.use((0,_dynamic_rendering_utils__WEBPACK_IMPORTED_MODULE_5__.makeHangingPromise)(workUnitStore.renderSignal, expression));\n            } else if (workUnitStore.type === 'prerender-ppr') {\n                // We're prerendering with PPR\n                postponeWithTracking(workStore.route, expression, workUnitStore.dynamicTracking);\n            } else if (workUnitStore.type === 'prerender-legacy') {\n                throwToInterruptStaticGeneration(expression, workStore, workUnitStore);\n            }\n        }\n    }\n}\nconst hasSuspenseRegex = /\\n\\s+at Suspense \\(<anonymous>\\)/;\nconst hasSuspenseAfterBodyOrHtmlRegex = /\\n\\s+at (?:body|html) \\(<anonymous>\\)[\\s\\S]*?\\n\\s+at Suspense \\(<anonymous>\\)/;\nconst hasMetadataRegex = new RegExp(`\\\\n\\\\s+at ${_lib_metadata_metadata_constants__WEBPACK_IMPORTED_MODULE_6__.METADATA_BOUNDARY_NAME}[\\\\n\\\\s]`);\nconst hasViewportRegex = new RegExp(`\\\\n\\\\s+at ${_lib_metadata_metadata_constants__WEBPACK_IMPORTED_MODULE_6__.VIEWPORT_BOUNDARY_NAME}[\\\\n\\\\s]`);\nconst hasOutletRegex = new RegExp(`\\\\n\\\\s+at ${_lib_metadata_metadata_constants__WEBPACK_IMPORTED_MODULE_6__.OUTLET_BOUNDARY_NAME}[\\\\n\\\\s]`);\nfunction trackAllowedDynamicAccess(workStore, componentStack, dynamicValidation, clientDynamic) {\n    if (hasOutletRegex.test(componentStack)) {\n        // We don't need to track that this is dynamic. It is only so when something else is also dynamic.\n        return;\n    } else if (hasMetadataRegex.test(componentStack)) {\n        dynamicValidation.hasDynamicMetadata = true;\n        return;\n    } else if (hasViewportRegex.test(componentStack)) {\n        dynamicValidation.hasDynamicViewport = true;\n        return;\n    } else if (hasSuspenseAfterBodyOrHtmlRegex.test(componentStack)) {\n        // This prerender has a Suspense boundary above the body which\n        // effectively opts the page into allowing 100% dynamic rendering\n        dynamicValidation.hasAllowedDynamic = true;\n        dynamicValidation.hasSuspenseAboveBody = true;\n        return;\n    } else if (hasSuspenseRegex.test(componentStack)) {\n        // this error had a Suspense boundary above it so we don't need to report it as a source\n        // of disallowed\n        dynamicValidation.hasAllowedDynamic = true;\n        return;\n    } else if (clientDynamic.syncDynamicErrorWithStack) {\n        // This task was the task that called the sync error.\n        dynamicValidation.dynamicErrors.push(clientDynamic.syncDynamicErrorWithStack);\n        return;\n    } else {\n        const message = `Route \"${workStore.route}\": A component accessed data, headers, params, searchParams, or a short-lived cache without a Suspense boundary nor a \"use cache\" above it. See more info: https://nextjs.org/docs/messages/next-prerender-missing-suspense`;\n        const error = createErrorWithComponentOrOwnerStack(message, componentStack);\n        dynamicValidation.dynamicErrors.push(error);\n        return;\n    }\n}\n/**\n * In dev mode, we prefer using the owner stack, otherwise the provided\n * component stack is used.\n */ function createErrorWithComponentOrOwnerStack(message, componentStack) {\n    const ownerStack =  true && react__WEBPACK_IMPORTED_MODULE_0__.captureOwnerStack ? react__WEBPACK_IMPORTED_MODULE_0__.captureOwnerStack() : null;\n    const error = Object.defineProperty(new Error(message), \"__NEXT_ERROR_CODE\", {\n        value: \"E394\",\n        enumerable: false,\n        configurable: true\n    });\n    error.stack = error.name + ': ' + message + (ownerStack ?? componentStack);\n    return error;\n}\nvar PreludeState = /*#__PURE__*/ function(PreludeState) {\n    PreludeState[PreludeState[\"Full\"] = 0] = \"Full\";\n    PreludeState[PreludeState[\"Empty\"] = 1] = \"Empty\";\n    PreludeState[PreludeState[\"Errored\"] = 2] = \"Errored\";\n    return PreludeState;\n}({});\nfunction logDisallowedDynamicError(workStore, error) {\n    console.error(error);\n    if (!workStore.dev) {\n        if (workStore.hasReadableErrorStacks) {\n            console.error(`To get a more detailed stack trace and pinpoint the issue, start the app in development mode by running \\`next dev\\`, then open \"${workStore.route}\" in your browser to investigate the error.`);\n        } else {\n            console.error(`To get a more detailed stack trace and pinpoint the issue, try one of the following:\n  - Start the app in development mode by running \\`next dev\\`, then open \"${workStore.route}\" in your browser to investigate the error.\n  - Rerun the production build with \\`next build --debug-prerender\\` to generate better stack traces.`);\n        }\n    }\n}\nfunction throwIfDisallowedDynamic(workStore, prelude, dynamicValidation, serverDynamic) {\n    if (workStore.invalidDynamicUsageError) {\n        logDisallowedDynamicError(workStore, workStore.invalidDynamicUsageError);\n        throw new _client_components_static_generation_bailout__WEBPACK_IMPORTED_MODULE_2__.StaticGenBailoutError();\n    }\n    if (prelude !== 0) {\n        if (dynamicValidation.hasSuspenseAboveBody) {\n            // This route has opted into allowing fully dynamic rendering\n            // by including a Suspense boundary above the body. In this case\n            // a lack of a shell is not considered disallowed so we simply return\n            return;\n        }\n        if (serverDynamic.syncDynamicErrorWithStack) {\n            // There is no shell and the server did something sync dynamic likely\n            // leading to an early termination of the prerender before the shell\n            // could be completed. We terminate the build/validating render.\n            logDisallowedDynamicError(workStore, serverDynamic.syncDynamicErrorWithStack);\n            throw new _client_components_static_generation_bailout__WEBPACK_IMPORTED_MODULE_2__.StaticGenBailoutError();\n        }\n        // We didn't have any sync bailouts but there may be user code which\n        // blocked the root. We would have captured these during the prerender\n        // and can log them here and then terminate the build/validating render\n        const dynamicErrors = dynamicValidation.dynamicErrors;\n        if (dynamicErrors.length > 0) {\n            for(let i = 0; i < dynamicErrors.length; i++){\n                logDisallowedDynamicError(workStore, dynamicErrors[i]);\n            }\n            throw new _client_components_static_generation_bailout__WEBPACK_IMPORTED_MODULE_2__.StaticGenBailoutError();\n        }\n        // If we got this far then the only other thing that could be blocking\n        // the root is dynamic Viewport. If this is dynamic then\n        // you need to opt into that by adding a Suspense boundary above the body\n        // to indicate your are ok with fully dynamic rendering.\n        if (dynamicValidation.hasDynamicViewport) {\n            console.error(`Route \"${workStore.route}\" has a \\`generateViewport\\` that depends on Request data (\\`cookies()\\`, etc...) or uncached external data (\\`fetch(...)\\`, etc...) without explicitly allowing fully dynamic rendering. See more info here: https://nextjs.org/docs/messages/next-prerender-dynamic-viewport`);\n            throw new _client_components_static_generation_bailout__WEBPACK_IMPORTED_MODULE_2__.StaticGenBailoutError();\n        }\n        if (prelude === 1) {\n            // If we ever get this far then we messed up the tracking of invalid dynamic.\n            // We still adhere to the constraint that you must produce a shell but invite the\n            // user to report this as a bug in Next.js.\n            console.error(`Route \"${workStore.route}\" did not produce a static shell and Next.js was unable to determine a reason. This is a bug in Next.js.`);\n            throw new _client_components_static_generation_bailout__WEBPACK_IMPORTED_MODULE_2__.StaticGenBailoutError();\n        }\n    } else {\n        if (dynamicValidation.hasAllowedDynamic === false && dynamicValidation.hasDynamicMetadata) {\n            console.error(`Route \"${workStore.route}\" has a \\`generateMetadata\\` that depends on Request data (\\`cookies()\\`, etc...) or uncached external data (\\`fetch(...)\\`, etc...) when the rest of the route does not. See more info here: https://nextjs.org/docs/messages/next-prerender-dynamic-metadata`);\n            throw new _client_components_static_generation_bailout__WEBPACK_IMPORTED_MODULE_2__.StaticGenBailoutError();\n        }\n    }\n} //# sourceMappingURL=dynamic-rendering.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./node_modules/next/dist/esm/server/app-render/dynamic-rendering.js\n");

/***/ }),

/***/ "(middleware)/./node_modules/next/dist/esm/server/request/cookies.js":
/*!**************************************************************!*\
  !*** ./node_modules/next/dist/esm/server/request/cookies.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cookies: () => (/* binding */ cookies)\n/* harmony export */ });\n/* harmony import */ var _web_spec_extension_adapters_request_cookies__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../web/spec-extension/adapters/request-cookies */ \"(middleware)/./node_modules/next/dist/esm/server/web/spec-extension/adapters/request-cookies.js\");\n/* harmony import */ var _web_spec_extension_cookies__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../web/spec-extension/cookies */ \"(middleware)/./node_modules/next/dist/esm/server/web/spec-extension/cookies.js\");\n/* harmony import */ var _app_render_work_async_storage_external__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../app-render/work-async-storage.external */ \"(shared)/./node_modules/next/dist/esm/server/app-render/work-async-storage.external.js\");\n/* harmony import */ var _app_render_work_unit_async_storage_external__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../app-render/work-unit-async-storage.external */ \"(shared)/./node_modules/next/dist/esm/server/app-render/work-unit-async-storage.external.js\");\n/* harmony import */ var _app_render_dynamic_rendering__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../app-render/dynamic-rendering */ \"(middleware)/./node_modules/next/dist/esm/server/app-render/dynamic-rendering.js\");\n/* harmony import */ var _client_components_static_generation_bailout__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../client/components/static-generation-bailout */ \"(middleware)/./node_modules/next/dist/esm/client/components/static-generation-bailout.js\");\n/* harmony import */ var _dynamic_rendering_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../dynamic-rendering-utils */ \"(middleware)/./node_modules/next/dist/esm/server/dynamic-rendering-utils.js\");\n/* harmony import */ var _create_deduped_by_callsite_server_error_logger__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../create-deduped-by-callsite-server-error-logger */ \"(middleware)/./node_modules/next/dist/esm/server/create-deduped-by-callsite-server-error-logger.js\");\n/* harmony import */ var _lib_scheduler__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../lib/scheduler */ \"(middleware)/./node_modules/next/dist/esm/lib/scheduler.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./utils */ \"(middleware)/./node_modules/next/dist/esm/server/request/utils.js\");\n/* harmony import */ var _shared_lib_invariant_error__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../../shared/lib/invariant-error */ \"(middleware)/./node_modules/next/dist/esm/shared/lib/invariant-error.js\");\n/* harmony import */ var _web_spec_extension_adapters_reflect__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../web/spec-extension/adapters/reflect */ \"(middleware)/./node_modules/next/dist/esm/server/web/spec-extension/adapters/reflect.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction cookies() {\n    const callingExpression = 'cookies';\n    const workStore = _app_render_work_async_storage_external__WEBPACK_IMPORTED_MODULE_2__.workAsyncStorage.getStore();\n    const workUnitStore = _app_render_work_unit_async_storage_external__WEBPACK_IMPORTED_MODULE_3__.workUnitAsyncStorage.getStore();\n    if (workStore) {\n        if (workUnitStore && workUnitStore.phase === 'after' && !(0,_utils__WEBPACK_IMPORTED_MODULE_9__.isRequestAPICallableInsideAfter)()) {\n            throw Object.defineProperty(new Error(`Route ${workStore.route} used \"cookies\" inside \"after(...)\". This is not supported. If you need this data inside an \"after\" callback, use \"cookies\" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`), \"__NEXT_ERROR_CODE\", {\n                value: \"E88\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n        if (workStore.forceStatic) {\n            // When using forceStatic we override all other logic and always just return an empty\n            // cookies object without tracking\n            const underlyingCookies = createEmptyCookies();\n            return makeUntrackedExoticCookies(underlyingCookies);\n        }\n        if (workUnitStore) {\n            if (workUnitStore.type === 'cache') {\n                throw Object.defineProperty(new Error(`Route ${workStore.route} used \"cookies\" inside \"use cache\". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \"cookies\" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E398\",\n                    enumerable: false,\n                    configurable: true\n                });\n            } else if (workUnitStore.type === 'unstable-cache') {\n                throw Object.defineProperty(new Error(`Route ${workStore.route} used \"cookies\" inside a function cached with \"unstable_cache(...)\". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \"cookies\" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E157\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n        }\n        if (workStore.dynamicShouldError) {\n            throw Object.defineProperty(new _client_components_static_generation_bailout__WEBPACK_IMPORTED_MODULE_5__.StaticGenBailoutError(`Route ${workStore.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`cookies\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`), \"__NEXT_ERROR_CODE\", {\n                value: \"E549\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n        if (workUnitStore) {\n            switch(workUnitStore.type){\n                case 'prerender':\n                    return makeHangingCookies(workUnitStore);\n                case 'prerender-client':\n                    const exportName = '`cookies`';\n                    throw Object.defineProperty(new _shared_lib_invariant_error__WEBPACK_IMPORTED_MODULE_10__.InvariantError(`${exportName} must not be used within a client component. Next.js should be preventing ${exportName} from being included in client components statically, but did not in this case.`), \"__NEXT_ERROR_CODE\", {\n                        value: \"E693\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                case 'prerender-ppr':\n                    // PPR Prerender (no dynamicIO)\n                    // We are prerendering with PPR. We need track dynamic access here eagerly\n                    // to keep continuity with how cookies has worked in PPR without dynamicIO.\n                    (0,_app_render_dynamic_rendering__WEBPACK_IMPORTED_MODULE_4__.postponeWithTracking)(workStore.route, callingExpression, workUnitStore.dynamicTracking);\n                    break;\n                case 'prerender-legacy':\n                    // Legacy Prerender\n                    // We track dynamic access here so we don't need to wrap the cookies in\n                    // individual property access tracking.\n                    (0,_app_render_dynamic_rendering__WEBPACK_IMPORTED_MODULE_4__.throwToInterruptStaticGeneration)(callingExpression, workStore, workUnitStore);\n                    break;\n                default:\n            }\n        }\n        // We fall through to the dynamic context below but we still track dynamic access\n        // because in dev we can still error for things like using cookies inside a cache context\n        (0,_app_render_dynamic_rendering__WEBPACK_IMPORTED_MODULE_4__.trackDynamicDataInDynamicRender)(workStore, workUnitStore);\n    }\n    // cookies is being called in a dynamic context\n    const requestStore = (0,_app_render_work_unit_async_storage_external__WEBPACK_IMPORTED_MODULE_3__.getExpectedRequestStore)(callingExpression);\n    let underlyingCookies;\n    if ((0,_web_spec_extension_adapters_request_cookies__WEBPACK_IMPORTED_MODULE_0__.areCookiesMutableInCurrentPhase)(requestStore)) {\n        // We can't conditionally return different types here based on the context.\n        // To avoid confusion, we always return the readonly type here.\n        underlyingCookies = requestStore.userspaceMutableCookies;\n    } else {\n        underlyingCookies = requestStore.cookies;\n    }\n    if ( true && !(workStore == null ? void 0 : workStore.isPrefetchRequest)) {\n        if (false) {}\n        return makeUntrackedExoticCookiesWithDevWarnings(underlyingCookies, workStore == null ? void 0 : workStore.route);\n    } else {\n        return makeUntrackedExoticCookies(underlyingCookies);\n    }\n}\nfunction createEmptyCookies() {\n    return _web_spec_extension_adapters_request_cookies__WEBPACK_IMPORTED_MODULE_0__.RequestCookiesAdapter.seal(new _web_spec_extension_cookies__WEBPACK_IMPORTED_MODULE_1__.RequestCookies(new Headers({})));\n}\nconst CachedCookies = new WeakMap();\nfunction makeHangingCookies(prerenderStore) {\n    const cachedPromise = CachedCookies.get(prerenderStore);\n    if (cachedPromise) {\n        return cachedPromise;\n    }\n    const promise = (0,_dynamic_rendering_utils__WEBPACK_IMPORTED_MODULE_6__.makeHangingPromise)(prerenderStore.renderSignal, '`cookies()`');\n    CachedCookies.set(prerenderStore, promise);\n    return promise;\n}\nfunction makeUntrackedExoticCookies(underlyingCookies) {\n    const cachedCookies = CachedCookies.get(underlyingCookies);\n    if (cachedCookies) {\n        return cachedCookies;\n    }\n    const promise = Promise.resolve(underlyingCookies);\n    CachedCookies.set(underlyingCookies, promise);\n    Object.defineProperties(promise, {\n        [Symbol.iterator]: {\n            value: underlyingCookies[Symbol.iterator] ? underlyingCookies[Symbol.iterator].bind(underlyingCookies) : // but that's already a hard thing to debug so we may as well implement it consistently. The biggest problem with\n            // implementing this in this way is the underlying cookie type is a ResponseCookie and not a RequestCookie and so it\n            // has extra properties not available on RequestCookie instances.\n            polyfilledResponseCookiesIterator.bind(underlyingCookies)\n        },\n        size: {\n            get () {\n                return underlyingCookies.size;\n            }\n        },\n        get: {\n            value: underlyingCookies.get.bind(underlyingCookies)\n        },\n        getAll: {\n            value: underlyingCookies.getAll.bind(underlyingCookies)\n        },\n        has: {\n            value: underlyingCookies.has.bind(underlyingCookies)\n        },\n        set: {\n            value: underlyingCookies.set.bind(underlyingCookies)\n        },\n        delete: {\n            value: underlyingCookies.delete.bind(underlyingCookies)\n        },\n        clear: {\n            value: typeof underlyingCookies.clear === 'function' ? underlyingCookies.clear.bind(underlyingCookies) : // but that's already a hard thing to debug so we may as well implement it consistently. The biggest problem with\n            // implementing this in this way is the underlying cookie type is a ResponseCookie and not a RequestCookie and so it\n            // has extra properties not available on RequestCookie instances.\n            polyfilledResponseCookiesClear.bind(underlyingCookies, promise)\n        },\n        toString: {\n            value: underlyingCookies.toString.bind(underlyingCookies)\n        }\n    });\n    return promise;\n}\nfunction makeUntrackedExoticCookiesWithDevWarnings(underlyingCookies, route) {\n    const cachedCookies = CachedCookies.get(underlyingCookies);\n    if (cachedCookies) {\n        return cachedCookies;\n    }\n    const promise = new Promise((resolve)=>(0,_lib_scheduler__WEBPACK_IMPORTED_MODULE_8__.scheduleImmediate)(()=>resolve(underlyingCookies)));\n    CachedCookies.set(underlyingCookies, promise);\n    Object.defineProperties(promise, {\n        [Symbol.iterator]: {\n            value: function() {\n                const expression = '`...cookies()` or similar iteration';\n                syncIODev(route, expression);\n                return underlyingCookies[Symbol.iterator] ? underlyingCookies[Symbol.iterator].apply(underlyingCookies, arguments) : // but that's already a hard thing to debug so we may as well implement it consistently. The biggest problem with\n                // implementing this in this way is the underlying cookie type is a ResponseCookie and not a RequestCookie and so it\n                // has extra properties not available on RequestCookie instances.\n                polyfilledResponseCookiesIterator.call(underlyingCookies);\n            },\n            writable: false\n        },\n        size: {\n            get () {\n                const expression = '`cookies().size`';\n                syncIODev(route, expression);\n                return underlyingCookies.size;\n            }\n        },\n        get: {\n            value: function get() {\n                let expression;\n                if (arguments.length === 0) {\n                    expression = '`cookies().get()`';\n                } else {\n                    expression = `\\`cookies().get(${describeNameArg(arguments[0])})\\``;\n                }\n                syncIODev(route, expression);\n                return underlyingCookies.get.apply(underlyingCookies, arguments);\n            },\n            writable: false\n        },\n        getAll: {\n            value: function getAll() {\n                let expression;\n                if (arguments.length === 0) {\n                    expression = '`cookies().getAll()`';\n                } else {\n                    expression = `\\`cookies().getAll(${describeNameArg(arguments[0])})\\``;\n                }\n                syncIODev(route, expression);\n                return underlyingCookies.getAll.apply(underlyingCookies, arguments);\n            },\n            writable: false\n        },\n        has: {\n            value: function get() {\n                let expression;\n                if (arguments.length === 0) {\n                    expression = '`cookies().has()`';\n                } else {\n                    expression = `\\`cookies().has(${describeNameArg(arguments[0])})\\``;\n                }\n                syncIODev(route, expression);\n                return underlyingCookies.has.apply(underlyingCookies, arguments);\n            },\n            writable: false\n        },\n        set: {\n            value: function set() {\n                let expression;\n                if (arguments.length === 0) {\n                    expression = '`cookies().set()`';\n                } else {\n                    const arg = arguments[0];\n                    if (arg) {\n                        expression = `\\`cookies().set(${describeNameArg(arg)}, ...)\\``;\n                    } else {\n                        expression = '`cookies().set(...)`';\n                    }\n                }\n                syncIODev(route, expression);\n                return underlyingCookies.set.apply(underlyingCookies, arguments);\n            },\n            writable: false\n        },\n        delete: {\n            value: function() {\n                let expression;\n                if (arguments.length === 0) {\n                    expression = '`cookies().delete()`';\n                } else if (arguments.length === 1) {\n                    expression = `\\`cookies().delete(${describeNameArg(arguments[0])})\\``;\n                } else {\n                    expression = `\\`cookies().delete(${describeNameArg(arguments[0])}, ...)\\``;\n                }\n                syncIODev(route, expression);\n                return underlyingCookies.delete.apply(underlyingCookies, arguments);\n            },\n            writable: false\n        },\n        clear: {\n            value: function clear() {\n                const expression = '`cookies().clear()`';\n                syncIODev(route, expression);\n                // @ts-ignore clear is defined in RequestCookies implementation but not in the type\n                return typeof underlyingCookies.clear === 'function' ? underlyingCookies.clear.apply(underlyingCookies, arguments) : // but that's already a hard thing to debug so we may as well implement it consistently. The biggest problem with\n                // implementing this in this way is the underlying cookie type is a ResponseCookie and not a RequestCookie and so it\n                // has extra properties not available on RequestCookie instances.\n                polyfilledResponseCookiesClear.call(underlyingCookies, promise);\n            },\n            writable: false\n        },\n        toString: {\n            value: function toString() {\n                const expression = '`cookies().toString()` or implicit casting';\n                syncIODev(route, expression);\n                return underlyingCookies.toString.apply(underlyingCookies, arguments);\n            },\n            writable: false\n        }\n    });\n    return promise;\n}\n// Similar to `makeUntrackedExoticCookiesWithDevWarnings`, but just logging the\n// sync access without actually defining the cookies properties on the promise.\nfunction makeUntrackedCookiesWithDevWarnings(underlyingCookies, route) {\n    const cachedCookies = CachedCookies.get(underlyingCookies);\n    if (cachedCookies) {\n        return cachedCookies;\n    }\n    const promise = new Promise((resolve)=>(0,_lib_scheduler__WEBPACK_IMPORTED_MODULE_8__.scheduleImmediate)(()=>resolve(underlyingCookies)));\n    const proxiedPromise = new Proxy(promise, {\n        get (target, prop, receiver) {\n            switch(prop){\n                case Symbol.iterator:\n                    {\n                        warnForSyncAccess(route, '`...cookies()` or similar iteration');\n                        break;\n                    }\n                case 'size':\n                case 'get':\n                case 'getAll':\n                case 'has':\n                case 'set':\n                case 'delete':\n                case 'clear':\n                case 'toString':\n                    {\n                        warnForSyncAccess(route, `\\`cookies().${prop}\\``);\n                        break;\n                    }\n                default:\n                    {\n                    // We only warn for well-defined properties of the cookies object.\n                    }\n            }\n            return _web_spec_extension_adapters_reflect__WEBPACK_IMPORTED_MODULE_11__.ReflectAdapter.get(target, prop, receiver);\n        }\n    });\n    CachedCookies.set(underlyingCookies, proxiedPromise);\n    return proxiedPromise;\n}\nfunction describeNameArg(arg) {\n    return typeof arg === 'object' && arg !== null && typeof arg.name === 'string' ? `'${arg.name}'` : typeof arg === 'string' ? `'${arg}'` : '...';\n}\nfunction syncIODev(route, expression) {\n    const workUnitStore = _app_render_work_unit_async_storage_external__WEBPACK_IMPORTED_MODULE_3__.workUnitAsyncStorage.getStore();\n    if (workUnitStore && workUnitStore.type === 'request' && workUnitStore.prerenderPhase === true) {\n        // When we're rendering dynamically in dev we need to advance out of the\n        // Prerender environment when we read Request data synchronously\n        const requestStore = workUnitStore;\n        (0,_app_render_dynamic_rendering__WEBPACK_IMPORTED_MODULE_4__.trackSynchronousRequestDataAccessInDev)(requestStore);\n    }\n    // In all cases we warn normally\n    warnForSyncAccess(route, expression);\n}\nconst warnForSyncAccess = (0,_create_deduped_by_callsite_server_error_logger__WEBPACK_IMPORTED_MODULE_7__.createDedupedByCallsiteServerErrorLoggerDev)(createCookiesAccessError);\nfunction createCookiesAccessError(route, expression) {\n    const prefix = route ? `Route \"${route}\" ` : 'This route ';\n    return Object.defineProperty(new Error(`${prefix}used ${expression}. ` + `\\`cookies()\\` should be awaited before using its value. ` + `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`), \"__NEXT_ERROR_CODE\", {\n        value: \"E223\",\n        enumerable: false,\n        configurable: true\n    });\n}\nfunction polyfilledResponseCookiesIterator() {\n    return this.getAll().map((c)=>[\n            c.name,\n            c\n        ]).values();\n}\nfunction polyfilledResponseCookiesClear(returnable) {\n    for (const cookie of this.getAll()){\n        this.delete(cookie.name);\n    }\n    return returnable;\n} //# sourceMappingURL=cookies.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./node_modules/next/dist/esm/server/request/cookies.js\n");

/***/ }),

/***/ "(middleware)/./node_modules/next/dist/esm/server/request/draft-mode.js":
/*!*****************************************************************!*\
  !*** ./node_modules/next/dist/esm/server/request/draft-mode.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   draftMode: () => (/* binding */ draftMode)\n/* harmony export */ });\n/* harmony import */ var _app_render_work_unit_async_storage_external__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../app-render/work-unit-async-storage.external */ \"(shared)/./node_modules/next/dist/esm/server/app-render/work-unit-async-storage.external.js\");\n/* harmony import */ var _app_render_work_async_storage_external__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../app-render/work-async-storage.external */ \"(shared)/./node_modules/next/dist/esm/server/app-render/work-async-storage.external.js\");\n/* harmony import */ var _app_render_dynamic_rendering__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../app-render/dynamic-rendering */ \"(middleware)/./node_modules/next/dist/esm/server/app-render/dynamic-rendering.js\");\n/* harmony import */ var _create_deduped_by_callsite_server_error_logger__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../create-deduped-by-callsite-server-error-logger */ \"(middleware)/./node_modules/next/dist/esm/server/create-deduped-by-callsite-server-error-logger.js\");\n/* harmony import */ var _client_components_static_generation_bailout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../client/components/static-generation-bailout */ \"(middleware)/./node_modules/next/dist/esm/client/components/static-generation-bailout.js\");\n/* harmony import */ var _client_components_hooks_server_context__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../client/components/hooks-server-context */ \"(middleware)/./node_modules/next/dist/esm/client/components/hooks-server-context.js\");\n/* harmony import */ var _shared_lib_invariant_error__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../shared/lib/invariant-error */ \"(middleware)/./node_modules/next/dist/esm/shared/lib/invariant-error.js\");\n/* harmony import */ var _web_spec_extension_adapters_reflect__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../web/spec-extension/adapters/reflect */ \"(middleware)/./node_modules/next/dist/esm/server/web/spec-extension/adapters/reflect.js\");\n\n\n\n\n\n\n\n\n\nfunction draftMode() {\n    const callingExpression = 'draftMode';\n    const workStore = _app_render_work_async_storage_external__WEBPACK_IMPORTED_MODULE_1__.workAsyncStorage.getStore();\n    const workUnitStore = _app_render_work_unit_async_storage_external__WEBPACK_IMPORTED_MODULE_0__.workUnitAsyncStorage.getStore();\n    if (!workStore || !workUnitStore) {\n        (0,_app_render_work_unit_async_storage_external__WEBPACK_IMPORTED_MODULE_0__.throwForMissingRequestStore)(callingExpression);\n    }\n    switch(workUnitStore.type){\n        case 'request':\n            return createOrGetCachedDraftMode(workUnitStore.draftMode, workStore);\n        case 'cache':\n        case 'unstable-cache':\n            // Inside of `\"use cache\"` or `unstable_cache`, draft mode is available if\n            // the outmost work unit store is a request store, and if draft mode is\n            // enabled.\n            const draftModeProvider = (0,_app_render_work_unit_async_storage_external__WEBPACK_IMPORTED_MODULE_0__.getDraftModeProviderForCacheScope)(workStore, workUnitStore);\n            if (draftModeProvider) {\n                return createOrGetCachedDraftMode(draftModeProvider, workStore);\n            }\n        // Otherwise, we fall through to providing an empty draft mode.\n        // eslint-disable-next-line no-fallthrough\n        case 'prerender':\n        case 'prerender-client':\n        case 'prerender-ppr':\n        case 'prerender-legacy':\n            // Return empty draft mode\n            return createOrGetCachedDraftMode(null, workStore);\n        default:\n            const _exhaustiveCheck = workUnitStore;\n            return _exhaustiveCheck;\n    }\n}\nfunction createOrGetCachedDraftMode(draftModeProvider, workStore) {\n    const cacheKey = draftModeProvider ?? NullDraftMode;\n    const cachedDraftMode = CachedDraftModes.get(cacheKey);\n    if (cachedDraftMode) {\n        return cachedDraftMode;\n    }\n    let promise;\n    if ( true && !(workStore == null ? void 0 : workStore.isPrefetchRequest)) {\n        const route = workStore == null ? void 0 : workStore.route;\n        if (false) {}\n        promise = createExoticDraftModeWithDevWarnings(draftModeProvider, route);\n    } else {\n        if (false) {}\n        promise = createExoticDraftMode(draftModeProvider);\n    }\n    CachedDraftModes.set(cacheKey, promise);\n    return promise;\n}\nconst NullDraftMode = {};\nconst CachedDraftModes = new WeakMap();\nfunction createExoticDraftMode(underlyingProvider) {\n    const instance = new DraftMode(underlyingProvider);\n    const promise = Promise.resolve(instance);\n    Object.defineProperty(promise, 'isEnabled', {\n        get () {\n            return instance.isEnabled;\n        },\n        enumerable: true,\n        configurable: true\n    });\n    promise.enable = instance.enable.bind(instance);\n    promise.disable = instance.disable.bind(instance);\n    return promise;\n}\nfunction createExoticDraftModeWithDevWarnings(underlyingProvider, route) {\n    const instance = new DraftMode(underlyingProvider);\n    const promise = Promise.resolve(instance);\n    Object.defineProperty(promise, 'isEnabled', {\n        get () {\n            const expression = '`draftMode().isEnabled`';\n            syncIODev(route, expression);\n            return instance.isEnabled;\n        },\n        enumerable: true,\n        configurable: true\n    });\n    Object.defineProperty(promise, 'enable', {\n        value: function get() {\n            const expression = '`draftMode().enable()`';\n            syncIODev(route, expression);\n            return instance.enable.apply(instance, arguments);\n        }\n    });\n    Object.defineProperty(promise, 'disable', {\n        value: function get() {\n            const expression = '`draftMode().disable()`';\n            syncIODev(route, expression);\n            return instance.disable.apply(instance, arguments);\n        }\n    });\n    return promise;\n}\n// Similar to `createExoticDraftModeWithDevWarnings`, but just logging the sync\n// access without actually defining the draftMode properties on the promise.\nfunction createDraftModeWithDevWarnings(underlyingProvider, route) {\n    const instance = new DraftMode(underlyingProvider);\n    const promise = Promise.resolve(instance);\n    const proxiedPromise = new Proxy(promise, {\n        get (target, prop, receiver) {\n            switch(prop){\n                case 'isEnabled':\n                    warnForSyncAccess(route, `\\`draftMode().${prop}\\``);\n                    break;\n                case 'enable':\n                case 'disable':\n                    {\n                        warnForSyncAccess(route, `\\`draftMode().${prop}()\\``);\n                        break;\n                    }\n                default:\n                    {\n                    // We only warn for well-defined properties of the draftMode object.\n                    }\n            }\n            return _web_spec_extension_adapters_reflect__WEBPACK_IMPORTED_MODULE_7__.ReflectAdapter.get(target, prop, receiver);\n        }\n    });\n    return proxiedPromise;\n}\nclass DraftMode {\n    constructor(provider){\n        this._provider = provider;\n    }\n    get isEnabled() {\n        if (this._provider !== null) {\n            return this._provider.isEnabled;\n        }\n        return false;\n    }\n    enable() {\n        // We have a store we want to track dynamic data access to ensure we\n        // don't statically generate routes that manipulate draft mode.\n        trackDynamicDraftMode('draftMode().enable()');\n        if (this._provider !== null) {\n            this._provider.enable();\n        }\n    }\n    disable() {\n        trackDynamicDraftMode('draftMode().disable()');\n        if (this._provider !== null) {\n            this._provider.disable();\n        }\n    }\n}\nfunction syncIODev(route, expression) {\n    const workUnitStore = _app_render_work_unit_async_storage_external__WEBPACK_IMPORTED_MODULE_0__.workUnitAsyncStorage.getStore();\n    if (workUnitStore && workUnitStore.type === 'request' && workUnitStore.prerenderPhase === true) {\n        // When we're rendering dynamically in dev we need to advance out of the\n        // Prerender environment when we read Request data synchronously\n        const requestStore = workUnitStore;\n        (0,_app_render_dynamic_rendering__WEBPACK_IMPORTED_MODULE_2__.trackSynchronousRequestDataAccessInDev)(requestStore);\n    }\n    // In all cases we warn normally\n    warnForSyncAccess(route, expression);\n}\nconst warnForSyncAccess = (0,_create_deduped_by_callsite_server_error_logger__WEBPACK_IMPORTED_MODULE_3__.createDedupedByCallsiteServerErrorLoggerDev)(createDraftModeAccessError);\nfunction createDraftModeAccessError(route, expression) {\n    const prefix = route ? `Route \"${route}\" ` : 'This route ';\n    return Object.defineProperty(new Error(`${prefix}used ${expression}. ` + `\\`draftMode()\\` should be awaited before using its value. ` + `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`), \"__NEXT_ERROR_CODE\", {\n        value: \"E377\",\n        enumerable: false,\n        configurable: true\n    });\n}\nfunction trackDynamicDraftMode(expression) {\n    const store = _app_render_work_async_storage_external__WEBPACK_IMPORTED_MODULE_1__.workAsyncStorage.getStore();\n    const workUnitStore = _app_render_work_unit_async_storage_external__WEBPACK_IMPORTED_MODULE_0__.workUnitAsyncStorage.getStore();\n    if (store) {\n        // We have a store we want to track dynamic data access to ensure we\n        // don't statically generate routes that manipulate draft mode.\n        if (workUnitStore) {\n            if (workUnitStore.type === 'cache') {\n                throw Object.defineProperty(new Error(`Route ${store.route} used \"${expression}\" inside \"use cache\". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E246\",\n                    enumerable: false,\n                    configurable: true\n                });\n            } else if (workUnitStore.type === 'unstable-cache') {\n                throw Object.defineProperty(new Error(`Route ${store.route} used \"${expression}\" inside a function cached with \"unstable_cache(...)\". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E259\",\n                    enumerable: false,\n                    configurable: true\n                });\n            } else if (workUnitStore.phase === 'after') {\n                throw Object.defineProperty(new Error(`Route ${store.route} used \"${expression}\" inside \\`after\\`. The enabled status of draftMode can be read inside \\`after\\` but you cannot enable or disable draftMode. See more info here: https://nextjs.org/docs/app/api-reference/functions/after`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E348\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n        }\n        if (store.dynamicShouldError) {\n            throw Object.defineProperty(new _client_components_static_generation_bailout__WEBPACK_IMPORTED_MODULE_4__.StaticGenBailoutError(`Route ${store.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`), \"__NEXT_ERROR_CODE\", {\n                value: \"E553\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n        if (workUnitStore) {\n            switch(workUnitStore.type){\n                case 'prerender':\n                    // dynamicIO Prerender\n                    const error = Object.defineProperty(new Error(`Route ${store.route} used ${expression} without first calling \\`await connection()\\`. See more info here: https://nextjs.org/docs/messages/next-prerender-sync-headers`), \"__NEXT_ERROR_CODE\", {\n                        value: \"E126\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                    (0,_app_render_dynamic_rendering__WEBPACK_IMPORTED_MODULE_2__.abortAndThrowOnSynchronousRequestDataAccess)(store.route, expression, error, workUnitStore);\n                    break;\n                case 'prerender-client':\n                    const exportName = '`draftMode`';\n                    throw Object.defineProperty(new _shared_lib_invariant_error__WEBPACK_IMPORTED_MODULE_6__.InvariantError(`${exportName} must not be used within a client component. Next.js should be preventing ${exportName} from being included in client components statically, but did not in this case.`), \"__NEXT_ERROR_CODE\", {\n                        value: \"E693\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                case 'prerender-ppr':\n                    // PPR Prerender\n                    (0,_app_render_dynamic_rendering__WEBPACK_IMPORTED_MODULE_2__.postponeWithTracking)(store.route, expression, workUnitStore.dynamicTracking);\n                    break;\n                case 'prerender-legacy':\n                    // legacy Prerender\n                    workUnitStore.revalidate = 0;\n                    const err = Object.defineProperty(new _client_components_hooks_server_context__WEBPACK_IMPORTED_MODULE_5__.DynamicServerError(`Route ${store.route} couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`), \"__NEXT_ERROR_CODE\", {\n                        value: \"E558\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                    store.dynamicUsageDescription = expression;\n                    store.dynamicUsageStack = err.stack;\n                    throw err;\n                case 'request':\n                    if (true) {\n                        workUnitStore.usedDynamic = true;\n                    }\n                    break;\n                default:\n            }\n        }\n    }\n} //# sourceMappingURL=draft-mode.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./node_modules/next/dist/esm/server/request/draft-mode.js\n");

/***/ }),

/***/ "(middleware)/./node_modules/next/dist/esm/server/request/headers.js":
/*!**************************************************************!*\
  !*** ./node_modules/next/dist/esm/server/request/headers.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headers: () => (/* binding */ headers)\n/* harmony export */ });\n/* harmony import */ var _web_spec_extension_adapters_headers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../web/spec-extension/adapters/headers */ \"(middleware)/./node_modules/next/dist/esm/server/web/spec-extension/adapters/headers.js\");\n/* harmony import */ var _app_render_work_async_storage_external__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../app-render/work-async-storage.external */ \"(shared)/./node_modules/next/dist/esm/server/app-render/work-async-storage.external.js\");\n/* harmony import */ var _app_render_work_unit_async_storage_external__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../app-render/work-unit-async-storage.external */ \"(shared)/./node_modules/next/dist/esm/server/app-render/work-unit-async-storage.external.js\");\n/* harmony import */ var _app_render_dynamic_rendering__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../app-render/dynamic-rendering */ \"(middleware)/./node_modules/next/dist/esm/server/app-render/dynamic-rendering.js\");\n/* harmony import */ var _client_components_static_generation_bailout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../client/components/static-generation-bailout */ \"(middleware)/./node_modules/next/dist/esm/client/components/static-generation-bailout.js\");\n/* harmony import */ var _dynamic_rendering_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../dynamic-rendering-utils */ \"(middleware)/./node_modules/next/dist/esm/server/dynamic-rendering-utils.js\");\n/* harmony import */ var _create_deduped_by_callsite_server_error_logger__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../create-deduped-by-callsite-server-error-logger */ \"(middleware)/./node_modules/next/dist/esm/server/create-deduped-by-callsite-server-error-logger.js\");\n/* harmony import */ var _lib_scheduler__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../lib/scheduler */ \"(middleware)/./node_modules/next/dist/esm/lib/scheduler.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./utils */ \"(middleware)/./node_modules/next/dist/esm/server/request/utils.js\");\n/* harmony import */ var _shared_lib_invariant_error__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../shared/lib/invariant-error */ \"(middleware)/./node_modules/next/dist/esm/shared/lib/invariant-error.js\");\n/* harmony import */ var _web_spec_extension_adapters_reflect__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../web/spec-extension/adapters/reflect */ \"(middleware)/./node_modules/next/dist/esm/server/web/spec-extension/adapters/reflect.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n/**\n * This function allows you to read the HTTP incoming request headers in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers) and\n * [Middleware](https://nextjs.org/docs/app/building-your-application/routing/middleware).\n *\n * Read more: [Next.js Docs: `headers`](https://nextjs.org/docs/app/api-reference/functions/headers)\n */ function headers() {\n    const workStore = _app_render_work_async_storage_external__WEBPACK_IMPORTED_MODULE_1__.workAsyncStorage.getStore();\n    const workUnitStore = _app_render_work_unit_async_storage_external__WEBPACK_IMPORTED_MODULE_2__.workUnitAsyncStorage.getStore();\n    if (workStore) {\n        if (workUnitStore && workUnitStore.phase === 'after' && !(0,_utils__WEBPACK_IMPORTED_MODULE_8__.isRequestAPICallableInsideAfter)()) {\n            throw Object.defineProperty(new Error(`Route ${workStore.route} used \"headers\" inside \"after(...)\". This is not supported. If you need this data inside an \"after\" callback, use \"headers\" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`), \"__NEXT_ERROR_CODE\", {\n                value: \"E367\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n        if (workStore.forceStatic) {\n            // When using forceStatic we override all other logic and always just return an empty\n            // headers object without tracking\n            const underlyingHeaders = _web_spec_extension_adapters_headers__WEBPACK_IMPORTED_MODULE_0__.HeadersAdapter.seal(new Headers({}));\n            return makeUntrackedExoticHeaders(underlyingHeaders);\n        }\n        if (workUnitStore) {\n            if (workUnitStore.type === 'cache') {\n                throw Object.defineProperty(new Error(`Route ${workStore.route} used \"headers\" inside \"use cache\". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \"headers\" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E304\",\n                    enumerable: false,\n                    configurable: true\n                });\n            } else if (workUnitStore.type === 'unstable-cache') {\n                throw Object.defineProperty(new Error(`Route ${workStore.route} used \"headers\" inside a function cached with \"unstable_cache(...)\". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \"headers\" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E127\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n        }\n        if (workStore.dynamicShouldError) {\n            throw Object.defineProperty(new _client_components_static_generation_bailout__WEBPACK_IMPORTED_MODULE_4__.StaticGenBailoutError(`Route ${workStore.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`headers\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`), \"__NEXT_ERROR_CODE\", {\n                value: \"E525\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n        if (workUnitStore) {\n            switch(workUnitStore.type){\n                case 'prerender':\n                    return makeHangingHeaders(workUnitStore);\n                case 'prerender-client':\n                    const exportName = '`headers`';\n                    throw Object.defineProperty(new _shared_lib_invariant_error__WEBPACK_IMPORTED_MODULE_9__.InvariantError(`${exportName} must not be used within a client component. Next.js should be preventing ${exportName} from being included in client components statically, but did not in this case.`), \"__NEXT_ERROR_CODE\", {\n                        value: \"E693\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                case 'prerender-ppr':\n                    // PPR Prerender (no dynamicIO)\n                    // We are prerendering with PPR. We need track dynamic access here eagerly\n                    // to keep continuity with how headers has worked in PPR without dynamicIO.\n                    // TODO consider switching the semantic to throw on property access instead\n                    (0,_app_render_dynamic_rendering__WEBPACK_IMPORTED_MODULE_3__.postponeWithTracking)(workStore.route, 'headers', workUnitStore.dynamicTracking);\n                    break;\n                case 'prerender-legacy':\n                    // Legacy Prerender\n                    // We are in a legacy static generation mode while prerendering\n                    // We track dynamic access here so we don't need to wrap the headers in\n                    // individual property access tracking.\n                    (0,_app_render_dynamic_rendering__WEBPACK_IMPORTED_MODULE_3__.throwToInterruptStaticGeneration)('headers', workStore, workUnitStore);\n                    break;\n                default:\n            }\n        }\n        // We fall through to the dynamic context below but we still track dynamic access\n        // because in dev we can still error for things like using headers inside a cache context\n        (0,_app_render_dynamic_rendering__WEBPACK_IMPORTED_MODULE_3__.trackDynamicDataInDynamicRender)(workStore, workUnitStore);\n    }\n    const requestStore = (0,_app_render_work_unit_async_storage_external__WEBPACK_IMPORTED_MODULE_2__.getExpectedRequestStore)('headers');\n    if ( true && !(workStore == null ? void 0 : workStore.isPrefetchRequest)) {\n        if (false) {}\n        return makeUntrackedExoticHeadersWithDevWarnings(requestStore.headers, workStore == null ? void 0 : workStore.route);\n    } else {\n        return makeUntrackedExoticHeaders(requestStore.headers);\n    }\n}\nconst CachedHeaders = new WeakMap();\nfunction makeHangingHeaders(prerenderStore) {\n    const cachedHeaders = CachedHeaders.get(prerenderStore);\n    if (cachedHeaders) {\n        return cachedHeaders;\n    }\n    const promise = (0,_dynamic_rendering_utils__WEBPACK_IMPORTED_MODULE_5__.makeHangingPromise)(prerenderStore.renderSignal, '`headers()`');\n    CachedHeaders.set(prerenderStore, promise);\n    return promise;\n}\nfunction makeUntrackedExoticHeaders(underlyingHeaders) {\n    const cachedHeaders = CachedHeaders.get(underlyingHeaders);\n    if (cachedHeaders) {\n        return cachedHeaders;\n    }\n    const promise = Promise.resolve(underlyingHeaders);\n    CachedHeaders.set(underlyingHeaders, promise);\n    Object.defineProperties(promise, {\n        append: {\n            value: underlyingHeaders.append.bind(underlyingHeaders)\n        },\n        delete: {\n            value: underlyingHeaders.delete.bind(underlyingHeaders)\n        },\n        get: {\n            value: underlyingHeaders.get.bind(underlyingHeaders)\n        },\n        has: {\n            value: underlyingHeaders.has.bind(underlyingHeaders)\n        },\n        set: {\n            value: underlyingHeaders.set.bind(underlyingHeaders)\n        },\n        getSetCookie: {\n            value: underlyingHeaders.getSetCookie.bind(underlyingHeaders)\n        },\n        forEach: {\n            value: underlyingHeaders.forEach.bind(underlyingHeaders)\n        },\n        keys: {\n            value: underlyingHeaders.keys.bind(underlyingHeaders)\n        },\n        values: {\n            value: underlyingHeaders.values.bind(underlyingHeaders)\n        },\n        entries: {\n            value: underlyingHeaders.entries.bind(underlyingHeaders)\n        },\n        [Symbol.iterator]: {\n            value: underlyingHeaders[Symbol.iterator].bind(underlyingHeaders)\n        }\n    });\n    return promise;\n}\nfunction makeUntrackedExoticHeadersWithDevWarnings(underlyingHeaders, route) {\n    const cachedHeaders = CachedHeaders.get(underlyingHeaders);\n    if (cachedHeaders) {\n        return cachedHeaders;\n    }\n    const promise = new Promise((resolve)=>(0,_lib_scheduler__WEBPACK_IMPORTED_MODULE_7__.scheduleImmediate)(()=>resolve(underlyingHeaders)));\n    CachedHeaders.set(underlyingHeaders, promise);\n    Object.defineProperties(promise, {\n        append: {\n            value: function append() {\n                const expression = `\\`headers().append(${describeNameArg(arguments[0])}, ...)\\``;\n                syncIODev(route, expression);\n                return underlyingHeaders.append.apply(underlyingHeaders, arguments);\n            }\n        },\n        delete: {\n            value: function _delete() {\n                const expression = `\\`headers().delete(${describeNameArg(arguments[0])})\\``;\n                syncIODev(route, expression);\n                return underlyingHeaders.delete.apply(underlyingHeaders, arguments);\n            }\n        },\n        get: {\n            value: function get() {\n                const expression = `\\`headers().get(${describeNameArg(arguments[0])})\\``;\n                syncIODev(route, expression);\n                return underlyingHeaders.get.apply(underlyingHeaders, arguments);\n            }\n        },\n        has: {\n            value: function has() {\n                const expression = `\\`headers().has(${describeNameArg(arguments[0])})\\``;\n                syncIODev(route, expression);\n                return underlyingHeaders.has.apply(underlyingHeaders, arguments);\n            }\n        },\n        set: {\n            value: function set() {\n                const expression = `\\`headers().set(${describeNameArg(arguments[0])}, ...)\\``;\n                syncIODev(route, expression);\n                return underlyingHeaders.set.apply(underlyingHeaders, arguments);\n            }\n        },\n        getSetCookie: {\n            value: function getSetCookie() {\n                const expression = '`headers().getSetCookie()`';\n                syncIODev(route, expression);\n                return underlyingHeaders.getSetCookie.apply(underlyingHeaders, arguments);\n            }\n        },\n        forEach: {\n            value: function forEach() {\n                const expression = '`headers().forEach(...)`';\n                syncIODev(route, expression);\n                return underlyingHeaders.forEach.apply(underlyingHeaders, arguments);\n            }\n        },\n        keys: {\n            value: function keys() {\n                const expression = '`headers().keys()`';\n                syncIODev(route, expression);\n                return underlyingHeaders.keys.apply(underlyingHeaders, arguments);\n            }\n        },\n        values: {\n            value: function values() {\n                const expression = '`headers().values()`';\n                syncIODev(route, expression);\n                return underlyingHeaders.values.apply(underlyingHeaders, arguments);\n            }\n        },\n        entries: {\n            value: function entries() {\n                const expression = '`headers().entries()`';\n                syncIODev(route, expression);\n                return underlyingHeaders.entries.apply(underlyingHeaders, arguments);\n            }\n        },\n        [Symbol.iterator]: {\n            value: function() {\n                const expression = '`...headers()` or similar iteration';\n                syncIODev(route, expression);\n                return underlyingHeaders[Symbol.iterator].apply(underlyingHeaders, arguments);\n            }\n        }\n    });\n    return promise;\n}\n// Similar to `makeUntrackedExoticHeadersWithDevWarnings`, but just logging the\n// sync access without actually defining the headers properties on the promise.\nfunction makeUntrackedHeadersWithDevWarnings(underlyingHeaders, route) {\n    const cachedHeaders = CachedHeaders.get(underlyingHeaders);\n    if (cachedHeaders) {\n        return cachedHeaders;\n    }\n    const promise = new Promise((resolve)=>(0,_lib_scheduler__WEBPACK_IMPORTED_MODULE_7__.scheduleImmediate)(()=>resolve(underlyingHeaders)));\n    const proxiedPromise = new Proxy(promise, {\n        get (target, prop, receiver) {\n            switch(prop){\n                case Symbol.iterator:\n                    {\n                        warnForSyncAccess(route, '`...headers()` or similar iteration');\n                        break;\n                    }\n                case 'append':\n                case 'delete':\n                case 'get':\n                case 'has':\n                case 'set':\n                case 'getSetCookie':\n                case 'forEach':\n                case 'keys':\n                case 'values':\n                case 'entries':\n                    {\n                        warnForSyncAccess(route, `\\`headers().${prop}\\``);\n                        break;\n                    }\n                default:\n                    {\n                    // We only warn for well-defined properties of the headers object.\n                    }\n            }\n            return _web_spec_extension_adapters_reflect__WEBPACK_IMPORTED_MODULE_10__.ReflectAdapter.get(target, prop, receiver);\n        }\n    });\n    CachedHeaders.set(underlyingHeaders, proxiedPromise);\n    return proxiedPromise;\n}\nfunction describeNameArg(arg) {\n    return typeof arg === 'string' ? `'${arg}'` : '...';\n}\nfunction syncIODev(route, expression) {\n    const workUnitStore = _app_render_work_unit_async_storage_external__WEBPACK_IMPORTED_MODULE_2__.workUnitAsyncStorage.getStore();\n    if (workUnitStore && workUnitStore.type === 'request' && workUnitStore.prerenderPhase === true) {\n        // When we're rendering dynamically in dev we need to advance out of the\n        // Prerender environment when we read Request data synchronously\n        const requestStore = workUnitStore;\n        (0,_app_render_dynamic_rendering__WEBPACK_IMPORTED_MODULE_3__.trackSynchronousRequestDataAccessInDev)(requestStore);\n    }\n    // In all cases we warn normally\n    warnForSyncAccess(route, expression);\n}\nconst warnForSyncAccess = (0,_create_deduped_by_callsite_server_error_logger__WEBPACK_IMPORTED_MODULE_6__.createDedupedByCallsiteServerErrorLoggerDev)(createHeadersAccessError);\nfunction createHeadersAccessError(route, expression) {\n    const prefix = route ? `Route \"${route}\" ` : 'This route ';\n    return Object.defineProperty(new Error(`${prefix}used ${expression}. ` + `\\`headers()\\` should be awaited before using its value. ` + `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`), \"__NEXT_ERROR_CODE\", {\n        value: \"E277\",\n        enumerable: false,\n        configurable: true\n    });\n} //# sourceMappingURL=headers.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKG1pZGRsZXdhcmUpLy4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9lc20vc2VydmVyL3JlcXVlc3QvaGVhZGVycy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7QUFHK0M7QUFDNkI7QUFDWTtBQUlqQztBQU1mO0FBQ2lEO0FBQzFCO0FBQ2dEO0FBQ3hEO0FBQ0U7QUFDUTtBQUNNO0FBeUJ2RTs7Ozs7Ozs7Q0FRQyxHQUNNLFNBQVNlO0lBQ2QsTUFBTUMsWUFBWWYscUZBQWdCQSxDQUFDZ0IsUUFBUTtJQUMzQyxNQUFNQyxnQkFBZ0JmLDhGQUFvQkEsQ0FBQ2MsUUFBUTtJQUVuRCxJQUFJRCxXQUFXO1FBQ2IsSUFDRUUsaUJBQ0FBLGNBQWNDLEtBQUssS0FBSyxXQUN4QixDQUFDUCx1RUFBK0JBLElBQ2hDO1lBQ0EsTUFBTSxxQkFFTCxDQUZLLElBQUlRLE1BQ1IsQ0FBQyxNQUFNLEVBQUVKLFVBQVVLLEtBQUssQ0FBQyx5T0FBeU8sQ0FBQyxHQUQvUDt1QkFBQTs0QkFBQTs4QkFBQTtZQUVOO1FBQ0Y7UUFFQSxJQUFJTCxVQUFVTSxXQUFXLEVBQUU7WUFDekIscUZBQXFGO1lBQ3JGLGtDQUFrQztZQUNsQyxNQUFNQyxvQkFBb0J2QixnRkFBY0EsQ0FBQ3dCLElBQUksQ0FBQyxJQUFJQyxRQUFRLENBQUM7WUFDM0QsT0FBT0MsMkJBQTJCSDtRQUNwQztRQUVBLElBQUlMLGVBQWU7WUFDakIsSUFBSUEsY0FBY1MsSUFBSSxLQUFLLFNBQVM7Z0JBQ2xDLE1BQU0scUJBRUwsQ0FGSyxJQUFJUCxNQUNSLENBQUMsTUFBTSxFQUFFSixVQUFVSyxLQUFLLENBQUMsMFVBQTBVLENBQUMsR0FEaFc7MkJBQUE7Z0NBQUE7a0NBQUE7Z0JBRU47WUFDRixPQUFPLElBQUlILGNBQWNTLElBQUksS0FBSyxrQkFBa0I7Z0JBQ2xELE1BQU0scUJBRUwsQ0FGSyxJQUFJUCxNQUNSLENBQUMsTUFBTSxFQUFFSixVQUFVSyxLQUFLLENBQUMsbVhBQW1YLENBQUMsR0FEelk7MkJBQUE7Z0NBQUE7a0NBQUE7Z0JBRU47WUFDRjtRQUNGO1FBQ0EsSUFBSUwsVUFBVVksa0JBQWtCLEVBQUU7WUFDaEMsTUFBTSxxQkFFTCxDQUZLLElBQUlwQiwrRkFBcUJBLENBQzdCLENBQUMsTUFBTSxFQUFFUSxVQUFVSyxLQUFLLENBQUMsaU5BQWlOLENBQUMsR0FEdk87dUJBQUE7NEJBQUE7OEJBQUE7WUFFTjtRQUNGO1FBRUEsSUFBSUgsZUFBZTtZQUNqQixPQUFRQSxjQUFjUyxJQUFJO2dCQUN4QixLQUFLO29CQUNILE9BQU9FLG1CQUFtQlg7Z0JBQzVCLEtBQUs7b0JBQ0gsTUFBTVksYUFBYTtvQkFDbkIsTUFBTSxxQkFFTCxDQUZLLElBQUlqQix1RUFBY0EsQ0FDdEIsR0FBR2lCLFdBQVcsMEVBQTBFLEVBQUVBLFdBQVcsK0VBQStFLENBQUMsR0FEakw7K0JBQUE7b0NBQUE7c0NBQUE7b0JBRU47Z0JBQ0YsS0FBSztvQkFDSCwrQkFBK0I7b0JBQy9CLDBFQUEwRTtvQkFDMUUsMkVBQTJFO29CQUMzRSwyRUFBMkU7b0JBQzNFMUIsbUZBQW9CQSxDQUNsQlksVUFBVUssS0FBSyxFQUNmLFdBQ0FILGNBQWNhLGVBQWU7b0JBRS9CO2dCQUNGLEtBQUs7b0JBQ0gsbUJBQW1CO29CQUNuQiwrREFBK0Q7b0JBQy9ELHVFQUF1RTtvQkFDdkUsdUNBQXVDO29CQUN2QzFCLCtGQUFnQ0EsQ0FBQyxXQUFXVyxXQUFXRTtvQkFDdkQ7Z0JBQ0Y7WUFFRjtRQUNGO1FBQ0EsaUZBQWlGO1FBQ2pGLHlGQUF5RjtRQUN6RlosOEZBQStCQSxDQUFDVSxXQUFXRTtJQUM3QztJQUVBLE1BQU1jLGVBQWU5QixxR0FBdUJBLENBQUM7SUFDN0MsSUFBSStCLEtBQW9CLElBQXNCLEVBQUNqQixhQUFBQSxPQUFBQSxLQUFBQSxJQUFBQSxVQUFXb0IsaUJBQUFBLEdBQW1CO1FBQzNFLElBQUlILEtBQTZCLEVBQUUsRUFLbEM7UUFFRCxPQUFPTSwwQ0FDTFAsYUFBYWpCLE9BQU8sRUFDcEJDLGFBQUFBLE9BQUFBLEtBQUFBLElBQUFBLFVBQVdLLEtBQUs7SUFFcEIsT0FBTztRQUNMLE9BQU9LLDJCQUEyQk0sYUFBYWpCLE9BQU87SUFDeEQ7QUFDRjtBQUdBLE1BQU15QixnQkFBZ0IsSUFBSUM7QUFFMUIsU0FBU1osbUJBQ1BhLGNBQW9DO0lBRXBDLE1BQU1DLGdCQUFnQkgsY0FBY0ksR0FBRyxDQUFDRjtJQUN4QyxJQUFJQyxlQUFlO1FBQ2pCLE9BQU9BO0lBQ1Q7SUFFQSxNQUFNRSxVQUFVcEMsNEVBQWtCQSxDQUNoQ2lDLGVBQWVJLFlBQVksRUFDM0I7SUFFRk4sY0FBY08sR0FBRyxDQUFDTCxnQkFBZ0JHO0lBRWxDLE9BQU9BO0FBQ1Q7QUFFQSxTQUFTbkIsMkJBQ1BILGlCQUFrQztJQUVsQyxNQUFNb0IsZ0JBQWdCSCxjQUFjSSxHQUFHLENBQUNyQjtJQUN4QyxJQUFJb0IsZUFBZTtRQUNqQixPQUFPQTtJQUNUO0lBRUEsTUFBTUUsVUFBVUcsUUFBUUMsT0FBTyxDQUFDMUI7SUFDaENpQixjQUFjTyxHQUFHLENBQUN4QixtQkFBbUJzQjtJQUVyQ0ssT0FBT0MsZ0JBQWdCLENBQUNOLFNBQVM7UUFDL0JPLFFBQVE7WUFDTkMsT0FBTzlCLGtCQUFrQjZCLE1BQU0sQ0FBQ0UsSUFBSSxDQUFDL0I7UUFDdkM7UUFDQWdDLFFBQVE7WUFDTkYsT0FBTzlCLGtCQUFrQmdDLE1BQU0sQ0FBQ0QsSUFBSSxDQUFDL0I7UUFDdkM7UUFDQXFCLEtBQUs7WUFDSFMsT0FBTzlCLGtCQUFrQnFCLEdBQUcsQ0FBQ1UsSUFBSSxDQUFDL0I7UUFDcEM7UUFDQWlDLEtBQUs7WUFDSEgsT0FBTzlCLGtCQUFrQmlDLEdBQUcsQ0FBQ0YsSUFBSSxDQUFDL0I7UUFDcEM7UUFDQXdCLEtBQUs7WUFDSE0sT0FBTzlCLGtCQUFrQndCLEdBQUcsQ0FBQ08sSUFBSSxDQUFDL0I7UUFDcEM7UUFDQWtDLGNBQWM7WUFDWkosT0FBTzlCLGtCQUFrQmtDLFlBQVksQ0FBQ0gsSUFBSSxDQUFDL0I7UUFDN0M7UUFDQW1DLFNBQVM7WUFDUEwsT0FBTzlCLGtCQUFrQm1DLE9BQU8sQ0FBQ0osSUFBSSxDQUFDL0I7UUFDeEM7UUFDQW9DLE1BQU07WUFDSk4sT0FBTzlCLGtCQUFrQm9DLElBQUksQ0FBQ0wsSUFBSSxDQUFDL0I7UUFDckM7UUFDQXFDLFFBQVE7WUFDTlAsT0FBTzlCLGtCQUFrQnFDLE1BQU0sQ0FBQ04sSUFBSSxDQUFDL0I7UUFDdkM7UUFDQXNDLFNBQVM7WUFDUFIsT0FBTzlCLGtCQUFrQnNDLE9BQU8sQ0FBQ1AsSUFBSSxDQUFDL0I7UUFDeEM7UUFDQSxDQUFDdUMsT0FBT0MsUUFBUSxDQUFDLEVBQUU7WUFDakJWLE9BQU85QixpQkFBaUIsQ0FBQ3VDLE9BQU9DLFFBQVEsQ0FBQyxDQUFDVCxJQUFJLENBQUMvQjtRQUNqRDtJQUNGO0lBRUEsT0FBT3NCO0FBQ1Q7QUFFQSxTQUFTTiwwQ0FDUGhCLGlCQUFrQyxFQUNsQ0YsS0FBYztJQUVkLE1BQU1zQixnQkFBZ0JILGNBQWNJLEdBQUcsQ0FBQ3JCO0lBQ3hDLElBQUlvQixlQUFlO1FBQ2pCLE9BQU9BO0lBQ1Q7SUFFQSxNQUFNRSxVQUFVLElBQUlHLFFBQXlCLENBQUNDLFVBQzVDdEMsaUVBQWlCQSxDQUFDLElBQU1zQyxRQUFRMUI7SUFHbENpQixjQUFjTyxHQUFHLENBQUN4QixtQkFBbUJzQjtJQUVyQ0ssT0FBT0MsZ0JBQWdCLENBQUNOLFNBQVM7UUFDL0JPLFFBQVE7WUFDTkMsT0FBTyxTQUFTRDtnQkFDZCxNQUFNWSxhQUFhLENBQUMsbUJBQW1CLEVBQUVDLGdCQUFnQkMsU0FBUyxDQUFDLEVBQUUsRUFBRSxRQUFRLENBQUM7Z0JBQ2hGQyxVQUFVOUMsT0FBTzJDO2dCQUNqQixPQUFPekMsa0JBQWtCNkIsTUFBTSxDQUFDZ0IsS0FBSyxDQUNuQzdDLG1CQUNBMkM7WUFFSjtRQUNGO1FBQ0FYLFFBQVE7WUFDTkYsT0FBTyxTQUFTZ0I7Z0JBQ2QsTUFBTUwsYUFBYSxDQUFDLG1CQUFtQixFQUFFQyxnQkFBZ0JDLFNBQVMsQ0FBQyxFQUFFLEVBQUUsR0FBRyxDQUFDO2dCQUMzRUMsVUFBVTlDLE9BQU8yQztnQkFDakIsT0FBT3pDLGtCQUFrQmdDLE1BQU0sQ0FBQ2EsS0FBSyxDQUNuQzdDLG1CQUNBMkM7WUFFSjtRQUNGO1FBQ0F0QixLQUFLO1lBQ0hTLE9BQU8sU0FBU1Q7Z0JBQ2QsTUFBTW9CLGFBQWEsQ0FBQyxnQkFBZ0IsRUFBRUMsZ0JBQWdCQyxTQUFTLENBQUMsRUFBRSxFQUFFLEdBQUcsQ0FBQztnQkFDeEVDLFVBQVU5QyxPQUFPMkM7Z0JBQ2pCLE9BQU96QyxrQkFBa0JxQixHQUFHLENBQUN3QixLQUFLLENBQUM3QyxtQkFBbUIyQztZQUN4RDtRQUNGO1FBQ0FWLEtBQUs7WUFDSEgsT0FBTyxTQUFTRztnQkFDZCxNQUFNUSxhQUFhLENBQUMsZ0JBQWdCLEVBQUVDLGdCQUFnQkMsU0FBUyxDQUFDLEVBQUUsRUFBRSxHQUFHLENBQUM7Z0JBQ3hFQyxVQUFVOUMsT0FBTzJDO2dCQUNqQixPQUFPekMsa0JBQWtCaUMsR0FBRyxDQUFDWSxLQUFLLENBQUM3QyxtQkFBbUIyQztZQUN4RDtRQUNGO1FBQ0FuQixLQUFLO1lBQ0hNLE9BQU8sU0FBU047Z0JBQ2QsTUFBTWlCLGFBQWEsQ0FBQyxnQkFBZ0IsRUFBRUMsZ0JBQWdCQyxTQUFTLENBQUMsRUFBRSxFQUFFLFFBQVEsQ0FBQztnQkFDN0VDLFVBQVU5QyxPQUFPMkM7Z0JBQ2pCLE9BQU96QyxrQkFBa0J3QixHQUFHLENBQUNxQixLQUFLLENBQUM3QyxtQkFBbUIyQztZQUN4RDtRQUNGO1FBQ0FULGNBQWM7WUFDWkosT0FBTyxTQUFTSTtnQkFDZCxNQUFNTyxhQUFhO2dCQUNuQkcsVUFBVTlDLE9BQU8yQztnQkFDakIsT0FBT3pDLGtCQUFrQmtDLFlBQVksQ0FBQ1csS0FBSyxDQUN6QzdDLG1CQUNBMkM7WUFFSjtRQUNGO1FBQ0FSLFNBQVM7WUFDUEwsT0FBTyxTQUFTSztnQkFDZCxNQUFNTSxhQUFhO2dCQUNuQkcsVUFBVTlDLE9BQU8yQztnQkFDakIsT0FBT3pDLGtCQUFrQm1DLE9BQU8sQ0FBQ1UsS0FBSyxDQUNwQzdDLG1CQUNBMkM7WUFFSjtRQUNGO1FBQ0FQLE1BQU07WUFDSk4sT0FBTyxTQUFTTTtnQkFDZCxNQUFNSyxhQUFhO2dCQUNuQkcsVUFBVTlDLE9BQU8yQztnQkFDakIsT0FBT3pDLGtCQUFrQm9DLElBQUksQ0FBQ1MsS0FBSyxDQUFDN0MsbUJBQW1CMkM7WUFDekQ7UUFDRjtRQUNBTixRQUFRO1lBQ05QLE9BQU8sU0FBU087Z0JBQ2QsTUFBTUksYUFBYTtnQkFDbkJHLFVBQVU5QyxPQUFPMkM7Z0JBQ2pCLE9BQU96QyxrQkFBa0JxQyxNQUFNLENBQUNRLEtBQUssQ0FDbkM3QyxtQkFDQTJDO1lBRUo7UUFDRjtRQUNBTCxTQUFTO1lBQ1BSLE9BQU8sU0FBU1E7Z0JBQ2QsTUFBTUcsYUFBYTtnQkFDbkJHLFVBQVU5QyxPQUFPMkM7Z0JBQ2pCLE9BQU96QyxrQkFBa0JzQyxPQUFPLENBQUNPLEtBQUssQ0FDcEM3QyxtQkFDQTJDO1lBRUo7UUFDRjtRQUNBLENBQUNKLE9BQU9DLFFBQVEsQ0FBQyxFQUFFO1lBQ2pCVixPQUFPO2dCQUNMLE1BQU1XLGFBQWE7Z0JBQ25CRyxVQUFVOUMsT0FBTzJDO2dCQUNqQixPQUFPekMsaUJBQWlCLENBQUN1QyxPQUFPQyxRQUFRLENBQUMsQ0FBQ0ssS0FBSyxDQUM3QzdDLG1CQUNBMkM7WUFFSjtRQUNGO0lBQ0Y7SUFFQSxPQUFPckI7QUFDVDtBQUVBLCtFQUErRTtBQUMvRSwrRUFBK0U7QUFDL0UsU0FBU1Asb0NBQ1BmLGlCQUFrQyxFQUNsQ0YsS0FBYztJQUVkLE1BQU1zQixnQkFBZ0JILGNBQWNJLEdBQUcsQ0FBQ3JCO0lBQ3hDLElBQUlvQixlQUFlO1FBQ2pCLE9BQU9BO0lBQ1Q7SUFFQSxNQUFNRSxVQUFVLElBQUlHLFFBQXlCLENBQUNDLFVBQzVDdEMsaUVBQWlCQSxDQUFDLElBQU1zQyxRQUFRMUI7SUFHbEMsTUFBTStDLGlCQUFpQixJQUFJQyxNQUFNMUIsU0FBUztRQUN4Q0QsS0FBSTRCLE1BQU0sRUFBRUMsSUFBSSxFQUFFQyxRQUFRO1lBQ3hCLE9BQVFEO2dCQUNOLEtBQUtYLE9BQU9DLFFBQVE7b0JBQUU7d0JBQ3BCWSxrQkFBa0J0RCxPQUFPO3dCQUN6QjtvQkFDRjtnQkFDQSxLQUFLO2dCQUNMLEtBQUs7Z0JBQ0wsS0FBSztnQkFDTCxLQUFLO2dCQUNMLEtBQUs7Z0JBQ0wsS0FBSztnQkFDTCxLQUFLO2dCQUNMLEtBQUs7Z0JBQ0wsS0FBSztnQkFDTCxLQUFLO29CQUFXO3dCQUNkc0Qsa0JBQWtCdEQsT0FBTyxDQUFDLFlBQVksRUFBRW9ELEtBQUssRUFBRSxDQUFDO3dCQUNoRDtvQkFDRjtnQkFDQTtvQkFBUztvQkFDUCxrRUFBa0U7b0JBQ3BFO1lBQ0Y7WUFFQSxPQUFPM0QsaUZBQWNBLENBQUM4QixHQUFHLENBQUM0QixRQUFRQyxNQUFNQztRQUMxQztJQUNGO0lBRUFsQyxjQUFjTyxHQUFHLENBQUN4QixtQkFBbUIrQztJQUVyQyxPQUFPQTtBQUNUO0FBRUEsU0FBU0wsZ0JBQWdCVyxHQUFZO0lBQ25DLE9BQU8sT0FBT0EsUUFBUSxXQUFXLENBQUMsQ0FBQyxFQUFFQSxJQUFJLENBQUMsQ0FBQyxHQUFHO0FBQ2hEO0FBRUEsU0FBU1QsVUFBVTlDLEtBQXlCLEVBQUUyQyxVQUFrQjtJQUM5RCxNQUFNOUMsZ0JBQWdCZiw4RkFBb0JBLENBQUNjLFFBQVE7SUFDbkQsSUFDRUMsaUJBQ0FBLGNBQWNTLElBQUksS0FBSyxhQUN2QlQsY0FBYzJELGNBQWMsS0FBSyxNQUNqQztRQUNBLHdFQUF3RTtRQUN4RSxnRUFBZ0U7UUFDaEUsTUFBTTdDLGVBQWVkO1FBQ3JCWCxxR0FBc0NBLENBQUN5QjtJQUN6QztJQUNBLGdDQUFnQztJQUNoQzJDLGtCQUFrQnRELE9BQU8yQztBQUMzQjtBQUVBLE1BQU1XLG9CQUFvQmpFLDRIQUEyQ0EsQ0FDbkVvRTtBQUdGLFNBQVNBLHlCQUNQekQsS0FBeUIsRUFDekIyQyxVQUFrQjtJQUVsQixNQUFNZSxTQUFTMUQsUUFBUSxDQUFDLE9BQU8sRUFBRUEsTUFBTSxFQUFFLENBQUMsR0FBRztJQUM3QyxPQUFPLHFCQUlOLENBSk0sSUFBSUQsTUFDVCxHQUFHMkQsT0FBTyxLQUFLLEVBQUVmLFdBQVcsRUFBRSxDQUFDLEdBQzdCLENBQUMsd0RBQXdELENBQUMsR0FDMUQsQ0FBQyw4REFBOEQsQ0FBQyxHQUg3RDtlQUFBO29CQUFBO3NCQUFBO0lBSVA7QUFDRiIsInNvdXJjZXMiOlsiL1VzZXJzL3NoYXdhei9zcmMvc2VydmVyL3JlcXVlc3QvaGVhZGVycy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge1xuICBIZWFkZXJzQWRhcHRlcixcbiAgdHlwZSBSZWFkb25seUhlYWRlcnMsXG59IGZyb20gJy4uL3dlYi9zcGVjLWV4dGVuc2lvbi9hZGFwdGVycy9oZWFkZXJzJ1xuaW1wb3J0IHsgd29ya0FzeW5jU3RvcmFnZSB9IGZyb20gJy4uL2FwcC1yZW5kZXIvd29yay1hc3luYy1zdG9yYWdlLmV4dGVybmFsJ1xuaW1wb3J0IHsgZ2V0RXhwZWN0ZWRSZXF1ZXN0U3RvcmUgfSBmcm9tICcuLi9hcHAtcmVuZGVyL3dvcmstdW5pdC1hc3luYy1zdG9yYWdlLmV4dGVybmFsJ1xuaW1wb3J0IHtcbiAgd29ya1VuaXRBc3luY1N0b3JhZ2UsXG4gIHR5cGUgUHJlcmVuZGVyU3RvcmVNb2Rlcm4sXG59IGZyb20gJy4uL2FwcC1yZW5kZXIvd29yay11bml0LWFzeW5jLXN0b3JhZ2UuZXh0ZXJuYWwnXG5pbXBvcnQge1xuICBwb3N0cG9uZVdpdGhUcmFja2luZyxcbiAgdGhyb3dUb0ludGVycnVwdFN0YXRpY0dlbmVyYXRpb24sXG4gIHRyYWNrRHluYW1pY0RhdGFJbkR5bmFtaWNSZW5kZXIsXG4gIHRyYWNrU3luY2hyb25vdXNSZXF1ZXN0RGF0YUFjY2Vzc0luRGV2LFxufSBmcm9tICcuLi9hcHAtcmVuZGVyL2R5bmFtaWMtcmVuZGVyaW5nJ1xuaW1wb3J0IHsgU3RhdGljR2VuQmFpbG91dEVycm9yIH0gZnJvbSAnLi4vLi4vY2xpZW50L2NvbXBvbmVudHMvc3RhdGljLWdlbmVyYXRpb24tYmFpbG91dCdcbmltcG9ydCB7IG1ha2VIYW5naW5nUHJvbWlzZSB9IGZyb20gJy4uL2R5bmFtaWMtcmVuZGVyaW5nLXV0aWxzJ1xuaW1wb3J0IHsgY3JlYXRlRGVkdXBlZEJ5Q2FsbHNpdGVTZXJ2ZXJFcnJvckxvZ2dlckRldiB9IGZyb20gJy4uL2NyZWF0ZS1kZWR1cGVkLWJ5LWNhbGxzaXRlLXNlcnZlci1lcnJvci1sb2dnZXInXG5pbXBvcnQgeyBzY2hlZHVsZUltbWVkaWF0ZSB9IGZyb20gJy4uLy4uL2xpYi9zY2hlZHVsZXInXG5pbXBvcnQgeyBpc1JlcXVlc3RBUElDYWxsYWJsZUluc2lkZUFmdGVyIH0gZnJvbSAnLi91dGlscydcbmltcG9ydCB7IEludmFyaWFudEVycm9yIH0gZnJvbSAnLi4vLi4vc2hhcmVkL2xpYi9pbnZhcmlhbnQtZXJyb3InXG5pbXBvcnQgeyBSZWZsZWN0QWRhcHRlciB9IGZyb20gJy4uL3dlYi9zcGVjLWV4dGVuc2lvbi9hZGFwdGVycy9yZWZsZWN0J1xuXG4vKipcbiAqIEluIHRoaXMgdmVyc2lvbiBvZiBOZXh0LmpzIGBoZWFkZXJzKClgIHJldHVybnMgYSBQcm9taXNlIGhvd2V2ZXIgeW91IGNhbiBzdGlsbCByZWZlcmVuY2UgdGhlIHByb3BlcnRpZXMgb2YgdGhlIHVuZGVybHlpbmcgSGVhZGVycyBpbnN0YW5jZVxuICogc3luY2hyb25vdXNseSB0byBmYWNpbGl0YXRlIG1pZ3JhdGlvbi4gVGhlIGBVbnNhZmVVbndyYXBwZWRIZWFkZXJzYCB0eXBlIGlzIGFkZGVkIHRvIHlvdXIgY29kZSBieSBhIGNvZGVtb2QgdGhhdCBhdHRlbXB0cyB0byBhdXRvbWF0aWNhbGx5XG4gKiB1cGRhdGVzIGNhbGxzaXRlcyB0byByZWZsZWN0IHRoZSBuZXcgUHJvbWlzZSByZXR1cm4gdHlwZS4gVGhlcmUgYXJlIHNvbWUgY2FzZXMgd2hlcmUgYGhlYWRlcnMoKWAgY2Fubm90IGJlIGF1dG9tYXRpY2FsbHkgY29udmVydGVkLCBuYW1lbHlcbiAqIHdoZW4gaXQgaXMgdXNlZCBpbnNpZGUgYSBzeW5jaHJvbm91cyBmdW5jdGlvbiBhbmQgd2UgY2FuJ3QgYmUgc3VyZSB0aGUgZnVuY3Rpb24gY2FuIGJlIG1hZGUgYXN5bmMgYXV0b21hdGljYWxseS4gSW4gdGhlc2UgY2FzZXMgd2UgYWRkIGFuXG4gKiBleHBsaWNpdCB0eXBlIGNhc2UgdG8gYFVuc2FmZVVud3JhcHBlZEhlYWRlcnNgIHRvIGVuYWJsZSB0eXBlc2NyaXB0IHRvIGFsbG93IGZvciB0aGUgc3luY2hyb25vdXMgdXNhZ2Ugb25seSB3aGVyZSBpdCBpcyBhY3R1YWxseSBuZWNlc3NhcnkuXG4gKlxuICogWW91IHNob3VsZCBzaG91bGQgdXBkYXRlIHRoZXNlIGNhbGxzaXRlcyB0byBlaXRoZXIgYmUgYXN5bmMgZnVuY3Rpb25zIHdoZXJlIHRoZSBgaGVhZGVycygpYCB2YWx1ZSBjYW4gYmUgYXdhaXRlZCBvciB5b3Ugc2hvdWxkIGNhbGwgYGhlYWRlcnMoKWBcbiAqIGZyb20gb3V0c2lkZSBhbmQgYXdhaXQgdGhlIHJldHVybiB2YWx1ZSBiZWZvcmUgcGFzc2luZyBpdCBpbnRvIHRoaXMgZnVuY3Rpb24uXG4gKlxuICogWW91IGNhbiBmaW5kIGluc3RhbmNlcyB0aGF0IHJlcXVpcmUgbWFudWFsIG1pZ3JhdGlvbiBieSBzZWFyY2hpbmcgZm9yIGBVbnNhZmVVbndyYXBwZWRIZWFkZXJzYCBpbiB5b3VyIGNvZGViYXNlIG9yIGJ5IHNlYXJjaCBmb3IgYSBjb21tZW50IHRoYXRcbiAqIHN0YXJ0cyB3aXRoIGBAbmV4dC1jb2RlbW9kLWVycm9yYC5cbiAqXG4gKiBJbiBhIGZ1dHVyZSB2ZXJzaW9uIG9mIE5leHQuanMgYGhlYWRlcnMoKWAgd2lsbCBvbmx5IHJldHVybiBhIFByb21pc2UgYW5kIHlvdSB3aWxsIG5vdCBiZSBhYmxlIHRvIGFjY2VzcyB0aGUgdW5kZXJseWluZyBIZWFkZXJzIGluc3RhbmNlXG4gKiB3aXRob3V0IGF3YWl0aW5nIHRoZSByZXR1cm4gdmFsdWUgZmlyc3QuIFdoZW4gdGhpcyBjaGFuZ2UgaGFwcGVucyB0aGUgdHlwZSBgVW5zYWZlVW53cmFwcGVkSGVhZGVyc2Agd2lsbCBiZSB1cGRhdGVkIHRvIHJlZmxlY3QgdGhhdCBpcyBpdCBubyBsb25nZXJcbiAqIHVzYWJsZS5cbiAqXG4gKiBUaGlzIHR5cGUgaXMgbWFya2VkIGRlcHJlY2F0ZWQgdG8gaGVscCBpZGVudGlmeSBpdCBhcyB0YXJnZXQgZm9yIHJlZmFjdG9yaW5nIGF3YXkuXG4gKlxuICogQGRlcHJlY2F0ZWRcbiAqL1xuZXhwb3J0IHR5cGUgVW5zYWZlVW53cmFwcGVkSGVhZGVycyA9IFJlYWRvbmx5SGVhZGVyc1xuXG4vKipcbiAqIFRoaXMgZnVuY3Rpb24gYWxsb3dzIHlvdSB0byByZWFkIHRoZSBIVFRQIGluY29taW5nIHJlcXVlc3QgaGVhZGVycyBpblxuICogW1NlcnZlciBDb21wb25lbnRzXShodHRwczovL25leHRqcy5vcmcvZG9jcy9hcHAvYnVpbGRpbmcteW91ci1hcHBsaWNhdGlvbi9yZW5kZXJpbmcvc2VydmVyLWNvbXBvbmVudHMpLFxuICogW1NlcnZlciBBY3Rpb25zXShodHRwczovL25leHRqcy5vcmcvZG9jcy9hcHAvYnVpbGRpbmcteW91ci1hcHBsaWNhdGlvbi9kYXRhLWZldGNoaW5nL3NlcnZlci1hY3Rpb25zLWFuZC1tdXRhdGlvbnMpLFxuICogW1JvdXRlIEhhbmRsZXJzXShodHRwczovL25leHRqcy5vcmcvZG9jcy9hcHAvYnVpbGRpbmcteW91ci1hcHBsaWNhdGlvbi9yb3V0aW5nL3JvdXRlLWhhbmRsZXJzKSBhbmRcbiAqIFtNaWRkbGV3YXJlXShodHRwczovL25leHRqcy5vcmcvZG9jcy9hcHAvYnVpbGRpbmcteW91ci1hcHBsaWNhdGlvbi9yb3V0aW5nL21pZGRsZXdhcmUpLlxuICpcbiAqIFJlYWQgbW9yZTogW05leHQuanMgRG9jczogYGhlYWRlcnNgXShodHRwczovL25leHRqcy5vcmcvZG9jcy9hcHAvYXBpLXJlZmVyZW5jZS9mdW5jdGlvbnMvaGVhZGVycylcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGhlYWRlcnMoKTogUHJvbWlzZTxSZWFkb25seUhlYWRlcnM+IHtcbiAgY29uc3Qgd29ya1N0b3JlID0gd29ya0FzeW5jU3RvcmFnZS5nZXRTdG9yZSgpXG4gIGNvbnN0IHdvcmtVbml0U3RvcmUgPSB3b3JrVW5pdEFzeW5jU3RvcmFnZS5nZXRTdG9yZSgpXG5cbiAgaWYgKHdvcmtTdG9yZSkge1xuICAgIGlmIChcbiAgICAgIHdvcmtVbml0U3RvcmUgJiZcbiAgICAgIHdvcmtVbml0U3RvcmUucGhhc2UgPT09ICdhZnRlcicgJiZcbiAgICAgICFpc1JlcXVlc3RBUElDYWxsYWJsZUluc2lkZUFmdGVyKClcbiAgICApIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihcbiAgICAgICAgYFJvdXRlICR7d29ya1N0b3JlLnJvdXRlfSB1c2VkIFwiaGVhZGVyc1wiIGluc2lkZSBcImFmdGVyKC4uLilcIi4gVGhpcyBpcyBub3Qgc3VwcG9ydGVkLiBJZiB5b3UgbmVlZCB0aGlzIGRhdGEgaW5zaWRlIGFuIFwiYWZ0ZXJcIiBjYWxsYmFjaywgdXNlIFwiaGVhZGVyc1wiIG91dHNpZGUgb2YgdGhlIGNhbGxiYWNrLiBTZWUgbW9yZSBpbmZvIGhlcmU6IGh0dHBzOi8vbmV4dGpzLm9yZy9kb2NzL2NhbmFyeS9hcHAvYXBpLXJlZmVyZW5jZS9mdW5jdGlvbnMvYWZ0ZXJgXG4gICAgICApXG4gICAgfVxuXG4gICAgaWYgKHdvcmtTdG9yZS5mb3JjZVN0YXRpYykge1xuICAgICAgLy8gV2hlbiB1c2luZyBmb3JjZVN0YXRpYyB3ZSBvdmVycmlkZSBhbGwgb3RoZXIgbG9naWMgYW5kIGFsd2F5cyBqdXN0IHJldHVybiBhbiBlbXB0eVxuICAgICAgLy8gaGVhZGVycyBvYmplY3Qgd2l0aG91dCB0cmFja2luZ1xuICAgICAgY29uc3QgdW5kZXJseWluZ0hlYWRlcnMgPSBIZWFkZXJzQWRhcHRlci5zZWFsKG5ldyBIZWFkZXJzKHt9KSlcbiAgICAgIHJldHVybiBtYWtlVW50cmFja2VkRXhvdGljSGVhZGVycyh1bmRlcmx5aW5nSGVhZGVycylcbiAgICB9XG5cbiAgICBpZiAod29ya1VuaXRTdG9yZSkge1xuICAgICAgaWYgKHdvcmtVbml0U3RvcmUudHlwZSA9PT0gJ2NhY2hlJykge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAgICAgYFJvdXRlICR7d29ya1N0b3JlLnJvdXRlfSB1c2VkIFwiaGVhZGVyc1wiIGluc2lkZSBcInVzZSBjYWNoZVwiLiBBY2Nlc3NpbmcgRHluYW1pYyBkYXRhIHNvdXJjZXMgaW5zaWRlIGEgY2FjaGUgc2NvcGUgaXMgbm90IHN1cHBvcnRlZC4gSWYgeW91IG5lZWQgdGhpcyBkYXRhIGluc2lkZSBhIGNhY2hlZCBmdW5jdGlvbiB1c2UgXCJoZWFkZXJzXCIgb3V0c2lkZSBvZiB0aGUgY2FjaGVkIGZ1bmN0aW9uIGFuZCBwYXNzIHRoZSByZXF1aXJlZCBkeW5hbWljIGRhdGEgaW4gYXMgYW4gYXJndW1lbnQuIFNlZSBtb3JlIGluZm8gaGVyZTogaHR0cHM6Ly9uZXh0anMub3JnL2RvY3MvbWVzc2FnZXMvbmV4dC1yZXF1ZXN0LWluLXVzZS1jYWNoZWBcbiAgICAgICAgKVxuICAgICAgfSBlbHNlIGlmICh3b3JrVW5pdFN0b3JlLnR5cGUgPT09ICd1bnN0YWJsZS1jYWNoZScpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKFxuICAgICAgICAgIGBSb3V0ZSAke3dvcmtTdG9yZS5yb3V0ZX0gdXNlZCBcImhlYWRlcnNcIiBpbnNpZGUgYSBmdW5jdGlvbiBjYWNoZWQgd2l0aCBcInVuc3RhYmxlX2NhY2hlKC4uLilcIi4gQWNjZXNzaW5nIER5bmFtaWMgZGF0YSBzb3VyY2VzIGluc2lkZSBhIGNhY2hlIHNjb3BlIGlzIG5vdCBzdXBwb3J0ZWQuIElmIHlvdSBuZWVkIHRoaXMgZGF0YSBpbnNpZGUgYSBjYWNoZWQgZnVuY3Rpb24gdXNlIFwiaGVhZGVyc1wiIG91dHNpZGUgb2YgdGhlIGNhY2hlZCBmdW5jdGlvbiBhbmQgcGFzcyB0aGUgcmVxdWlyZWQgZHluYW1pYyBkYXRhIGluIGFzIGFuIGFyZ3VtZW50LiBTZWUgbW9yZSBpbmZvIGhlcmU6IGh0dHBzOi8vbmV4dGpzLm9yZy9kb2NzL2FwcC9hcGktcmVmZXJlbmNlL2Z1bmN0aW9ucy91bnN0YWJsZV9jYWNoZWBcbiAgICAgICAgKVxuICAgICAgfVxuICAgIH1cbiAgICBpZiAod29ya1N0b3JlLmR5bmFtaWNTaG91bGRFcnJvcikge1xuICAgICAgdGhyb3cgbmV3IFN0YXRpY0dlbkJhaWxvdXRFcnJvcihcbiAgICAgICAgYFJvdXRlICR7d29ya1N0b3JlLnJvdXRlfSB3aXRoIFxcYGR5bmFtaWMgPSBcImVycm9yXCJcXGAgY291bGRuJ3QgYmUgcmVuZGVyZWQgc3RhdGljYWxseSBiZWNhdXNlIGl0IHVzZWQgXFxgaGVhZGVyc1xcYC4gU2VlIG1vcmUgaW5mbyBoZXJlOiBodHRwczovL25leHRqcy5vcmcvZG9jcy9hcHAvYnVpbGRpbmcteW91ci1hcHBsaWNhdGlvbi9yZW5kZXJpbmcvc3RhdGljLWFuZC1keW5hbWljI2R5bmFtaWMtcmVuZGVyaW5nYFxuICAgICAgKVxuICAgIH1cblxuICAgIGlmICh3b3JrVW5pdFN0b3JlKSB7XG4gICAgICBzd2l0Y2ggKHdvcmtVbml0U3RvcmUudHlwZSkge1xuICAgICAgICBjYXNlICdwcmVyZW5kZXInOlxuICAgICAgICAgIHJldHVybiBtYWtlSGFuZ2luZ0hlYWRlcnMod29ya1VuaXRTdG9yZSlcbiAgICAgICAgY2FzZSAncHJlcmVuZGVyLWNsaWVudCc6XG4gICAgICAgICAgY29uc3QgZXhwb3J0TmFtZSA9ICdgaGVhZGVyc2AnXG4gICAgICAgICAgdGhyb3cgbmV3IEludmFyaWFudEVycm9yKFxuICAgICAgICAgICAgYCR7ZXhwb3J0TmFtZX0gbXVzdCBub3QgYmUgdXNlZCB3aXRoaW4gYSBjbGllbnQgY29tcG9uZW50LiBOZXh0LmpzIHNob3VsZCBiZSBwcmV2ZW50aW5nICR7ZXhwb3J0TmFtZX0gZnJvbSBiZWluZyBpbmNsdWRlZCBpbiBjbGllbnQgY29tcG9uZW50cyBzdGF0aWNhbGx5LCBidXQgZGlkIG5vdCBpbiB0aGlzIGNhc2UuYFxuICAgICAgICAgIClcbiAgICAgICAgY2FzZSAncHJlcmVuZGVyLXBwcic6XG4gICAgICAgICAgLy8gUFBSIFByZXJlbmRlciAobm8gZHluYW1pY0lPKVxuICAgICAgICAgIC8vIFdlIGFyZSBwcmVyZW5kZXJpbmcgd2l0aCBQUFIuIFdlIG5lZWQgdHJhY2sgZHluYW1pYyBhY2Nlc3MgaGVyZSBlYWdlcmx5XG4gICAgICAgICAgLy8gdG8ga2VlcCBjb250aW51aXR5IHdpdGggaG93IGhlYWRlcnMgaGFzIHdvcmtlZCBpbiBQUFIgd2l0aG91dCBkeW5hbWljSU8uXG4gICAgICAgICAgLy8gVE9ETyBjb25zaWRlciBzd2l0Y2hpbmcgdGhlIHNlbWFudGljIHRvIHRocm93IG9uIHByb3BlcnR5IGFjY2VzcyBpbnN0ZWFkXG4gICAgICAgICAgcG9zdHBvbmVXaXRoVHJhY2tpbmcoXG4gICAgICAgICAgICB3b3JrU3RvcmUucm91dGUsXG4gICAgICAgICAgICAnaGVhZGVycycsXG4gICAgICAgICAgICB3b3JrVW5pdFN0b3JlLmR5bmFtaWNUcmFja2luZ1xuICAgICAgICAgIClcbiAgICAgICAgICBicmVha1xuICAgICAgICBjYXNlICdwcmVyZW5kZXItbGVnYWN5JzpcbiAgICAgICAgICAvLyBMZWdhY3kgUHJlcmVuZGVyXG4gICAgICAgICAgLy8gV2UgYXJlIGluIGEgbGVnYWN5IHN0YXRpYyBnZW5lcmF0aW9uIG1vZGUgd2hpbGUgcHJlcmVuZGVyaW5nXG4gICAgICAgICAgLy8gV2UgdHJhY2sgZHluYW1pYyBhY2Nlc3MgaGVyZSBzbyB3ZSBkb24ndCBuZWVkIHRvIHdyYXAgdGhlIGhlYWRlcnMgaW5cbiAgICAgICAgICAvLyBpbmRpdmlkdWFsIHByb3BlcnR5IGFjY2VzcyB0cmFja2luZy5cbiAgICAgICAgICB0aHJvd1RvSW50ZXJydXB0U3RhdGljR2VuZXJhdGlvbignaGVhZGVycycsIHdvcmtTdG9yZSwgd29ya1VuaXRTdG9yZSlcbiAgICAgICAgICBicmVha1xuICAgICAgICBkZWZhdWx0OlxuICAgICAgICAvLyBmYWxsdGhyb3VnaFxuICAgICAgfVxuICAgIH1cbiAgICAvLyBXZSBmYWxsIHRocm91Z2ggdG8gdGhlIGR5bmFtaWMgY29udGV4dCBiZWxvdyBidXQgd2Ugc3RpbGwgdHJhY2sgZHluYW1pYyBhY2Nlc3NcbiAgICAvLyBiZWNhdXNlIGluIGRldiB3ZSBjYW4gc3RpbGwgZXJyb3IgZm9yIHRoaW5ncyBsaWtlIHVzaW5nIGhlYWRlcnMgaW5zaWRlIGEgY2FjaGUgY29udGV4dFxuICAgIHRyYWNrRHluYW1pY0RhdGFJbkR5bmFtaWNSZW5kZXIod29ya1N0b3JlLCB3b3JrVW5pdFN0b3JlKVxuICB9XG5cbiAgY29uc3QgcmVxdWVzdFN0b3JlID0gZ2V0RXhwZWN0ZWRSZXF1ZXN0U3RvcmUoJ2hlYWRlcnMnKVxuICBpZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdkZXZlbG9wbWVudCcgJiYgIXdvcmtTdG9yZT8uaXNQcmVmZXRjaFJlcXVlc3QpIHtcbiAgICBpZiAocHJvY2Vzcy5lbnYuX19ORVhUX0RZTkFNSUNfSU8pIHtcbiAgICAgIHJldHVybiBtYWtlVW50cmFja2VkSGVhZGVyc1dpdGhEZXZXYXJuaW5ncyhcbiAgICAgICAgcmVxdWVzdFN0b3JlLmhlYWRlcnMsXG4gICAgICAgIHdvcmtTdG9yZT8ucm91dGVcbiAgICAgIClcbiAgICB9XG5cbiAgICByZXR1cm4gbWFrZVVudHJhY2tlZEV4b3RpY0hlYWRlcnNXaXRoRGV2V2FybmluZ3MoXG4gICAgICByZXF1ZXN0U3RvcmUuaGVhZGVycyxcbiAgICAgIHdvcmtTdG9yZT8ucm91dGVcbiAgICApXG4gIH0gZWxzZSB7XG4gICAgcmV0dXJuIG1ha2VVbnRyYWNrZWRFeG90aWNIZWFkZXJzKHJlcXVlc3RTdG9yZS5oZWFkZXJzKVxuICB9XG59XG5cbmludGVyZmFjZSBDYWNoZUxpZmV0aW1lIHt9XG5jb25zdCBDYWNoZWRIZWFkZXJzID0gbmV3IFdlYWtNYXA8Q2FjaGVMaWZldGltZSwgUHJvbWlzZTxSZWFkb25seUhlYWRlcnM+PigpXG5cbmZ1bmN0aW9uIG1ha2VIYW5naW5nSGVhZGVycyhcbiAgcHJlcmVuZGVyU3RvcmU6IFByZXJlbmRlclN0b3JlTW9kZXJuXG4pOiBQcm9taXNlPFJlYWRvbmx5SGVhZGVycz4ge1xuICBjb25zdCBjYWNoZWRIZWFkZXJzID0gQ2FjaGVkSGVhZGVycy5nZXQocHJlcmVuZGVyU3RvcmUpXG4gIGlmIChjYWNoZWRIZWFkZXJzKSB7XG4gICAgcmV0dXJuIGNhY2hlZEhlYWRlcnNcbiAgfVxuXG4gIGNvbnN0IHByb21pc2UgPSBtYWtlSGFuZ2luZ1Byb21pc2U8UmVhZG9ubHlIZWFkZXJzPihcbiAgICBwcmVyZW5kZXJTdG9yZS5yZW5kZXJTaWduYWwsXG4gICAgJ2BoZWFkZXJzKClgJ1xuICApXG4gIENhY2hlZEhlYWRlcnMuc2V0KHByZXJlbmRlclN0b3JlLCBwcm9taXNlKVxuXG4gIHJldHVybiBwcm9taXNlXG59XG5cbmZ1bmN0aW9uIG1ha2VVbnRyYWNrZWRFeG90aWNIZWFkZXJzKFxuICB1bmRlcmx5aW5nSGVhZGVyczogUmVhZG9ubHlIZWFkZXJzXG4pOiBQcm9taXNlPFJlYWRvbmx5SGVhZGVycz4ge1xuICBjb25zdCBjYWNoZWRIZWFkZXJzID0gQ2FjaGVkSGVhZGVycy5nZXQodW5kZXJseWluZ0hlYWRlcnMpXG4gIGlmIChjYWNoZWRIZWFkZXJzKSB7XG4gICAgcmV0dXJuIGNhY2hlZEhlYWRlcnNcbiAgfVxuXG4gIGNvbnN0IHByb21pc2UgPSBQcm9taXNlLnJlc29sdmUodW5kZXJseWluZ0hlYWRlcnMpXG4gIENhY2hlZEhlYWRlcnMuc2V0KHVuZGVybHlpbmdIZWFkZXJzLCBwcm9taXNlKVxuXG4gIE9iamVjdC5kZWZpbmVQcm9wZXJ0aWVzKHByb21pc2UsIHtcbiAgICBhcHBlbmQ6IHtcbiAgICAgIHZhbHVlOiB1bmRlcmx5aW5nSGVhZGVycy5hcHBlbmQuYmluZCh1bmRlcmx5aW5nSGVhZGVycyksXG4gICAgfSxcbiAgICBkZWxldGU6IHtcbiAgICAgIHZhbHVlOiB1bmRlcmx5aW5nSGVhZGVycy5kZWxldGUuYmluZCh1bmRlcmx5aW5nSGVhZGVycyksXG4gICAgfSxcbiAgICBnZXQ6IHtcbiAgICAgIHZhbHVlOiB1bmRlcmx5aW5nSGVhZGVycy5nZXQuYmluZCh1bmRlcmx5aW5nSGVhZGVycyksXG4gICAgfSxcbiAgICBoYXM6IHtcbiAgICAgIHZhbHVlOiB1bmRlcmx5aW5nSGVhZGVycy5oYXMuYmluZCh1bmRlcmx5aW5nSGVhZGVycyksXG4gICAgfSxcbiAgICBzZXQ6IHtcbiAgICAgIHZhbHVlOiB1bmRlcmx5aW5nSGVhZGVycy5zZXQuYmluZCh1bmRlcmx5aW5nSGVhZGVycyksXG4gICAgfSxcbiAgICBnZXRTZXRDb29raWU6IHtcbiAgICAgIHZhbHVlOiB1bmRlcmx5aW5nSGVhZGVycy5nZXRTZXRDb29raWUuYmluZCh1bmRlcmx5aW5nSGVhZGVycyksXG4gICAgfSxcbiAgICBmb3JFYWNoOiB7XG4gICAgICB2YWx1ZTogdW5kZXJseWluZ0hlYWRlcnMuZm9yRWFjaC5iaW5kKHVuZGVybHlpbmdIZWFkZXJzKSxcbiAgICB9LFxuICAgIGtleXM6IHtcbiAgICAgIHZhbHVlOiB1bmRlcmx5aW5nSGVhZGVycy5rZXlzLmJpbmQodW5kZXJseWluZ0hlYWRlcnMpLFxuICAgIH0sXG4gICAgdmFsdWVzOiB7XG4gICAgICB2YWx1ZTogdW5kZXJseWluZ0hlYWRlcnMudmFsdWVzLmJpbmQodW5kZXJseWluZ0hlYWRlcnMpLFxuICAgIH0sXG4gICAgZW50cmllczoge1xuICAgICAgdmFsdWU6IHVuZGVybHlpbmdIZWFkZXJzLmVudHJpZXMuYmluZCh1bmRlcmx5aW5nSGVhZGVycyksXG4gICAgfSxcbiAgICBbU3ltYm9sLml0ZXJhdG9yXToge1xuICAgICAgdmFsdWU6IHVuZGVybHlpbmdIZWFkZXJzW1N5bWJvbC5pdGVyYXRvcl0uYmluZCh1bmRlcmx5aW5nSGVhZGVycyksXG4gICAgfSxcbiAgfSBzYXRpc2ZpZXMgSGVhZGVyc0V4dGVuc2lvbnMpXG5cbiAgcmV0dXJuIHByb21pc2Vcbn1cblxuZnVuY3Rpb24gbWFrZVVudHJhY2tlZEV4b3RpY0hlYWRlcnNXaXRoRGV2V2FybmluZ3MoXG4gIHVuZGVybHlpbmdIZWFkZXJzOiBSZWFkb25seUhlYWRlcnMsXG4gIHJvdXRlPzogc3RyaW5nXG4pOiBQcm9taXNlPFJlYWRvbmx5SGVhZGVycz4ge1xuICBjb25zdCBjYWNoZWRIZWFkZXJzID0gQ2FjaGVkSGVhZGVycy5nZXQodW5kZXJseWluZ0hlYWRlcnMpXG4gIGlmIChjYWNoZWRIZWFkZXJzKSB7XG4gICAgcmV0dXJuIGNhY2hlZEhlYWRlcnNcbiAgfVxuXG4gIGNvbnN0IHByb21pc2UgPSBuZXcgUHJvbWlzZTxSZWFkb25seUhlYWRlcnM+KChyZXNvbHZlKSA9PlxuICAgIHNjaGVkdWxlSW1tZWRpYXRlKCgpID0+IHJlc29sdmUodW5kZXJseWluZ0hlYWRlcnMpKVxuICApXG5cbiAgQ2FjaGVkSGVhZGVycy5zZXQodW5kZXJseWluZ0hlYWRlcnMsIHByb21pc2UpXG5cbiAgT2JqZWN0LmRlZmluZVByb3BlcnRpZXMocHJvbWlzZSwge1xuICAgIGFwcGVuZDoge1xuICAgICAgdmFsdWU6IGZ1bmN0aW9uIGFwcGVuZCgpIHtcbiAgICAgICAgY29uc3QgZXhwcmVzc2lvbiA9IGBcXGBoZWFkZXJzKCkuYXBwZW5kKCR7ZGVzY3JpYmVOYW1lQXJnKGFyZ3VtZW50c1swXSl9LCAuLi4pXFxgYFxuICAgICAgICBzeW5jSU9EZXYocm91dGUsIGV4cHJlc3Npb24pXG4gICAgICAgIHJldHVybiB1bmRlcmx5aW5nSGVhZGVycy5hcHBlbmQuYXBwbHkoXG4gICAgICAgICAgdW5kZXJseWluZ0hlYWRlcnMsXG4gICAgICAgICAgYXJndW1lbnRzIGFzIGFueVxuICAgICAgICApXG4gICAgICB9LFxuICAgIH0sXG4gICAgZGVsZXRlOiB7XG4gICAgICB2YWx1ZTogZnVuY3Rpb24gX2RlbGV0ZSgpIHtcbiAgICAgICAgY29uc3QgZXhwcmVzc2lvbiA9IGBcXGBoZWFkZXJzKCkuZGVsZXRlKCR7ZGVzY3JpYmVOYW1lQXJnKGFyZ3VtZW50c1swXSl9KVxcYGBcbiAgICAgICAgc3luY0lPRGV2KHJvdXRlLCBleHByZXNzaW9uKVxuICAgICAgICByZXR1cm4gdW5kZXJseWluZ0hlYWRlcnMuZGVsZXRlLmFwcGx5KFxuICAgICAgICAgIHVuZGVybHlpbmdIZWFkZXJzLFxuICAgICAgICAgIGFyZ3VtZW50cyBhcyBhbnlcbiAgICAgICAgKVxuICAgICAgfSxcbiAgICB9LFxuICAgIGdldDoge1xuICAgICAgdmFsdWU6IGZ1bmN0aW9uIGdldCgpIHtcbiAgICAgICAgY29uc3QgZXhwcmVzc2lvbiA9IGBcXGBoZWFkZXJzKCkuZ2V0KCR7ZGVzY3JpYmVOYW1lQXJnKGFyZ3VtZW50c1swXSl9KVxcYGBcbiAgICAgICAgc3luY0lPRGV2KHJvdXRlLCBleHByZXNzaW9uKVxuICAgICAgICByZXR1cm4gdW5kZXJseWluZ0hlYWRlcnMuZ2V0LmFwcGx5KHVuZGVybHlpbmdIZWFkZXJzLCBhcmd1bWVudHMgYXMgYW55KVxuICAgICAgfSxcbiAgICB9LFxuICAgIGhhczoge1xuICAgICAgdmFsdWU6IGZ1bmN0aW9uIGhhcygpIHtcbiAgICAgICAgY29uc3QgZXhwcmVzc2lvbiA9IGBcXGBoZWFkZXJzKCkuaGFzKCR7ZGVzY3JpYmVOYW1lQXJnKGFyZ3VtZW50c1swXSl9KVxcYGBcbiAgICAgICAgc3luY0lPRGV2KHJvdXRlLCBleHByZXNzaW9uKVxuICAgICAgICByZXR1cm4gdW5kZXJseWluZ0hlYWRlcnMuaGFzLmFwcGx5KHVuZGVybHlpbmdIZWFkZXJzLCBhcmd1bWVudHMgYXMgYW55KVxuICAgICAgfSxcbiAgICB9LFxuICAgIHNldDoge1xuICAgICAgdmFsdWU6IGZ1bmN0aW9uIHNldCgpIHtcbiAgICAgICAgY29uc3QgZXhwcmVzc2lvbiA9IGBcXGBoZWFkZXJzKCkuc2V0KCR7ZGVzY3JpYmVOYW1lQXJnKGFyZ3VtZW50c1swXSl9LCAuLi4pXFxgYFxuICAgICAgICBzeW5jSU9EZXYocm91dGUsIGV4cHJlc3Npb24pXG4gICAgICAgIHJldHVybiB1bmRlcmx5aW5nSGVhZGVycy5zZXQuYXBwbHkodW5kZXJseWluZ0hlYWRlcnMsIGFyZ3VtZW50cyBhcyBhbnkpXG4gICAgICB9LFxuICAgIH0sXG4gICAgZ2V0U2V0Q29va2llOiB7XG4gICAgICB2YWx1ZTogZnVuY3Rpb24gZ2V0U2V0Q29va2llKCkge1xuICAgICAgICBjb25zdCBleHByZXNzaW9uID0gJ2BoZWFkZXJzKCkuZ2V0U2V0Q29va2llKClgJ1xuICAgICAgICBzeW5jSU9EZXYocm91dGUsIGV4cHJlc3Npb24pXG4gICAgICAgIHJldHVybiB1bmRlcmx5aW5nSGVhZGVycy5nZXRTZXRDb29raWUuYXBwbHkoXG4gICAgICAgICAgdW5kZXJseWluZ0hlYWRlcnMsXG4gICAgICAgICAgYXJndW1lbnRzIGFzIGFueVxuICAgICAgICApXG4gICAgICB9LFxuICAgIH0sXG4gICAgZm9yRWFjaDoge1xuICAgICAgdmFsdWU6IGZ1bmN0aW9uIGZvckVhY2goKSB7XG4gICAgICAgIGNvbnN0IGV4cHJlc3Npb24gPSAnYGhlYWRlcnMoKS5mb3JFYWNoKC4uLilgJ1xuICAgICAgICBzeW5jSU9EZXYocm91dGUsIGV4cHJlc3Npb24pXG4gICAgICAgIHJldHVybiB1bmRlcmx5aW5nSGVhZGVycy5mb3JFYWNoLmFwcGx5KFxuICAgICAgICAgIHVuZGVybHlpbmdIZWFkZXJzLFxuICAgICAgICAgIGFyZ3VtZW50cyBhcyBhbnlcbiAgICAgICAgKVxuICAgICAgfSxcbiAgICB9LFxuICAgIGtleXM6IHtcbiAgICAgIHZhbHVlOiBmdW5jdGlvbiBrZXlzKCkge1xuICAgICAgICBjb25zdCBleHByZXNzaW9uID0gJ2BoZWFkZXJzKCkua2V5cygpYCdcbiAgICAgICAgc3luY0lPRGV2KHJvdXRlLCBleHByZXNzaW9uKVxuICAgICAgICByZXR1cm4gdW5kZXJseWluZ0hlYWRlcnMua2V5cy5hcHBseSh1bmRlcmx5aW5nSGVhZGVycywgYXJndW1lbnRzIGFzIGFueSlcbiAgICAgIH0sXG4gICAgfSxcbiAgICB2YWx1ZXM6IHtcbiAgICAgIHZhbHVlOiBmdW5jdGlvbiB2YWx1ZXMoKSB7XG4gICAgICAgIGNvbnN0IGV4cHJlc3Npb24gPSAnYGhlYWRlcnMoKS52YWx1ZXMoKWAnXG4gICAgICAgIHN5bmNJT0Rldihyb3V0ZSwgZXhwcmVzc2lvbilcbiAgICAgICAgcmV0dXJuIHVuZGVybHlpbmdIZWFkZXJzLnZhbHVlcy5hcHBseShcbiAgICAgICAgICB1bmRlcmx5aW5nSGVhZGVycyxcbiAgICAgICAgICBhcmd1bWVudHMgYXMgYW55XG4gICAgICAgIClcbiAgICAgIH0sXG4gICAgfSxcbiAgICBlbnRyaWVzOiB7XG4gICAgICB2YWx1ZTogZnVuY3Rpb24gZW50cmllcygpIHtcbiAgICAgICAgY29uc3QgZXhwcmVzc2lvbiA9ICdgaGVhZGVycygpLmVudHJpZXMoKWAnXG4gICAgICAgIHN5bmNJT0Rldihyb3V0ZSwgZXhwcmVzc2lvbilcbiAgICAgICAgcmV0dXJuIHVuZGVybHlpbmdIZWFkZXJzLmVudHJpZXMuYXBwbHkoXG4gICAgICAgICAgdW5kZXJseWluZ0hlYWRlcnMsXG4gICAgICAgICAgYXJndW1lbnRzIGFzIGFueVxuICAgICAgICApXG4gICAgICB9LFxuICAgIH0sXG4gICAgW1N5bWJvbC5pdGVyYXRvcl06IHtcbiAgICAgIHZhbHVlOiBmdW5jdGlvbiAoKSB7XG4gICAgICAgIGNvbnN0IGV4cHJlc3Npb24gPSAnYC4uLmhlYWRlcnMoKWAgb3Igc2ltaWxhciBpdGVyYXRpb24nXG4gICAgICAgIHN5bmNJT0Rldihyb3V0ZSwgZXhwcmVzc2lvbilcbiAgICAgICAgcmV0dXJuIHVuZGVybHlpbmdIZWFkZXJzW1N5bWJvbC5pdGVyYXRvcl0uYXBwbHkoXG4gICAgICAgICAgdW5kZXJseWluZ0hlYWRlcnMsXG4gICAgICAgICAgYXJndW1lbnRzIGFzIGFueVxuICAgICAgICApXG4gICAgICB9LFxuICAgIH0sXG4gIH0gc2F0aXNmaWVzIEhlYWRlcnNFeHRlbnNpb25zKVxuXG4gIHJldHVybiBwcm9taXNlXG59XG5cbi8vIFNpbWlsYXIgdG8gYG1ha2VVbnRyYWNrZWRFeG90aWNIZWFkZXJzV2l0aERldldhcm5pbmdzYCwgYnV0IGp1c3QgbG9nZ2luZyB0aGVcbi8vIHN5bmMgYWNjZXNzIHdpdGhvdXQgYWN0dWFsbHkgZGVmaW5pbmcgdGhlIGhlYWRlcnMgcHJvcGVydGllcyBvbiB0aGUgcHJvbWlzZS5cbmZ1bmN0aW9uIG1ha2VVbnRyYWNrZWRIZWFkZXJzV2l0aERldldhcm5pbmdzKFxuICB1bmRlcmx5aW5nSGVhZGVyczogUmVhZG9ubHlIZWFkZXJzLFxuICByb3V0ZT86IHN0cmluZ1xuKTogUHJvbWlzZTxSZWFkb25seUhlYWRlcnM+IHtcbiAgY29uc3QgY2FjaGVkSGVhZGVycyA9IENhY2hlZEhlYWRlcnMuZ2V0KHVuZGVybHlpbmdIZWFkZXJzKVxuICBpZiAoY2FjaGVkSGVhZGVycykge1xuICAgIHJldHVybiBjYWNoZWRIZWFkZXJzXG4gIH1cblxuICBjb25zdCBwcm9taXNlID0gbmV3IFByb21pc2U8UmVhZG9ubHlIZWFkZXJzPigocmVzb2x2ZSkgPT5cbiAgICBzY2hlZHVsZUltbWVkaWF0ZSgoKSA9PiByZXNvbHZlKHVuZGVybHlpbmdIZWFkZXJzKSlcbiAgKVxuXG4gIGNvbnN0IHByb3hpZWRQcm9taXNlID0gbmV3IFByb3h5KHByb21pc2UsIHtcbiAgICBnZXQodGFyZ2V0LCBwcm9wLCByZWNlaXZlcikge1xuICAgICAgc3dpdGNoIChwcm9wKSB7XG4gICAgICAgIGNhc2UgU3ltYm9sLml0ZXJhdG9yOiB7XG4gICAgICAgICAgd2FybkZvclN5bmNBY2Nlc3Mocm91dGUsICdgLi4uaGVhZGVycygpYCBvciBzaW1pbGFyIGl0ZXJhdGlvbicpXG4gICAgICAgICAgYnJlYWtcbiAgICAgICAgfVxuICAgICAgICBjYXNlICdhcHBlbmQnOlxuICAgICAgICBjYXNlICdkZWxldGUnOlxuICAgICAgICBjYXNlICdnZXQnOlxuICAgICAgICBjYXNlICdoYXMnOlxuICAgICAgICBjYXNlICdzZXQnOlxuICAgICAgICBjYXNlICdnZXRTZXRDb29raWUnOlxuICAgICAgICBjYXNlICdmb3JFYWNoJzpcbiAgICAgICAgY2FzZSAna2V5cyc6XG4gICAgICAgIGNhc2UgJ3ZhbHVlcyc6XG4gICAgICAgIGNhc2UgJ2VudHJpZXMnOiB7XG4gICAgICAgICAgd2FybkZvclN5bmNBY2Nlc3Mocm91dGUsIGBcXGBoZWFkZXJzKCkuJHtwcm9wfVxcYGApXG4gICAgICAgICAgYnJlYWtcbiAgICAgICAgfVxuICAgICAgICBkZWZhdWx0OiB7XG4gICAgICAgICAgLy8gV2Ugb25seSB3YXJuIGZvciB3ZWxsLWRlZmluZWQgcHJvcGVydGllcyBvZiB0aGUgaGVhZGVycyBvYmplY3QuXG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgcmV0dXJuIFJlZmxlY3RBZGFwdGVyLmdldCh0YXJnZXQsIHByb3AsIHJlY2VpdmVyKVxuICAgIH0sXG4gIH0pXG5cbiAgQ2FjaGVkSGVhZGVycy5zZXQodW5kZXJseWluZ0hlYWRlcnMsIHByb3hpZWRQcm9taXNlKVxuXG4gIHJldHVybiBwcm94aWVkUHJvbWlzZVxufVxuXG5mdW5jdGlvbiBkZXNjcmliZU5hbWVBcmcoYXJnOiB1bmtub3duKSB7XG4gIHJldHVybiB0eXBlb2YgYXJnID09PSAnc3RyaW5nJyA/IGAnJHthcmd9J2AgOiAnLi4uJ1xufVxuXG5mdW5jdGlvbiBzeW5jSU9EZXYocm91dGU6IHN0cmluZyB8IHVuZGVmaW5lZCwgZXhwcmVzc2lvbjogc3RyaW5nKSB7XG4gIGNvbnN0IHdvcmtVbml0U3RvcmUgPSB3b3JrVW5pdEFzeW5jU3RvcmFnZS5nZXRTdG9yZSgpXG4gIGlmIChcbiAgICB3b3JrVW5pdFN0b3JlICYmXG4gICAgd29ya1VuaXRTdG9yZS50eXBlID09PSAncmVxdWVzdCcgJiZcbiAgICB3b3JrVW5pdFN0b3JlLnByZXJlbmRlclBoYXNlID09PSB0cnVlXG4gICkge1xuICAgIC8vIFdoZW4gd2UncmUgcmVuZGVyaW5nIGR5bmFtaWNhbGx5IGluIGRldiB3ZSBuZWVkIHRvIGFkdmFuY2Ugb3V0IG9mIHRoZVxuICAgIC8vIFByZXJlbmRlciBlbnZpcm9ubWVudCB3aGVuIHdlIHJlYWQgUmVxdWVzdCBkYXRhIHN5bmNocm9ub3VzbHlcbiAgICBjb25zdCByZXF1ZXN0U3RvcmUgPSB3b3JrVW5pdFN0b3JlXG4gICAgdHJhY2tTeW5jaHJvbm91c1JlcXVlc3REYXRhQWNjZXNzSW5EZXYocmVxdWVzdFN0b3JlKVxuICB9XG4gIC8vIEluIGFsbCBjYXNlcyB3ZSB3YXJuIG5vcm1hbGx5XG4gIHdhcm5Gb3JTeW5jQWNjZXNzKHJvdXRlLCBleHByZXNzaW9uKVxufVxuXG5jb25zdCB3YXJuRm9yU3luY0FjY2VzcyA9IGNyZWF0ZURlZHVwZWRCeUNhbGxzaXRlU2VydmVyRXJyb3JMb2dnZXJEZXYoXG4gIGNyZWF0ZUhlYWRlcnNBY2Nlc3NFcnJvclxuKVxuXG5mdW5jdGlvbiBjcmVhdGVIZWFkZXJzQWNjZXNzRXJyb3IoXG4gIHJvdXRlOiBzdHJpbmcgfCB1bmRlZmluZWQsXG4gIGV4cHJlc3Npb246IHN0cmluZ1xuKSB7XG4gIGNvbnN0IHByZWZpeCA9IHJvdXRlID8gYFJvdXRlIFwiJHtyb3V0ZX1cIiBgIDogJ1RoaXMgcm91dGUgJ1xuICByZXR1cm4gbmV3IEVycm9yKFxuICAgIGAke3ByZWZpeH11c2VkICR7ZXhwcmVzc2lvbn0uIGAgK1xuICAgICAgYFxcYGhlYWRlcnMoKVxcYCBzaG91bGQgYmUgYXdhaXRlZCBiZWZvcmUgdXNpbmcgaXRzIHZhbHVlLiBgICtcbiAgICAgIGBMZWFybiBtb3JlOiBodHRwczovL25leHRqcy5vcmcvZG9jcy9tZXNzYWdlcy9zeW5jLWR5bmFtaWMtYXBpc2BcbiAgKVxufVxuXG50eXBlIEhlYWRlcnNFeHRlbnNpb25zID0ge1xuICBbSyBpbiBrZXlvZiBSZWFkb25seUhlYWRlcnNdOiB1bmtub3duXG59XG4iXSwibmFtZXMiOlsiSGVhZGVyc0FkYXB0ZXIiLCJ3b3JrQXN5bmNTdG9yYWdlIiwiZ2V0RXhwZWN0ZWRSZXF1ZXN0U3RvcmUiLCJ3b3JrVW5pdEFzeW5jU3RvcmFnZSIsInBvc3Rwb25lV2l0aFRyYWNraW5nIiwidGhyb3dUb0ludGVycnVwdFN0YXRpY0dlbmVyYXRpb24iLCJ0cmFja0R5bmFtaWNEYXRhSW5EeW5hbWljUmVuZGVyIiwidHJhY2tTeW5jaHJvbm91c1JlcXVlc3REYXRhQWNjZXNzSW5EZXYiLCJTdGF0aWNHZW5CYWlsb3V0RXJyb3IiLCJtYWtlSGFuZ2luZ1Byb21pc2UiLCJjcmVhdGVEZWR1cGVkQnlDYWxsc2l0ZVNlcnZlckVycm9yTG9nZ2VyRGV2Iiwic2NoZWR1bGVJbW1lZGlhdGUiLCJpc1JlcXVlc3RBUElDYWxsYWJsZUluc2lkZUFmdGVyIiwiSW52YXJpYW50RXJyb3IiLCJSZWZsZWN0QWRhcHRlciIsImhlYWRlcnMiLCJ3b3JrU3RvcmUiLCJnZXRTdG9yZSIsIndvcmtVbml0U3RvcmUiLCJwaGFzZSIsIkVycm9yIiwicm91dGUiLCJmb3JjZVN0YXRpYyIsInVuZGVybHlpbmdIZWFkZXJzIiwic2VhbCIsIkhlYWRlcnMiLCJtYWtlVW50cmFja2VkRXhvdGljSGVhZGVycyIsInR5cGUiLCJkeW5hbWljU2hvdWxkRXJyb3IiLCJtYWtlSGFuZ2luZ0hlYWRlcnMiLCJleHBvcnROYW1lIiwiZHluYW1pY1RyYWNraW5nIiwicmVxdWVzdFN0b3JlIiwicHJvY2VzcyIsImVudiIsIk5PREVfRU5WIiwiaXNQcmVmZXRjaFJlcXVlc3QiLCJfX05FWFRfRFlOQU1JQ19JTyIsIm1ha2VVbnRyYWNrZWRIZWFkZXJzV2l0aERldldhcm5pbmdzIiwibWFrZVVudHJhY2tlZEV4b3RpY0hlYWRlcnNXaXRoRGV2V2FybmluZ3MiLCJDYWNoZWRIZWFkZXJzIiwiV2Vha01hcCIsInByZXJlbmRlclN0b3JlIiwiY2FjaGVkSGVhZGVycyIsImdldCIsInByb21pc2UiLCJyZW5kZXJTaWduYWwiLCJzZXQiLCJQcm9taXNlIiwicmVzb2x2ZSIsIk9iamVjdCIsImRlZmluZVByb3BlcnRpZXMiLCJhcHBlbmQiLCJ2YWx1ZSIsImJpbmQiLCJkZWxldGUiLCJoYXMiLCJnZXRTZXRDb29raWUiLCJmb3JFYWNoIiwia2V5cyIsInZhbHVlcyIsImVudHJpZXMiLCJTeW1ib2wiLCJpdGVyYXRvciIsImV4cHJlc3Npb24iLCJkZXNjcmliZU5hbWVBcmciLCJhcmd1bWVudHMiLCJzeW5jSU9EZXYiLCJhcHBseSIsIl9kZWxldGUiLCJwcm94aWVkUHJvbWlzZSIsIlByb3h5IiwidGFyZ2V0IiwicHJvcCIsInJlY2VpdmVyIiwid2FybkZvclN5bmNBY2Nlc3MiLCJhcmciLCJwcmVyZW5kZXJQaGFzZSIsImNyZWF0ZUhlYWRlcnNBY2Nlc3NFcnJvciIsInByZWZpeCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(middleware)/./node_modules/next/dist/esm/server/request/headers.js\n");

/***/ })

});