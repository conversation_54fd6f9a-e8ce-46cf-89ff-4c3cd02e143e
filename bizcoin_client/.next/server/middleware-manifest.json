{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "Xmp6Y3lign+dStwiYSDp6SM2Zh8vQj/zVg6+BTuiuus=", "__NEXT_PREVIEW_MODE_ID": "2c1ed4dbbc44f5fedb9ddb81a5cc7960", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "52c3212b182e37c782641f0534a109a3a0887e5ea1a4b23f03359faa8b07e586", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "ca7a062db2797a2c00bd9f7031950cc4c52daa071f689ffa8d3ac454b079a79b"}}}, "functions": {}, "sortedMiddleware": ["/"]}