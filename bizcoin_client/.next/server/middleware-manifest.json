{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "Xmp6Y3lign+dStwiYSDp6SM2Zh8vQj/zVg6+BTuiuus=", "__NEXT_PREVIEW_MODE_ID": "bdfa8e1273e87c17b793f45887ec8b1a", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "17c4bbd56ac5cdcb179cb97c9cd9ed6e72322c874e31e6533e8f39565509de18", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "ceee62d39e7c52aa0e59337901a7f3f49d423ce9409e7b24facc5d54bdefdb5e"}}}, "functions": {}, "sortedMiddleware": ["/"]}