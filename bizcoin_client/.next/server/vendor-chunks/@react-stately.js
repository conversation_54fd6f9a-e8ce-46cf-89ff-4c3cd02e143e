"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@react-stately";
exports.ids = ["vendor-chunks/@react-stately"];
exports.modules = {

/***/ "(ssr)/./node_modules/@react-stately/flags/dist/import.mjs":
/*!***********************************************************!*\
  !*** ./node_modules/@react-stately/flags/dist/import.mjs ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   enableShadowDOM: () => (/* binding */ $f4e2df6bd15f8569$export$12b151d9882e9985),\n/* harmony export */   enableTableNestedRows: () => (/* binding */ $f4e2df6bd15f8569$export$d9d8a0f82de49530),\n/* harmony export */   shadowDOM: () => (/* binding */ $f4e2df6bd15f8569$export$98658e8c59125e6a),\n/* harmony export */   tableNestedRows: () => (/* binding */ $f4e2df6bd15f8569$export$1b00cb14a96194e6)\n/* harmony export */ });\n/*\n * Copyright 2023 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ let $f4e2df6bd15f8569$var$_tableNestedRows = false;\nlet $f4e2df6bd15f8569$var$_shadowDOM = false;\nfunction $f4e2df6bd15f8569$export$d9d8a0f82de49530() {\n    $f4e2df6bd15f8569$var$_tableNestedRows = true;\n}\nfunction $f4e2df6bd15f8569$export$1b00cb14a96194e6() {\n    return $f4e2df6bd15f8569$var$_tableNestedRows;\n}\nfunction $f4e2df6bd15f8569$export$12b151d9882e9985() {\n    $f4e2df6bd15f8569$var$_shadowDOM = true;\n}\nfunction $f4e2df6bd15f8569$export$98658e8c59125e6a() {\n    return $f4e2df6bd15f8569$var$_shadowDOM;\n}\n\n\n\n//# sourceMappingURL=module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@react-stately/flags/dist/import.mjs\n");

/***/ })

};
;