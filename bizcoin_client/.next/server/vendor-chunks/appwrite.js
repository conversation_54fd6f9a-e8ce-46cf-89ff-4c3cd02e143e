"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/appwrite";
exports.ids = ["vendor-chunks/appwrite"];
exports.modules = {

/***/ "(ssr)/./node_modules/appwrite/dist/esm/sdk.js":
/*!***********************************************!*\
  !*** ./node_modules/appwrite/dist/esm/sdk.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Account: () => (/* binding */ Account),\n/* harmony export */   AppwriteException: () => (/* binding */ AppwriteException),\n/* harmony export */   AuthenticationFactor: () => (/* binding */ AuthenticationFactor),\n/* harmony export */   AuthenticatorType: () => (/* binding */ AuthenticatorType),\n/* harmony export */   Avatars: () => (/* binding */ Avatars),\n/* harmony export */   Browser: () => (/* binding */ Browser),\n/* harmony export */   Client: () => (/* binding */ Client),\n/* harmony export */   CreditCard: () => (/* binding */ CreditCard),\n/* harmony export */   Databases: () => (/* binding */ Databases),\n/* harmony export */   ExecutionMethod: () => (/* binding */ ExecutionMethod),\n/* harmony export */   Flag: () => (/* binding */ Flag),\n/* harmony export */   Functions: () => (/* binding */ Functions),\n/* harmony export */   Graphql: () => (/* binding */ Graphql),\n/* harmony export */   ID: () => (/* binding */ ID),\n/* harmony export */   ImageFormat: () => (/* binding */ ImageFormat),\n/* harmony export */   ImageGravity: () => (/* binding */ ImageGravity),\n/* harmony export */   Locale: () => (/* binding */ Locale),\n/* harmony export */   Messaging: () => (/* binding */ Messaging),\n/* harmony export */   OAuthProvider: () => (/* binding */ OAuthProvider),\n/* harmony export */   Permission: () => (/* binding */ Permission),\n/* harmony export */   Query: () => (/* binding */ Query),\n/* harmony export */   Role: () => (/* binding */ Role),\n/* harmony export */   Storage: () => (/* binding */ Storage),\n/* harmony export */   Teams: () => (/* binding */ Teams)\n/* harmony export */ });\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n\r\nfunction __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nfunction __classPrivateFieldGet(receiver, state, kind, f) {\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\r\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\r\n}\n\n/**\n * Helper class to generate query strings.\n */\nclass Query {\n    /**\n     * Constructor for Query class.\n     *\n     * @param {string} method\n     * @param {AttributesTypes} attribute\n     * @param {QueryTypes} values\n     */\n    constructor(method, attribute, values) {\n        this.method = method;\n        this.attribute = attribute;\n        if (values !== undefined) {\n            if (Array.isArray(values)) {\n                this.values = values;\n            }\n            else {\n                this.values = [values];\n            }\n        }\n    }\n    /**\n     * Convert the query object to a JSON string.\n     *\n     * @returns {string}\n     */\n    toString() {\n        return JSON.stringify({\n            method: this.method,\n            attribute: this.attribute,\n            values: this.values,\n        });\n    }\n}\n/**\n * Filter resources where attribute is equal to value.\n *\n * @param {string} attribute\n * @param {QueryTypes} value\n * @returns {string}\n */\nQuery.equal = (attribute, value) => new Query(\"equal\", attribute, value).toString();\n/**\n * Filter resources where attribute is not equal to value.\n *\n * @param {string} attribute\n * @param {QueryTypes} value\n * @returns {string}\n */\nQuery.notEqual = (attribute, value) => new Query(\"notEqual\", attribute, value).toString();\n/**\n * Filter resources where attribute is less than value.\n *\n * @param {string} attribute\n * @param {QueryTypes} value\n * @returns {string}\n */\nQuery.lessThan = (attribute, value) => new Query(\"lessThan\", attribute, value).toString();\n/**\n * Filter resources where attribute is less than or equal to value.\n *\n * @param {string} attribute\n * @param {QueryTypes} value\n * @returns {string}\n */\nQuery.lessThanEqual = (attribute, value) => new Query(\"lessThanEqual\", attribute, value).toString();\n/**\n * Filter resources where attribute is greater than value.\n *\n * @param {string} attribute\n * @param {QueryTypes} value\n * @returns {string}\n */\nQuery.greaterThan = (attribute, value) => new Query(\"greaterThan\", attribute, value).toString();\n/**\n * Filter resources where attribute is greater than or equal to value.\n *\n * @param {string} attribute\n * @param {QueryTypes} value\n * @returns {string}\n */\nQuery.greaterThanEqual = (attribute, value) => new Query(\"greaterThanEqual\", attribute, value).toString();\n/**\n * Filter resources where attribute is null.\n *\n * @param {string} attribute\n * @returns {string}\n */\nQuery.isNull = (attribute) => new Query(\"isNull\", attribute).toString();\n/**\n * Filter resources where attribute is not null.\n *\n * @param {string} attribute\n * @returns {string}\n */\nQuery.isNotNull = (attribute) => new Query(\"isNotNull\", attribute).toString();\n/**\n * Filter resources where attribute is between start and end (inclusive).\n *\n * @param {string} attribute\n * @param {string | number} start\n * @param {string | number} end\n * @returns {string}\n */\nQuery.between = (attribute, start, end) => new Query(\"between\", attribute, [start, end]).toString();\n/**\n * Filter resources where attribute starts with value.\n *\n * @param {string} attribute\n * @param {string} value\n * @returns {string}\n */\nQuery.startsWith = (attribute, value) => new Query(\"startsWith\", attribute, value).toString();\n/**\n * Filter resources where attribute ends with value.\n *\n * @param {string} attribute\n * @param {string} value\n * @returns {string}\n */\nQuery.endsWith = (attribute, value) => new Query(\"endsWith\", attribute, value).toString();\n/**\n * Specify which attributes should be returned by the API call.\n *\n * @param {string[]} attributes\n * @returns {string}\n */\nQuery.select = (attributes) => new Query(\"select\", undefined, attributes).toString();\n/**\n * Filter resources by searching attribute for value.\n * A fulltext index on attribute is required for this query to work.\n *\n * @param {string} attribute\n * @param {string} value\n * @returns {string}\n */\nQuery.search = (attribute, value) => new Query(\"search\", attribute, value).toString();\n/**\n * Sort results by attribute descending.\n *\n * @param {string} attribute\n * @returns {string}\n */\nQuery.orderDesc = (attribute) => new Query(\"orderDesc\", attribute).toString();\n/**\n * Sort results by attribute ascending.\n *\n * @param {string} attribute\n * @returns {string}\n */\nQuery.orderAsc = (attribute) => new Query(\"orderAsc\", attribute).toString();\n/**\n * Return results after documentId.\n *\n * @param {string} documentId\n * @returns {string}\n */\nQuery.cursorAfter = (documentId) => new Query(\"cursorAfter\", undefined, documentId).toString();\n/**\n * Return results before documentId.\n *\n * @param {string} documentId\n * @returns {string}\n */\nQuery.cursorBefore = (documentId) => new Query(\"cursorBefore\", undefined, documentId).toString();\n/**\n * Return only limit results.\n *\n * @param {number} limit\n * @returns {string}\n */\nQuery.limit = (limit) => new Query(\"limit\", undefined, limit).toString();\n/**\n * Filter resources by skipping the first offset results.\n *\n * @param {number} offset\n * @returns {string}\n */\nQuery.offset = (offset) => new Query(\"offset\", undefined, offset).toString();\n/**\n * Filter resources where attribute contains the specified value.\n *\n * @param {string} attribute\n * @param {string | string[]} value\n * @returns {string}\n */\nQuery.contains = (attribute, value) => new Query(\"contains\", attribute, value).toString();\n/**\n * Combine multiple queries using logical OR operator.\n *\n * @param {string[]} queries\n * @returns {string}\n */\nQuery.or = (queries) => new Query(\"or\", undefined, queries.map((query) => JSON.parse(query))).toString();\n/**\n * Combine multiple queries using logical AND operator.\n *\n * @param {string[]} queries\n * @returns {string}\n */\nQuery.and = (queries) => new Query(\"and\", undefined, queries.map((query) => JSON.parse(query))).toString();\n\n/**\n * Exception thrown by the  package\n */\nclass AppwriteException extends Error {\n    /**\n     * Initializes a Appwrite Exception.\n     *\n     * @param {string} message - The error message.\n     * @param {number} code - The error code. Default is 0.\n     * @param {string} type - The error type. Default is an empty string.\n     * @param {string} response - The response string. Default is an empty string.\n     */\n    constructor(message, code = 0, type = '', response = '') {\n        super(message);\n        this.name = 'AppwriteException';\n        this.message = message;\n        this.code = code;\n        this.type = type;\n        this.response = response;\n    }\n}\n/**\n * Client that handles requests to Appwrite\n */\nclass Client {\n    constructor() {\n        /**\n         * Holds configuration such as project.\n         */\n        this.config = {\n            endpoint: 'https://cloud.appwrite.io/v1',\n            endpointRealtime: '',\n            project: '',\n            jwt: '',\n            locale: '',\n            session: '',\n            devkey: '',\n        };\n        /**\n         * Custom headers for API requests.\n         */\n        this.headers = {\n            'x-sdk-name': 'Web',\n            'x-sdk-platform': 'client',\n            'x-sdk-language': 'web',\n            'x-sdk-version': '18.2.0',\n            'X-Appwrite-Response-Format': '1.7.0',\n        };\n        this.realtime = {\n            socket: undefined,\n            timeout: undefined,\n            heartbeat: undefined,\n            url: '',\n            channels: new Set(),\n            subscriptions: new Map(),\n            subscriptionsCounter: 0,\n            reconnect: true,\n            reconnectAttempts: 0,\n            lastMessage: undefined,\n            connect: () => {\n                clearTimeout(this.realtime.timeout);\n                this.realtime.timeout = window === null || window === void 0 ? void 0 : window.setTimeout(() => {\n                    this.realtime.createSocket();\n                }, 50);\n            },\n            getTimeout: () => {\n                switch (true) {\n                    case this.realtime.reconnectAttempts < 5:\n                        return 1000;\n                    case this.realtime.reconnectAttempts < 15:\n                        return 5000;\n                    case this.realtime.reconnectAttempts < 100:\n                        return 10000;\n                    default:\n                        return 60000;\n                }\n            },\n            createHeartbeat: () => {\n                if (this.realtime.heartbeat) {\n                    clearTimeout(this.realtime.heartbeat);\n                }\n                this.realtime.heartbeat = window === null || window === void 0 ? void 0 : window.setInterval(() => {\n                    var _a;\n                    (_a = this.realtime.socket) === null || _a === void 0 ? void 0 : _a.send(JSON.stringify({\n                        type: 'ping'\n                    }));\n                }, 20000);\n            },\n            createSocket: () => {\n                var _a, _b, _c;\n                if (this.realtime.channels.size < 1) {\n                    this.realtime.reconnect = false;\n                    (_a = this.realtime.socket) === null || _a === void 0 ? void 0 : _a.close();\n                    return;\n                }\n                const channels = new URLSearchParams();\n                channels.set('project', this.config.project);\n                this.realtime.channels.forEach(channel => {\n                    channels.append('channels[]', channel);\n                });\n                const url = this.config.endpointRealtime + '/realtime?' + channels.toString();\n                if (url !== this.realtime.url || // Check if URL is present\n                    !this.realtime.socket || // Check if WebSocket has not been created\n                    ((_b = this.realtime.socket) === null || _b === void 0 ? void 0 : _b.readyState) > WebSocket.OPEN // Check if WebSocket is CLOSING (3) or CLOSED (4)\n                ) {\n                    if (this.realtime.socket &&\n                        ((_c = this.realtime.socket) === null || _c === void 0 ? void 0 : _c.readyState) < WebSocket.CLOSING // Close WebSocket if it is CONNECTING (0) or OPEN (1)\n                    ) {\n                        this.realtime.reconnect = false;\n                        this.realtime.socket.close();\n                    }\n                    this.realtime.url = url;\n                    this.realtime.socket = new WebSocket(url);\n                    this.realtime.socket.addEventListener('message', this.realtime.onMessage);\n                    this.realtime.socket.addEventListener('open', _event => {\n                        this.realtime.reconnectAttempts = 0;\n                        this.realtime.createHeartbeat();\n                    });\n                    this.realtime.socket.addEventListener('close', event => {\n                        var _a, _b, _c;\n                        if (!this.realtime.reconnect ||\n                            (((_b = (_a = this.realtime) === null || _a === void 0 ? void 0 : _a.lastMessage) === null || _b === void 0 ? void 0 : _b.type) === 'error' && // Check if last message was of type error\n                                ((_c = this.realtime) === null || _c === void 0 ? void 0 : _c.lastMessage.data).code === 1008 // Check for policy violation 1008\n                            )) {\n                            this.realtime.reconnect = true;\n                            return;\n                        }\n                        const timeout = this.realtime.getTimeout();\n                        console.error(`Realtime got disconnected. Reconnect will be attempted in ${timeout / 1000} seconds.`, event.reason);\n                        setTimeout(() => {\n                            this.realtime.reconnectAttempts++;\n                            this.realtime.createSocket();\n                        }, timeout);\n                    });\n                }\n            },\n            onMessage: (event) => {\n                var _a, _b;\n                try {\n                    const message = JSON.parse(event.data);\n                    this.realtime.lastMessage = message;\n                    switch (message.type) {\n                        case 'connected':\n                            const cookie = JSON.parse((_a = window.localStorage.getItem('cookieFallback')) !== null && _a !== void 0 ? _a : '{}');\n                            const session = cookie === null || cookie === void 0 ? void 0 : cookie[`a_session_${this.config.project}`];\n                            const messageData = message.data;\n                            if (session && !messageData.user) {\n                                (_b = this.realtime.socket) === null || _b === void 0 ? void 0 : _b.send(JSON.stringify({\n                                    type: 'authentication',\n                                    data: {\n                                        session\n                                    }\n                                }));\n                            }\n                            break;\n                        case 'event':\n                            let data = message.data;\n                            if (data === null || data === void 0 ? void 0 : data.channels) {\n                                const isSubscribed = data.channels.some(channel => this.realtime.channels.has(channel));\n                                if (!isSubscribed)\n                                    return;\n                                this.realtime.subscriptions.forEach(subscription => {\n                                    if (data.channels.some(channel => subscription.channels.includes(channel))) {\n                                        setTimeout(() => subscription.callback(data));\n                                    }\n                                });\n                            }\n                            break;\n                        case 'pong':\n                            break; // Handle pong response if needed\n                        case 'error':\n                            throw message.data;\n                        default:\n                            break;\n                    }\n                }\n                catch (e) {\n                    console.error(e);\n                }\n            },\n            cleanUp: channels => {\n                this.realtime.channels.forEach(channel => {\n                    if (channels.includes(channel)) {\n                        let found = Array.from(this.realtime.subscriptions).some(([_key, subscription]) => {\n                            return subscription.channels.includes(channel);\n                        });\n                        if (!found) {\n                            this.realtime.channels.delete(channel);\n                        }\n                    }\n                });\n            }\n        };\n    }\n    /**\n     * Set Endpoint\n     *\n     * Your project endpoint\n     *\n     * @param {string} endpoint\n     *\n     * @returns {this}\n     */\n    setEndpoint(endpoint) {\n        if (!endpoint.startsWith('http://') && !endpoint.startsWith('https://')) {\n            throw new AppwriteException('Invalid endpoint URL: ' + endpoint);\n        }\n        this.config.endpoint = endpoint;\n        this.config.endpointRealtime = endpoint.replace('https://', 'wss://').replace('http://', 'ws://');\n        return this;\n    }\n    /**\n     * Set Realtime Endpoint\n     *\n     * @param {string} endpointRealtime\n     *\n     * @returns {this}\n     */\n    setEndpointRealtime(endpointRealtime) {\n        if (!endpointRealtime.startsWith('ws://') && !endpointRealtime.startsWith('wss://')) {\n            throw new AppwriteException('Invalid realtime endpoint URL: ' + endpointRealtime);\n        }\n        this.config.endpointRealtime = endpointRealtime;\n        return this;\n    }\n    /**\n     * Set Project\n     *\n     * Your project ID\n     *\n     * @param value string\n     *\n     * @return {this}\n     */\n    setProject(value) {\n        this.headers['X-Appwrite-Project'] = value;\n        this.config.project = value;\n        return this;\n    }\n    /**\n     * Set JWT\n     *\n     * Your secret JSON Web Token\n     *\n     * @param value string\n     *\n     * @return {this}\n     */\n    setJWT(value) {\n        this.headers['X-Appwrite-JWT'] = value;\n        this.config.jwt = value;\n        return this;\n    }\n    /**\n     * Set Locale\n     *\n     * @param value string\n     *\n     * @return {this}\n     */\n    setLocale(value) {\n        this.headers['X-Appwrite-Locale'] = value;\n        this.config.locale = value;\n        return this;\n    }\n    /**\n     * Set Session\n     *\n     * The user session to authenticate with\n     *\n     * @param value string\n     *\n     * @return {this}\n     */\n    setSession(value) {\n        this.headers['X-Appwrite-Session'] = value;\n        this.config.session = value;\n        return this;\n    }\n    /**\n     * Set DevKey\n     *\n     * Your secret dev API key\n     *\n     * @param value string\n     *\n     * @return {this}\n     */\n    setDevKey(value) {\n        this.headers['X-Appwrite-Dev-Key'] = value;\n        this.config.devkey = value;\n        return this;\n    }\n    /**\n     * Subscribes to Appwrite events and passes you the payload in realtime.\n     *\n     * @param {string|string[]} channels\n     * Channel to subscribe - pass a single channel as a string or multiple with an array of strings.\n     *\n     * Possible channels are:\n     * - account\n     * - collections\n     * - collections.[ID]\n     * - collections.[ID].documents\n     * - documents\n     * - documents.[ID]\n     * - files\n     * - files.[ID]\n     * - executions\n     * - executions.[ID]\n     * - functions.[ID]\n     * - teams\n     * - teams.[ID]\n     * - memberships\n     * - memberships.[ID]\n     * @param {(payload: RealtimeMessage) => void} callback Is called on every realtime update.\n     * @returns {() => void} Unsubscribes from events.\n     */\n    subscribe(channels, callback) {\n        let channelArray = typeof channels === 'string' ? [channels] : channels;\n        channelArray.forEach(channel => this.realtime.channels.add(channel));\n        const counter = this.realtime.subscriptionsCounter++;\n        this.realtime.subscriptions.set(counter, {\n            channels: channelArray,\n            callback\n        });\n        this.realtime.connect();\n        return () => {\n            this.realtime.subscriptions.delete(counter);\n            this.realtime.cleanUp(channelArray);\n            this.realtime.connect();\n        };\n    }\n    prepareRequest(method, url, headers = {}, params = {}) {\n        method = method.toUpperCase();\n        headers = Object.assign({}, this.headers, headers);\n        if (typeof window !== 'undefined' && window.localStorage) {\n            const cookieFallback = window.localStorage.getItem('cookieFallback');\n            if (cookieFallback) {\n                headers['X-Fallback-Cookies'] = cookieFallback;\n            }\n        }\n        let options = {\n            method,\n            headers,\n        };\n        if (headers['X-Appwrite-Dev-Key'] === undefined) {\n            options.credentials = 'include';\n        }\n        if (method === 'GET') {\n            for (const [key, value] of Object.entries(Client.flatten(params))) {\n                url.searchParams.append(key, value);\n            }\n        }\n        else {\n            switch (headers['content-type']) {\n                case 'application/json':\n                    options.body = JSON.stringify(params);\n                    break;\n                case 'multipart/form-data':\n                    const formData = new FormData();\n                    for (const [key, value] of Object.entries(params)) {\n                        if (value instanceof File) {\n                            formData.append(key, value, value.name);\n                        }\n                        else if (Array.isArray(value)) {\n                            for (const nestedValue of value) {\n                                formData.append(`${key}[]`, nestedValue);\n                            }\n                        }\n                        else {\n                            formData.append(key, value);\n                        }\n                    }\n                    options.body = formData;\n                    delete headers['content-type'];\n                    break;\n            }\n        }\n        return { uri: url.toString(), options };\n    }\n    chunkedUpload(method, url, headers = {}, originalPayload = {}, onProgress) {\n        var _a;\n        return __awaiter(this, void 0, void 0, function* () {\n            const [fileParam, file] = (_a = Object.entries(originalPayload).find(([_, value]) => value instanceof File)) !== null && _a !== void 0 ? _a : [];\n            if (!file || !fileParam) {\n                throw new Error('File not found in payload');\n            }\n            if (file.size <= Client.CHUNK_SIZE) {\n                return yield this.call(method, url, headers, originalPayload);\n            }\n            let start = 0;\n            let response = null;\n            while (start < file.size) {\n                let end = start + Client.CHUNK_SIZE; // Prepare end for the next chunk\n                if (end >= file.size) {\n                    end = file.size; // Adjust for the last chunk to include the last byte\n                }\n                headers['content-range'] = `bytes ${start}-${end - 1}/${file.size}`;\n                const chunk = file.slice(start, end);\n                let payload = Object.assign({}, originalPayload);\n                payload[fileParam] = new File([chunk], file.name);\n                response = yield this.call(method, url, headers, payload);\n                if (onProgress && typeof onProgress === 'function') {\n                    onProgress({\n                        $id: response.$id,\n                        progress: Math.round((end / file.size) * 100),\n                        sizeUploaded: end,\n                        chunksTotal: Math.ceil(file.size / Client.CHUNK_SIZE),\n                        chunksUploaded: Math.ceil(end / Client.CHUNK_SIZE)\n                    });\n                }\n                if (response && response.$id) {\n                    headers['x-appwrite-id'] = response.$id;\n                }\n                start = end;\n            }\n            return response;\n        });\n    }\n    ping() {\n        return __awaiter(this, void 0, void 0, function* () {\n            return this.call('GET', new URL(this.config.endpoint + '/ping'));\n        });\n    }\n    call(method, url, headers = {}, params = {}, responseType = 'json') {\n        var _a, _b;\n        return __awaiter(this, void 0, void 0, function* () {\n            const { uri, options } = this.prepareRequest(method, url, headers, params);\n            let data = null;\n            const response = yield fetch(uri, options);\n            // type opaque: No-CORS, different-origin response (CORS-issue)\n            if (response.type === 'opaque') {\n                throw new AppwriteException(`Invalid Origin. Register your new client (${window.location.host}) as a new Web platform on your project console dashboard`, 403, \"forbidden\", \"\");\n            }\n            const warnings = response.headers.get('x-appwrite-warning');\n            if (warnings) {\n                warnings.split(';').forEach((warning) => console.warn('Warning: ' + warning));\n            }\n            if ((_a = response.headers.get('content-type')) === null || _a === void 0 ? void 0 : _a.includes('application/json')) {\n                data = yield response.json();\n            }\n            else if (responseType === 'arrayBuffer') {\n                data = yield response.arrayBuffer();\n            }\n            else {\n                data = {\n                    message: yield response.text()\n                };\n            }\n            if (400 <= response.status) {\n                let responseText = '';\n                if (((_b = response.headers.get('content-type')) === null || _b === void 0 ? void 0 : _b.includes('application/json')) || responseType === 'arrayBuffer') {\n                    responseText = JSON.stringify(data);\n                }\n                else {\n                    responseText = data === null || data === void 0 ? void 0 : data.message;\n                }\n                throw new AppwriteException(data === null || data === void 0 ? void 0 : data.message, response.status, data === null || data === void 0 ? void 0 : data.type, responseText);\n            }\n            const cookieFallback = response.headers.get('X-Fallback-Cookies');\n            if (typeof window !== 'undefined' && window.localStorage && cookieFallback) {\n                window.console.warn('Appwrite is using localStorage for session management. Increase your security by adding a custom domain as your API endpoint.');\n                window.localStorage.setItem('cookieFallback', cookieFallback);\n            }\n            return data;\n        });\n    }\n    static flatten(data, prefix = '') {\n        let output = {};\n        for (const [key, value] of Object.entries(data)) {\n            let finalKey = prefix ? prefix + '[' + key + ']' : key;\n            if (Array.isArray(value)) {\n                output = Object.assign(Object.assign({}, output), Client.flatten(value, finalKey));\n            }\n            else {\n                output[finalKey] = value;\n            }\n        }\n        return output;\n    }\n}\nClient.CHUNK_SIZE = 1024 * 1024 * 5;\n\nclass Service {\n    constructor(client) {\n        this.client = client;\n    }\n    static flatten(data, prefix = '') {\n        let output = {};\n        for (const [key, value] of Object.entries(data)) {\n            let finalKey = prefix ? prefix + '[' + key + ']' : key;\n            if (Array.isArray(value)) {\n                output = Object.assign(Object.assign({}, output), Service.flatten(value, finalKey));\n            }\n            else {\n                output[finalKey] = value;\n            }\n        }\n        return output;\n    }\n}\n/**\n * The size for chunked uploads in bytes.\n */\nService.CHUNK_SIZE = 5 * 1024 * 1024; // 5MB\n\nclass Account {\n    constructor(client) {\n        this.client = client;\n    }\n    /**\n     * Get the currently logged in user.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.User<Preferences>>}\n     */\n    get() {\n        const apiPath = '/account';\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n    /**\n     * Use this endpoint to allow a new user to register a new account in your project. After the user registration completes successfully, you can use the [/account/verfication](https://appwrite.io/docs/references/cloud/client-web/account#createVerification) route to start verifying the user email address. To allow the new user to login to their new account, you need to create a new [account session](https://appwrite.io/docs/references/cloud/client-web/account#createEmailSession).\n     *\n     * @param {string} userId\n     * @param {string} email\n     * @param {string} password\n     * @param {string} name\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.User<Preferences>>}\n     */\n    create(userId, email, password, name) {\n        if (typeof userId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"userId\"');\n        }\n        if (typeof email === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"email\"');\n        }\n        if (typeof password === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"password\"');\n        }\n        const apiPath = '/account';\n        const payload = {};\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n        if (typeof email !== 'undefined') {\n            payload['email'] = email;\n        }\n        if (typeof password !== 'undefined') {\n            payload['password'] = password;\n        }\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('post', uri, apiHeaders, payload);\n    }\n    /**\n     * Update currently logged in user account email address. After changing user address, the user confirmation status will get reset. A new confirmation email is not sent automatically however you can use the send confirmation email endpoint again to send the confirmation email. For security measures, user password is required to complete this request.\n     * This endpoint can also be used to convert an anonymous account to a normal one, by passing an email address and a new password.\n     *\n     *\n     * @param {string} email\n     * @param {string} password\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.User<Preferences>>}\n     */\n    updateEmail(email, password) {\n        if (typeof email === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"email\"');\n        }\n        if (typeof password === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"password\"');\n        }\n        const apiPath = '/account/email';\n        const payload = {};\n        if (typeof email !== 'undefined') {\n            payload['email'] = email;\n        }\n        if (typeof password !== 'undefined') {\n            payload['password'] = password;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('patch', uri, apiHeaders, payload);\n    }\n    /**\n     * Get the list of identities for the currently logged in user.\n     *\n     * @param {string[]} queries\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.IdentityList>}\n     */\n    listIdentities(queries) {\n        const apiPath = '/account/identities';\n        const payload = {};\n        if (typeof queries !== 'undefined') {\n            payload['queries'] = queries;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n    /**\n     * Delete an identity by its unique ID.\n     *\n     * @param {string} identityId\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    deleteIdentity(identityId) {\n        if (typeof identityId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"identityId\"');\n        }\n        const apiPath = '/account/identities/{identityId}'.replace('{identityId}', identityId);\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('delete', uri, apiHeaders, payload);\n    }\n    /**\n     * Use this endpoint to create a JSON Web Token. You can use the resulting JWT to authenticate on behalf of the current user when working with the Appwrite server-side API and SDKs. The JWT secret is valid for 15 minutes from its creation and will be invalid if the user will logout in that time frame.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Jwt>}\n     */\n    createJWT() {\n        const apiPath = '/account/jwts';\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('post', uri, apiHeaders, payload);\n    }\n    /**\n     * Get the list of latest security activity logs for the currently logged in user. Each log returns user IP address, location and date and time of log.\n     *\n     * @param {string[]} queries\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.LogList>}\n     */\n    listLogs(queries) {\n        const apiPath = '/account/logs';\n        const payload = {};\n        if (typeof queries !== 'undefined') {\n            payload['queries'] = queries;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n    /**\n     * Enable or disable MFA on an account.\n     *\n     * @param {boolean} mfa\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.User<Preferences>>}\n     */\n    updateMFA(mfa) {\n        if (typeof mfa === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"mfa\"');\n        }\n        const apiPath = '/account/mfa';\n        const payload = {};\n        if (typeof mfa !== 'undefined') {\n            payload['mfa'] = mfa;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('patch', uri, apiHeaders, payload);\n    }\n    /**\n     * Add an authenticator app to be used as an MFA factor. Verify the authenticator using the [verify authenticator](/docs/references/cloud/client-web/account#updateMfaAuthenticator) method.\n     *\n     * @param {AuthenticatorType} type\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.MfaType>}\n     */\n    createMfaAuthenticator(type) {\n        if (typeof type === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"type\"');\n        }\n        const apiPath = '/account/mfa/authenticators/{type}'.replace('{type}', type);\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('post', uri, apiHeaders, payload);\n    }\n    /**\n     * Verify an authenticator app after adding it using the [add authenticator](/docs/references/cloud/client-web/account#createMfaAuthenticator) method.\n     *\n     * @param {AuthenticatorType} type\n     * @param {string} otp\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.User<Preferences>>}\n     */\n    updateMfaAuthenticator(type, otp) {\n        if (typeof type === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"type\"');\n        }\n        if (typeof otp === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"otp\"');\n        }\n        const apiPath = '/account/mfa/authenticators/{type}'.replace('{type}', type);\n        const payload = {};\n        if (typeof otp !== 'undefined') {\n            payload['otp'] = otp;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('put', uri, apiHeaders, payload);\n    }\n    /**\n     * Delete an authenticator for a user by ID.\n     *\n     * @param {AuthenticatorType} type\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    deleteMfaAuthenticator(type) {\n        if (typeof type === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"type\"');\n        }\n        const apiPath = '/account/mfa/authenticators/{type}'.replace('{type}', type);\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('delete', uri, apiHeaders, payload);\n    }\n    /**\n     * Begin the process of MFA verification after sign-in. Finish the flow with [updateMfaChallenge](/docs/references/cloud/client-web/account#updateMfaChallenge) method.\n     *\n     * @param {AuthenticationFactor} factor\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.MfaChallenge>}\n     */\n    createMfaChallenge(factor) {\n        if (typeof factor === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"factor\"');\n        }\n        const apiPath = '/account/mfa/challenge';\n        const payload = {};\n        if (typeof factor !== 'undefined') {\n            payload['factor'] = factor;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('post', uri, apiHeaders, payload);\n    }\n    /**\n     * Complete the MFA challenge by providing the one-time password. Finish the process of MFA verification by providing the one-time password. To begin the flow, use [createMfaChallenge](/docs/references/cloud/client-web/account#createMfaChallenge) method.\n     *\n     * @param {string} challengeId\n     * @param {string} otp\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Session>}\n     */\n    updateMfaChallenge(challengeId, otp) {\n        if (typeof challengeId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"challengeId\"');\n        }\n        if (typeof otp === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"otp\"');\n        }\n        const apiPath = '/account/mfa/challenge';\n        const payload = {};\n        if (typeof challengeId !== 'undefined') {\n            payload['challengeId'] = challengeId;\n        }\n        if (typeof otp !== 'undefined') {\n            payload['otp'] = otp;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('put', uri, apiHeaders, payload);\n    }\n    /**\n     * List the factors available on the account to be used as a MFA challange.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.MfaFactors>}\n     */\n    listMfaFactors() {\n        const apiPath = '/account/mfa/factors';\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n    /**\n     * Get recovery codes that can be used as backup for MFA flow. Before getting codes, they must be generated using [createMfaRecoveryCodes](/docs/references/cloud/client-web/account#createMfaRecoveryCodes) method. An OTP challenge is required to read recovery codes.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.MfaRecoveryCodes>}\n     */\n    getMfaRecoveryCodes() {\n        const apiPath = '/account/mfa/recovery-codes';\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n    /**\n     * Generate recovery codes as backup for MFA flow. It&#039;s recommended to generate and show then immediately after user successfully adds their authehticator. Recovery codes can be used as a MFA verification type in [createMfaChallenge](/docs/references/cloud/client-web/account#createMfaChallenge) method.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.MfaRecoveryCodes>}\n     */\n    createMfaRecoveryCodes() {\n        const apiPath = '/account/mfa/recovery-codes';\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('post', uri, apiHeaders, payload);\n    }\n    /**\n     * Regenerate recovery codes that can be used as backup for MFA flow. Before regenerating codes, they must be first generated using [createMfaRecoveryCodes](/docs/references/cloud/client-web/account#createMfaRecoveryCodes) method. An OTP challenge is required to regenreate recovery codes.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.MfaRecoveryCodes>}\n     */\n    updateMfaRecoveryCodes() {\n        const apiPath = '/account/mfa/recovery-codes';\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('patch', uri, apiHeaders, payload);\n    }\n    /**\n     * Update currently logged in user account name.\n     *\n     * @param {string} name\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.User<Preferences>>}\n     */\n    updateName(name) {\n        if (typeof name === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"name\"');\n        }\n        const apiPath = '/account/name';\n        const payload = {};\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('patch', uri, apiHeaders, payload);\n    }\n    /**\n     * Update currently logged in user password. For validation, user is required to pass in the new password, and the old password. For users created with OAuth, Team Invites and Magic URL, oldPassword is optional.\n     *\n     * @param {string} password\n     * @param {string} oldPassword\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.User<Preferences>>}\n     */\n    updatePassword(password, oldPassword) {\n        if (typeof password === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"password\"');\n        }\n        const apiPath = '/account/password';\n        const payload = {};\n        if (typeof password !== 'undefined') {\n            payload['password'] = password;\n        }\n        if (typeof oldPassword !== 'undefined') {\n            payload['oldPassword'] = oldPassword;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('patch', uri, apiHeaders, payload);\n    }\n    /**\n     * Update the currently logged in user&#039;s phone number. After updating the phone number, the phone verification status will be reset. A confirmation SMS is not sent automatically, however you can use the [POST /account/verification/phone](https://appwrite.io/docs/references/cloud/client-web/account#createPhoneVerification) endpoint to send a confirmation SMS.\n     *\n     * @param {string} phone\n     * @param {string} password\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.User<Preferences>>}\n     */\n    updatePhone(phone, password) {\n        if (typeof phone === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"phone\"');\n        }\n        if (typeof password === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"password\"');\n        }\n        const apiPath = '/account/phone';\n        const payload = {};\n        if (typeof phone !== 'undefined') {\n            payload['phone'] = phone;\n        }\n        if (typeof password !== 'undefined') {\n            payload['password'] = password;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('patch', uri, apiHeaders, payload);\n    }\n    /**\n     * Get the preferences as a key-value object for the currently logged in user.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Preferences>}\n     */\n    getPrefs() {\n        const apiPath = '/account/prefs';\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n    /**\n     * Update currently logged in user account preferences. The object you pass is stored as is, and replaces any previous value. The maximum allowed prefs size is 64kB and throws error if exceeded.\n     *\n     * @param {Partial<Preferences>} prefs\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.User<Preferences>>}\n     */\n    updatePrefs(prefs) {\n        if (typeof prefs === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"prefs\"');\n        }\n        const apiPath = '/account/prefs';\n        const payload = {};\n        if (typeof prefs !== 'undefined') {\n            payload['prefs'] = prefs;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('patch', uri, apiHeaders, payload);\n    }\n    /**\n     * Sends the user an email with a temporary secret key for password reset. When the user clicks the confirmation link he is redirected back to your app password reset URL with the secret key and email address values attached to the URL query string. Use the query string params to submit a request to the [PUT /account/recovery](https://appwrite.io/docs/references/cloud/client-web/account#updateRecovery) endpoint to complete the process. The verification link sent to the user&#039;s email address is valid for 1 hour.\n     *\n     * @param {string} email\n     * @param {string} url\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Token>}\n     */\n    createRecovery(email, url) {\n        if (typeof email === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"email\"');\n        }\n        if (typeof url === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"url\"');\n        }\n        const apiPath = '/account/recovery';\n        const payload = {};\n        if (typeof email !== 'undefined') {\n            payload['email'] = email;\n        }\n        if (typeof url !== 'undefined') {\n            payload['url'] = url;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('post', uri, apiHeaders, payload);\n    }\n    /**\n     * Use this endpoint to complete the user account password reset. Both the **userId** and **secret** arguments will be passed as query parameters to the redirect URL you have provided when sending your request to the [POST /account/recovery](https://appwrite.io/docs/references/cloud/client-web/account#createRecovery) endpoint.\n     *\n     * Please note that in order to avoid a [Redirect Attack](https://github.com/OWASP/CheatSheetSeries/blob/master/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.md) the only valid redirect URLs are the ones from domains you have set when adding your platforms in the console interface.\n     *\n     * @param {string} userId\n     * @param {string} secret\n     * @param {string} password\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Token>}\n     */\n    updateRecovery(userId, secret, password) {\n        if (typeof userId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"userId\"');\n        }\n        if (typeof secret === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"secret\"');\n        }\n        if (typeof password === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"password\"');\n        }\n        const apiPath = '/account/recovery';\n        const payload = {};\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n        if (typeof secret !== 'undefined') {\n            payload['secret'] = secret;\n        }\n        if (typeof password !== 'undefined') {\n            payload['password'] = password;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('put', uri, apiHeaders, payload);\n    }\n    /**\n     * Get the list of active sessions across different devices for the currently logged in user.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.SessionList>}\n     */\n    listSessions() {\n        const apiPath = '/account/sessions';\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n    /**\n     * Delete all sessions from the user account and remove any sessions cookies from the end client.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    deleteSessions() {\n        const apiPath = '/account/sessions';\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('delete', uri, apiHeaders, payload);\n    }\n    /**\n     * Use this endpoint to allow a new user to register an anonymous account in your project. This route will also create a new session for the user. To allow the new user to convert an anonymous account to a normal account, you need to update its [email and password](https://appwrite.io/docs/references/cloud/client-web/account#updateEmail) or create an [OAuth2 session](https://appwrite.io/docs/references/cloud/client-web/account#CreateOAuth2Session).\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Session>}\n     */\n    createAnonymousSession() {\n        const apiPath = '/account/sessions/anonymous';\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('post', uri, apiHeaders, payload);\n    }\n    /**\n     * Allow the user to login into their account by providing a valid email and password combination. This route will create a new session for the user.\n     *\n     * A user is limited to 10 active sessions at a time by default. [Learn more about session limits](https://appwrite.io/docs/authentication-security#limits).\n     *\n     * @param {string} email\n     * @param {string} password\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Session>}\n     */\n    createEmailPasswordSession(email, password) {\n        if (typeof email === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"email\"');\n        }\n        if (typeof password === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"password\"');\n        }\n        const apiPath = '/account/sessions/email';\n        const payload = {};\n        if (typeof email !== 'undefined') {\n            payload['email'] = email;\n        }\n        if (typeof password !== 'undefined') {\n            payload['password'] = password;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('post', uri, apiHeaders, payload);\n    }\n    /**\n     * Use this endpoint to create a session from token. Provide the **userId** and **secret** parameters from the successful response of authentication flows initiated by token creation. For example, magic URL and phone login.\n     *\n     * @param {string} userId\n     * @param {string} secret\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Session>}\n     */\n    updateMagicURLSession(userId, secret) {\n        if (typeof userId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"userId\"');\n        }\n        if (typeof secret === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"secret\"');\n        }\n        const apiPath = '/account/sessions/magic-url';\n        const payload = {};\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n        if (typeof secret !== 'undefined') {\n            payload['secret'] = secret;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('put', uri, apiHeaders, payload);\n    }\n    /**\n     * Allow the user to login to their account using the OAuth2 provider of their choice. Each OAuth2 provider should be enabled from the Appwrite console first. Use the success and failure arguments to provide a redirect URL&#039;s back to your app when login is completed.\n     *\n     * If there is already an active session, the new session will be attached to the logged-in account. If there are no active sessions, the server will attempt to look for a user with the same email address as the email received from the OAuth2 provider and attach the new session to the existing user. If no matching user is found - the server will create a new user.\n     *\n     * A user is limited to 10 active sessions at a time by default. [Learn more about session limits](https://appwrite.io/docs/authentication-security#limits).\n     *\n     *\n     * @param {OAuthProvider} provider\n     * @param {string} success\n     * @param {string} failure\n     * @param {string[]} scopes\n     * @throws {AppwriteException}\n     * @returns {void | string}\n     */\n    createOAuth2Session(provider, success, failure, scopes) {\n        if (typeof provider === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"provider\"');\n        }\n        const apiPath = '/account/sessions/oauth2/{provider}'.replace('{provider}', provider);\n        const payload = {};\n        if (typeof success !== 'undefined') {\n            payload['success'] = success;\n        }\n        if (typeof failure !== 'undefined') {\n            payload['failure'] = failure;\n        }\n        if (typeof scopes !== 'undefined') {\n            payload['scopes'] = scopes;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        payload['project'] = this.client.config.project;\n        for (const [key, value] of Object.entries(Service.flatten(payload))) {\n            uri.searchParams.append(key, value);\n        }\n        if (typeof window !== 'undefined' && (window === null || window === void 0 ? void 0 : window.location)) {\n            window.location.href = uri.toString();\n            return;\n        }\n        else {\n            return uri.toString();\n        }\n    }\n    /**\n     * Use this endpoint to create a session from token. Provide the **userId** and **secret** parameters from the successful response of authentication flows initiated by token creation. For example, magic URL and phone login.\n     *\n     * @param {string} userId\n     * @param {string} secret\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Session>}\n     */\n    updatePhoneSession(userId, secret) {\n        if (typeof userId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"userId\"');\n        }\n        if (typeof secret === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"secret\"');\n        }\n        const apiPath = '/account/sessions/phone';\n        const payload = {};\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n        if (typeof secret !== 'undefined') {\n            payload['secret'] = secret;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('put', uri, apiHeaders, payload);\n    }\n    /**\n     * Use this endpoint to create a session from token. Provide the **userId** and **secret** parameters from the successful response of authentication flows initiated by token creation. For example, magic URL and phone login.\n     *\n     * @param {string} userId\n     * @param {string} secret\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Session>}\n     */\n    createSession(userId, secret) {\n        if (typeof userId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"userId\"');\n        }\n        if (typeof secret === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"secret\"');\n        }\n        const apiPath = '/account/sessions/token';\n        const payload = {};\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n        if (typeof secret !== 'undefined') {\n            payload['secret'] = secret;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('post', uri, apiHeaders, payload);\n    }\n    /**\n     * Use this endpoint to get a logged in user&#039;s session using a Session ID. Inputting &#039;current&#039; will return the current session being used.\n     *\n     * @param {string} sessionId\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Session>}\n     */\n    getSession(sessionId) {\n        if (typeof sessionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"sessionId\"');\n        }\n        const apiPath = '/account/sessions/{sessionId}'.replace('{sessionId}', sessionId);\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n    /**\n     * Use this endpoint to extend a session&#039;s length. Extending a session is useful when session expiry is short. If the session was created using an OAuth provider, this endpoint refreshes the access token from the provider.\n     *\n     * @param {string} sessionId\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Session>}\n     */\n    updateSession(sessionId) {\n        if (typeof sessionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"sessionId\"');\n        }\n        const apiPath = '/account/sessions/{sessionId}'.replace('{sessionId}', sessionId);\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('patch', uri, apiHeaders, payload);\n    }\n    /**\n     * Logout the user. Use &#039;current&#039; as the session ID to logout on this device, use a session ID to logout on another device. If you&#039;re looking to logout the user on all devices, use [Delete Sessions](https://appwrite.io/docs/references/cloud/client-web/account#deleteSessions) instead.\n     *\n     * @param {string} sessionId\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    deleteSession(sessionId) {\n        if (typeof sessionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"sessionId\"');\n        }\n        const apiPath = '/account/sessions/{sessionId}'.replace('{sessionId}', sessionId);\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('delete', uri, apiHeaders, payload);\n    }\n    /**\n     * Block the currently logged in user account. Behind the scene, the user record is not deleted but permanently blocked from any access. To completely delete a user, use the Users API instead.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.User<Preferences>>}\n     */\n    updateStatus() {\n        const apiPath = '/account/status';\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('patch', uri, apiHeaders, payload);\n    }\n    /**\n     * Use this endpoint to register a device for push notifications. Provide a target ID (custom or generated using ID.unique()), a device identifier (usually a device token), and optionally specify which provider should send notifications to this target. The target is automatically linked to the current session and includes device information like brand and model.\n     *\n     * @param {string} targetId\n     * @param {string} identifier\n     * @param {string} providerId\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Target>}\n     */\n    createPushTarget(targetId, identifier, providerId) {\n        if (typeof targetId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"targetId\"');\n        }\n        if (typeof identifier === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"identifier\"');\n        }\n        const apiPath = '/account/targets/push';\n        const payload = {};\n        if (typeof targetId !== 'undefined') {\n            payload['targetId'] = targetId;\n        }\n        if (typeof identifier !== 'undefined') {\n            payload['identifier'] = identifier;\n        }\n        if (typeof providerId !== 'undefined') {\n            payload['providerId'] = providerId;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('post', uri, apiHeaders, payload);\n    }\n    /**\n     * Update the currently logged in user&#039;s push notification target. You can modify the target&#039;s identifier (device token) and provider ID (token, email, phone etc.). The target must exist and belong to the current user. If you change the provider ID, notifications will be sent through the new messaging provider instead.\n     *\n     * @param {string} targetId\n     * @param {string} identifier\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Target>}\n     */\n    updatePushTarget(targetId, identifier) {\n        if (typeof targetId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"targetId\"');\n        }\n        if (typeof identifier === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"identifier\"');\n        }\n        const apiPath = '/account/targets/{targetId}/push'.replace('{targetId}', targetId);\n        const payload = {};\n        if (typeof identifier !== 'undefined') {\n            payload['identifier'] = identifier;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('put', uri, apiHeaders, payload);\n    }\n    /**\n     * Delete a push notification target for the currently logged in user. After deletion, the device will no longer receive push notifications. The target must exist and belong to the current user.\n     *\n     * @param {string} targetId\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    deletePushTarget(targetId) {\n        if (typeof targetId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"targetId\"');\n        }\n        const apiPath = '/account/targets/{targetId}/push'.replace('{targetId}', targetId);\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('delete', uri, apiHeaders, payload);\n    }\n    /**\n     * Sends the user an email with a secret key for creating a session. If the provided user ID has not be registered, a new user will be created. Use the returned user ID and secret and submit a request to the [POST /v1/account/sessions/token](https://appwrite.io/docs/references/cloud/client-web/account#createSession) endpoint to complete the login process. The secret sent to the user&#039;s email is valid for 15 minutes.\n     *\n     * A user is limited to 10 active sessions at a time by default. [Learn more about session limits](https://appwrite.io/docs/authentication-security#limits).\n     *\n     * @param {string} userId\n     * @param {string} email\n     * @param {boolean} phrase\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Token>}\n     */\n    createEmailToken(userId, email, phrase) {\n        if (typeof userId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"userId\"');\n        }\n        if (typeof email === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"email\"');\n        }\n        const apiPath = '/account/tokens/email';\n        const payload = {};\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n        if (typeof email !== 'undefined') {\n            payload['email'] = email;\n        }\n        if (typeof phrase !== 'undefined') {\n            payload['phrase'] = phrase;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('post', uri, apiHeaders, payload);\n    }\n    /**\n     * Sends the user an email with a secret key for creating a session. If the provided user ID has not been registered, a new user will be created. When the user clicks the link in the email, the user is redirected back to the URL you provided with the secret key and userId values attached to the URL query string. Use the query string parameters to submit a request to the [POST /v1/account/sessions/token](https://appwrite.io/docs/references/cloud/client-web/account#createSession) endpoint to complete the login process. The link sent to the user&#039;s email address is valid for 1 hour.\n     *\n     * A user is limited to 10 active sessions at a time by default. [Learn more about session limits](https://appwrite.io/docs/authentication-security#limits).\n     *\n     *\n     * @param {string} userId\n     * @param {string} email\n     * @param {string} url\n     * @param {boolean} phrase\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Token>}\n     */\n    createMagicURLToken(userId, email, url, phrase) {\n        if (typeof userId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"userId\"');\n        }\n        if (typeof email === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"email\"');\n        }\n        const apiPath = '/account/tokens/magic-url';\n        const payload = {};\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n        if (typeof email !== 'undefined') {\n            payload['email'] = email;\n        }\n        if (typeof url !== 'undefined') {\n            payload['url'] = url;\n        }\n        if (typeof phrase !== 'undefined') {\n            payload['phrase'] = phrase;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('post', uri, apiHeaders, payload);\n    }\n    /**\n     * Allow the user to login to their account using the OAuth2 provider of their choice. Each OAuth2 provider should be enabled from the Appwrite console first. Use the success and failure arguments to provide a redirect URL&#039;s back to your app when login is completed.\n     *\n     * If authentication succeeds, `userId` and `secret` of a token will be appended to the success URL as query parameters. These can be used to create a new session using the [Create session](https://appwrite.io/docs/references/cloud/client-web/account#createSession) endpoint.\n     *\n     * A user is limited to 10 active sessions at a time by default. [Learn more about session limits](https://appwrite.io/docs/authentication-security#limits).\n     *\n     * @param {OAuthProvider} provider\n     * @param {string} success\n     * @param {string} failure\n     * @param {string[]} scopes\n     * @throws {AppwriteException}\n     * @returns {void | string}\n     */\n    createOAuth2Token(provider, success, failure, scopes) {\n        if (typeof provider === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"provider\"');\n        }\n        const apiPath = '/account/tokens/oauth2/{provider}'.replace('{provider}', provider);\n        const payload = {};\n        if (typeof success !== 'undefined') {\n            payload['success'] = success;\n        }\n        if (typeof failure !== 'undefined') {\n            payload['failure'] = failure;\n        }\n        if (typeof scopes !== 'undefined') {\n            payload['scopes'] = scopes;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        payload['project'] = this.client.config.project;\n        for (const [key, value] of Object.entries(Service.flatten(payload))) {\n            uri.searchParams.append(key, value);\n        }\n        if (typeof window !== 'undefined' && (window === null || window === void 0 ? void 0 : window.location)) {\n            window.location.href = uri.toString();\n            return;\n        }\n        else {\n            return uri.toString();\n        }\n    }\n    /**\n     * Sends the user an SMS with a secret key for creating a session. If the provided user ID has not be registered, a new user will be created. Use the returned user ID and secret and submit a request to the [POST /v1/account/sessions/token](https://appwrite.io/docs/references/cloud/client-web/account#createSession) endpoint to complete the login process. The secret sent to the user&#039;s phone is valid for 15 minutes.\n     *\n     * A user is limited to 10 active sessions at a time by default. [Learn more about session limits](https://appwrite.io/docs/authentication-security#limits).\n     *\n     * @param {string} userId\n     * @param {string} phone\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Token>}\n     */\n    createPhoneToken(userId, phone) {\n        if (typeof userId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"userId\"');\n        }\n        if (typeof phone === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"phone\"');\n        }\n        const apiPath = '/account/tokens/phone';\n        const payload = {};\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n        if (typeof phone !== 'undefined') {\n            payload['phone'] = phone;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('post', uri, apiHeaders, payload);\n    }\n    /**\n     * Use this endpoint to send a verification message to your user email address to confirm they are the valid owners of that address. Both the **userId** and **secret** arguments will be passed as query parameters to the URL you have provided to be attached to the verification email. The provided URL should redirect the user back to your app and allow you to complete the verification process by verifying both the **userId** and **secret** parameters. Learn more about how to [complete the verification process](https://appwrite.io/docs/references/cloud/client-web/account#updateVerification). The verification link sent to the user&#039;s email address is valid for 7 days.\n     *\n     * Please note that in order to avoid a [Redirect Attack](https://github.com/OWASP/CheatSheetSeries/blob/master/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.md), the only valid redirect URLs are the ones from domains you have set when adding your platforms in the console interface.\n     *\n     *\n     * @param {string} url\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Token>}\n     */\n    createVerification(url) {\n        if (typeof url === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"url\"');\n        }\n        const apiPath = '/account/verification';\n        const payload = {};\n        if (typeof url !== 'undefined') {\n            payload['url'] = url;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('post', uri, apiHeaders, payload);\n    }\n    /**\n     * Use this endpoint to complete the user email verification process. Use both the **userId** and **secret** parameters that were attached to your app URL to verify the user email ownership. If confirmed this route will return a 200 status code.\n     *\n     * @param {string} userId\n     * @param {string} secret\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Token>}\n     */\n    updateVerification(userId, secret) {\n        if (typeof userId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"userId\"');\n        }\n        if (typeof secret === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"secret\"');\n        }\n        const apiPath = '/account/verification';\n        const payload = {};\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n        if (typeof secret !== 'undefined') {\n            payload['secret'] = secret;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('put', uri, apiHeaders, payload);\n    }\n    /**\n     * Use this endpoint to send a verification SMS to the currently logged in user. This endpoint is meant for use after updating a user&#039;s phone number using the [accountUpdatePhone](https://appwrite.io/docs/references/cloud/client-web/account#updatePhone) endpoint. Learn more about how to [complete the verification process](https://appwrite.io/docs/references/cloud/client-web/account#updatePhoneVerification). The verification code sent to the user&#039;s phone number is valid for 15 minutes.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Token>}\n     */\n    createPhoneVerification() {\n        const apiPath = '/account/verification/phone';\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('post', uri, apiHeaders, payload);\n    }\n    /**\n     * Use this endpoint to complete the user phone verification process. Use the **userId** and **secret** that were sent to your user&#039;s phone number to verify the user email ownership. If confirmed this route will return a 200 status code.\n     *\n     * @param {string} userId\n     * @param {string} secret\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Token>}\n     */\n    updatePhoneVerification(userId, secret) {\n        if (typeof userId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"userId\"');\n        }\n        if (typeof secret === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"secret\"');\n        }\n        const apiPath = '/account/verification/phone';\n        const payload = {};\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n        if (typeof secret !== 'undefined') {\n            payload['secret'] = secret;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('put', uri, apiHeaders, payload);\n    }\n}\n\nclass Avatars {\n    constructor(client) {\n        this.client = client;\n    }\n    /**\n     * You can use this endpoint to show different browser icons to your users. The code argument receives the browser code as it appears in your user [GET /account/sessions](https://appwrite.io/docs/references/cloud/client-web/account#getSessions) endpoint. Use width, height and quality arguments to change the output settings.\n     *\n     * When one dimension is specified and the other is 0, the image is scaled with preserved aspect ratio. If both dimensions are 0, the API provides an image at source quality. If dimensions are not specified, the default size of image returned is 100x100px.\n     *\n     * @param {Browser} code\n     * @param {number} width\n     * @param {number} height\n     * @param {number} quality\n     * @throws {AppwriteException}\n     * @returns {string}\n     */\n    getBrowser(code, width, height, quality) {\n        if (typeof code === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"code\"');\n        }\n        const apiPath = '/avatars/browsers/{code}'.replace('{code}', code);\n        const payload = {};\n        if (typeof width !== 'undefined') {\n            payload['width'] = width;\n        }\n        if (typeof height !== 'undefined') {\n            payload['height'] = height;\n        }\n        if (typeof quality !== 'undefined') {\n            payload['quality'] = quality;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        payload['project'] = this.client.config.project;\n        for (const [key, value] of Object.entries(Service.flatten(payload))) {\n            uri.searchParams.append(key, value);\n        }\n        return uri.toString();\n    }\n    /**\n     * The credit card endpoint will return you the icon of the credit card provider you need. Use width, height and quality arguments to change the output settings.\n     *\n     * When one dimension is specified and the other is 0, the image is scaled with preserved aspect ratio. If both dimensions are 0, the API provides an image at source quality. If dimensions are not specified, the default size of image returned is 100x100px.\n     *\n     *\n     * @param {CreditCard} code\n     * @param {number} width\n     * @param {number} height\n     * @param {number} quality\n     * @throws {AppwriteException}\n     * @returns {string}\n     */\n    getCreditCard(code, width, height, quality) {\n        if (typeof code === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"code\"');\n        }\n        const apiPath = '/avatars/credit-cards/{code}'.replace('{code}', code);\n        const payload = {};\n        if (typeof width !== 'undefined') {\n            payload['width'] = width;\n        }\n        if (typeof height !== 'undefined') {\n            payload['height'] = height;\n        }\n        if (typeof quality !== 'undefined') {\n            payload['quality'] = quality;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        payload['project'] = this.client.config.project;\n        for (const [key, value] of Object.entries(Service.flatten(payload))) {\n            uri.searchParams.append(key, value);\n        }\n        return uri.toString();\n    }\n    /**\n     * Use this endpoint to fetch the favorite icon (AKA favicon) of any remote website URL.\n     *\n     * This endpoint does not follow HTTP redirects.\n     *\n     * @param {string} url\n     * @throws {AppwriteException}\n     * @returns {string}\n     */\n    getFavicon(url) {\n        if (typeof url === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"url\"');\n        }\n        const apiPath = '/avatars/favicon';\n        const payload = {};\n        if (typeof url !== 'undefined') {\n            payload['url'] = url;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        payload['project'] = this.client.config.project;\n        for (const [key, value] of Object.entries(Service.flatten(payload))) {\n            uri.searchParams.append(key, value);\n        }\n        return uri.toString();\n    }\n    /**\n     * You can use this endpoint to show different country flags icons to your users. The code argument receives the 2 letter country code. Use width, height and quality arguments to change the output settings. Country codes follow the [ISO 3166-1](https://en.wikipedia.org/wiki/ISO_3166-1) standard.\n     *\n     * When one dimension is specified and the other is 0, the image is scaled with preserved aspect ratio. If both dimensions are 0, the API provides an image at source quality. If dimensions are not specified, the default size of image returned is 100x100px.\n     *\n     *\n     * @param {Flag} code\n     * @param {number} width\n     * @param {number} height\n     * @param {number} quality\n     * @throws {AppwriteException}\n     * @returns {string}\n     */\n    getFlag(code, width, height, quality) {\n        if (typeof code === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"code\"');\n        }\n        const apiPath = '/avatars/flags/{code}'.replace('{code}', code);\n        const payload = {};\n        if (typeof width !== 'undefined') {\n            payload['width'] = width;\n        }\n        if (typeof height !== 'undefined') {\n            payload['height'] = height;\n        }\n        if (typeof quality !== 'undefined') {\n            payload['quality'] = quality;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        payload['project'] = this.client.config.project;\n        for (const [key, value] of Object.entries(Service.flatten(payload))) {\n            uri.searchParams.append(key, value);\n        }\n        return uri.toString();\n    }\n    /**\n     * Use this endpoint to fetch a remote image URL and crop it to any image size you want. This endpoint is very useful if you need to crop and display remote images in your app or in case you want to make sure a 3rd party image is properly served using a TLS protocol.\n     *\n     * When one dimension is specified and the other is 0, the image is scaled with preserved aspect ratio. If both dimensions are 0, the API provides an image at source quality. If dimensions are not specified, the default size of image returned is 400x400px.\n     *\n     * This endpoint does not follow HTTP redirects.\n     *\n     * @param {string} url\n     * @param {number} width\n     * @param {number} height\n     * @throws {AppwriteException}\n     * @returns {string}\n     */\n    getImage(url, width, height) {\n        if (typeof url === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"url\"');\n        }\n        const apiPath = '/avatars/image';\n        const payload = {};\n        if (typeof url !== 'undefined') {\n            payload['url'] = url;\n        }\n        if (typeof width !== 'undefined') {\n            payload['width'] = width;\n        }\n        if (typeof height !== 'undefined') {\n            payload['height'] = height;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        payload['project'] = this.client.config.project;\n        for (const [key, value] of Object.entries(Service.flatten(payload))) {\n            uri.searchParams.append(key, value);\n        }\n        return uri.toString();\n    }\n    /**\n     * Use this endpoint to show your user initials avatar icon on your website or app. By default, this route will try to print your logged-in user name or email initials. You can also overwrite the user name if you pass the &#039;name&#039; parameter. If no name is given and no user is logged, an empty avatar will be returned.\n     *\n     * You can use the color and background params to change the avatar colors. By default, a random theme will be selected. The random theme will persist for the user&#039;s initials when reloading the same theme will always return for the same initials.\n     *\n     * When one dimension is specified and the other is 0, the image is scaled with preserved aspect ratio. If both dimensions are 0, the API provides an image at source quality. If dimensions are not specified, the default size of image returned is 100x100px.\n     *\n     *\n     * @param {string} name\n     * @param {number} width\n     * @param {number} height\n     * @param {string} background\n     * @throws {AppwriteException}\n     * @returns {string}\n     */\n    getInitials(name, width, height, background) {\n        const apiPath = '/avatars/initials';\n        const payload = {};\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n        if (typeof width !== 'undefined') {\n            payload['width'] = width;\n        }\n        if (typeof height !== 'undefined') {\n            payload['height'] = height;\n        }\n        if (typeof background !== 'undefined') {\n            payload['background'] = background;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        payload['project'] = this.client.config.project;\n        for (const [key, value] of Object.entries(Service.flatten(payload))) {\n            uri.searchParams.append(key, value);\n        }\n        return uri.toString();\n    }\n    /**\n     * Converts a given plain text to a QR code image. You can use the query parameters to change the size and style of the resulting image.\n     *\n     *\n     * @param {string} text\n     * @param {number} size\n     * @param {number} margin\n     * @param {boolean} download\n     * @throws {AppwriteException}\n     * @returns {string}\n     */\n    getQR(text, size, margin, download) {\n        if (typeof text === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"text\"');\n        }\n        const apiPath = '/avatars/qr';\n        const payload = {};\n        if (typeof text !== 'undefined') {\n            payload['text'] = text;\n        }\n        if (typeof size !== 'undefined') {\n            payload['size'] = size;\n        }\n        if (typeof margin !== 'undefined') {\n            payload['margin'] = margin;\n        }\n        if (typeof download !== 'undefined') {\n            payload['download'] = download;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        payload['project'] = this.client.config.project;\n        for (const [key, value] of Object.entries(Service.flatten(payload))) {\n            uri.searchParams.append(key, value);\n        }\n        return uri.toString();\n    }\n}\n\nclass Databases {\n    constructor(client) {\n        this.client = client;\n    }\n    /**\n     * Get a list of all the user&#039;s documents in a given collection. You can use the query params to filter your results.\n     *\n     * @param {string} databaseId\n     * @param {string} collectionId\n     * @param {string[]} queries\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.DocumentList<Document>>}\n     */\n    listDocuments(databaseId, collectionId, queries) {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n        if (typeof collectionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"collectionId\"');\n        }\n        const apiPath = '/databases/{databaseId}/collections/{collectionId}/documents'.replace('{databaseId}', databaseId).replace('{collectionId}', collectionId);\n        const payload = {};\n        if (typeof queries !== 'undefined') {\n            payload['queries'] = queries;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n    /**\n     * Create a new Document. Before using this route, you should create a new collection resource using either a [server integration](https://appwrite.io/docs/server/databases#databasesCreateCollection) API or directly from your database console.\n     *\n     * @param {string} databaseId\n     * @param {string} collectionId\n     * @param {string} documentId\n     * @param {Document extends Models.DefaultDocument ? Models.DataWithoutDocumentKeys : Omit<Document, keyof Models.Document>} data\n     * @param {string[]} permissions\n     * @throws {AppwriteException}\n     * @returns {Promise<Document>}\n     */\n    createDocument(databaseId, collectionId, documentId, data, permissions) {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n        if (typeof collectionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"collectionId\"');\n        }\n        if (typeof documentId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"documentId\"');\n        }\n        if (typeof data === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"data\"');\n        }\n        const apiPath = '/databases/{databaseId}/collections/{collectionId}/documents'.replace('{databaseId}', databaseId).replace('{collectionId}', collectionId);\n        const payload = {};\n        if (typeof documentId !== 'undefined') {\n            payload['documentId'] = documentId;\n        }\n        if (typeof data !== 'undefined') {\n            payload['data'] = data;\n        }\n        if (typeof permissions !== 'undefined') {\n            payload['permissions'] = permissions;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('post', uri, apiHeaders, payload);\n    }\n    /**\n     * Get a document by its unique ID. This endpoint response returns a JSON object with the document data.\n     *\n     * @param {string} databaseId\n     * @param {string} collectionId\n     * @param {string} documentId\n     * @param {string[]} queries\n     * @throws {AppwriteException}\n     * @returns {Promise<Document>}\n     */\n    getDocument(databaseId, collectionId, documentId, queries) {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n        if (typeof collectionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"collectionId\"');\n        }\n        if (typeof documentId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"documentId\"');\n        }\n        const apiPath = '/databases/{databaseId}/collections/{collectionId}/documents/{documentId}'.replace('{databaseId}', databaseId).replace('{collectionId}', collectionId).replace('{documentId}', documentId);\n        const payload = {};\n        if (typeof queries !== 'undefined') {\n            payload['queries'] = queries;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n    /**\n     * **WARNING: Experimental Feature** - This endpoint is experimental and not yet officially supported. It may be subject to breaking changes or removal in future versions.\n     *\n     * Create or update a Document. Before using this route, you should create a new collection resource using either a [server integration](https://appwrite.io/docs/server/databases#databasesCreateCollection) API or directly from your database console.\n     *\n     * @param {string} databaseId\n     * @param {string} collectionId\n     * @param {string} documentId\n     * @param {object} data\n     * @param {string[]} permissions\n     * @throws {AppwriteException}\n     * @returns {Promise<Document>}\n     */\n    upsertDocument(databaseId, collectionId, documentId, data, permissions) {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n        if (typeof collectionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"collectionId\"');\n        }\n        if (typeof documentId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"documentId\"');\n        }\n        if (typeof data === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"data\"');\n        }\n        const apiPath = '/databases/{databaseId}/collections/{collectionId}/documents/{documentId}'.replace('{databaseId}', databaseId).replace('{collectionId}', collectionId).replace('{documentId}', documentId);\n        const payload = {};\n        if (typeof data !== 'undefined') {\n            payload['data'] = data;\n        }\n        if (typeof permissions !== 'undefined') {\n            payload['permissions'] = permissions;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('put', uri, apiHeaders, payload);\n    }\n    /**\n     * Update a document by its unique ID. Using the patch method you can pass only specific fields that will get updated.\n     *\n     * @param {string} databaseId\n     * @param {string} collectionId\n     * @param {string} documentId\n     * @param {Partial<Document extends Models.DefaultDocument ? Models.DataWithoutDocumentKeys : Omit<Document, keyof Models.Document>>} data\n     * @param {string[]} permissions\n     * @throws {AppwriteException}\n     * @returns {Promise<Document>}\n     */\n    updateDocument(databaseId, collectionId, documentId, data, permissions) {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n        if (typeof collectionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"collectionId\"');\n        }\n        if (typeof documentId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"documentId\"');\n        }\n        const apiPath = '/databases/{databaseId}/collections/{collectionId}/documents/{documentId}'.replace('{databaseId}', databaseId).replace('{collectionId}', collectionId).replace('{documentId}', documentId);\n        const payload = {};\n        if (typeof data !== 'undefined') {\n            payload['data'] = data;\n        }\n        if (typeof permissions !== 'undefined') {\n            payload['permissions'] = permissions;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('patch', uri, apiHeaders, payload);\n    }\n    /**\n     * Delete a document by its unique ID.\n     *\n     * @param {string} databaseId\n     * @param {string} collectionId\n     * @param {string} documentId\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    deleteDocument(databaseId, collectionId, documentId) {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n        if (typeof collectionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"collectionId\"');\n        }\n        if (typeof documentId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"documentId\"');\n        }\n        const apiPath = '/databases/{databaseId}/collections/{collectionId}/documents/{documentId}'.replace('{databaseId}', databaseId).replace('{collectionId}', collectionId).replace('{documentId}', documentId);\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('delete', uri, apiHeaders, payload);\n    }\n    /**\n     * Decrement a specific attribute of a document by a given value.\n     *\n     * @param {string} databaseId\n     * @param {string} collectionId\n     * @param {string} documentId\n     * @param {string} attribute\n     * @param {number} value\n     * @param {number} min\n     * @throws {AppwriteException}\n     * @returns {Promise<Document>}\n     */\n    decrementDocumentAttribute(databaseId, collectionId, documentId, attribute, value, min) {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n        if (typeof collectionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"collectionId\"');\n        }\n        if (typeof documentId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"documentId\"');\n        }\n        if (typeof attribute === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"attribute\"');\n        }\n        const apiPath = '/databases/{databaseId}/collections/{collectionId}/documents/{documentId}/{attribute}/decrement'.replace('{databaseId}', databaseId).replace('{collectionId}', collectionId).replace('{documentId}', documentId).replace('{attribute}', attribute);\n        const payload = {};\n        if (typeof value !== 'undefined') {\n            payload['value'] = value;\n        }\n        if (typeof min !== 'undefined') {\n            payload['min'] = min;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('patch', uri, apiHeaders, payload);\n    }\n    /**\n     * Increment a specific attribute of a document by a given value.\n     *\n     * @param {string} databaseId\n     * @param {string} collectionId\n     * @param {string} documentId\n     * @param {string} attribute\n     * @param {number} value\n     * @param {number} max\n     * @throws {AppwriteException}\n     * @returns {Promise<Document>}\n     */\n    incrementDocumentAttribute(databaseId, collectionId, documentId, attribute, value, max) {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n        if (typeof collectionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"collectionId\"');\n        }\n        if (typeof documentId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"documentId\"');\n        }\n        if (typeof attribute === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"attribute\"');\n        }\n        const apiPath = '/databases/{databaseId}/collections/{collectionId}/documents/{documentId}/{attribute}/increment'.replace('{databaseId}', databaseId).replace('{collectionId}', collectionId).replace('{documentId}', documentId).replace('{attribute}', attribute);\n        const payload = {};\n        if (typeof value !== 'undefined') {\n            payload['value'] = value;\n        }\n        if (typeof max !== 'undefined') {\n            payload['max'] = max;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('patch', uri, apiHeaders, payload);\n    }\n}\n\nclass Functions {\n    constructor(client) {\n        this.client = client;\n    }\n    /**\n     * Get a list of all the current user function execution logs. You can use the query params to filter your results.\n     *\n     * @param {string} functionId\n     * @param {string[]} queries\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.ExecutionList>}\n     */\n    listExecutions(functionId, queries) {\n        if (typeof functionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"functionId\"');\n        }\n        const apiPath = '/functions/{functionId}/executions'.replace('{functionId}', functionId);\n        const payload = {};\n        if (typeof queries !== 'undefined') {\n            payload['queries'] = queries;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n    /**\n     * Trigger a function execution. The returned object will return you the current execution status. You can ping the `Get Execution` endpoint to get updates on the current execution status. Once this endpoint is called, your function execution process will start asynchronously.\n     *\n     * @param {string} functionId\n     * @param {string} body\n     * @param {boolean} async\n     * @param {string} xpath\n     * @param {ExecutionMethod} method\n     * @param {object} headers\n     * @param {string} scheduledAt\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Execution>}\n     */\n    createExecution(functionId, body, async, xpath, method, headers, scheduledAt) {\n        if (typeof functionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"functionId\"');\n        }\n        const apiPath = '/functions/{functionId}/executions'.replace('{functionId}', functionId);\n        const payload = {};\n        if (typeof body !== 'undefined') {\n            payload['body'] = body;\n        }\n        if (typeof async !== 'undefined') {\n            payload['async'] = async;\n        }\n        if (typeof xpath !== 'undefined') {\n            payload['path'] = xpath;\n        }\n        if (typeof method !== 'undefined') {\n            payload['method'] = method;\n        }\n        if (typeof headers !== 'undefined') {\n            payload['headers'] = headers;\n        }\n        if (typeof scheduledAt !== 'undefined') {\n            payload['scheduledAt'] = scheduledAt;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('post', uri, apiHeaders, payload);\n    }\n    /**\n     * Get a function execution log by its unique ID.\n     *\n     * @param {string} functionId\n     * @param {string} executionId\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Execution>}\n     */\n    getExecution(functionId, executionId) {\n        if (typeof functionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"functionId\"');\n        }\n        if (typeof executionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"executionId\"');\n        }\n        const apiPath = '/functions/{functionId}/executions/{executionId}'.replace('{functionId}', functionId).replace('{executionId}', executionId);\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n}\n\nclass Graphql {\n    constructor(client) {\n        this.client = client;\n    }\n    /**\n     * Execute a GraphQL mutation.\n     *\n     * @param {object} query\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    query(query) {\n        if (typeof query === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"query\"');\n        }\n        const apiPath = '/graphql';\n        const payload = {};\n        if (typeof query !== 'undefined') {\n            payload['query'] = query;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'x-sdk-graphql': 'true',\n            'content-type': 'application/json',\n        };\n        return this.client.call('post', uri, apiHeaders, payload);\n    }\n    /**\n     * Execute a GraphQL mutation.\n     *\n     * @param {object} query\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    mutation(query) {\n        if (typeof query === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"query\"');\n        }\n        const apiPath = '/graphql/mutation';\n        const payload = {};\n        if (typeof query !== 'undefined') {\n            payload['query'] = query;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'x-sdk-graphql': 'true',\n            'content-type': 'application/json',\n        };\n        return this.client.call('post', uri, apiHeaders, payload);\n    }\n}\n\nclass Locale {\n    constructor(client) {\n        this.client = client;\n    }\n    /**\n     * Get the current user location based on IP. Returns an object with user country code, country name, continent name, continent code, ip address and suggested currency. You can use the locale header to get the data in a supported language.\n     *\n     * ([IP Geolocation by DB-IP](https://db-ip.com))\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Locale>}\n     */\n    get() {\n        const apiPath = '/locale';\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n    /**\n     * List of all locale codes in [ISO 639-1](https://en.wikipedia.org/wiki/List_of_ISO_639-1_codes).\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.LocaleCodeList>}\n     */\n    listCodes() {\n        const apiPath = '/locale/codes';\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n    /**\n     * List of all continents. You can use the locale header to get the data in a supported language.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.ContinentList>}\n     */\n    listContinents() {\n        const apiPath = '/locale/continents';\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n    /**\n     * List of all countries. You can use the locale header to get the data in a supported language.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.CountryList>}\n     */\n    listCountries() {\n        const apiPath = '/locale/countries';\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n    /**\n     * List of all countries that are currently members of the EU. You can use the locale header to get the data in a supported language.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.CountryList>}\n     */\n    listCountriesEU() {\n        const apiPath = '/locale/countries/eu';\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n    /**\n     * List of all countries phone codes. You can use the locale header to get the data in a supported language.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.PhoneList>}\n     */\n    listCountriesPhones() {\n        const apiPath = '/locale/countries/phones';\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n    /**\n     * List of all currencies, including currency symbol, name, plural, and decimal digits for all major and minor currencies. You can use the locale header to get the data in a supported language.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.CurrencyList>}\n     */\n    listCurrencies() {\n        const apiPath = '/locale/currencies';\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n    /**\n     * List of all languages classified by ISO 639-1 including 2-letter code, name in English, and name in the respective language.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.LanguageList>}\n     */\n    listLanguages() {\n        const apiPath = '/locale/languages';\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n}\n\nclass Messaging {\n    constructor(client) {\n        this.client = client;\n    }\n    /**\n     * Create a new subscriber.\n     *\n     * @param {string} topicId\n     * @param {string} subscriberId\n     * @param {string} targetId\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Subscriber>}\n     */\n    createSubscriber(topicId, subscriberId, targetId) {\n        if (typeof topicId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"topicId\"');\n        }\n        if (typeof subscriberId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"subscriberId\"');\n        }\n        if (typeof targetId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"targetId\"');\n        }\n        const apiPath = '/messaging/topics/{topicId}/subscribers'.replace('{topicId}', topicId);\n        const payload = {};\n        if (typeof subscriberId !== 'undefined') {\n            payload['subscriberId'] = subscriberId;\n        }\n        if (typeof targetId !== 'undefined') {\n            payload['targetId'] = targetId;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('post', uri, apiHeaders, payload);\n    }\n    /**\n     * Delete a subscriber by its unique ID.\n     *\n     * @param {string} topicId\n     * @param {string} subscriberId\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    deleteSubscriber(topicId, subscriberId) {\n        if (typeof topicId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"topicId\"');\n        }\n        if (typeof subscriberId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"subscriberId\"');\n        }\n        const apiPath = '/messaging/topics/{topicId}/subscribers/{subscriberId}'.replace('{topicId}', topicId).replace('{subscriberId}', subscriberId);\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('delete', uri, apiHeaders, payload);\n    }\n}\n\nclass Storage {\n    constructor(client) {\n        this.client = client;\n    }\n    /**\n     * Get a list of all the user files. You can use the query params to filter your results.\n     *\n     * @param {string} bucketId\n     * @param {string[]} queries\n     * @param {string} search\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.FileList>}\n     */\n    listFiles(bucketId, queries, search) {\n        if (typeof bucketId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"bucketId\"');\n        }\n        const apiPath = '/storage/buckets/{bucketId}/files'.replace('{bucketId}', bucketId);\n        const payload = {};\n        if (typeof queries !== 'undefined') {\n            payload['queries'] = queries;\n        }\n        if (typeof search !== 'undefined') {\n            payload['search'] = search;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n    /**\n     * Create a new file. Before using this route, you should create a new bucket resource using either a [server integration](https://appwrite.io/docs/server/storage#storageCreateBucket) API or directly from your Appwrite console.\n     *\n     * Larger files should be uploaded using multiple requests with the [content-range](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Content-Range) header to send a partial request with a maximum supported chunk of `5MB`. The `content-range` header values should always be in bytes.\n     *\n     * When the first request is sent, the server will return the **File** object, and the subsequent part request must include the file&#039;s **id** in `x-appwrite-id` header to allow the server to know that the partial upload is for the existing file and not for a new one.\n     *\n     * If you&#039;re creating a new file using one of the Appwrite SDKs, all the chunking logic will be managed by the SDK internally.\n     *\n     *\n     * @param {string} bucketId\n     * @param {string} fileId\n     * @param {File} file\n     * @param {string[]} permissions\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.File>}\n     */\n    createFile(bucketId, fileId, file, permissions, onProgress = (progress) => { }) {\n        if (typeof bucketId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"bucketId\"');\n        }\n        if (typeof fileId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"fileId\"');\n        }\n        if (typeof file === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"file\"');\n        }\n        const apiPath = '/storage/buckets/{bucketId}/files'.replace('{bucketId}', bucketId);\n        const payload = {};\n        if (typeof fileId !== 'undefined') {\n            payload['fileId'] = fileId;\n        }\n        if (typeof file !== 'undefined') {\n            payload['file'] = file;\n        }\n        if (typeof permissions !== 'undefined') {\n            payload['permissions'] = permissions;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'multipart/form-data',\n        };\n        return this.client.chunkedUpload('post', uri, apiHeaders, payload, onProgress);\n    }\n    /**\n     * Get a file by its unique ID. This endpoint response returns a JSON object with the file metadata.\n     *\n     * @param {string} bucketId\n     * @param {string} fileId\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.File>}\n     */\n    getFile(bucketId, fileId) {\n        if (typeof bucketId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"bucketId\"');\n        }\n        if (typeof fileId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"fileId\"');\n        }\n        const apiPath = '/storage/buckets/{bucketId}/files/{fileId}'.replace('{bucketId}', bucketId).replace('{fileId}', fileId);\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n    /**\n     * Update a file by its unique ID. Only users with write permissions have access to update this resource.\n     *\n     * @param {string} bucketId\n     * @param {string} fileId\n     * @param {string} name\n     * @param {string[]} permissions\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.File>}\n     */\n    updateFile(bucketId, fileId, name, permissions) {\n        if (typeof bucketId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"bucketId\"');\n        }\n        if (typeof fileId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"fileId\"');\n        }\n        const apiPath = '/storage/buckets/{bucketId}/files/{fileId}'.replace('{bucketId}', bucketId).replace('{fileId}', fileId);\n        const payload = {};\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n        if (typeof permissions !== 'undefined') {\n            payload['permissions'] = permissions;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('put', uri, apiHeaders, payload);\n    }\n    /**\n     * Delete a file by its unique ID. Only users with write permissions have access to delete this resource.\n     *\n     * @param {string} bucketId\n     * @param {string} fileId\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    deleteFile(bucketId, fileId) {\n        if (typeof bucketId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"bucketId\"');\n        }\n        if (typeof fileId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"fileId\"');\n        }\n        const apiPath = '/storage/buckets/{bucketId}/files/{fileId}'.replace('{bucketId}', bucketId).replace('{fileId}', fileId);\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('delete', uri, apiHeaders, payload);\n    }\n    /**\n     * Get a file content by its unique ID. The endpoint response return with a &#039;Content-Disposition: attachment&#039; header that tells the browser to start downloading the file to user downloads directory.\n     *\n     * @param {string} bucketId\n     * @param {string} fileId\n     * @param {string} token\n     * @throws {AppwriteException}\n     * @returns {string}\n     */\n    getFileDownload(bucketId, fileId, token) {\n        if (typeof bucketId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"bucketId\"');\n        }\n        if (typeof fileId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"fileId\"');\n        }\n        const apiPath = '/storage/buckets/{bucketId}/files/{fileId}/download'.replace('{bucketId}', bucketId).replace('{fileId}', fileId);\n        const payload = {};\n        if (typeof token !== 'undefined') {\n            payload['token'] = token;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        payload['project'] = this.client.config.project;\n        for (const [key, value] of Object.entries(Service.flatten(payload))) {\n            uri.searchParams.append(key, value);\n        }\n        return uri.toString();\n    }\n    /**\n     * Get a file preview image. Currently, this method supports preview for image files (jpg, png, and gif), other supported formats, like pdf, docs, slides, and spreadsheets, will return the file icon image. You can also pass query string arguments for cutting and resizing your preview image. Preview is supported only for image files smaller than 10MB.\n     *\n     * @param {string} bucketId\n     * @param {string} fileId\n     * @param {number} width\n     * @param {number} height\n     * @param {ImageGravity} gravity\n     * @param {number} quality\n     * @param {number} borderWidth\n     * @param {string} borderColor\n     * @param {number} borderRadius\n     * @param {number} opacity\n     * @param {number} rotation\n     * @param {string} background\n     * @param {ImageFormat} output\n     * @param {string} token\n     * @throws {AppwriteException}\n     * @returns {string}\n     */\n    getFilePreview(bucketId, fileId, width, height, gravity, quality, borderWidth, borderColor, borderRadius, opacity, rotation, background, output, token) {\n        if (typeof bucketId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"bucketId\"');\n        }\n        if (typeof fileId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"fileId\"');\n        }\n        const apiPath = '/storage/buckets/{bucketId}/files/{fileId}/preview'.replace('{bucketId}', bucketId).replace('{fileId}', fileId);\n        const payload = {};\n        if (typeof width !== 'undefined') {\n            payload['width'] = width;\n        }\n        if (typeof height !== 'undefined') {\n            payload['height'] = height;\n        }\n        if (typeof gravity !== 'undefined') {\n            payload['gravity'] = gravity;\n        }\n        if (typeof quality !== 'undefined') {\n            payload['quality'] = quality;\n        }\n        if (typeof borderWidth !== 'undefined') {\n            payload['borderWidth'] = borderWidth;\n        }\n        if (typeof borderColor !== 'undefined') {\n            payload['borderColor'] = borderColor;\n        }\n        if (typeof borderRadius !== 'undefined') {\n            payload['borderRadius'] = borderRadius;\n        }\n        if (typeof opacity !== 'undefined') {\n            payload['opacity'] = opacity;\n        }\n        if (typeof rotation !== 'undefined') {\n            payload['rotation'] = rotation;\n        }\n        if (typeof background !== 'undefined') {\n            payload['background'] = background;\n        }\n        if (typeof output !== 'undefined') {\n            payload['output'] = output;\n        }\n        if (typeof token !== 'undefined') {\n            payload['token'] = token;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        payload['project'] = this.client.config.project;\n        for (const [key, value] of Object.entries(Service.flatten(payload))) {\n            uri.searchParams.append(key, value);\n        }\n        return uri.toString();\n    }\n    /**\n     * Get a file content by its unique ID. This endpoint is similar to the download method but returns with no  &#039;Content-Disposition: attachment&#039; header.\n     *\n     * @param {string} bucketId\n     * @param {string} fileId\n     * @param {string} token\n     * @throws {AppwriteException}\n     * @returns {string}\n     */\n    getFileView(bucketId, fileId, token) {\n        if (typeof bucketId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"bucketId\"');\n        }\n        if (typeof fileId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"fileId\"');\n        }\n        const apiPath = '/storage/buckets/{bucketId}/files/{fileId}/view'.replace('{bucketId}', bucketId).replace('{fileId}', fileId);\n        const payload = {};\n        if (typeof token !== 'undefined') {\n            payload['token'] = token;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        payload['project'] = this.client.config.project;\n        for (const [key, value] of Object.entries(Service.flatten(payload))) {\n            uri.searchParams.append(key, value);\n        }\n        return uri.toString();\n    }\n}\n\nclass Teams {\n    constructor(client) {\n        this.client = client;\n    }\n    /**\n     * Get a list of all the teams in which the current user is a member. You can use the parameters to filter your results.\n     *\n     * @param {string[]} queries\n     * @param {string} search\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.TeamList<Preferences>>}\n     */\n    list(queries, search) {\n        const apiPath = '/teams';\n        const payload = {};\n        if (typeof queries !== 'undefined') {\n            payload['queries'] = queries;\n        }\n        if (typeof search !== 'undefined') {\n            payload['search'] = search;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n    /**\n     * Create a new team. The user who creates the team will automatically be assigned as the owner of the team. Only the users with the owner role can invite new members, add new owners and delete or update the team.\n     *\n     * @param {string} teamId\n     * @param {string} name\n     * @param {string[]} roles\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Team<Preferences>>}\n     */\n    create(teamId, name, roles) {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n        if (typeof name === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"name\"');\n        }\n        const apiPath = '/teams';\n        const payload = {};\n        if (typeof teamId !== 'undefined') {\n            payload['teamId'] = teamId;\n        }\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n        if (typeof roles !== 'undefined') {\n            payload['roles'] = roles;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('post', uri, apiHeaders, payload);\n    }\n    /**\n     * Get a team by its ID. All team members have read access for this resource.\n     *\n     * @param {string} teamId\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Team<Preferences>>}\n     */\n    get(teamId) {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n        const apiPath = '/teams/{teamId}'.replace('{teamId}', teamId);\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n    /**\n     * Update the team&#039;s name by its unique ID.\n     *\n     * @param {string} teamId\n     * @param {string} name\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Team<Preferences>>}\n     */\n    updateName(teamId, name) {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n        if (typeof name === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"name\"');\n        }\n        const apiPath = '/teams/{teamId}'.replace('{teamId}', teamId);\n        const payload = {};\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('put', uri, apiHeaders, payload);\n    }\n    /**\n     * Delete a team using its ID. Only team members with the owner role can delete the team.\n     *\n     * @param {string} teamId\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    delete(teamId) {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n        const apiPath = '/teams/{teamId}'.replace('{teamId}', teamId);\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('delete', uri, apiHeaders, payload);\n    }\n    /**\n     * Use this endpoint to list a team&#039;s members using the team&#039;s ID. All team members have read access to this endpoint. Hide sensitive attributes from the response by toggling membership privacy in the Console.\n     *\n     * @param {string} teamId\n     * @param {string[]} queries\n     * @param {string} search\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.MembershipList>}\n     */\n    listMemberships(teamId, queries, search) {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n        const apiPath = '/teams/{teamId}/memberships'.replace('{teamId}', teamId);\n        const payload = {};\n        if (typeof queries !== 'undefined') {\n            payload['queries'] = queries;\n        }\n        if (typeof search !== 'undefined') {\n            payload['search'] = search;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n    /**\n     * Invite a new member to join your team. Provide an ID for existing users, or invite unregistered users using an email or phone number. If initiated from a Client SDK, Appwrite will send an email or sms with a link to join the team to the invited user, and an account will be created for them if one doesn&#039;t exist. If initiated from a Server SDK, the new member will be added automatically to the team.\n     *\n     * You only need to provide one of a user ID, email, or phone number. Appwrite will prioritize accepting the user ID &gt; email &gt; phone number if you provide more than one of these parameters.\n     *\n     * Use the `url` parameter to redirect the user from the invitation email to your app. After the user is redirected, use the [Update Team Membership Status](https://appwrite.io/docs/references/cloud/client-web/teams#updateMembershipStatus) endpoint to allow the user to accept the invitation to the team.\n     *\n     * Please note that to avoid a [Redirect Attack](https://github.com/OWASP/CheatSheetSeries/blob/master/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.md) Appwrite will accept the only redirect URLs under the domains you have added as a platform on the Appwrite Console.\n     *\n     *\n     * @param {string} teamId\n     * @param {string[]} roles\n     * @param {string} email\n     * @param {string} userId\n     * @param {string} phone\n     * @param {string} url\n     * @param {string} name\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Membership>}\n     */\n    createMembership(teamId, roles, email, userId, phone, url, name) {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n        if (typeof roles === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"roles\"');\n        }\n        const apiPath = '/teams/{teamId}/memberships'.replace('{teamId}', teamId);\n        const payload = {};\n        if (typeof email !== 'undefined') {\n            payload['email'] = email;\n        }\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n        if (typeof phone !== 'undefined') {\n            payload['phone'] = phone;\n        }\n        if (typeof roles !== 'undefined') {\n            payload['roles'] = roles;\n        }\n        if (typeof url !== 'undefined') {\n            payload['url'] = url;\n        }\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('post', uri, apiHeaders, payload);\n    }\n    /**\n     * Get a team member by the membership unique id. All team members have read access for this resource. Hide sensitive attributes from the response by toggling membership privacy in the Console.\n     *\n     * @param {string} teamId\n     * @param {string} membershipId\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Membership>}\n     */\n    getMembership(teamId, membershipId) {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n        if (typeof membershipId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"membershipId\"');\n        }\n        const apiPath = '/teams/{teamId}/memberships/{membershipId}'.replace('{teamId}', teamId).replace('{membershipId}', membershipId);\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n    /**\n     * Modify the roles of a team member. Only team members with the owner role have access to this endpoint. Learn more about [roles and permissions](https://appwrite.io/docs/permissions).\n     *\n     *\n     * @param {string} teamId\n     * @param {string} membershipId\n     * @param {string[]} roles\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Membership>}\n     */\n    updateMembership(teamId, membershipId, roles) {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n        if (typeof membershipId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"membershipId\"');\n        }\n        if (typeof roles === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"roles\"');\n        }\n        const apiPath = '/teams/{teamId}/memberships/{membershipId}'.replace('{teamId}', teamId).replace('{membershipId}', membershipId);\n        const payload = {};\n        if (typeof roles !== 'undefined') {\n            payload['roles'] = roles;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('patch', uri, apiHeaders, payload);\n    }\n    /**\n     * This endpoint allows a user to leave a team or for a team owner to delete the membership of any other team member. You can also use this endpoint to delete a user membership even if it is not accepted.\n     *\n     * @param {string} teamId\n     * @param {string} membershipId\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    deleteMembership(teamId, membershipId) {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n        if (typeof membershipId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"membershipId\"');\n        }\n        const apiPath = '/teams/{teamId}/memberships/{membershipId}'.replace('{teamId}', teamId).replace('{membershipId}', membershipId);\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('delete', uri, apiHeaders, payload);\n    }\n    /**\n     * Use this endpoint to allow a user to accept an invitation to join a team after being redirected back to your app from the invitation email received by the user.\n     *\n     * If the request is successful, a session for the user is automatically created.\n     *\n     *\n     * @param {string} teamId\n     * @param {string} membershipId\n     * @param {string} userId\n     * @param {string} secret\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Membership>}\n     */\n    updateMembershipStatus(teamId, membershipId, userId, secret) {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n        if (typeof membershipId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"membershipId\"');\n        }\n        if (typeof userId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"userId\"');\n        }\n        if (typeof secret === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"secret\"');\n        }\n        const apiPath = '/teams/{teamId}/memberships/{membershipId}/status'.replace('{teamId}', teamId).replace('{membershipId}', membershipId);\n        const payload = {};\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n        if (typeof secret !== 'undefined') {\n            payload['secret'] = secret;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('patch', uri, apiHeaders, payload);\n    }\n    /**\n     * Get the team&#039;s shared preferences by its unique ID. If a preference doesn&#039;t need to be shared by all team members, prefer storing them in [user preferences](https://appwrite.io/docs/references/cloud/client-web/account#getPrefs).\n     *\n     * @param {string} teamId\n     * @throws {AppwriteException}\n     * @returns {Promise<Preferences>}\n     */\n    getPrefs(teamId) {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n        const apiPath = '/teams/{teamId}/prefs'.replace('{teamId}', teamId);\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n    /**\n     * Update the team&#039;s preferences by its unique ID. The object you pass is stored as is and replaces any previous value. The maximum allowed prefs size is 64kB and throws an error if exceeded.\n     *\n     * @param {string} teamId\n     * @param {object} prefs\n     * @throws {AppwriteException}\n     * @returns {Promise<Preferences>}\n     */\n    updatePrefs(teamId, prefs) {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n        if (typeof prefs === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"prefs\"');\n        }\n        const apiPath = '/teams/{teamId}/prefs'.replace('{teamId}', teamId);\n        const payload = {};\n        if (typeof prefs !== 'undefined') {\n            payload['prefs'] = prefs;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('put', uri, apiHeaders, payload);\n    }\n}\n\n/**\n * Helper class to generate permission strings for resources.\n */\nclass Permission {\n}\n/**\n * Generate read permission string for the provided role.\n *\n * @param {string} role\n * @returns {string}\n */\nPermission.read = (role) => {\n    return `read(\"${role}\")`;\n};\n/**\n * Generate write permission string for the provided role.\n *\n * This is an alias of update, delete, and possibly create.\n * Don't use write in combination with update, delete, or create.\n *\n * @param {string} role\n * @returns {string}\n */\nPermission.write = (role) => {\n    return `write(\"${role}\")`;\n};\n/**\n * Generate create permission string for the provided role.\n *\n * @param {string} role\n * @returns {string}\n */\nPermission.create = (role) => {\n    return `create(\"${role}\")`;\n};\n/**\n * Generate update permission string for the provided role.\n *\n * @param {string} role\n * @returns {string}\n */\nPermission.update = (role) => {\n    return `update(\"${role}\")`;\n};\n/**\n * Generate delete permission string for the provided role.\n *\n * @param {string} role\n * @returns {string}\n */\nPermission.delete = (role) => {\n    return `delete(\"${role}\")`;\n};\n\n/**\n * Helper class to generate role strings for `Permission`.\n */\nclass Role {\n    /**\n     * Grants access to anyone.\n     *\n     * This includes authenticated and unauthenticated users.\n     *\n     * @returns {string}\n     */\n    static any() {\n        return 'any';\n    }\n    /**\n     * Grants access to a specific user by user ID.\n     *\n     * You can optionally pass verified or unverified for\n     * `status` to target specific types of users.\n     *\n     * @param {string} id\n     * @param {string} status\n     * @returns {string}\n     */\n    static user(id, status = '') {\n        if (status === '') {\n            return `user:${id}`;\n        }\n        return `user:${id}/${status}`;\n    }\n    /**\n     * Grants access to any authenticated or anonymous user.\n     *\n     * You can optionally pass verified or unverified for\n     * `status` to target specific types of users.\n     *\n     * @param {string} status\n     * @returns {string}\n     */\n    static users(status = '') {\n        if (status === '') {\n            return 'users';\n        }\n        return `users/${status}`;\n    }\n    /**\n     * Grants access to any guest user without a session.\n     *\n     * Authenticated users don't have access to this role.\n     *\n     * @returns {string}\n     */\n    static guests() {\n        return 'guests';\n    }\n    /**\n     * Grants access to a team by team ID.\n     *\n     * You can optionally pass a role for `role` to target\n     * team members with the specified role.\n     *\n     * @param {string} id\n     * @param {string} role\n     * @returns {string}\n     */\n    static team(id, role = '') {\n        if (role === '') {\n            return `team:${id}`;\n        }\n        return `team:${id}/${role}`;\n    }\n    /**\n     * Grants access to a specific member of a team.\n     *\n     * When the member is removed from the team, they will\n     * no longer have access.\n     *\n     * @param {string} id\n     * @returns {string}\n     */\n    static member(id) {\n        return `member:${id}`;\n    }\n    /**\n     * Grants access to a user with the specified label.\n     *\n     * @param {string} name\n     * @returns  {string}\n     */\n    static label(name) {\n        return `label:${name}`;\n    }\n}\n\nvar _a, _ID_hexTimestamp;\n/**\n * Helper class to generate ID strings for resources.\n */\nclass ID {\n    /**\n     * Uses the provided ID as the ID for the resource.\n     *\n     * @param {string} id\n     * @returns {string}\n     */\n    static custom(id) {\n        return id;\n    }\n    /**\n     * Have Appwrite generate a unique ID for you.\n     *\n     * @param {number} padding. Default is 7.\n     * @returns {string}\n     */\n    static unique(padding = 7) {\n        // Generate a unique ID with padding to have a longer ID\n        const baseId = __classPrivateFieldGet(ID, _a, \"m\", _ID_hexTimestamp).call(ID);\n        let randomPadding = '';\n        for (let i = 0; i < padding; i++) {\n            const randomHexDigit = Math.floor(Math.random() * 16).toString(16);\n            randomPadding += randomHexDigit;\n        }\n        return baseId + randomPadding;\n    }\n}\n_a = ID, _ID_hexTimestamp = function _ID_hexTimestamp() {\n    const now = new Date();\n    const sec = Math.floor(now.getTime() / 1000);\n    const msec = now.getMilliseconds();\n    // Convert to hexadecimal\n    const hexTimestamp = sec.toString(16) + msec.toString(16).padStart(5, '0');\n    return hexTimestamp;\n};\n\nvar AuthenticatorType;\n(function (AuthenticatorType) {\n    AuthenticatorType[\"Totp\"] = \"totp\";\n})(AuthenticatorType || (AuthenticatorType = {}));\n\nvar AuthenticationFactor;\n(function (AuthenticationFactor) {\n    AuthenticationFactor[\"Email\"] = \"email\";\n    AuthenticationFactor[\"Phone\"] = \"phone\";\n    AuthenticationFactor[\"Totp\"] = \"totp\";\n    AuthenticationFactor[\"Recoverycode\"] = \"recoverycode\";\n})(AuthenticationFactor || (AuthenticationFactor = {}));\n\nvar OAuthProvider;\n(function (OAuthProvider) {\n    OAuthProvider[\"Amazon\"] = \"amazon\";\n    OAuthProvider[\"Apple\"] = \"apple\";\n    OAuthProvider[\"Auth0\"] = \"auth0\";\n    OAuthProvider[\"Authentik\"] = \"authentik\";\n    OAuthProvider[\"Autodesk\"] = \"autodesk\";\n    OAuthProvider[\"Bitbucket\"] = \"bitbucket\";\n    OAuthProvider[\"Bitly\"] = \"bitly\";\n    OAuthProvider[\"Box\"] = \"box\";\n    OAuthProvider[\"Dailymotion\"] = \"dailymotion\";\n    OAuthProvider[\"Discord\"] = \"discord\";\n    OAuthProvider[\"Disqus\"] = \"disqus\";\n    OAuthProvider[\"Dropbox\"] = \"dropbox\";\n    OAuthProvider[\"Etsy\"] = \"etsy\";\n    OAuthProvider[\"Facebook\"] = \"facebook\";\n    OAuthProvider[\"Figma\"] = \"figma\";\n    OAuthProvider[\"Github\"] = \"github\";\n    OAuthProvider[\"Gitlab\"] = \"gitlab\";\n    OAuthProvider[\"Google\"] = \"google\";\n    OAuthProvider[\"Linkedin\"] = \"linkedin\";\n    OAuthProvider[\"Microsoft\"] = \"microsoft\";\n    OAuthProvider[\"Notion\"] = \"notion\";\n    OAuthProvider[\"Oidc\"] = \"oidc\";\n    OAuthProvider[\"Okta\"] = \"okta\";\n    OAuthProvider[\"Paypal\"] = \"paypal\";\n    OAuthProvider[\"PaypalSandbox\"] = \"paypalSandbox\";\n    OAuthProvider[\"Podio\"] = \"podio\";\n    OAuthProvider[\"Salesforce\"] = \"salesforce\";\n    OAuthProvider[\"Slack\"] = \"slack\";\n    OAuthProvider[\"Spotify\"] = \"spotify\";\n    OAuthProvider[\"Stripe\"] = \"stripe\";\n    OAuthProvider[\"Tradeshift\"] = \"tradeshift\";\n    OAuthProvider[\"TradeshiftBox\"] = \"tradeshiftBox\";\n    OAuthProvider[\"Twitch\"] = \"twitch\";\n    OAuthProvider[\"Wordpress\"] = \"wordpress\";\n    OAuthProvider[\"Yahoo\"] = \"yahoo\";\n    OAuthProvider[\"Yammer\"] = \"yammer\";\n    OAuthProvider[\"Yandex\"] = \"yandex\";\n    OAuthProvider[\"Zoho\"] = \"zoho\";\n    OAuthProvider[\"Zoom\"] = \"zoom\";\n    OAuthProvider[\"Mock\"] = \"mock\";\n})(OAuthProvider || (OAuthProvider = {}));\n\nvar Browser;\n(function (Browser) {\n    Browser[\"AvantBrowser\"] = \"aa\";\n    Browser[\"AndroidWebViewBeta\"] = \"an\";\n    Browser[\"GoogleChrome\"] = \"ch\";\n    Browser[\"GoogleChromeIOS\"] = \"ci\";\n    Browser[\"GoogleChromeMobile\"] = \"cm\";\n    Browser[\"Chromium\"] = \"cr\";\n    Browser[\"MozillaFirefox\"] = \"ff\";\n    Browser[\"Safari\"] = \"sf\";\n    Browser[\"MobileSafari\"] = \"mf\";\n    Browser[\"MicrosoftEdge\"] = \"ps\";\n    Browser[\"MicrosoftEdgeIOS\"] = \"oi\";\n    Browser[\"OperaMini\"] = \"om\";\n    Browser[\"Opera\"] = \"op\";\n    Browser[\"OperaNext\"] = \"on\";\n})(Browser || (Browser = {}));\n\nvar CreditCard;\n(function (CreditCard) {\n    CreditCard[\"AmericanExpress\"] = \"amex\";\n    CreditCard[\"Argencard\"] = \"argencard\";\n    CreditCard[\"Cabal\"] = \"cabal\";\n    CreditCard[\"Cencosud\"] = \"cencosud\";\n    CreditCard[\"DinersClub\"] = \"diners\";\n    CreditCard[\"Discover\"] = \"discover\";\n    CreditCard[\"Elo\"] = \"elo\";\n    CreditCard[\"Hipercard\"] = \"hipercard\";\n    CreditCard[\"JCB\"] = \"jcb\";\n    CreditCard[\"Mastercard\"] = \"mastercard\";\n    CreditCard[\"Naranja\"] = \"naranja\";\n    CreditCard[\"TarjetaShopping\"] = \"targeta-shopping\";\n    CreditCard[\"UnionChinaPay\"] = \"union-china-pay\";\n    CreditCard[\"Visa\"] = \"visa\";\n    CreditCard[\"MIR\"] = \"mir\";\n    CreditCard[\"Maestro\"] = \"maestro\";\n    CreditCard[\"Rupay\"] = \"rupay\";\n})(CreditCard || (CreditCard = {}));\n\nvar Flag;\n(function (Flag) {\n    Flag[\"Afghanistan\"] = \"af\";\n    Flag[\"Angola\"] = \"ao\";\n    Flag[\"Albania\"] = \"al\";\n    Flag[\"Andorra\"] = \"ad\";\n    Flag[\"UnitedArabEmirates\"] = \"ae\";\n    Flag[\"Argentina\"] = \"ar\";\n    Flag[\"Armenia\"] = \"am\";\n    Flag[\"AntiguaAndBarbuda\"] = \"ag\";\n    Flag[\"Australia\"] = \"au\";\n    Flag[\"Austria\"] = \"at\";\n    Flag[\"Azerbaijan\"] = \"az\";\n    Flag[\"Burundi\"] = \"bi\";\n    Flag[\"Belgium\"] = \"be\";\n    Flag[\"Benin\"] = \"bj\";\n    Flag[\"BurkinaFaso\"] = \"bf\";\n    Flag[\"Bangladesh\"] = \"bd\";\n    Flag[\"Bulgaria\"] = \"bg\";\n    Flag[\"Bahrain\"] = \"bh\";\n    Flag[\"Bahamas\"] = \"bs\";\n    Flag[\"BosniaAndHerzegovina\"] = \"ba\";\n    Flag[\"Belarus\"] = \"by\";\n    Flag[\"Belize\"] = \"bz\";\n    Flag[\"Bolivia\"] = \"bo\";\n    Flag[\"Brazil\"] = \"br\";\n    Flag[\"Barbados\"] = \"bb\";\n    Flag[\"BruneiDarussalam\"] = \"bn\";\n    Flag[\"Bhutan\"] = \"bt\";\n    Flag[\"Botswana\"] = \"bw\";\n    Flag[\"CentralAfricanRepublic\"] = \"cf\";\n    Flag[\"Canada\"] = \"ca\";\n    Flag[\"Switzerland\"] = \"ch\";\n    Flag[\"Chile\"] = \"cl\";\n    Flag[\"China\"] = \"cn\";\n    Flag[\"CoteDIvoire\"] = \"ci\";\n    Flag[\"Cameroon\"] = \"cm\";\n    Flag[\"DemocraticRepublicOfTheCongo\"] = \"cd\";\n    Flag[\"RepublicOfTheCongo\"] = \"cg\";\n    Flag[\"Colombia\"] = \"co\";\n    Flag[\"Comoros\"] = \"km\";\n    Flag[\"CapeVerde\"] = \"cv\";\n    Flag[\"CostaRica\"] = \"cr\";\n    Flag[\"Cuba\"] = \"cu\";\n    Flag[\"Cyprus\"] = \"cy\";\n    Flag[\"CzechRepublic\"] = \"cz\";\n    Flag[\"Germany\"] = \"de\";\n    Flag[\"Djibouti\"] = \"dj\";\n    Flag[\"Dominica\"] = \"dm\";\n    Flag[\"Denmark\"] = \"dk\";\n    Flag[\"DominicanRepublic\"] = \"do\";\n    Flag[\"Algeria\"] = \"dz\";\n    Flag[\"Ecuador\"] = \"ec\";\n    Flag[\"Egypt\"] = \"eg\";\n    Flag[\"Eritrea\"] = \"er\";\n    Flag[\"Spain\"] = \"es\";\n    Flag[\"Estonia\"] = \"ee\";\n    Flag[\"Ethiopia\"] = \"et\";\n    Flag[\"Finland\"] = \"fi\";\n    Flag[\"Fiji\"] = \"fj\";\n    Flag[\"France\"] = \"fr\";\n    Flag[\"MicronesiaFederatedStatesOf\"] = \"fm\";\n    Flag[\"Gabon\"] = \"ga\";\n    Flag[\"UnitedKingdom\"] = \"gb\";\n    Flag[\"Georgia\"] = \"ge\";\n    Flag[\"Ghana\"] = \"gh\";\n    Flag[\"Guinea\"] = \"gn\";\n    Flag[\"Gambia\"] = \"gm\";\n    Flag[\"GuineaBissau\"] = \"gw\";\n    Flag[\"EquatorialGuinea\"] = \"gq\";\n    Flag[\"Greece\"] = \"gr\";\n    Flag[\"Grenada\"] = \"gd\";\n    Flag[\"Guatemala\"] = \"gt\";\n    Flag[\"Guyana\"] = \"gy\";\n    Flag[\"Honduras\"] = \"hn\";\n    Flag[\"Croatia\"] = \"hr\";\n    Flag[\"Haiti\"] = \"ht\";\n    Flag[\"Hungary\"] = \"hu\";\n    Flag[\"Indonesia\"] = \"id\";\n    Flag[\"India\"] = \"in\";\n    Flag[\"Ireland\"] = \"ie\";\n    Flag[\"IranIslamicRepublicOf\"] = \"ir\";\n    Flag[\"Iraq\"] = \"iq\";\n    Flag[\"Iceland\"] = \"is\";\n    Flag[\"Israel\"] = \"il\";\n    Flag[\"Italy\"] = \"it\";\n    Flag[\"Jamaica\"] = \"jm\";\n    Flag[\"Jordan\"] = \"jo\";\n    Flag[\"Japan\"] = \"jp\";\n    Flag[\"Kazakhstan\"] = \"kz\";\n    Flag[\"Kenya\"] = \"ke\";\n    Flag[\"Kyrgyzstan\"] = \"kg\";\n    Flag[\"Cambodia\"] = \"kh\";\n    Flag[\"Kiribati\"] = \"ki\";\n    Flag[\"SaintKittsAndNevis\"] = \"kn\";\n    Flag[\"SouthKorea\"] = \"kr\";\n    Flag[\"Kuwait\"] = \"kw\";\n    Flag[\"LaoPeopleSDemocraticRepublic\"] = \"la\";\n    Flag[\"Lebanon\"] = \"lb\";\n    Flag[\"Liberia\"] = \"lr\";\n    Flag[\"Libya\"] = \"ly\";\n    Flag[\"SaintLucia\"] = \"lc\";\n    Flag[\"Liechtenstein\"] = \"li\";\n    Flag[\"SriLanka\"] = \"lk\";\n    Flag[\"Lesotho\"] = \"ls\";\n    Flag[\"Lithuania\"] = \"lt\";\n    Flag[\"Luxembourg\"] = \"lu\";\n    Flag[\"Latvia\"] = \"lv\";\n    Flag[\"Morocco\"] = \"ma\";\n    Flag[\"Monaco\"] = \"mc\";\n    Flag[\"Moldova\"] = \"md\";\n    Flag[\"Madagascar\"] = \"mg\";\n    Flag[\"Maldives\"] = \"mv\";\n    Flag[\"Mexico\"] = \"mx\";\n    Flag[\"MarshallIslands\"] = \"mh\";\n    Flag[\"NorthMacedonia\"] = \"mk\";\n    Flag[\"Mali\"] = \"ml\";\n    Flag[\"Malta\"] = \"mt\";\n    Flag[\"Myanmar\"] = \"mm\";\n    Flag[\"Montenegro\"] = \"me\";\n    Flag[\"Mongolia\"] = \"mn\";\n    Flag[\"Mozambique\"] = \"mz\";\n    Flag[\"Mauritania\"] = \"mr\";\n    Flag[\"Mauritius\"] = \"mu\";\n    Flag[\"Malawi\"] = \"mw\";\n    Flag[\"Malaysia\"] = \"my\";\n    Flag[\"Namibia\"] = \"na\";\n    Flag[\"Niger\"] = \"ne\";\n    Flag[\"Nigeria\"] = \"ng\";\n    Flag[\"Nicaragua\"] = \"ni\";\n    Flag[\"Netherlands\"] = \"nl\";\n    Flag[\"Norway\"] = \"no\";\n    Flag[\"Nepal\"] = \"np\";\n    Flag[\"Nauru\"] = \"nr\";\n    Flag[\"NewZealand\"] = \"nz\";\n    Flag[\"Oman\"] = \"om\";\n    Flag[\"Pakistan\"] = \"pk\";\n    Flag[\"Panama\"] = \"pa\";\n    Flag[\"Peru\"] = \"pe\";\n    Flag[\"Philippines\"] = \"ph\";\n    Flag[\"Palau\"] = \"pw\";\n    Flag[\"PapuaNewGuinea\"] = \"pg\";\n    Flag[\"Poland\"] = \"pl\";\n    Flag[\"FrenchPolynesia\"] = \"pf\";\n    Flag[\"NorthKorea\"] = \"kp\";\n    Flag[\"Portugal\"] = \"pt\";\n    Flag[\"Paraguay\"] = \"py\";\n    Flag[\"Qatar\"] = \"qa\";\n    Flag[\"Romania\"] = \"ro\";\n    Flag[\"Russia\"] = \"ru\";\n    Flag[\"Rwanda\"] = \"rw\";\n    Flag[\"SaudiArabia\"] = \"sa\";\n    Flag[\"Sudan\"] = \"sd\";\n    Flag[\"Senegal\"] = \"sn\";\n    Flag[\"Singapore\"] = \"sg\";\n    Flag[\"SolomonIslands\"] = \"sb\";\n    Flag[\"SierraLeone\"] = \"sl\";\n    Flag[\"ElSalvador\"] = \"sv\";\n    Flag[\"SanMarino\"] = \"sm\";\n    Flag[\"Somalia\"] = \"so\";\n    Flag[\"Serbia\"] = \"rs\";\n    Flag[\"SouthSudan\"] = \"ss\";\n    Flag[\"SaoTomeAndPrincipe\"] = \"st\";\n    Flag[\"Suriname\"] = \"sr\";\n    Flag[\"Slovakia\"] = \"sk\";\n    Flag[\"Slovenia\"] = \"si\";\n    Flag[\"Sweden\"] = \"se\";\n    Flag[\"Eswatini\"] = \"sz\";\n    Flag[\"Seychelles\"] = \"sc\";\n    Flag[\"Syria\"] = \"sy\";\n    Flag[\"Chad\"] = \"td\";\n    Flag[\"Togo\"] = \"tg\";\n    Flag[\"Thailand\"] = \"th\";\n    Flag[\"Tajikistan\"] = \"tj\";\n    Flag[\"Turkmenistan\"] = \"tm\";\n    Flag[\"TimorLeste\"] = \"tl\";\n    Flag[\"Tonga\"] = \"to\";\n    Flag[\"TrinidadAndTobago\"] = \"tt\";\n    Flag[\"Tunisia\"] = \"tn\";\n    Flag[\"Turkey\"] = \"tr\";\n    Flag[\"Tuvalu\"] = \"tv\";\n    Flag[\"Tanzania\"] = \"tz\";\n    Flag[\"Uganda\"] = \"ug\";\n    Flag[\"Ukraine\"] = \"ua\";\n    Flag[\"Uruguay\"] = \"uy\";\n    Flag[\"UnitedStates\"] = \"us\";\n    Flag[\"Uzbekistan\"] = \"uz\";\n    Flag[\"VaticanCity\"] = \"va\";\n    Flag[\"SaintVincentAndTheGrenadines\"] = \"vc\";\n    Flag[\"Venezuela\"] = \"ve\";\n    Flag[\"Vietnam\"] = \"vn\";\n    Flag[\"Vanuatu\"] = \"vu\";\n    Flag[\"Samoa\"] = \"ws\";\n    Flag[\"Yemen\"] = \"ye\";\n    Flag[\"SouthAfrica\"] = \"za\";\n    Flag[\"Zambia\"] = \"zm\";\n    Flag[\"Zimbabwe\"] = \"zw\";\n})(Flag || (Flag = {}));\n\nvar ExecutionMethod;\n(function (ExecutionMethod) {\n    ExecutionMethod[\"GET\"] = \"GET\";\n    ExecutionMethod[\"POST\"] = \"POST\";\n    ExecutionMethod[\"PUT\"] = \"PUT\";\n    ExecutionMethod[\"PATCH\"] = \"PATCH\";\n    ExecutionMethod[\"DELETE\"] = \"DELETE\";\n    ExecutionMethod[\"OPTIONS\"] = \"OPTIONS\";\n})(ExecutionMethod || (ExecutionMethod = {}));\n\nvar ImageGravity;\n(function (ImageGravity) {\n    ImageGravity[\"Center\"] = \"center\";\n    ImageGravity[\"Topleft\"] = \"top-left\";\n    ImageGravity[\"Top\"] = \"top\";\n    ImageGravity[\"Topright\"] = \"top-right\";\n    ImageGravity[\"Left\"] = \"left\";\n    ImageGravity[\"Right\"] = \"right\";\n    ImageGravity[\"Bottomleft\"] = \"bottom-left\";\n    ImageGravity[\"Bottom\"] = \"bottom\";\n    ImageGravity[\"Bottomright\"] = \"bottom-right\";\n})(ImageGravity || (ImageGravity = {}));\n\nvar ImageFormat;\n(function (ImageFormat) {\n    ImageFormat[\"Jpg\"] = \"jpg\";\n    ImageFormat[\"Jpeg\"] = \"jpeg\";\n    ImageFormat[\"Png\"] = \"png\";\n    ImageFormat[\"Webp\"] = \"webp\";\n    ImageFormat[\"Heic\"] = \"heic\";\n    ImageFormat[\"Avif\"] = \"avif\";\n    ImageFormat[\"Gif\"] = \"gif\";\n})(ImageFormat || (ImageFormat = {}));\n\n\n//# sourceMappingURL=sdk.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvYXBwd3JpdGUvZGlzdC9lc20vc2RrLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNEJBQTRCLCtEQUErRCxpQkFBaUI7QUFDNUc7QUFDQSxvQ0FBb0MsTUFBTSwrQkFBK0IsWUFBWTtBQUNyRixtQ0FBbUMsTUFBTSxtQ0FBbUMsWUFBWTtBQUN4RixnQ0FBZ0M7QUFDaEM7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLFFBQVE7QUFDdkIsZUFBZSxpQkFBaUI7QUFDaEMsZUFBZSxZQUFZO0FBQzNCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CLFdBQVcsWUFBWTtBQUN2QixhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsUUFBUTtBQUNuQixXQUFXLFlBQVk7QUFDdkIsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFFBQVE7QUFDbkIsV0FBVyxZQUFZO0FBQ3ZCLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CLFdBQVcsWUFBWTtBQUN2QixhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsUUFBUTtBQUNuQixXQUFXLFlBQVk7QUFDdkIsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFFBQVE7QUFDbkIsV0FBVyxZQUFZO0FBQ3ZCLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CLFdBQVcsaUJBQWlCO0FBQzVCLFdBQVcsaUJBQWlCO0FBQzVCLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CLFdBQVcsUUFBUTtBQUNuQixhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsUUFBUTtBQUNuQixXQUFXLFFBQVE7QUFDbkIsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFVBQVU7QUFDckIsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsUUFBUTtBQUNuQixXQUFXLFFBQVE7QUFDbkIsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFFBQVE7QUFDbkIsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFFBQVE7QUFDbkIsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFFBQVE7QUFDbkIsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFFBQVE7QUFDbkIsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFFBQVE7QUFDbkIsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFFBQVE7QUFDbkIsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFFBQVE7QUFDbkIsV0FBVyxtQkFBbUI7QUFDOUIsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFVBQVU7QUFDckIsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFVBQVU7QUFDckIsYUFBYTtBQUNiO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakIsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQjtBQUNyQixpQkFBaUI7QUFDakIsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQjtBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1HQUFtRyxnQkFBZ0I7QUFDbkg7QUFDQTtBQUNBO0FBQ0EseUJBQXlCO0FBQ3pCLHFCQUFxQjtBQUNyQjtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLCtJQUErSTtBQUMvSSxnSEFBZ0gsb0JBQW9CO0FBQ3BJO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUNBQWlDO0FBQ2pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQztBQUNqQztBQUNBO0FBQ0E7QUFDQSxtQ0FBbUM7QUFDbkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5QkFBeUI7QUFDekI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsUUFBUTtBQUN2QjtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLFFBQVE7QUFDdkI7QUFDQSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCO0FBQ2hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCO0FBQ2hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0I7QUFDaEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0I7QUFDaEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0I7QUFDaEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxpQkFBaUI7QUFDaEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxvQ0FBb0M7QUFDbkQsaUJBQWlCLFlBQVk7QUFDN0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRDQUE0QyxhQUFhO0FBQ3pEO0FBQ0Esa0NBQWtDO0FBQ2xDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbURBQW1ELElBQUk7QUFDdkQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBLDJDQUEyQyxzQkFBc0I7QUFDakU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscURBQXFEO0FBQ3JEO0FBQ0EscUNBQXFDO0FBQ3JDO0FBQ0Esb0RBQW9ELE1BQU0sR0FBRyxRQUFRLEdBQUcsVUFBVTtBQUNsRjtBQUNBLDhDQUE4QztBQUM5QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQkFBcUI7QUFDckI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQSxrQ0FBa0MsYUFBYTtBQUMvQztBQUNBO0FBQ0Esb0JBQW9CLGVBQWU7QUFDbkM7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5RkFBeUYscUJBQXFCO0FBQzlHO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQztBQUNqQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1REFBdUQ7QUFDdkQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVEQUF1RDtBQUN2RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0NBQXNDOztBQUV0QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsVUFBVTtBQUN6QixnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLFFBQVE7QUFDdkIsZ0JBQWdCO0FBQ2hCLGlCQUFpQixVQUFVO0FBQzNCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4Q0FBOEMsV0FBVyxZQUFZLFdBQVc7QUFDaEY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxVQUFVO0FBQ3pCLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsU0FBUztBQUN4QixnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxtQkFBbUI7QUFDbEMsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0RBQXNELEtBQUssWUFBWSxLQUFLO0FBQzVFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxtQkFBbUI7QUFDbEMsZUFBZSxRQUFRO0FBQ3ZCLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNEQUFzRCxLQUFLLFlBQVksS0FBSztBQUM1RTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsbUJBQW1CO0FBQ2xDLGdCQUFnQjtBQUNoQixpQkFBaUIsVUFBVTtBQUMzQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0RBQXNELEtBQUssWUFBWSxLQUFLO0FBQzVFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxzQkFBc0I7QUFDckMsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsK0RBQStEO0FBQy9EO0FBQ0EsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLFFBQVE7QUFDdkIsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0RBQWdEO0FBQ2hEO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxzQkFBc0I7QUFDckMsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd2VBQXdlO0FBQ3hlO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0I7QUFDaEIsaUJBQWlCLFVBQVU7QUFDM0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd09BQXdPO0FBQ3hPO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsZUFBZTtBQUM5QixlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsVUFBVTtBQUN6QixnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtREFBbUQsU0FBUyxZQUFZLFNBQVM7QUFDakY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0RBQXNELDhDQUE4QyxjQUFjO0FBQ2xIO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRDQUE0QyxVQUFVLFlBQVksVUFBVTtBQUM1RTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrREFBa0Q7QUFDbEQ7QUFDQSxlQUFlLFFBQVE7QUFDdkIsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNENBQTRDLFVBQVUsWUFBWSxVQUFVO0FBQzVFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQ0FBa0MsY0FBYyxzR0FBc0c7QUFDdEo7QUFDQSxlQUFlLFFBQVE7QUFDdkIsZ0JBQWdCO0FBQ2hCLGlCQUFpQixVQUFVO0FBQzNCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0Q0FBNEMsVUFBVSxZQUFZLFVBQVU7QUFDNUU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0RBQWdELDJEQUEyRDtBQUMzRztBQUNBLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkNBQTJDLFNBQVMsaUJBQWlCLFNBQVM7QUFDOUU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLFFBQVE7QUFDdkIsZ0JBQWdCO0FBQ2hCLGlCQUFpQixVQUFVO0FBQzNCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyQ0FBMkMsU0FBUyxpQkFBaUIsU0FBUztBQUM5RTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMllBQTJZO0FBQzNZO0FBQ0E7QUFDQTtBQUNBLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZUFBZSxTQUFTO0FBQ3hCLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhpQkFBOGlCO0FBQzlpQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsU0FBUztBQUN4QixnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3T0FBd087QUFDeE87QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsZUFBZTtBQUM5QixlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsVUFBVTtBQUN6QixnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpREFBaUQsU0FBUyxZQUFZLFNBQVM7QUFDL0U7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseVlBQXlZO0FBQ3pZO0FBQ0E7QUFDQTtBQUNBLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb29CQUFvb0I7QUFDcG9CO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLCtJQUErSSxpVUFBaVU7QUFDaGQ7QUFDQSxnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2SUFBNkk7QUFDN0k7QUFDQSxlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLFNBQVM7QUFDeEIsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNENBQTRDLEtBQUssWUFBWSxLQUFLO0FBQ2xFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLFlBQVk7QUFDM0IsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0RBQWdELEtBQUssWUFBWSxLQUFLO0FBQ3RFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsTUFBTTtBQUNyQixlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5Q0FBeUMsS0FBSyxZQUFZLEtBQUs7QUFDL0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3T0FBd08sV0FBVztBQUNuUDtBQUNBLDZLQUE2SztBQUM3SztBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsU0FBUztBQUN4QixnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUNBQXVDO0FBQ3ZDO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixlQUFlLFVBQVU7QUFDekIsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUNBQXFDLFdBQVcsY0FBYyxhQUFhLHNCQUFzQixXQUFXLHlCQUF5QixhQUFhO0FBQ2xKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixlQUFlLGtIQUFrSDtBQUNqSSxlQUFlLFVBQVU7QUFDekIsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUNBQXFDLFdBQVcsY0FBYyxhQUFhLHNCQUFzQixXQUFXLHlCQUF5QixhQUFhO0FBQ2xKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZUFBZSxVQUFVO0FBQ3pCLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFDQUFxQyxXQUFXLGNBQWMsYUFBYSxZQUFZLFdBQVcsWUFBWSxXQUFXLHlCQUF5QixhQUFhLDJCQUEyQixXQUFXO0FBQ3JNO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsVUFBVTtBQUN6QixnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQ0FBcUMsV0FBVyxjQUFjLGFBQWEsWUFBWSxXQUFXLFlBQVksV0FBVyx5QkFBeUIsYUFBYSwyQkFBMkIsV0FBVztBQUNyTTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsMkhBQTJIO0FBQzFJLGVBQWUsVUFBVTtBQUN6QixnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQ0FBcUMsV0FBVyxjQUFjLGFBQWEsWUFBWSxXQUFXLFlBQVksV0FBVyx5QkFBeUIsYUFBYSwyQkFBMkIsV0FBVztBQUNyTTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGdCQUFnQjtBQUNoQixpQkFBaUIsVUFBVTtBQUMzQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUNBQXFDLFdBQVcsY0FBYyxhQUFhLFlBQVksV0FBVyxZQUFZLFdBQVcseUJBQXlCLGFBQWEsMkJBQTJCLFdBQVc7QUFDck07QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQ0FBcUMsV0FBVyxjQUFjLGFBQWEsWUFBWSxXQUFXLEVBQUUsVUFBVSxzQkFBc0IsV0FBVyx5QkFBeUIsYUFBYSwyQkFBMkIsV0FBVyx5QkFBeUIsVUFBVTtBQUM5UDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFDQUFxQyxXQUFXLGNBQWMsYUFBYSxZQUFZLFdBQVcsRUFBRSxVQUFVLHNCQUFzQixXQUFXLHlCQUF5QixhQUFhLDJCQUEyQixXQUFXLHlCQUF5QixVQUFVO0FBQzlQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLFFBQVE7QUFDdkIsZUFBZSxVQUFVO0FBQ3pCLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFDQUFxQyxXQUFXLHVCQUF1QixXQUFXO0FBQ2xGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsU0FBUztBQUN4QixlQUFlLFFBQVE7QUFDdkIsZUFBZSxpQkFBaUI7QUFDaEMsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQ0FBcUMsV0FBVyx1QkFBdUIsV0FBVztBQUNsRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUNBQXFDLFdBQVcsYUFBYSxZQUFZLFlBQVksV0FBVyx5QkFBeUIsWUFBWTtBQUNySTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLFFBQVE7QUFDdkIsZ0JBQWdCO0FBQ2hCLGlCQUFpQixVQUFVO0FBQzNCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLFFBQVE7QUFDdkIsZ0JBQWdCO0FBQ2hCLGlCQUFpQixVQUFVO0FBQzNCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRDQUE0QyxRQUFRLHdCQUF3QixRQUFRO0FBQ3BGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixnQkFBZ0I7QUFDaEIsaUJBQWlCLFVBQVU7QUFDM0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRDQUE0QyxRQUFRLGNBQWMsYUFBYSxZQUFZLFFBQVEsc0JBQXNCLGFBQWE7QUFDdEk7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsUUFBUTtBQUN2QixlQUFlLFVBQVU7QUFDekIsZUFBZSxRQUFRO0FBQ3ZCLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJDQUEyQyxTQUFTLGtCQUFrQixTQUFTO0FBQy9FO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOElBQThJO0FBQzlJO0FBQ0EsbUJBQW1CO0FBQ25CO0FBQ0E7QUFDQSxlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsTUFBTTtBQUNyQixlQUFlLFVBQVU7QUFDekIsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBLGtGQUFrRjtBQUNsRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyQ0FBMkMsU0FBUyxrQkFBa0IsU0FBUztBQUMvRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkNBQTJDLFNBQVMsUUFBUSxPQUFPLFlBQVksU0FBUyx1QkFBdUIsT0FBTztBQUN0SDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZUFBZSxVQUFVO0FBQ3pCLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJDQUEyQyxTQUFTLFFBQVEsT0FBTyxZQUFZLFNBQVMsdUJBQXVCLE9BQU87QUFDdEg7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGdCQUFnQjtBQUNoQixpQkFBaUIsVUFBVTtBQUMzQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkNBQTJDLFNBQVMsUUFBUSxPQUFPLFlBQVksU0FBUyx1QkFBdUIsT0FBTztBQUN0SDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0ZBQXNGLHNDQUFzQztBQUM1SDtBQUNBLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJDQUEyQyxTQUFTLFFBQVEsT0FBTyxxQkFBcUIsU0FBUyx1QkFBdUIsT0FBTztBQUMvSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsY0FBYztBQUM3QixlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZUFBZSxhQUFhO0FBQzVCLGVBQWUsUUFBUTtBQUN2QixnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyQ0FBMkMsU0FBUyxRQUFRLE9BQU8sb0JBQW9CLFNBQVMsdUJBQXVCLE9BQU87QUFDOUg7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUhBQXVILHNDQUFzQztBQUM3SjtBQUNBLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJDQUEyQyxTQUFTLFFBQVEsT0FBTyxpQkFBaUIsU0FBUyx1QkFBdUIsT0FBTztBQUMzSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLFVBQVU7QUFDekIsZUFBZSxRQUFRO0FBQ3ZCLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZUFBZSxVQUFVO0FBQ3pCLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLFFBQVE7QUFDdkIsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUNBQWlDLE9BQU8sWUFBWSxPQUFPO0FBQzNEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRCQUE0QjtBQUM1QjtBQUNBLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUNBQWlDLE9BQU8sWUFBWSxPQUFPO0FBQzNEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCLGdCQUFnQjtBQUNoQixpQkFBaUIsVUFBVTtBQUMzQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUNBQWlDLE9BQU8sWUFBWSxPQUFPO0FBQzNEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2Q0FBNkMsOEJBQThCO0FBQzNFO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsVUFBVTtBQUN6QixlQUFlLFFBQVE7QUFDdkIsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUNBQWlDLE9BQU8sd0JBQXdCLE9BQU87QUFDdkU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNFRBQTRUO0FBQzVUO0FBQ0EsOEhBQThILFdBQVc7QUFDekk7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsVUFBVTtBQUN6QixlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQyxPQUFPLHdCQUF3QixPQUFPO0FBQ3ZFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQ0FBaUMsT0FBTyxjQUFjLGFBQWEsWUFBWSxPQUFPLHFCQUFxQixhQUFhO0FBQ3hIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZUFBZSxVQUFVO0FBQ3pCLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQyxPQUFPLGNBQWMsYUFBYSxZQUFZLE9BQU8scUJBQXFCLGFBQWE7QUFDeEg7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGdCQUFnQjtBQUNoQixpQkFBaUIsVUFBVTtBQUMzQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUNBQWlDLE9BQU8sY0FBYyxhQUFhLFlBQVksT0FBTyxxQkFBcUIsYUFBYTtBQUN4SDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQ0FBaUMsT0FBTyxjQUFjLGFBQWEsbUJBQW1CLE9BQU8scUJBQXFCLGFBQWE7QUFDL0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlCQUF5QixrRUFBa0U7QUFDM0Y7QUFDQSxlQUFlLFFBQVE7QUFDdkIsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUNBQWlDLE9BQU8sa0JBQWtCLE9BQU87QUFDakU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNEJBQTRCO0FBQzVCO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQ0FBaUMsT0FBTyxrQkFBa0IsT0FBTztBQUNqRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFFBQVE7QUFDbkIsYUFBYTtBQUNiO0FBQ0E7QUFDQSxvQkFBb0IsS0FBSztBQUN6QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsUUFBUTtBQUNuQixhQUFhO0FBQ2I7QUFDQTtBQUNBLHFCQUFxQixLQUFLO0FBQzFCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CLGFBQWE7QUFDYjtBQUNBO0FBQ0Esc0JBQXNCLEtBQUs7QUFDM0I7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFFBQVE7QUFDbkIsYUFBYTtBQUNiO0FBQ0E7QUFDQSxzQkFBc0IsS0FBSztBQUMzQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsUUFBUTtBQUNuQixhQUFhO0FBQ2I7QUFDQTtBQUNBLHNCQUFzQixLQUFLO0FBQzNCOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBLDJCQUEyQixHQUFHO0FBQzlCO0FBQ0EsdUJBQXVCLEdBQUcsR0FBRyxPQUFPO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLE9BQU87QUFDL0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0EsMkJBQTJCLEdBQUc7QUFDOUI7QUFDQSx1QkFBdUIsR0FBRyxHQUFHLEtBQUs7QUFDbEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLFFBQVE7QUFDdkIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQSx5QkFBeUIsR0FBRztBQUM1QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsUUFBUTtBQUN2QixrQkFBa0I7QUFDbEI7QUFDQTtBQUNBLHdCQUF3QixLQUFLO0FBQzdCO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsUUFBUTtBQUN2QixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLFFBQVE7QUFDdkIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IsYUFBYTtBQUNyQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLENBQUMsOENBQThDOztBQUUvQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLG9EQUFvRDs7QUFFckQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyxzQ0FBc0M7O0FBRXZDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQywwQkFBMEI7O0FBRTNCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyxnQ0FBZ0M7O0FBRWpDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLG9CQUFvQjs7QUFFckI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsMENBQTBDOztBQUUzQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyxvQ0FBb0M7O0FBRXJDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsa0NBQWtDOztBQUVrUDtBQUNyUiIsInNvdXJjZXMiOlsiL1VzZXJzL3NoYXdhei9EZXZlbG9wZXIvYml6Y29pbi9iaXpjb2luX2NsaWVudC9ub2RlX21vZHVsZXMvYXBwd3JpdGUvZGlzdC9lc20vc2RrLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKipcclxuQ29weXJpZ2h0IChjKSBNaWNyb3NvZnQgQ29ycG9yYXRpb24uXHJcblxyXG5QZXJtaXNzaW9uIHRvIHVzZSwgY29weSwgbW9kaWZ5LCBhbmQvb3IgZGlzdHJpYnV0ZSB0aGlzIHNvZnR3YXJlIGZvciBhbnlcclxucHVycG9zZSB3aXRoIG9yIHdpdGhvdXQgZmVlIGlzIGhlcmVieSBncmFudGVkLlxyXG5cclxuVEhFIFNPRlRXQVJFIElTIFBST1ZJREVEIFwiQVMgSVNcIiBBTkQgVEhFIEFVVEhPUiBESVNDTEFJTVMgQUxMIFdBUlJBTlRJRVMgV0lUSFxyXG5SRUdBUkQgVE8gVEhJUyBTT0ZUV0FSRSBJTkNMVURJTkcgQUxMIElNUExJRUQgV0FSUkFOVElFUyBPRiBNRVJDSEFOVEFCSUxJVFlcclxuQU5EIEZJVE5FU1MuIElOIE5PIEVWRU5UIFNIQUxMIFRIRSBBVVRIT1IgQkUgTElBQkxFIEZPUiBBTlkgU1BFQ0lBTCwgRElSRUNULFxyXG5JTkRJUkVDVCwgT1IgQ09OU0VRVUVOVElBTCBEQU1BR0VTIE9SIEFOWSBEQU1BR0VTIFdIQVRTT0VWRVIgUkVTVUxUSU5HIEZST01cclxuTE9TUyBPRiBVU0UsIERBVEEgT1IgUFJPRklUUywgV0hFVEhFUiBJTiBBTiBBQ1RJT04gT0YgQ09OVFJBQ1QsIE5FR0xJR0VOQ0UgT1JcclxuT1RIRVIgVE9SVElPVVMgQUNUSU9OLCBBUklTSU5HIE9VVCBPRiBPUiBJTiBDT05ORUNUSU9OIFdJVEggVEhFIFVTRSBPUlxyXG5QRVJGT1JNQU5DRSBPRiBUSElTIFNPRlRXQVJFLlxyXG4qKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKiAqL1xyXG5cclxuZnVuY3Rpb24gX19hd2FpdGVyKHRoaXNBcmcsIF9hcmd1bWVudHMsIFAsIGdlbmVyYXRvcikge1xyXG4gICAgZnVuY3Rpb24gYWRvcHQodmFsdWUpIHsgcmV0dXJuIHZhbHVlIGluc3RhbmNlb2YgUCA/IHZhbHVlIDogbmV3IFAoZnVuY3Rpb24gKHJlc29sdmUpIHsgcmVzb2x2ZSh2YWx1ZSk7IH0pOyB9XHJcbiAgICByZXR1cm4gbmV3IChQIHx8IChQID0gUHJvbWlzZSkpKGZ1bmN0aW9uIChyZXNvbHZlLCByZWplY3QpIHtcclxuICAgICAgICBmdW5jdGlvbiBmdWxmaWxsZWQodmFsdWUpIHsgdHJ5IHsgc3RlcChnZW5lcmF0b3IubmV4dCh2YWx1ZSkpOyB9IGNhdGNoIChlKSB7IHJlamVjdChlKTsgfSB9XHJcbiAgICAgICAgZnVuY3Rpb24gcmVqZWN0ZWQodmFsdWUpIHsgdHJ5IHsgc3RlcChnZW5lcmF0b3JbXCJ0aHJvd1wiXSh2YWx1ZSkpOyB9IGNhdGNoIChlKSB7IHJlamVjdChlKTsgfSB9XHJcbiAgICAgICAgZnVuY3Rpb24gc3RlcChyZXN1bHQpIHsgcmVzdWx0LmRvbmUgPyByZXNvbHZlKHJlc3VsdC52YWx1ZSkgOiBhZG9wdChyZXN1bHQudmFsdWUpLnRoZW4oZnVsZmlsbGVkLCByZWplY3RlZCk7IH1cclxuICAgICAgICBzdGVwKChnZW5lcmF0b3IgPSBnZW5lcmF0b3IuYXBwbHkodGhpc0FyZywgX2FyZ3VtZW50cyB8fCBbXSkpLm5leHQoKSk7XHJcbiAgICB9KTtcclxufVxyXG5cclxuZnVuY3Rpb24gX19jbGFzc1ByaXZhdGVGaWVsZEdldChyZWNlaXZlciwgc3RhdGUsIGtpbmQsIGYpIHtcclxuICAgIGlmIChraW5kID09PSBcImFcIiAmJiAhZikgdGhyb3cgbmV3IFR5cGVFcnJvcihcIlByaXZhdGUgYWNjZXNzb3Igd2FzIGRlZmluZWQgd2l0aG91dCBhIGdldHRlclwiKTtcclxuICAgIGlmICh0eXBlb2Ygc3RhdGUgPT09IFwiZnVuY3Rpb25cIiA/IHJlY2VpdmVyICE9PSBzdGF0ZSB8fCAhZiA6ICFzdGF0ZS5oYXMocmVjZWl2ZXIpKSB0aHJvdyBuZXcgVHlwZUVycm9yKFwiQ2Fubm90IHJlYWQgcHJpdmF0ZSBtZW1iZXIgZnJvbSBhbiBvYmplY3Qgd2hvc2UgY2xhc3MgZGlkIG5vdCBkZWNsYXJlIGl0XCIpO1xyXG4gICAgcmV0dXJuIGtpbmQgPT09IFwibVwiID8gZiA6IGtpbmQgPT09IFwiYVwiID8gZi5jYWxsKHJlY2VpdmVyKSA6IGYgPyBmLnZhbHVlIDogc3RhdGUuZ2V0KHJlY2VpdmVyKTtcclxufVxuXG4vKipcbiAqIEhlbHBlciBjbGFzcyB0byBnZW5lcmF0ZSBxdWVyeSBzdHJpbmdzLlxuICovXG5jbGFzcyBRdWVyeSB7XG4gICAgLyoqXG4gICAgICogQ29uc3RydWN0b3IgZm9yIFF1ZXJ5IGNsYXNzLlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IG1ldGhvZFxuICAgICAqIEBwYXJhbSB7QXR0cmlidXRlc1R5cGVzfSBhdHRyaWJ1dGVcbiAgICAgKiBAcGFyYW0ge1F1ZXJ5VHlwZXN9IHZhbHVlc1xuICAgICAqL1xuICAgIGNvbnN0cnVjdG9yKG1ldGhvZCwgYXR0cmlidXRlLCB2YWx1ZXMpIHtcbiAgICAgICAgdGhpcy5tZXRob2QgPSBtZXRob2Q7XG4gICAgICAgIHRoaXMuYXR0cmlidXRlID0gYXR0cmlidXRlO1xuICAgICAgICBpZiAodmFsdWVzICE9PSB1bmRlZmluZWQpIHtcbiAgICAgICAgICAgIGlmIChBcnJheS5pc0FycmF5KHZhbHVlcykpIHtcbiAgICAgICAgICAgICAgICB0aGlzLnZhbHVlcyA9IHZhbHVlcztcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgIHRoaXMudmFsdWVzID0gW3ZhbHVlc107XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9XG4gICAgLyoqXG4gICAgICogQ29udmVydCB0aGUgcXVlcnkgb2JqZWN0IHRvIGEgSlNPTiBzdHJpbmcuXG4gICAgICpcbiAgICAgKiBAcmV0dXJucyB7c3RyaW5nfVxuICAgICAqL1xuICAgIHRvU3RyaW5nKCkge1xuICAgICAgICByZXR1cm4gSlNPTi5zdHJpbmdpZnkoe1xuICAgICAgICAgICAgbWV0aG9kOiB0aGlzLm1ldGhvZCxcbiAgICAgICAgICAgIGF0dHJpYnV0ZTogdGhpcy5hdHRyaWJ1dGUsXG4gICAgICAgICAgICB2YWx1ZXM6IHRoaXMudmFsdWVzLFxuICAgICAgICB9KTtcbiAgICB9XG59XG4vKipcbiAqIEZpbHRlciByZXNvdXJjZXMgd2hlcmUgYXR0cmlidXRlIGlzIGVxdWFsIHRvIHZhbHVlLlxuICpcbiAqIEBwYXJhbSB7c3RyaW5nfSBhdHRyaWJ1dGVcbiAqIEBwYXJhbSB7UXVlcnlUeXBlc30gdmFsdWVcbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKi9cblF1ZXJ5LmVxdWFsID0gKGF0dHJpYnV0ZSwgdmFsdWUpID0+IG5ldyBRdWVyeShcImVxdWFsXCIsIGF0dHJpYnV0ZSwgdmFsdWUpLnRvU3RyaW5nKCk7XG4vKipcbiAqIEZpbHRlciByZXNvdXJjZXMgd2hlcmUgYXR0cmlidXRlIGlzIG5vdCBlcXVhbCB0byB2YWx1ZS5cbiAqXG4gKiBAcGFyYW0ge3N0cmluZ30gYXR0cmlidXRlXG4gKiBAcGFyYW0ge1F1ZXJ5VHlwZXN9IHZhbHVlXG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICovXG5RdWVyeS5ub3RFcXVhbCA9IChhdHRyaWJ1dGUsIHZhbHVlKSA9PiBuZXcgUXVlcnkoXCJub3RFcXVhbFwiLCBhdHRyaWJ1dGUsIHZhbHVlKS50b1N0cmluZygpO1xuLyoqXG4gKiBGaWx0ZXIgcmVzb3VyY2VzIHdoZXJlIGF0dHJpYnV0ZSBpcyBsZXNzIHRoYW4gdmFsdWUuXG4gKlxuICogQHBhcmFtIHtzdHJpbmd9IGF0dHJpYnV0ZVxuICogQHBhcmFtIHtRdWVyeVR5cGVzfSB2YWx1ZVxuICogQHJldHVybnMge3N0cmluZ31cbiAqL1xuUXVlcnkubGVzc1RoYW4gPSAoYXR0cmlidXRlLCB2YWx1ZSkgPT4gbmV3IFF1ZXJ5KFwibGVzc1RoYW5cIiwgYXR0cmlidXRlLCB2YWx1ZSkudG9TdHJpbmcoKTtcbi8qKlxuICogRmlsdGVyIHJlc291cmNlcyB3aGVyZSBhdHRyaWJ1dGUgaXMgbGVzcyB0aGFuIG9yIGVxdWFsIHRvIHZhbHVlLlxuICpcbiAqIEBwYXJhbSB7c3RyaW5nfSBhdHRyaWJ1dGVcbiAqIEBwYXJhbSB7UXVlcnlUeXBlc30gdmFsdWVcbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKi9cblF1ZXJ5Lmxlc3NUaGFuRXF1YWwgPSAoYXR0cmlidXRlLCB2YWx1ZSkgPT4gbmV3IFF1ZXJ5KFwibGVzc1RoYW5FcXVhbFwiLCBhdHRyaWJ1dGUsIHZhbHVlKS50b1N0cmluZygpO1xuLyoqXG4gKiBGaWx0ZXIgcmVzb3VyY2VzIHdoZXJlIGF0dHJpYnV0ZSBpcyBncmVhdGVyIHRoYW4gdmFsdWUuXG4gKlxuICogQHBhcmFtIHtzdHJpbmd9IGF0dHJpYnV0ZVxuICogQHBhcmFtIHtRdWVyeVR5cGVzfSB2YWx1ZVxuICogQHJldHVybnMge3N0cmluZ31cbiAqL1xuUXVlcnkuZ3JlYXRlclRoYW4gPSAoYXR0cmlidXRlLCB2YWx1ZSkgPT4gbmV3IFF1ZXJ5KFwiZ3JlYXRlclRoYW5cIiwgYXR0cmlidXRlLCB2YWx1ZSkudG9TdHJpbmcoKTtcbi8qKlxuICogRmlsdGVyIHJlc291cmNlcyB3aGVyZSBhdHRyaWJ1dGUgaXMgZ3JlYXRlciB0aGFuIG9yIGVxdWFsIHRvIHZhbHVlLlxuICpcbiAqIEBwYXJhbSB7c3RyaW5nfSBhdHRyaWJ1dGVcbiAqIEBwYXJhbSB7UXVlcnlUeXBlc30gdmFsdWVcbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKi9cblF1ZXJ5LmdyZWF0ZXJUaGFuRXF1YWwgPSAoYXR0cmlidXRlLCB2YWx1ZSkgPT4gbmV3IFF1ZXJ5KFwiZ3JlYXRlclRoYW5FcXVhbFwiLCBhdHRyaWJ1dGUsIHZhbHVlKS50b1N0cmluZygpO1xuLyoqXG4gKiBGaWx0ZXIgcmVzb3VyY2VzIHdoZXJlIGF0dHJpYnV0ZSBpcyBudWxsLlxuICpcbiAqIEBwYXJhbSB7c3RyaW5nfSBhdHRyaWJ1dGVcbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKi9cblF1ZXJ5LmlzTnVsbCA9IChhdHRyaWJ1dGUpID0+IG5ldyBRdWVyeShcImlzTnVsbFwiLCBhdHRyaWJ1dGUpLnRvU3RyaW5nKCk7XG4vKipcbiAqIEZpbHRlciByZXNvdXJjZXMgd2hlcmUgYXR0cmlidXRlIGlzIG5vdCBudWxsLlxuICpcbiAqIEBwYXJhbSB7c3RyaW5nfSBhdHRyaWJ1dGVcbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKi9cblF1ZXJ5LmlzTm90TnVsbCA9IChhdHRyaWJ1dGUpID0+IG5ldyBRdWVyeShcImlzTm90TnVsbFwiLCBhdHRyaWJ1dGUpLnRvU3RyaW5nKCk7XG4vKipcbiAqIEZpbHRlciByZXNvdXJjZXMgd2hlcmUgYXR0cmlidXRlIGlzIGJldHdlZW4gc3RhcnQgYW5kIGVuZCAoaW5jbHVzaXZlKS5cbiAqXG4gKiBAcGFyYW0ge3N0cmluZ30gYXR0cmlidXRlXG4gKiBAcGFyYW0ge3N0cmluZyB8IG51bWJlcn0gc3RhcnRcbiAqIEBwYXJhbSB7c3RyaW5nIHwgbnVtYmVyfSBlbmRcbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKi9cblF1ZXJ5LmJldHdlZW4gPSAoYXR0cmlidXRlLCBzdGFydCwgZW5kKSA9PiBuZXcgUXVlcnkoXCJiZXR3ZWVuXCIsIGF0dHJpYnV0ZSwgW3N0YXJ0LCBlbmRdKS50b1N0cmluZygpO1xuLyoqXG4gKiBGaWx0ZXIgcmVzb3VyY2VzIHdoZXJlIGF0dHJpYnV0ZSBzdGFydHMgd2l0aCB2YWx1ZS5cbiAqXG4gKiBAcGFyYW0ge3N0cmluZ30gYXR0cmlidXRlXG4gKiBAcGFyYW0ge3N0cmluZ30gdmFsdWVcbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKi9cblF1ZXJ5LnN0YXJ0c1dpdGggPSAoYXR0cmlidXRlLCB2YWx1ZSkgPT4gbmV3IFF1ZXJ5KFwic3RhcnRzV2l0aFwiLCBhdHRyaWJ1dGUsIHZhbHVlKS50b1N0cmluZygpO1xuLyoqXG4gKiBGaWx0ZXIgcmVzb3VyY2VzIHdoZXJlIGF0dHJpYnV0ZSBlbmRzIHdpdGggdmFsdWUuXG4gKlxuICogQHBhcmFtIHtzdHJpbmd9IGF0dHJpYnV0ZVxuICogQHBhcmFtIHtzdHJpbmd9IHZhbHVlXG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICovXG5RdWVyeS5lbmRzV2l0aCA9IChhdHRyaWJ1dGUsIHZhbHVlKSA9PiBuZXcgUXVlcnkoXCJlbmRzV2l0aFwiLCBhdHRyaWJ1dGUsIHZhbHVlKS50b1N0cmluZygpO1xuLyoqXG4gKiBTcGVjaWZ5IHdoaWNoIGF0dHJpYnV0ZXMgc2hvdWxkIGJlIHJldHVybmVkIGJ5IHRoZSBBUEkgY2FsbC5cbiAqXG4gKiBAcGFyYW0ge3N0cmluZ1tdfSBhdHRyaWJ1dGVzXG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICovXG5RdWVyeS5zZWxlY3QgPSAoYXR0cmlidXRlcykgPT4gbmV3IFF1ZXJ5KFwic2VsZWN0XCIsIHVuZGVmaW5lZCwgYXR0cmlidXRlcykudG9TdHJpbmcoKTtcbi8qKlxuICogRmlsdGVyIHJlc291cmNlcyBieSBzZWFyY2hpbmcgYXR0cmlidXRlIGZvciB2YWx1ZS5cbiAqIEEgZnVsbHRleHQgaW5kZXggb24gYXR0cmlidXRlIGlzIHJlcXVpcmVkIGZvciB0aGlzIHF1ZXJ5IHRvIHdvcmsuXG4gKlxuICogQHBhcmFtIHtzdHJpbmd9IGF0dHJpYnV0ZVxuICogQHBhcmFtIHtzdHJpbmd9IHZhbHVlXG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICovXG5RdWVyeS5zZWFyY2ggPSAoYXR0cmlidXRlLCB2YWx1ZSkgPT4gbmV3IFF1ZXJ5KFwic2VhcmNoXCIsIGF0dHJpYnV0ZSwgdmFsdWUpLnRvU3RyaW5nKCk7XG4vKipcbiAqIFNvcnQgcmVzdWx0cyBieSBhdHRyaWJ1dGUgZGVzY2VuZGluZy5cbiAqXG4gKiBAcGFyYW0ge3N0cmluZ30gYXR0cmlidXRlXG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICovXG5RdWVyeS5vcmRlckRlc2MgPSAoYXR0cmlidXRlKSA9PiBuZXcgUXVlcnkoXCJvcmRlckRlc2NcIiwgYXR0cmlidXRlKS50b1N0cmluZygpO1xuLyoqXG4gKiBTb3J0IHJlc3VsdHMgYnkgYXR0cmlidXRlIGFzY2VuZGluZy5cbiAqXG4gKiBAcGFyYW0ge3N0cmluZ30gYXR0cmlidXRlXG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICovXG5RdWVyeS5vcmRlckFzYyA9IChhdHRyaWJ1dGUpID0+IG5ldyBRdWVyeShcIm9yZGVyQXNjXCIsIGF0dHJpYnV0ZSkudG9TdHJpbmcoKTtcbi8qKlxuICogUmV0dXJuIHJlc3VsdHMgYWZ0ZXIgZG9jdW1lbnRJZC5cbiAqXG4gKiBAcGFyYW0ge3N0cmluZ30gZG9jdW1lbnRJZFxuICogQHJldHVybnMge3N0cmluZ31cbiAqL1xuUXVlcnkuY3Vyc29yQWZ0ZXIgPSAoZG9jdW1lbnRJZCkgPT4gbmV3IFF1ZXJ5KFwiY3Vyc29yQWZ0ZXJcIiwgdW5kZWZpbmVkLCBkb2N1bWVudElkKS50b1N0cmluZygpO1xuLyoqXG4gKiBSZXR1cm4gcmVzdWx0cyBiZWZvcmUgZG9jdW1lbnRJZC5cbiAqXG4gKiBAcGFyYW0ge3N0cmluZ30gZG9jdW1lbnRJZFxuICogQHJldHVybnMge3N0cmluZ31cbiAqL1xuUXVlcnkuY3Vyc29yQmVmb3JlID0gKGRvY3VtZW50SWQpID0+IG5ldyBRdWVyeShcImN1cnNvckJlZm9yZVwiLCB1bmRlZmluZWQsIGRvY3VtZW50SWQpLnRvU3RyaW5nKCk7XG4vKipcbiAqIFJldHVybiBvbmx5IGxpbWl0IHJlc3VsdHMuXG4gKlxuICogQHBhcmFtIHtudW1iZXJ9IGxpbWl0XG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICovXG5RdWVyeS5saW1pdCA9IChsaW1pdCkgPT4gbmV3IFF1ZXJ5KFwibGltaXRcIiwgdW5kZWZpbmVkLCBsaW1pdCkudG9TdHJpbmcoKTtcbi8qKlxuICogRmlsdGVyIHJlc291cmNlcyBieSBza2lwcGluZyB0aGUgZmlyc3Qgb2Zmc2V0IHJlc3VsdHMuXG4gKlxuICogQHBhcmFtIHtudW1iZXJ9IG9mZnNldFxuICogQHJldHVybnMge3N0cmluZ31cbiAqL1xuUXVlcnkub2Zmc2V0ID0gKG9mZnNldCkgPT4gbmV3IFF1ZXJ5KFwib2Zmc2V0XCIsIHVuZGVmaW5lZCwgb2Zmc2V0KS50b1N0cmluZygpO1xuLyoqXG4gKiBGaWx0ZXIgcmVzb3VyY2VzIHdoZXJlIGF0dHJpYnV0ZSBjb250YWlucyB0aGUgc3BlY2lmaWVkIHZhbHVlLlxuICpcbiAqIEBwYXJhbSB7c3RyaW5nfSBhdHRyaWJ1dGVcbiAqIEBwYXJhbSB7c3RyaW5nIHwgc3RyaW5nW119IHZhbHVlXG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICovXG5RdWVyeS5jb250YWlucyA9IChhdHRyaWJ1dGUsIHZhbHVlKSA9PiBuZXcgUXVlcnkoXCJjb250YWluc1wiLCBhdHRyaWJ1dGUsIHZhbHVlKS50b1N0cmluZygpO1xuLyoqXG4gKiBDb21iaW5lIG11bHRpcGxlIHF1ZXJpZXMgdXNpbmcgbG9naWNhbCBPUiBvcGVyYXRvci5cbiAqXG4gKiBAcGFyYW0ge3N0cmluZ1tdfSBxdWVyaWVzXG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICovXG5RdWVyeS5vciA9IChxdWVyaWVzKSA9PiBuZXcgUXVlcnkoXCJvclwiLCB1bmRlZmluZWQsIHF1ZXJpZXMubWFwKChxdWVyeSkgPT4gSlNPTi5wYXJzZShxdWVyeSkpKS50b1N0cmluZygpO1xuLyoqXG4gKiBDb21iaW5lIG11bHRpcGxlIHF1ZXJpZXMgdXNpbmcgbG9naWNhbCBBTkQgb3BlcmF0b3IuXG4gKlxuICogQHBhcmFtIHtzdHJpbmdbXX0gcXVlcmllc1xuICogQHJldHVybnMge3N0cmluZ31cbiAqL1xuUXVlcnkuYW5kID0gKHF1ZXJpZXMpID0+IG5ldyBRdWVyeShcImFuZFwiLCB1bmRlZmluZWQsIHF1ZXJpZXMubWFwKChxdWVyeSkgPT4gSlNPTi5wYXJzZShxdWVyeSkpKS50b1N0cmluZygpO1xuXG4vKipcbiAqIEV4Y2VwdGlvbiB0aHJvd24gYnkgdGhlICBwYWNrYWdlXG4gKi9cbmNsYXNzIEFwcHdyaXRlRXhjZXB0aW9uIGV4dGVuZHMgRXJyb3Ige1xuICAgIC8qKlxuICAgICAqIEluaXRpYWxpemVzIGEgQXBwd3JpdGUgRXhjZXB0aW9uLlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IG1lc3NhZ2UgLSBUaGUgZXJyb3IgbWVzc2FnZS5cbiAgICAgKiBAcGFyYW0ge251bWJlcn0gY29kZSAtIFRoZSBlcnJvciBjb2RlLiBEZWZhdWx0IGlzIDAuXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IHR5cGUgLSBUaGUgZXJyb3IgdHlwZS4gRGVmYXVsdCBpcyBhbiBlbXB0eSBzdHJpbmcuXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IHJlc3BvbnNlIC0gVGhlIHJlc3BvbnNlIHN0cmluZy4gRGVmYXVsdCBpcyBhbiBlbXB0eSBzdHJpbmcuXG4gICAgICovXG4gICAgY29uc3RydWN0b3IobWVzc2FnZSwgY29kZSA9IDAsIHR5cGUgPSAnJywgcmVzcG9uc2UgPSAnJykge1xuICAgICAgICBzdXBlcihtZXNzYWdlKTtcbiAgICAgICAgdGhpcy5uYW1lID0gJ0FwcHdyaXRlRXhjZXB0aW9uJztcbiAgICAgICAgdGhpcy5tZXNzYWdlID0gbWVzc2FnZTtcbiAgICAgICAgdGhpcy5jb2RlID0gY29kZTtcbiAgICAgICAgdGhpcy50eXBlID0gdHlwZTtcbiAgICAgICAgdGhpcy5yZXNwb25zZSA9IHJlc3BvbnNlO1xuICAgIH1cbn1cbi8qKlxuICogQ2xpZW50IHRoYXQgaGFuZGxlcyByZXF1ZXN0cyB0byBBcHB3cml0ZVxuICovXG5jbGFzcyBDbGllbnQge1xuICAgIGNvbnN0cnVjdG9yKCkge1xuICAgICAgICAvKipcbiAgICAgICAgICogSG9sZHMgY29uZmlndXJhdGlvbiBzdWNoIGFzIHByb2plY3QuXG4gICAgICAgICAqL1xuICAgICAgICB0aGlzLmNvbmZpZyA9IHtcbiAgICAgICAgICAgIGVuZHBvaW50OiAnaHR0cHM6Ly9jbG91ZC5hcHB3cml0ZS5pby92MScsXG4gICAgICAgICAgICBlbmRwb2ludFJlYWx0aW1lOiAnJyxcbiAgICAgICAgICAgIHByb2plY3Q6ICcnLFxuICAgICAgICAgICAgand0OiAnJyxcbiAgICAgICAgICAgIGxvY2FsZTogJycsXG4gICAgICAgICAgICBzZXNzaW9uOiAnJyxcbiAgICAgICAgICAgIGRldmtleTogJycsXG4gICAgICAgIH07XG4gICAgICAgIC8qKlxuICAgICAgICAgKiBDdXN0b20gaGVhZGVycyBmb3IgQVBJIHJlcXVlc3RzLlxuICAgICAgICAgKi9cbiAgICAgICAgdGhpcy5oZWFkZXJzID0ge1xuICAgICAgICAgICAgJ3gtc2RrLW5hbWUnOiAnV2ViJyxcbiAgICAgICAgICAgICd4LXNkay1wbGF0Zm9ybSc6ICdjbGllbnQnLFxuICAgICAgICAgICAgJ3gtc2RrLWxhbmd1YWdlJzogJ3dlYicsXG4gICAgICAgICAgICAneC1zZGstdmVyc2lvbic6ICcxOC4yLjAnLFxuICAgICAgICAgICAgJ1gtQXBwd3JpdGUtUmVzcG9uc2UtRm9ybWF0JzogJzEuNy4wJyxcbiAgICAgICAgfTtcbiAgICAgICAgdGhpcy5yZWFsdGltZSA9IHtcbiAgICAgICAgICAgIHNvY2tldDogdW5kZWZpbmVkLFxuICAgICAgICAgICAgdGltZW91dDogdW5kZWZpbmVkLFxuICAgICAgICAgICAgaGVhcnRiZWF0OiB1bmRlZmluZWQsXG4gICAgICAgICAgICB1cmw6ICcnLFxuICAgICAgICAgICAgY2hhbm5lbHM6IG5ldyBTZXQoKSxcbiAgICAgICAgICAgIHN1YnNjcmlwdGlvbnM6IG5ldyBNYXAoKSxcbiAgICAgICAgICAgIHN1YnNjcmlwdGlvbnNDb3VudGVyOiAwLFxuICAgICAgICAgICAgcmVjb25uZWN0OiB0cnVlLFxuICAgICAgICAgICAgcmVjb25uZWN0QXR0ZW1wdHM6IDAsXG4gICAgICAgICAgICBsYXN0TWVzc2FnZTogdW5kZWZpbmVkLFxuICAgICAgICAgICAgY29ubmVjdDogKCkgPT4ge1xuICAgICAgICAgICAgICAgIGNsZWFyVGltZW91dCh0aGlzLnJlYWx0aW1lLnRpbWVvdXQpO1xuICAgICAgICAgICAgICAgIHRoaXMucmVhbHRpbWUudGltZW91dCA9IHdpbmRvdyA9PT0gbnVsbCB8fCB3aW5kb3cgPT09IHZvaWQgMCA/IHZvaWQgMCA6IHdpbmRvdy5zZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5yZWFsdGltZS5jcmVhdGVTb2NrZXQoKTtcbiAgICAgICAgICAgICAgICB9LCA1MCk7XG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAgZ2V0VGltZW91dDogKCkgPT4ge1xuICAgICAgICAgICAgICAgIHN3aXRjaCAodHJ1ZSkge1xuICAgICAgICAgICAgICAgICAgICBjYXNlIHRoaXMucmVhbHRpbWUucmVjb25uZWN0QXR0ZW1wdHMgPCA1OlxuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIDEwMDA7XG4gICAgICAgICAgICAgICAgICAgIGNhc2UgdGhpcy5yZWFsdGltZS5yZWNvbm5lY3RBdHRlbXB0cyA8IDE1OlxuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIDUwMDA7XG4gICAgICAgICAgICAgICAgICAgIGNhc2UgdGhpcy5yZWFsdGltZS5yZWNvbm5lY3RBdHRlbXB0cyA8IDEwMDpcbiAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiAxMDAwMDtcbiAgICAgICAgICAgICAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiA2MDAwMDtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAgY3JlYXRlSGVhcnRiZWF0OiAoKSA9PiB7XG4gICAgICAgICAgICAgICAgaWYgKHRoaXMucmVhbHRpbWUuaGVhcnRiZWF0KSB7XG4gICAgICAgICAgICAgICAgICAgIGNsZWFyVGltZW91dCh0aGlzLnJlYWx0aW1lLmhlYXJ0YmVhdCk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIHRoaXMucmVhbHRpbWUuaGVhcnRiZWF0ID0gd2luZG93ID09PSBudWxsIHx8IHdpbmRvdyA9PT0gdm9pZCAwID8gdm9pZCAwIDogd2luZG93LnNldEludGVydmFsKCgpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgdmFyIF9hO1xuICAgICAgICAgICAgICAgICAgICAoX2EgPSB0aGlzLnJlYWx0aW1lLnNvY2tldCkgPT09IG51bGwgfHwgX2EgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9hLnNlbmQoSlNPTi5zdHJpbmdpZnkoe1xuICAgICAgICAgICAgICAgICAgICAgICAgdHlwZTogJ3BpbmcnXG4gICAgICAgICAgICAgICAgICAgIH0pKTtcbiAgICAgICAgICAgICAgICB9LCAyMDAwMCk7XG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAgY3JlYXRlU29ja2V0OiAoKSA9PiB7XG4gICAgICAgICAgICAgICAgdmFyIF9hLCBfYiwgX2M7XG4gICAgICAgICAgICAgICAgaWYgKHRoaXMucmVhbHRpbWUuY2hhbm5lbHMuc2l6ZSA8IDEpIHtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5yZWFsdGltZS5yZWNvbm5lY3QgPSBmYWxzZTtcbiAgICAgICAgICAgICAgICAgICAgKF9hID0gdGhpcy5yZWFsdGltZS5zb2NrZXQpID09PSBudWxsIHx8IF9hID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfYS5jbG9zZSgpO1xuICAgICAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGNvbnN0IGNoYW5uZWxzID0gbmV3IFVSTFNlYXJjaFBhcmFtcygpO1xuICAgICAgICAgICAgICAgIGNoYW5uZWxzLnNldCgncHJvamVjdCcsIHRoaXMuY29uZmlnLnByb2plY3QpO1xuICAgICAgICAgICAgICAgIHRoaXMucmVhbHRpbWUuY2hhbm5lbHMuZm9yRWFjaChjaGFubmVsID0+IHtcbiAgICAgICAgICAgICAgICAgICAgY2hhbm5lbHMuYXBwZW5kKCdjaGFubmVsc1tdJywgY2hhbm5lbCk7XG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgY29uc3QgdXJsID0gdGhpcy5jb25maWcuZW5kcG9pbnRSZWFsdGltZSArICcvcmVhbHRpbWU/JyArIGNoYW5uZWxzLnRvU3RyaW5nKCk7XG4gICAgICAgICAgICAgICAgaWYgKHVybCAhPT0gdGhpcy5yZWFsdGltZS51cmwgfHwgLy8gQ2hlY2sgaWYgVVJMIGlzIHByZXNlbnRcbiAgICAgICAgICAgICAgICAgICAgIXRoaXMucmVhbHRpbWUuc29ja2V0IHx8IC8vIENoZWNrIGlmIFdlYlNvY2tldCBoYXMgbm90IGJlZW4gY3JlYXRlZFxuICAgICAgICAgICAgICAgICAgICAoKF9iID0gdGhpcy5yZWFsdGltZS5zb2NrZXQpID09PSBudWxsIHx8IF9iID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfYi5yZWFkeVN0YXRlKSA+IFdlYlNvY2tldC5PUEVOIC8vIENoZWNrIGlmIFdlYlNvY2tldCBpcyBDTE9TSU5HICgzKSBvciBDTE9TRUQgKDQpXG4gICAgICAgICAgICAgICAgKSB7XG4gICAgICAgICAgICAgICAgICAgIGlmICh0aGlzLnJlYWx0aW1lLnNvY2tldCAmJlxuICAgICAgICAgICAgICAgICAgICAgICAgKChfYyA9IHRoaXMucmVhbHRpbWUuc29ja2V0KSA9PT0gbnVsbCB8fCBfYyA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2MucmVhZHlTdGF0ZSkgPCBXZWJTb2NrZXQuQ0xPU0lORyAvLyBDbG9zZSBXZWJTb2NrZXQgaWYgaXQgaXMgQ09OTkVDVElORyAoMCkgb3IgT1BFTiAoMSlcbiAgICAgICAgICAgICAgICAgICAgKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICB0aGlzLnJlYWx0aW1lLnJlY29ubmVjdCA9IGZhbHNlO1xuICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5yZWFsdGltZS5zb2NrZXQuY2xvc2UoKTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICB0aGlzLnJlYWx0aW1lLnVybCA9IHVybDtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5yZWFsdGltZS5zb2NrZXQgPSBuZXcgV2ViU29ja2V0KHVybCk7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMucmVhbHRpbWUuc29ja2V0LmFkZEV2ZW50TGlzdGVuZXIoJ21lc3NhZ2UnLCB0aGlzLnJlYWx0aW1lLm9uTWVzc2FnZSk7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMucmVhbHRpbWUuc29ja2V0LmFkZEV2ZW50TGlzdGVuZXIoJ29wZW4nLCBfZXZlbnQgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5yZWFsdGltZS5yZWNvbm5lY3RBdHRlbXB0cyA9IDA7XG4gICAgICAgICAgICAgICAgICAgICAgICB0aGlzLnJlYWx0aW1lLmNyZWF0ZUhlYXJ0YmVhdCgpO1xuICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5yZWFsdGltZS5zb2NrZXQuYWRkRXZlbnRMaXN0ZW5lcignY2xvc2UnLCBldmVudCA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICB2YXIgX2EsIF9iLCBfYztcbiAgICAgICAgICAgICAgICAgICAgICAgIGlmICghdGhpcy5yZWFsdGltZS5yZWNvbm5lY3QgfHxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAoKChfYiA9IChfYSA9IHRoaXMucmVhbHRpbWUpID09PSBudWxsIHx8IF9hID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfYS5sYXN0TWVzc2FnZSkgPT09IG51bGwgfHwgX2IgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9iLnR5cGUpID09PSAnZXJyb3InICYmIC8vIENoZWNrIGlmIGxhc3QgbWVzc2FnZSB3YXMgb2YgdHlwZSBlcnJvclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAoKF9jID0gdGhpcy5yZWFsdGltZSkgPT09IG51bGwgfHwgX2MgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9jLmxhc3RNZXNzYWdlLmRhdGEpLmNvZGUgPT09IDEwMDggLy8gQ2hlY2sgZm9yIHBvbGljeSB2aW9sYXRpb24gMTAwOFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICkpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLnJlYWx0aW1lLnJlY29ubmVjdCA9IHRydWU7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgdGltZW91dCA9IHRoaXMucmVhbHRpbWUuZ2V0VGltZW91dCgpO1xuICAgICAgICAgICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcihgUmVhbHRpbWUgZ290IGRpc2Nvbm5lY3RlZC4gUmVjb25uZWN0IHdpbGwgYmUgYXR0ZW1wdGVkIGluICR7dGltZW91dCAvIDEwMDB9IHNlY29uZHMuYCwgZXZlbnQucmVhc29uKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMucmVhbHRpbWUucmVjb25uZWN0QXR0ZW1wdHMrKztcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLnJlYWx0aW1lLmNyZWF0ZVNvY2tldCgpO1xuICAgICAgICAgICAgICAgICAgICAgICAgfSwgdGltZW91dCk7XG4gICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICBvbk1lc3NhZ2U6IChldmVudCkgPT4ge1xuICAgICAgICAgICAgICAgIHZhciBfYSwgX2I7XG4gICAgICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgbWVzc2FnZSA9IEpTT04ucGFyc2UoZXZlbnQuZGF0YSk7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMucmVhbHRpbWUubGFzdE1lc3NhZ2UgPSBtZXNzYWdlO1xuICAgICAgICAgICAgICAgICAgICBzd2l0Y2ggKG1lc3NhZ2UudHlwZSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgY2FzZSAnY29ubmVjdGVkJzpcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBjb29raWUgPSBKU09OLnBhcnNlKChfYSA9IHdpbmRvdy5sb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnY29va2llRmFsbGJhY2snKSkgIT09IG51bGwgJiYgX2EgIT09IHZvaWQgMCA/IF9hIDogJ3t9Jyk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3Qgc2Vzc2lvbiA9IGNvb2tpZSA9PT0gbnVsbCB8fCBjb29raWUgPT09IHZvaWQgMCA/IHZvaWQgMCA6IGNvb2tpZVtgYV9zZXNzaW9uXyR7dGhpcy5jb25maWcucHJvamVjdH1gXTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBtZXNzYWdlRGF0YSA9IG1lc3NhZ2UuZGF0YTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAoc2Vzc2lvbiAmJiAhbWVzc2FnZURhdGEudXNlcikge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAoX2IgPSB0aGlzLnJlYWx0aW1lLnNvY2tldCkgPT09IG51bGwgfHwgX2IgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9iLnNlbmQoSlNPTi5zdHJpbmdpZnkoe1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZTogJ2F1dGhlbnRpY2F0aW9uJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRhdGE6IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZXNzaW9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgICAgICAgICBjYXNlICdldmVudCc6XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgbGV0IGRhdGEgPSBtZXNzYWdlLmRhdGE7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGRhdGEgPT09IG51bGwgfHwgZGF0YSA9PT0gdm9pZCAwID8gdm9pZCAwIDogZGF0YS5jaGFubmVscykge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBpc1N1YnNjcmliZWQgPSBkYXRhLmNoYW5uZWxzLnNvbWUoY2hhbm5lbCA9PiB0aGlzLnJlYWx0aW1lLmNoYW5uZWxzLmhhcyhjaGFubmVsKSk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmICghaXNTdWJzY3JpYmVkKVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLnJlYWx0aW1lLnN1YnNjcmlwdGlvbnMuZm9yRWFjaChzdWJzY3JpcHRpb24gPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGRhdGEuY2hhbm5lbHMuc29tZShjaGFubmVsID0+IHN1YnNjcmlwdGlvbi5jaGFubmVscy5pbmNsdWRlcyhjaGFubmVsKSkpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHN1YnNjcmlwdGlvbi5jYWxsYmFjayhkYXRhKSk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICAgICAgICAgIGNhc2UgJ3BvbmcnOlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJyZWFrOyAvLyBIYW5kbGUgcG9uZyByZXNwb25zZSBpZiBuZWVkZWRcbiAgICAgICAgICAgICAgICAgICAgICAgIGNhc2UgJ2Vycm9yJzpcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aHJvdyBtZXNzYWdlLmRhdGE7XG4gICAgICAgICAgICAgICAgICAgICAgICBkZWZhdWx0OlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGNhdGNoIChlKSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoZSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIGNsZWFuVXA6IGNoYW5uZWxzID0+IHtcbiAgICAgICAgICAgICAgICB0aGlzLnJlYWx0aW1lLmNoYW5uZWxzLmZvckVhY2goY2hhbm5lbCA9PiB7XG4gICAgICAgICAgICAgICAgICAgIGlmIChjaGFubmVscy5pbmNsdWRlcyhjaGFubmVsKSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgbGV0IGZvdW5kID0gQXJyYXkuZnJvbSh0aGlzLnJlYWx0aW1lLnN1YnNjcmlwdGlvbnMpLnNvbWUoKFtfa2V5LCBzdWJzY3JpcHRpb25dKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHN1YnNjcmlwdGlvbi5jaGFubmVscy5pbmNsdWRlcyhjaGFubmVsKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKCFmb3VuZCkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMucmVhbHRpbWUuY2hhbm5lbHMuZGVsZXRlKGNoYW5uZWwpO1xuICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH07XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFNldCBFbmRwb2ludFxuICAgICAqXG4gICAgICogWW91ciBwcm9qZWN0IGVuZHBvaW50XG4gICAgICpcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gZW5kcG9pbnRcbiAgICAgKlxuICAgICAqIEByZXR1cm5zIHt0aGlzfVxuICAgICAqL1xuICAgIHNldEVuZHBvaW50KGVuZHBvaW50KSB7XG4gICAgICAgIGlmICghZW5kcG9pbnQuc3RhcnRzV2l0aCgnaHR0cDovLycpICYmICFlbmRwb2ludC5zdGFydHNXaXRoKCdodHRwczovLycpKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ0ludmFsaWQgZW5kcG9pbnQgVVJMOiAnICsgZW5kcG9pbnQpO1xuICAgICAgICB9XG4gICAgICAgIHRoaXMuY29uZmlnLmVuZHBvaW50ID0gZW5kcG9pbnQ7XG4gICAgICAgIHRoaXMuY29uZmlnLmVuZHBvaW50UmVhbHRpbWUgPSBlbmRwb2ludC5yZXBsYWNlKCdodHRwczovLycsICd3c3M6Ly8nKS5yZXBsYWNlKCdodHRwOi8vJywgJ3dzOi8vJyk7XG4gICAgICAgIHJldHVybiB0aGlzO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBTZXQgUmVhbHRpbWUgRW5kcG9pbnRcbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBlbmRwb2ludFJlYWx0aW1lXG4gICAgICpcbiAgICAgKiBAcmV0dXJucyB7dGhpc31cbiAgICAgKi9cbiAgICBzZXRFbmRwb2ludFJlYWx0aW1lKGVuZHBvaW50UmVhbHRpbWUpIHtcbiAgICAgICAgaWYgKCFlbmRwb2ludFJlYWx0aW1lLnN0YXJ0c1dpdGgoJ3dzOi8vJykgJiYgIWVuZHBvaW50UmVhbHRpbWUuc3RhcnRzV2l0aCgnd3NzOi8vJykpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignSW52YWxpZCByZWFsdGltZSBlbmRwb2ludCBVUkw6ICcgKyBlbmRwb2ludFJlYWx0aW1lKTtcbiAgICAgICAgfVxuICAgICAgICB0aGlzLmNvbmZpZy5lbmRwb2ludFJlYWx0aW1lID0gZW5kcG9pbnRSZWFsdGltZTtcbiAgICAgICAgcmV0dXJuIHRoaXM7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFNldCBQcm9qZWN0XG4gICAgICpcbiAgICAgKiBZb3VyIHByb2plY3QgSURcbiAgICAgKlxuICAgICAqIEBwYXJhbSB2YWx1ZSBzdHJpbmdcbiAgICAgKlxuICAgICAqIEByZXR1cm4ge3RoaXN9XG4gICAgICovXG4gICAgc2V0UHJvamVjdCh2YWx1ZSkge1xuICAgICAgICB0aGlzLmhlYWRlcnNbJ1gtQXBwd3JpdGUtUHJvamVjdCddID0gdmFsdWU7XG4gICAgICAgIHRoaXMuY29uZmlnLnByb2plY3QgPSB2YWx1ZTtcbiAgICAgICAgcmV0dXJuIHRoaXM7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFNldCBKV1RcbiAgICAgKlxuICAgICAqIFlvdXIgc2VjcmV0IEpTT04gV2ViIFRva2VuXG4gICAgICpcbiAgICAgKiBAcGFyYW0gdmFsdWUgc3RyaW5nXG4gICAgICpcbiAgICAgKiBAcmV0dXJuIHt0aGlzfVxuICAgICAqL1xuICAgIHNldEpXVCh2YWx1ZSkge1xuICAgICAgICB0aGlzLmhlYWRlcnNbJ1gtQXBwd3JpdGUtSldUJ10gPSB2YWx1ZTtcbiAgICAgICAgdGhpcy5jb25maWcuand0ID0gdmFsdWU7XG4gICAgICAgIHJldHVybiB0aGlzO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBTZXQgTG9jYWxlXG4gICAgICpcbiAgICAgKiBAcGFyYW0gdmFsdWUgc3RyaW5nXG4gICAgICpcbiAgICAgKiBAcmV0dXJuIHt0aGlzfVxuICAgICAqL1xuICAgIHNldExvY2FsZSh2YWx1ZSkge1xuICAgICAgICB0aGlzLmhlYWRlcnNbJ1gtQXBwd3JpdGUtTG9jYWxlJ10gPSB2YWx1ZTtcbiAgICAgICAgdGhpcy5jb25maWcubG9jYWxlID0gdmFsdWU7XG4gICAgICAgIHJldHVybiB0aGlzO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBTZXQgU2Vzc2lvblxuICAgICAqXG4gICAgICogVGhlIHVzZXIgc2Vzc2lvbiB0byBhdXRoZW50aWNhdGUgd2l0aFxuICAgICAqXG4gICAgICogQHBhcmFtIHZhbHVlIHN0cmluZ1xuICAgICAqXG4gICAgICogQHJldHVybiB7dGhpc31cbiAgICAgKi9cbiAgICBzZXRTZXNzaW9uKHZhbHVlKSB7XG4gICAgICAgIHRoaXMuaGVhZGVyc1snWC1BcHB3cml0ZS1TZXNzaW9uJ10gPSB2YWx1ZTtcbiAgICAgICAgdGhpcy5jb25maWcuc2Vzc2lvbiA9IHZhbHVlO1xuICAgICAgICByZXR1cm4gdGhpcztcbiAgICB9XG4gICAgLyoqXG4gICAgICogU2V0IERldktleVxuICAgICAqXG4gICAgICogWW91ciBzZWNyZXQgZGV2IEFQSSBrZXlcbiAgICAgKlxuICAgICAqIEBwYXJhbSB2YWx1ZSBzdHJpbmdcbiAgICAgKlxuICAgICAqIEByZXR1cm4ge3RoaXN9XG4gICAgICovXG4gICAgc2V0RGV2S2V5KHZhbHVlKSB7XG4gICAgICAgIHRoaXMuaGVhZGVyc1snWC1BcHB3cml0ZS1EZXYtS2V5J10gPSB2YWx1ZTtcbiAgICAgICAgdGhpcy5jb25maWcuZGV2a2V5ID0gdmFsdWU7XG4gICAgICAgIHJldHVybiB0aGlzO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBTdWJzY3JpYmVzIHRvIEFwcHdyaXRlIGV2ZW50cyBhbmQgcGFzc2VzIHlvdSB0aGUgcGF5bG9hZCBpbiByZWFsdGltZS5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nfHN0cmluZ1tdfSBjaGFubmVsc1xuICAgICAqIENoYW5uZWwgdG8gc3Vic2NyaWJlIC0gcGFzcyBhIHNpbmdsZSBjaGFubmVsIGFzIGEgc3RyaW5nIG9yIG11bHRpcGxlIHdpdGggYW4gYXJyYXkgb2Ygc3RyaW5ncy5cbiAgICAgKlxuICAgICAqIFBvc3NpYmxlIGNoYW5uZWxzIGFyZTpcbiAgICAgKiAtIGFjY291bnRcbiAgICAgKiAtIGNvbGxlY3Rpb25zXG4gICAgICogLSBjb2xsZWN0aW9ucy5bSURdXG4gICAgICogLSBjb2xsZWN0aW9ucy5bSURdLmRvY3VtZW50c1xuICAgICAqIC0gZG9jdW1lbnRzXG4gICAgICogLSBkb2N1bWVudHMuW0lEXVxuICAgICAqIC0gZmlsZXNcbiAgICAgKiAtIGZpbGVzLltJRF1cbiAgICAgKiAtIGV4ZWN1dGlvbnNcbiAgICAgKiAtIGV4ZWN1dGlvbnMuW0lEXVxuICAgICAqIC0gZnVuY3Rpb25zLltJRF1cbiAgICAgKiAtIHRlYW1zXG4gICAgICogLSB0ZWFtcy5bSURdXG4gICAgICogLSBtZW1iZXJzaGlwc1xuICAgICAqIC0gbWVtYmVyc2hpcHMuW0lEXVxuICAgICAqIEBwYXJhbSB7KHBheWxvYWQ6IFJlYWx0aW1lTWVzc2FnZSkgPT4gdm9pZH0gY2FsbGJhY2sgSXMgY2FsbGVkIG9uIGV2ZXJ5IHJlYWx0aW1lIHVwZGF0ZS5cbiAgICAgKiBAcmV0dXJucyB7KCkgPT4gdm9pZH0gVW5zdWJzY3JpYmVzIGZyb20gZXZlbnRzLlxuICAgICAqL1xuICAgIHN1YnNjcmliZShjaGFubmVscywgY2FsbGJhY2spIHtcbiAgICAgICAgbGV0IGNoYW5uZWxBcnJheSA9IHR5cGVvZiBjaGFubmVscyA9PT0gJ3N0cmluZycgPyBbY2hhbm5lbHNdIDogY2hhbm5lbHM7XG4gICAgICAgIGNoYW5uZWxBcnJheS5mb3JFYWNoKGNoYW5uZWwgPT4gdGhpcy5yZWFsdGltZS5jaGFubmVscy5hZGQoY2hhbm5lbCkpO1xuICAgICAgICBjb25zdCBjb3VudGVyID0gdGhpcy5yZWFsdGltZS5zdWJzY3JpcHRpb25zQ291bnRlcisrO1xuICAgICAgICB0aGlzLnJlYWx0aW1lLnN1YnNjcmlwdGlvbnMuc2V0KGNvdW50ZXIsIHtcbiAgICAgICAgICAgIGNoYW5uZWxzOiBjaGFubmVsQXJyYXksXG4gICAgICAgICAgICBjYWxsYmFja1xuICAgICAgICB9KTtcbiAgICAgICAgdGhpcy5yZWFsdGltZS5jb25uZWN0KCk7XG4gICAgICAgIHJldHVybiAoKSA9PiB7XG4gICAgICAgICAgICB0aGlzLnJlYWx0aW1lLnN1YnNjcmlwdGlvbnMuZGVsZXRlKGNvdW50ZXIpO1xuICAgICAgICAgICAgdGhpcy5yZWFsdGltZS5jbGVhblVwKGNoYW5uZWxBcnJheSk7XG4gICAgICAgICAgICB0aGlzLnJlYWx0aW1lLmNvbm5lY3QoKTtcbiAgICAgICAgfTtcbiAgICB9XG4gICAgcHJlcGFyZVJlcXVlc3QobWV0aG9kLCB1cmwsIGhlYWRlcnMgPSB7fSwgcGFyYW1zID0ge30pIHtcbiAgICAgICAgbWV0aG9kID0gbWV0aG9kLnRvVXBwZXJDYXNlKCk7XG4gICAgICAgIGhlYWRlcnMgPSBPYmplY3QuYXNzaWduKHt9LCB0aGlzLmhlYWRlcnMsIGhlYWRlcnMpO1xuICAgICAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcgJiYgd2luZG93LmxvY2FsU3RvcmFnZSkge1xuICAgICAgICAgICAgY29uc3QgY29va2llRmFsbGJhY2sgPSB3aW5kb3cubG9jYWxTdG9yYWdlLmdldEl0ZW0oJ2Nvb2tpZUZhbGxiYWNrJyk7XG4gICAgICAgICAgICBpZiAoY29va2llRmFsbGJhY2spIHtcbiAgICAgICAgICAgICAgICBoZWFkZXJzWydYLUZhbGxiYWNrLUNvb2tpZXMnXSA9IGNvb2tpZUZhbGxiYWNrO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIGxldCBvcHRpb25zID0ge1xuICAgICAgICAgICAgbWV0aG9kLFxuICAgICAgICAgICAgaGVhZGVycyxcbiAgICAgICAgfTtcbiAgICAgICAgaWYgKGhlYWRlcnNbJ1gtQXBwd3JpdGUtRGV2LUtleSddID09PSB1bmRlZmluZWQpIHtcbiAgICAgICAgICAgIG9wdGlvbnMuY3JlZGVudGlhbHMgPSAnaW5jbHVkZSc7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKG1ldGhvZCA9PT0gJ0dFVCcpIHtcbiAgICAgICAgICAgIGZvciAoY29uc3QgW2tleSwgdmFsdWVdIG9mIE9iamVjdC5lbnRyaWVzKENsaWVudC5mbGF0dGVuKHBhcmFtcykpKSB7XG4gICAgICAgICAgICAgICAgdXJsLnNlYXJjaFBhcmFtcy5hcHBlbmQoa2V5LCB2YWx1ZSk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICBzd2l0Y2ggKGhlYWRlcnNbJ2NvbnRlbnQtdHlwZSddKSB7XG4gICAgICAgICAgICAgICAgY2FzZSAnYXBwbGljYXRpb24vanNvbic6XG4gICAgICAgICAgICAgICAgICAgIG9wdGlvbnMuYm9keSA9IEpTT04uc3RyaW5naWZ5KHBhcmFtcyk7XG4gICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgICAgIGNhc2UgJ211bHRpcGFydC9mb3JtLWRhdGEnOlxuICAgICAgICAgICAgICAgICAgICBjb25zdCBmb3JtRGF0YSA9IG5ldyBGb3JtRGF0YSgpO1xuICAgICAgICAgICAgICAgICAgICBmb3IgKGNvbnN0IFtrZXksIHZhbHVlXSBvZiBPYmplY3QuZW50cmllcyhwYXJhbXMpKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAodmFsdWUgaW5zdGFuY2VvZiBGaWxlKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZm9ybURhdGEuYXBwZW5kKGtleSwgdmFsdWUsIHZhbHVlLm5hbWUpO1xuICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgZWxzZSBpZiAoQXJyYXkuaXNBcnJheSh2YWx1ZSkpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb3IgKGNvbnN0IG5lc3RlZFZhbHVlIG9mIHZhbHVlKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZvcm1EYXRhLmFwcGVuZChgJHtrZXl9W11gLCBuZXN0ZWRWYWx1ZSk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZm9ybURhdGEuYXBwZW5kKGtleSwgdmFsdWUpO1xuICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIG9wdGlvbnMuYm9keSA9IGZvcm1EYXRhO1xuICAgICAgICAgICAgICAgICAgICBkZWxldGUgaGVhZGVyc1snY29udGVudC10eXBlJ107XG4gICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIHJldHVybiB7IHVyaTogdXJsLnRvU3RyaW5nKCksIG9wdGlvbnMgfTtcbiAgICB9XG4gICAgY2h1bmtlZFVwbG9hZChtZXRob2QsIHVybCwgaGVhZGVycyA9IHt9LCBvcmlnaW5hbFBheWxvYWQgPSB7fSwgb25Qcm9ncmVzcykge1xuICAgICAgICB2YXIgX2E7XG4gICAgICAgIHJldHVybiBfX2F3YWl0ZXIodGhpcywgdm9pZCAwLCB2b2lkIDAsIGZ1bmN0aW9uKiAoKSB7XG4gICAgICAgICAgICBjb25zdCBbZmlsZVBhcmFtLCBmaWxlXSA9IChfYSA9IE9iamVjdC5lbnRyaWVzKG9yaWdpbmFsUGF5bG9hZCkuZmluZCgoW18sIHZhbHVlXSkgPT4gdmFsdWUgaW5zdGFuY2VvZiBGaWxlKSkgIT09IG51bGwgJiYgX2EgIT09IHZvaWQgMCA/IF9hIDogW107XG4gICAgICAgICAgICBpZiAoIWZpbGUgfHwgIWZpbGVQYXJhbSkge1xuICAgICAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcignRmlsZSBub3QgZm91bmQgaW4gcGF5bG9hZCcpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKGZpbGUuc2l6ZSA8PSBDbGllbnQuQ0hVTktfU0laRSkge1xuICAgICAgICAgICAgICAgIHJldHVybiB5aWVsZCB0aGlzLmNhbGwobWV0aG9kLCB1cmwsIGhlYWRlcnMsIG9yaWdpbmFsUGF5bG9hZCk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBsZXQgc3RhcnQgPSAwO1xuICAgICAgICAgICAgbGV0IHJlc3BvbnNlID0gbnVsbDtcbiAgICAgICAgICAgIHdoaWxlIChzdGFydCA8IGZpbGUuc2l6ZSkge1xuICAgICAgICAgICAgICAgIGxldCBlbmQgPSBzdGFydCArIENsaWVudC5DSFVOS19TSVpFOyAvLyBQcmVwYXJlIGVuZCBmb3IgdGhlIG5leHQgY2h1bmtcbiAgICAgICAgICAgICAgICBpZiAoZW5kID49IGZpbGUuc2l6ZSkge1xuICAgICAgICAgICAgICAgICAgICBlbmQgPSBmaWxlLnNpemU7IC8vIEFkanVzdCBmb3IgdGhlIGxhc3QgY2h1bmsgdG8gaW5jbHVkZSB0aGUgbGFzdCBieXRlXG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGhlYWRlcnNbJ2NvbnRlbnQtcmFuZ2UnXSA9IGBieXRlcyAke3N0YXJ0fS0ke2VuZCAtIDF9LyR7ZmlsZS5zaXplfWA7XG4gICAgICAgICAgICAgICAgY29uc3QgY2h1bmsgPSBmaWxlLnNsaWNlKHN0YXJ0LCBlbmQpO1xuICAgICAgICAgICAgICAgIGxldCBwYXlsb2FkID0gT2JqZWN0LmFzc2lnbih7fSwgb3JpZ2luYWxQYXlsb2FkKTtcbiAgICAgICAgICAgICAgICBwYXlsb2FkW2ZpbGVQYXJhbV0gPSBuZXcgRmlsZShbY2h1bmtdLCBmaWxlLm5hbWUpO1xuICAgICAgICAgICAgICAgIHJlc3BvbnNlID0geWllbGQgdGhpcy5jYWxsKG1ldGhvZCwgdXJsLCBoZWFkZXJzLCBwYXlsb2FkKTtcbiAgICAgICAgICAgICAgICBpZiAob25Qcm9ncmVzcyAmJiB0eXBlb2Ygb25Qcm9ncmVzcyA9PT0gJ2Z1bmN0aW9uJykge1xuICAgICAgICAgICAgICAgICAgICBvblByb2dyZXNzKHtcbiAgICAgICAgICAgICAgICAgICAgICAgICRpZDogcmVzcG9uc2UuJGlkLFxuICAgICAgICAgICAgICAgICAgICAgICAgcHJvZ3Jlc3M6IE1hdGgucm91bmQoKGVuZCAvIGZpbGUuc2l6ZSkgKiAxMDApLFxuICAgICAgICAgICAgICAgICAgICAgICAgc2l6ZVVwbG9hZGVkOiBlbmQsXG4gICAgICAgICAgICAgICAgICAgICAgICBjaHVua3NUb3RhbDogTWF0aC5jZWlsKGZpbGUuc2l6ZSAvIENsaWVudC5DSFVOS19TSVpFKSxcbiAgICAgICAgICAgICAgICAgICAgICAgIGNodW5rc1VwbG9hZGVkOiBNYXRoLmNlaWwoZW5kIC8gQ2xpZW50LkNIVU5LX1NJWkUpXG4gICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBpZiAocmVzcG9uc2UgJiYgcmVzcG9uc2UuJGlkKSB7XG4gICAgICAgICAgICAgICAgICAgIGhlYWRlcnNbJ3gtYXBwd3JpdGUtaWQnXSA9IHJlc3BvbnNlLiRpZDtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgc3RhcnQgPSBlbmQ7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZXR1cm4gcmVzcG9uc2U7XG4gICAgICAgIH0pO1xuICAgIH1cbiAgICBwaW5nKCkge1xuICAgICAgICByZXR1cm4gX19hd2FpdGVyKHRoaXMsIHZvaWQgMCwgdm9pZCAwLCBmdW5jdGlvbiogKCkge1xuICAgICAgICAgICAgcmV0dXJuIHRoaXMuY2FsbCgnR0VUJywgbmV3IFVSTCh0aGlzLmNvbmZpZy5lbmRwb2ludCArICcvcGluZycpKTtcbiAgICAgICAgfSk7XG4gICAgfVxuICAgIGNhbGwobWV0aG9kLCB1cmwsIGhlYWRlcnMgPSB7fSwgcGFyYW1zID0ge30sIHJlc3BvbnNlVHlwZSA9ICdqc29uJykge1xuICAgICAgICB2YXIgX2EsIF9iO1xuICAgICAgICByZXR1cm4gX19hd2FpdGVyKHRoaXMsIHZvaWQgMCwgdm9pZCAwLCBmdW5jdGlvbiogKCkge1xuICAgICAgICAgICAgY29uc3QgeyB1cmksIG9wdGlvbnMgfSA9IHRoaXMucHJlcGFyZVJlcXVlc3QobWV0aG9kLCB1cmwsIGhlYWRlcnMsIHBhcmFtcyk7XG4gICAgICAgICAgICBsZXQgZGF0YSA9IG51bGw7XG4gICAgICAgICAgICBjb25zdCByZXNwb25zZSA9IHlpZWxkIGZldGNoKHVyaSwgb3B0aW9ucyk7XG4gICAgICAgICAgICAvLyB0eXBlIG9wYXF1ZTogTm8tQ09SUywgZGlmZmVyZW50LW9yaWdpbiByZXNwb25zZSAoQ09SUy1pc3N1ZSlcbiAgICAgICAgICAgIGlmIChyZXNwb25zZS50eXBlID09PSAnb3BhcXVlJykge1xuICAgICAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbihgSW52YWxpZCBPcmlnaW4uIFJlZ2lzdGVyIHlvdXIgbmV3IGNsaWVudCAoJHt3aW5kb3cubG9jYXRpb24uaG9zdH0pIGFzIGEgbmV3IFdlYiBwbGF0Zm9ybSBvbiB5b3VyIHByb2plY3QgY29uc29sZSBkYXNoYm9hcmRgLCA0MDMsIFwiZm9yYmlkZGVuXCIsIFwiXCIpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY29uc3Qgd2FybmluZ3MgPSByZXNwb25zZS5oZWFkZXJzLmdldCgneC1hcHB3cml0ZS13YXJuaW5nJyk7XG4gICAgICAgICAgICBpZiAod2FybmluZ3MpIHtcbiAgICAgICAgICAgICAgICB3YXJuaW5ncy5zcGxpdCgnOycpLmZvckVhY2goKHdhcm5pbmcpID0+IGNvbnNvbGUud2FybignV2FybmluZzogJyArIHdhcm5pbmcpKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmICgoX2EgPSByZXNwb25zZS5oZWFkZXJzLmdldCgnY29udGVudC10eXBlJykpID09PSBudWxsIHx8IF9hID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfYS5pbmNsdWRlcygnYXBwbGljYXRpb24vanNvbicpKSB7XG4gICAgICAgICAgICAgICAgZGF0YSA9IHlpZWxkIHJlc3BvbnNlLmpzb24oKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2UgaWYgKHJlc3BvbnNlVHlwZSA9PT0gJ2FycmF5QnVmZmVyJykge1xuICAgICAgICAgICAgICAgIGRhdGEgPSB5aWVsZCByZXNwb25zZS5hcnJheUJ1ZmZlcigpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgZGF0YSA9IHtcbiAgICAgICAgICAgICAgICAgICAgbWVzc2FnZTogeWllbGQgcmVzcG9uc2UudGV4dCgpXG4gICAgICAgICAgICAgICAgfTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmICg0MDAgPD0gcmVzcG9uc2Uuc3RhdHVzKSB7XG4gICAgICAgICAgICAgICAgbGV0IHJlc3BvbnNlVGV4dCA9ICcnO1xuICAgICAgICAgICAgICAgIGlmICgoKF9iID0gcmVzcG9uc2UuaGVhZGVycy5nZXQoJ2NvbnRlbnQtdHlwZScpKSA9PT0gbnVsbCB8fCBfYiA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2IuaW5jbHVkZXMoJ2FwcGxpY2F0aW9uL2pzb24nKSkgfHwgcmVzcG9uc2VUeXBlID09PSAnYXJyYXlCdWZmZXInKSB7XG4gICAgICAgICAgICAgICAgICAgIHJlc3BvbnNlVGV4dCA9IEpTT04uc3RyaW5naWZ5KGRhdGEpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICAgICAgcmVzcG9uc2VUZXh0ID0gZGF0YSA9PT0gbnVsbCB8fCBkYXRhID09PSB2b2lkIDAgPyB2b2lkIDAgOiBkYXRhLm1lc3NhZ2U7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbihkYXRhID09PSBudWxsIHx8IGRhdGEgPT09IHZvaWQgMCA/IHZvaWQgMCA6IGRhdGEubWVzc2FnZSwgcmVzcG9uc2Uuc3RhdHVzLCBkYXRhID09PSBudWxsIHx8IGRhdGEgPT09IHZvaWQgMCA/IHZvaWQgMCA6IGRhdGEudHlwZSwgcmVzcG9uc2VUZXh0KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNvbnN0IGNvb2tpZUZhbGxiYWNrID0gcmVzcG9uc2UuaGVhZGVycy5nZXQoJ1gtRmFsbGJhY2stQ29va2llcycpO1xuICAgICAgICAgICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnICYmIHdpbmRvdy5sb2NhbFN0b3JhZ2UgJiYgY29va2llRmFsbGJhY2spIHtcbiAgICAgICAgICAgICAgICB3aW5kb3cuY29uc29sZS53YXJuKCdBcHB3cml0ZSBpcyB1c2luZyBsb2NhbFN0b3JhZ2UgZm9yIHNlc3Npb24gbWFuYWdlbWVudC4gSW5jcmVhc2UgeW91ciBzZWN1cml0eSBieSBhZGRpbmcgYSBjdXN0b20gZG9tYWluIGFzIHlvdXIgQVBJIGVuZHBvaW50LicpO1xuICAgICAgICAgICAgICAgIHdpbmRvdy5sb2NhbFN0b3JhZ2Uuc2V0SXRlbSgnY29va2llRmFsbGJhY2snLCBjb29raWVGYWxsYmFjayk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZXR1cm4gZGF0YTtcbiAgICAgICAgfSk7XG4gICAgfVxuICAgIHN0YXRpYyBmbGF0dGVuKGRhdGEsIHByZWZpeCA9ICcnKSB7XG4gICAgICAgIGxldCBvdXRwdXQgPSB7fTtcbiAgICAgICAgZm9yIChjb25zdCBba2V5LCB2YWx1ZV0gb2YgT2JqZWN0LmVudHJpZXMoZGF0YSkpIHtcbiAgICAgICAgICAgIGxldCBmaW5hbEtleSA9IHByZWZpeCA/IHByZWZpeCArICdbJyArIGtleSArICddJyA6IGtleTtcbiAgICAgICAgICAgIGlmIChBcnJheS5pc0FycmF5KHZhbHVlKSkge1xuICAgICAgICAgICAgICAgIG91dHB1dCA9IE9iamVjdC5hc3NpZ24oT2JqZWN0LmFzc2lnbih7fSwgb3V0cHV0KSwgQ2xpZW50LmZsYXR0ZW4odmFsdWUsIGZpbmFsS2V5KSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICBvdXRwdXRbZmluYWxLZXldID0gdmFsdWU7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIG91dHB1dDtcbiAgICB9XG59XG5DbGllbnQuQ0hVTktfU0laRSA9IDEwMjQgKiAxMDI0ICogNTtcblxuY2xhc3MgU2VydmljZSB7XG4gICAgY29uc3RydWN0b3IoY2xpZW50KSB7XG4gICAgICAgIHRoaXMuY2xpZW50ID0gY2xpZW50O1xuICAgIH1cbiAgICBzdGF0aWMgZmxhdHRlbihkYXRhLCBwcmVmaXggPSAnJykge1xuICAgICAgICBsZXQgb3V0cHV0ID0ge307XG4gICAgICAgIGZvciAoY29uc3QgW2tleSwgdmFsdWVdIG9mIE9iamVjdC5lbnRyaWVzKGRhdGEpKSB7XG4gICAgICAgICAgICBsZXQgZmluYWxLZXkgPSBwcmVmaXggPyBwcmVmaXggKyAnWycgKyBrZXkgKyAnXScgOiBrZXk7XG4gICAgICAgICAgICBpZiAoQXJyYXkuaXNBcnJheSh2YWx1ZSkpIHtcbiAgICAgICAgICAgICAgICBvdXRwdXQgPSBPYmplY3QuYXNzaWduKE9iamVjdC5hc3NpZ24oe30sIG91dHB1dCksIFNlcnZpY2UuZmxhdHRlbih2YWx1ZSwgZmluYWxLZXkpKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgIG91dHB1dFtmaW5hbEtleV0gPSB2YWx1ZTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gb3V0cHV0O1xuICAgIH1cbn1cbi8qKlxuICogVGhlIHNpemUgZm9yIGNodW5rZWQgdXBsb2FkcyBpbiBieXRlcy5cbiAqL1xuU2VydmljZS5DSFVOS19TSVpFID0gNSAqIDEwMjQgKiAxMDI0OyAvLyA1TUJcblxuY2xhc3MgQWNjb3VudCB7XG4gICAgY29uc3RydWN0b3IoY2xpZW50KSB7XG4gICAgICAgIHRoaXMuY2xpZW50ID0gY2xpZW50O1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBHZXQgdGhlIGN1cnJlbnRseSBsb2dnZWQgaW4gdXNlci5cbiAgICAgKlxuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPE1vZGVscy5Vc2VyPFByZWZlcmVuY2VzPj59XG4gICAgICovXG4gICAgZ2V0KCkge1xuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy9hY2NvdW50JztcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge307XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdnZXQnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBVc2UgdGhpcyBlbmRwb2ludCB0byBhbGxvdyBhIG5ldyB1c2VyIHRvIHJlZ2lzdGVyIGEgbmV3IGFjY291bnQgaW4geW91ciBwcm9qZWN0LiBBZnRlciB0aGUgdXNlciByZWdpc3RyYXRpb24gY29tcGxldGVzIHN1Y2Nlc3NmdWxseSwgeW91IGNhbiB1c2UgdGhlIFsvYWNjb3VudC92ZXJmaWNhdGlvbl0oaHR0cHM6Ly9hcHB3cml0ZS5pby9kb2NzL3JlZmVyZW5jZXMvY2xvdWQvY2xpZW50LXdlYi9hY2NvdW50I2NyZWF0ZVZlcmlmaWNhdGlvbikgcm91dGUgdG8gc3RhcnQgdmVyaWZ5aW5nIHRoZSB1c2VyIGVtYWlsIGFkZHJlc3MuIFRvIGFsbG93IHRoZSBuZXcgdXNlciB0byBsb2dpbiB0byB0aGVpciBuZXcgYWNjb3VudCwgeW91IG5lZWQgdG8gY3JlYXRlIGEgbmV3IFthY2NvdW50IHNlc3Npb25dKGh0dHBzOi8vYXBwd3JpdGUuaW8vZG9jcy9yZWZlcmVuY2VzL2Nsb3VkL2NsaWVudC13ZWIvYWNjb3VudCNjcmVhdGVFbWFpbFNlc3Npb24pLlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IHVzZXJJZFxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBlbWFpbFxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBwYXNzd29yZFxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBuYW1lXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8TW9kZWxzLlVzZXI8UHJlZmVyZW5jZXM+Pn1cbiAgICAgKi9cbiAgICBjcmVhdGUodXNlcklkLCBlbWFpbCwgcGFzc3dvcmQsIG5hbWUpIHtcbiAgICAgICAgaWYgKHR5cGVvZiB1c2VySWQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcInVzZXJJZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBlbWFpbCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwiZW1haWxcIicpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgcGFzc3dvcmQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcInBhc3N3b3JkXCInKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy9hY2NvdW50JztcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBpZiAodHlwZW9mIHVzZXJJZCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3VzZXJJZCddID0gdXNlcklkO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgZW1haWwgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydlbWFpbCddID0gZW1haWw7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBwYXNzd29yZCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3Bhc3N3b3JkJ10gPSBwYXNzd29yZDtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIG5hbWUgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWyduYW1lJ10gPSBuYW1lO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7XG4gICAgICAgICAgICAnY29udGVudC10eXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgncG9zdCcsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFVwZGF0ZSBjdXJyZW50bHkgbG9nZ2VkIGluIHVzZXIgYWNjb3VudCBlbWFpbCBhZGRyZXNzLiBBZnRlciBjaGFuZ2luZyB1c2VyIGFkZHJlc3MsIHRoZSB1c2VyIGNvbmZpcm1hdGlvbiBzdGF0dXMgd2lsbCBnZXQgcmVzZXQuIEEgbmV3IGNvbmZpcm1hdGlvbiBlbWFpbCBpcyBub3Qgc2VudCBhdXRvbWF0aWNhbGx5IGhvd2V2ZXIgeW91IGNhbiB1c2UgdGhlIHNlbmQgY29uZmlybWF0aW9uIGVtYWlsIGVuZHBvaW50IGFnYWluIHRvIHNlbmQgdGhlIGNvbmZpcm1hdGlvbiBlbWFpbC4gRm9yIHNlY3VyaXR5IG1lYXN1cmVzLCB1c2VyIHBhc3N3b3JkIGlzIHJlcXVpcmVkIHRvIGNvbXBsZXRlIHRoaXMgcmVxdWVzdC5cbiAgICAgKiBUaGlzIGVuZHBvaW50IGNhbiBhbHNvIGJlIHVzZWQgdG8gY29udmVydCBhbiBhbm9ueW1vdXMgYWNjb3VudCB0byBhIG5vcm1hbCBvbmUsIGJ5IHBhc3NpbmcgYW4gZW1haWwgYWRkcmVzcyBhbmQgYSBuZXcgcGFzc3dvcmQuXG4gICAgICpcbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBlbWFpbFxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBwYXNzd29yZFxuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPE1vZGVscy5Vc2VyPFByZWZlcmVuY2VzPj59XG4gICAgICovXG4gICAgdXBkYXRlRW1haWwoZW1haWwsIHBhc3N3b3JkKSB7XG4gICAgICAgIGlmICh0eXBlb2YgZW1haWwgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcImVtYWlsXCInKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIHBhc3N3b3JkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJwYXNzd29yZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvYWNjb3VudC9lbWFpbCc7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgaWYgKHR5cGVvZiBlbWFpbCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ2VtYWlsJ10gPSBlbWFpbDtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIHBhc3N3b3JkICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsncGFzc3dvcmQnXSA9IHBhc3N3b3JkO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7XG4gICAgICAgICAgICAnY29udGVudC10eXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgncGF0Y2gnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBHZXQgdGhlIGxpc3Qgb2YgaWRlbnRpdGllcyBmb3IgdGhlIGN1cnJlbnRseSBsb2dnZWQgaW4gdXNlci5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nW119IHF1ZXJpZXNcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTxNb2RlbHMuSWRlbnRpdHlMaXN0Pn1cbiAgICAgKi9cbiAgICBsaXN0SWRlbnRpdGllcyhxdWVyaWVzKSB7XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2FjY291bnQvaWRlbnRpdGllcyc7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgaWYgKHR5cGVvZiBxdWVyaWVzICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsncXVlcmllcyddID0gcXVlcmllcztcbiAgICAgICAgfVxuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge307XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdnZXQnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBEZWxldGUgYW4gaWRlbnRpdHkgYnkgaXRzIHVuaXF1ZSBJRC5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBpZGVudGl0eUlkXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8e30+fVxuICAgICAqL1xuICAgIGRlbGV0ZUlkZW50aXR5KGlkZW50aXR5SWQpIHtcbiAgICAgICAgaWYgKHR5cGVvZiBpZGVudGl0eUlkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJpZGVudGl0eUlkXCInKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy9hY2NvdW50L2lkZW50aXRpZXMve2lkZW50aXR5SWR9Jy5yZXBsYWNlKCd7aWRlbnRpdHlJZH0nLCBpZGVudGl0eUlkKTtcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge1xuICAgICAgICAgICAgJ2NvbnRlbnQtdHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ2RlbGV0ZScsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFVzZSB0aGlzIGVuZHBvaW50IHRvIGNyZWF0ZSBhIEpTT04gV2ViIFRva2VuLiBZb3UgY2FuIHVzZSB0aGUgcmVzdWx0aW5nIEpXVCB0byBhdXRoZW50aWNhdGUgb24gYmVoYWxmIG9mIHRoZSBjdXJyZW50IHVzZXIgd2hlbiB3b3JraW5nIHdpdGggdGhlIEFwcHdyaXRlIHNlcnZlci1zaWRlIEFQSSBhbmQgU0RLcy4gVGhlIEpXVCBzZWNyZXQgaXMgdmFsaWQgZm9yIDE1IG1pbnV0ZXMgZnJvbSBpdHMgY3JlYXRpb24gYW5kIHdpbGwgYmUgaW52YWxpZCBpZiB0aGUgdXNlciB3aWxsIGxvZ291dCBpbiB0aGF0IHRpbWUgZnJhbWUuXG4gICAgICpcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTxNb2RlbHMuSnd0Pn1cbiAgICAgKi9cbiAgICBjcmVhdGVKV1QoKSB7XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2FjY291bnQvand0cyc7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgY29uc3QgYXBpSGVhZGVycyA9IHtcbiAgICAgICAgICAgICdjb250ZW50LXR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgIH07XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdwb3N0JywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogR2V0IHRoZSBsaXN0IG9mIGxhdGVzdCBzZWN1cml0eSBhY3Rpdml0eSBsb2dzIGZvciB0aGUgY3VycmVudGx5IGxvZ2dlZCBpbiB1c2VyLiBFYWNoIGxvZyByZXR1cm5zIHVzZXIgSVAgYWRkcmVzcywgbG9jYXRpb24gYW5kIGRhdGUgYW5kIHRpbWUgb2YgbG9nLlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmdbXX0gcXVlcmllc1xuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPE1vZGVscy5Mb2dMaXN0Pn1cbiAgICAgKi9cbiAgICBsaXN0TG9ncyhxdWVyaWVzKSB7XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2FjY291bnQvbG9ncyc7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgaWYgKHR5cGVvZiBxdWVyaWVzICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsncXVlcmllcyddID0gcXVlcmllcztcbiAgICAgICAgfVxuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge307XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdnZXQnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBFbmFibGUgb3IgZGlzYWJsZSBNRkEgb24gYW4gYWNjb3VudC5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7Ym9vbGVhbn0gbWZhXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8TW9kZWxzLlVzZXI8UHJlZmVyZW5jZXM+Pn1cbiAgICAgKi9cbiAgICB1cGRhdGVNRkEobWZhKSB7XG4gICAgICAgIGlmICh0eXBlb2YgbWZhID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJtZmFcIicpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2FjY291bnQvbWZhJztcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBpZiAodHlwZW9mIG1mYSAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ21mYSddID0gbWZhO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7XG4gICAgICAgICAgICAnY29udGVudC10eXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgncGF0Y2gnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBBZGQgYW4gYXV0aGVudGljYXRvciBhcHAgdG8gYmUgdXNlZCBhcyBhbiBNRkEgZmFjdG9yLiBWZXJpZnkgdGhlIGF1dGhlbnRpY2F0b3IgdXNpbmcgdGhlIFt2ZXJpZnkgYXV0aGVudGljYXRvcl0oL2RvY3MvcmVmZXJlbmNlcy9jbG91ZC9jbGllbnQtd2ViL2FjY291bnQjdXBkYXRlTWZhQXV0aGVudGljYXRvcikgbWV0aG9kLlxuICAgICAqXG4gICAgICogQHBhcmFtIHtBdXRoZW50aWNhdG9yVHlwZX0gdHlwZVxuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPE1vZGVscy5NZmFUeXBlPn1cbiAgICAgKi9cbiAgICBjcmVhdGVNZmFBdXRoZW50aWNhdG9yKHR5cGUpIHtcbiAgICAgICAgaWYgKHR5cGVvZiB0eXBlID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJ0eXBlXCInKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy9hY2NvdW50L21mYS9hdXRoZW50aWNhdG9ycy97dHlwZX0nLnJlcGxhY2UoJ3t0eXBlfScsIHR5cGUpO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7XG4gICAgICAgICAgICAnY29udGVudC10eXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgncG9zdCcsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFZlcmlmeSBhbiBhdXRoZW50aWNhdG9yIGFwcCBhZnRlciBhZGRpbmcgaXQgdXNpbmcgdGhlIFthZGQgYXV0aGVudGljYXRvcl0oL2RvY3MvcmVmZXJlbmNlcy9jbG91ZC9jbGllbnQtd2ViL2FjY291bnQjY3JlYXRlTWZhQXV0aGVudGljYXRvcikgbWV0aG9kLlxuICAgICAqXG4gICAgICogQHBhcmFtIHtBdXRoZW50aWNhdG9yVHlwZX0gdHlwZVxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBvdHBcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTxNb2RlbHMuVXNlcjxQcmVmZXJlbmNlcz4+fVxuICAgICAqL1xuICAgIHVwZGF0ZU1mYUF1dGhlbnRpY2F0b3IodHlwZSwgb3RwKSB7XG4gICAgICAgIGlmICh0eXBlb2YgdHlwZSA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwidHlwZVwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBvdHAgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcIm90cFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvYWNjb3VudC9tZmEvYXV0aGVudGljYXRvcnMve3R5cGV9Jy5yZXBsYWNlKCd7dHlwZX0nLCB0eXBlKTtcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBpZiAodHlwZW9mIG90cCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ290cCddID0gb3RwO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7XG4gICAgICAgICAgICAnY29udGVudC10eXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgncHV0JywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogRGVsZXRlIGFuIGF1dGhlbnRpY2F0b3IgZm9yIGEgdXNlciBieSBJRC5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7QXV0aGVudGljYXRvclR5cGV9IHR5cGVcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTx7fT59XG4gICAgICovXG4gICAgZGVsZXRlTWZhQXV0aGVudGljYXRvcih0eXBlKSB7XG4gICAgICAgIGlmICh0eXBlb2YgdHlwZSA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwidHlwZVwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvYWNjb3VudC9tZmEvYXV0aGVudGljYXRvcnMve3R5cGV9Jy5yZXBsYWNlKCd7dHlwZX0nLCB0eXBlKTtcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge1xuICAgICAgICAgICAgJ2NvbnRlbnQtdHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ2RlbGV0ZScsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIEJlZ2luIHRoZSBwcm9jZXNzIG9mIE1GQSB2ZXJpZmljYXRpb24gYWZ0ZXIgc2lnbi1pbi4gRmluaXNoIHRoZSBmbG93IHdpdGggW3VwZGF0ZU1mYUNoYWxsZW5nZV0oL2RvY3MvcmVmZXJlbmNlcy9jbG91ZC9jbGllbnQtd2ViL2FjY291bnQjdXBkYXRlTWZhQ2hhbGxlbmdlKSBtZXRob2QuXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge0F1dGhlbnRpY2F0aW9uRmFjdG9yfSBmYWN0b3JcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTxNb2RlbHMuTWZhQ2hhbGxlbmdlPn1cbiAgICAgKi9cbiAgICBjcmVhdGVNZmFDaGFsbGVuZ2UoZmFjdG9yKSB7XG4gICAgICAgIGlmICh0eXBlb2YgZmFjdG9yID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJmYWN0b3JcIicpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2FjY291bnQvbWZhL2NoYWxsZW5nZSc7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgaWYgKHR5cGVvZiBmYWN0b3IgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydmYWN0b3InXSA9IGZhY3RvcjtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge1xuICAgICAgICAgICAgJ2NvbnRlbnQtdHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ3Bvc3QnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBDb21wbGV0ZSB0aGUgTUZBIGNoYWxsZW5nZSBieSBwcm92aWRpbmcgdGhlIG9uZS10aW1lIHBhc3N3b3JkLiBGaW5pc2ggdGhlIHByb2Nlc3Mgb2YgTUZBIHZlcmlmaWNhdGlvbiBieSBwcm92aWRpbmcgdGhlIG9uZS10aW1lIHBhc3N3b3JkLiBUbyBiZWdpbiB0aGUgZmxvdywgdXNlIFtjcmVhdGVNZmFDaGFsbGVuZ2VdKC9kb2NzL3JlZmVyZW5jZXMvY2xvdWQvY2xpZW50LXdlYi9hY2NvdW50I2NyZWF0ZU1mYUNoYWxsZW5nZSkgbWV0aG9kLlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGNoYWxsZW5nZUlkXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IG90cFxuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPE1vZGVscy5TZXNzaW9uPn1cbiAgICAgKi9cbiAgICB1cGRhdGVNZmFDaGFsbGVuZ2UoY2hhbGxlbmdlSWQsIG90cCkge1xuICAgICAgICBpZiAodHlwZW9mIGNoYWxsZW5nZUlkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJjaGFsbGVuZ2VJZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBvdHAgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcIm90cFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvYWNjb3VudC9tZmEvY2hhbGxlbmdlJztcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBpZiAodHlwZW9mIGNoYWxsZW5nZUlkICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsnY2hhbGxlbmdlSWQnXSA9IGNoYWxsZW5nZUlkO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2Ygb3RwICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsnb3RwJ10gPSBvdHA7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgY29uc3QgYXBpSGVhZGVycyA9IHtcbiAgICAgICAgICAgICdjb250ZW50LXR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgIH07XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdwdXQnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBMaXN0IHRoZSBmYWN0b3JzIGF2YWlsYWJsZSBvbiB0aGUgYWNjb3VudCB0byBiZSB1c2VkIGFzIGEgTUZBIGNoYWxsYW5nZS5cbiAgICAgKlxuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPE1vZGVscy5NZmFGYWN0b3JzPn1cbiAgICAgKi9cbiAgICBsaXN0TWZhRmFjdG9ycygpIHtcbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvYWNjb3VudC9tZmEvZmFjdG9ycyc7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgY29uc3QgYXBpSGVhZGVycyA9IHt9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgnZ2V0JywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogR2V0IHJlY292ZXJ5IGNvZGVzIHRoYXQgY2FuIGJlIHVzZWQgYXMgYmFja3VwIGZvciBNRkEgZmxvdy4gQmVmb3JlIGdldHRpbmcgY29kZXMsIHRoZXkgbXVzdCBiZSBnZW5lcmF0ZWQgdXNpbmcgW2NyZWF0ZU1mYVJlY292ZXJ5Q29kZXNdKC9kb2NzL3JlZmVyZW5jZXMvY2xvdWQvY2xpZW50LXdlYi9hY2NvdW50I2NyZWF0ZU1mYVJlY292ZXJ5Q29kZXMpIG1ldGhvZC4gQW4gT1RQIGNoYWxsZW5nZSBpcyByZXF1aXJlZCB0byByZWFkIHJlY292ZXJ5IGNvZGVzLlxuICAgICAqXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8TW9kZWxzLk1mYVJlY292ZXJ5Q29kZXM+fVxuICAgICAqL1xuICAgIGdldE1mYVJlY292ZXJ5Q29kZXMoKSB7XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2FjY291bnQvbWZhL3JlY292ZXJ5LWNvZGVzJztcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge307XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdnZXQnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBHZW5lcmF0ZSByZWNvdmVyeSBjb2RlcyBhcyBiYWNrdXAgZm9yIE1GQSBmbG93LiBJdCYjMDM5O3MgcmVjb21tZW5kZWQgdG8gZ2VuZXJhdGUgYW5kIHNob3cgdGhlbiBpbW1lZGlhdGVseSBhZnRlciB1c2VyIHN1Y2Nlc3NmdWxseSBhZGRzIHRoZWlyIGF1dGhlaHRpY2F0b3IuIFJlY292ZXJ5IGNvZGVzIGNhbiBiZSB1c2VkIGFzIGEgTUZBIHZlcmlmaWNhdGlvbiB0eXBlIGluIFtjcmVhdGVNZmFDaGFsbGVuZ2VdKC9kb2NzL3JlZmVyZW5jZXMvY2xvdWQvY2xpZW50LXdlYi9hY2NvdW50I2NyZWF0ZU1mYUNoYWxsZW5nZSkgbWV0aG9kLlxuICAgICAqXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8TW9kZWxzLk1mYVJlY292ZXJ5Q29kZXM+fVxuICAgICAqL1xuICAgIGNyZWF0ZU1mYVJlY292ZXJ5Q29kZXMoKSB7XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2FjY291bnQvbWZhL3JlY292ZXJ5LWNvZGVzJztcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge1xuICAgICAgICAgICAgJ2NvbnRlbnQtdHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ3Bvc3QnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBSZWdlbmVyYXRlIHJlY292ZXJ5IGNvZGVzIHRoYXQgY2FuIGJlIHVzZWQgYXMgYmFja3VwIGZvciBNRkEgZmxvdy4gQmVmb3JlIHJlZ2VuZXJhdGluZyBjb2RlcywgdGhleSBtdXN0IGJlIGZpcnN0IGdlbmVyYXRlZCB1c2luZyBbY3JlYXRlTWZhUmVjb3ZlcnlDb2Rlc10oL2RvY3MvcmVmZXJlbmNlcy9jbG91ZC9jbGllbnQtd2ViL2FjY291bnQjY3JlYXRlTWZhUmVjb3ZlcnlDb2RlcykgbWV0aG9kLiBBbiBPVFAgY2hhbGxlbmdlIGlzIHJlcXVpcmVkIHRvIHJlZ2VucmVhdGUgcmVjb3ZlcnkgY29kZXMuXG4gICAgICpcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTxNb2RlbHMuTWZhUmVjb3ZlcnlDb2Rlcz59XG4gICAgICovXG4gICAgdXBkYXRlTWZhUmVjb3ZlcnlDb2RlcygpIHtcbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvYWNjb3VudC9tZmEvcmVjb3ZlcnktY29kZXMnO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7XG4gICAgICAgICAgICAnY29udGVudC10eXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgncGF0Y2gnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBVcGRhdGUgY3VycmVudGx5IGxvZ2dlZCBpbiB1c2VyIGFjY291bnQgbmFtZS5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBuYW1lXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8TW9kZWxzLlVzZXI8UHJlZmVyZW5jZXM+Pn1cbiAgICAgKi9cbiAgICB1cGRhdGVOYW1lKG5hbWUpIHtcbiAgICAgICAgaWYgKHR5cGVvZiBuYW1lID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJuYW1lXCInKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy9hY2NvdW50L25hbWUnO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGlmICh0eXBlb2YgbmFtZSAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ25hbWUnXSA9IG5hbWU7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgY29uc3QgYXBpSGVhZGVycyA9IHtcbiAgICAgICAgICAgICdjb250ZW50LXR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgIH07XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdwYXRjaCcsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFVwZGF0ZSBjdXJyZW50bHkgbG9nZ2VkIGluIHVzZXIgcGFzc3dvcmQuIEZvciB2YWxpZGF0aW9uLCB1c2VyIGlzIHJlcXVpcmVkIHRvIHBhc3MgaW4gdGhlIG5ldyBwYXNzd29yZCwgYW5kIHRoZSBvbGQgcGFzc3dvcmQuIEZvciB1c2VycyBjcmVhdGVkIHdpdGggT0F1dGgsIFRlYW0gSW52aXRlcyBhbmQgTWFnaWMgVVJMLCBvbGRQYXNzd29yZCBpcyBvcHRpb25hbC5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBwYXNzd29yZFxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBvbGRQYXNzd29yZFxuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPE1vZGVscy5Vc2VyPFByZWZlcmVuY2VzPj59XG4gICAgICovXG4gICAgdXBkYXRlUGFzc3dvcmQocGFzc3dvcmQsIG9sZFBhc3N3b3JkKSB7XG4gICAgICAgIGlmICh0eXBlb2YgcGFzc3dvcmQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcInBhc3N3b3JkXCInKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy9hY2NvdW50L3Bhc3N3b3JkJztcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBpZiAodHlwZW9mIHBhc3N3b3JkICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsncGFzc3dvcmQnXSA9IHBhc3N3b3JkO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2Ygb2xkUGFzc3dvcmQgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydvbGRQYXNzd29yZCddID0gb2xkUGFzc3dvcmQ7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgY29uc3QgYXBpSGVhZGVycyA9IHtcbiAgICAgICAgICAgICdjb250ZW50LXR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgIH07XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdwYXRjaCcsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFVwZGF0ZSB0aGUgY3VycmVudGx5IGxvZ2dlZCBpbiB1c2VyJiMwMzk7cyBwaG9uZSBudW1iZXIuIEFmdGVyIHVwZGF0aW5nIHRoZSBwaG9uZSBudW1iZXIsIHRoZSBwaG9uZSB2ZXJpZmljYXRpb24gc3RhdHVzIHdpbGwgYmUgcmVzZXQuIEEgY29uZmlybWF0aW9uIFNNUyBpcyBub3Qgc2VudCBhdXRvbWF0aWNhbGx5LCBob3dldmVyIHlvdSBjYW4gdXNlIHRoZSBbUE9TVCAvYWNjb3VudC92ZXJpZmljYXRpb24vcGhvbmVdKGh0dHBzOi8vYXBwd3JpdGUuaW8vZG9jcy9yZWZlcmVuY2VzL2Nsb3VkL2NsaWVudC13ZWIvYWNjb3VudCNjcmVhdGVQaG9uZVZlcmlmaWNhdGlvbikgZW5kcG9pbnQgdG8gc2VuZCBhIGNvbmZpcm1hdGlvbiBTTVMuXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gcGhvbmVcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gcGFzc3dvcmRcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTxNb2RlbHMuVXNlcjxQcmVmZXJlbmNlcz4+fVxuICAgICAqL1xuICAgIHVwZGF0ZVBob25lKHBob25lLCBwYXNzd29yZCkge1xuICAgICAgICBpZiAodHlwZW9mIHBob25lID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJwaG9uZVwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBwYXNzd29yZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwicGFzc3dvcmRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2FjY291bnQvcGhvbmUnO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGlmICh0eXBlb2YgcGhvbmUgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydwaG9uZSddID0gcGhvbmU7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBwYXNzd29yZCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3Bhc3N3b3JkJ10gPSBwYXNzd29yZDtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge1xuICAgICAgICAgICAgJ2NvbnRlbnQtdHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ3BhdGNoJywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogR2V0IHRoZSBwcmVmZXJlbmNlcyBhcyBhIGtleS12YWx1ZSBvYmplY3QgZm9yIHRoZSBjdXJyZW50bHkgbG9nZ2VkIGluIHVzZXIuXG4gICAgICpcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTxQcmVmZXJlbmNlcz59XG4gICAgICovXG4gICAgZ2V0UHJlZnMoKSB7XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2FjY291bnQvcHJlZnMnO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7fTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ2dldCcsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFVwZGF0ZSBjdXJyZW50bHkgbG9nZ2VkIGluIHVzZXIgYWNjb3VudCBwcmVmZXJlbmNlcy4gVGhlIG9iamVjdCB5b3UgcGFzcyBpcyBzdG9yZWQgYXMgaXMsIGFuZCByZXBsYWNlcyBhbnkgcHJldmlvdXMgdmFsdWUuIFRoZSBtYXhpbXVtIGFsbG93ZWQgcHJlZnMgc2l6ZSBpcyA2NGtCIGFuZCB0aHJvd3MgZXJyb3IgaWYgZXhjZWVkZWQuXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge1BhcnRpYWw8UHJlZmVyZW5jZXM+fSBwcmVmc1xuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPE1vZGVscy5Vc2VyPFByZWZlcmVuY2VzPj59XG4gICAgICovXG4gICAgdXBkYXRlUHJlZnMocHJlZnMpIHtcbiAgICAgICAgaWYgKHR5cGVvZiBwcmVmcyA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwicHJlZnNcIicpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2FjY291bnQvcHJlZnMnO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGlmICh0eXBlb2YgcHJlZnMgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydwcmVmcyddID0gcHJlZnM7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgY29uc3QgYXBpSGVhZGVycyA9IHtcbiAgICAgICAgICAgICdjb250ZW50LXR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgIH07XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdwYXRjaCcsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFNlbmRzIHRoZSB1c2VyIGFuIGVtYWlsIHdpdGggYSB0ZW1wb3Jhcnkgc2VjcmV0IGtleSBmb3IgcGFzc3dvcmQgcmVzZXQuIFdoZW4gdGhlIHVzZXIgY2xpY2tzIHRoZSBjb25maXJtYXRpb24gbGluayBoZSBpcyByZWRpcmVjdGVkIGJhY2sgdG8geW91ciBhcHAgcGFzc3dvcmQgcmVzZXQgVVJMIHdpdGggdGhlIHNlY3JldCBrZXkgYW5kIGVtYWlsIGFkZHJlc3MgdmFsdWVzIGF0dGFjaGVkIHRvIHRoZSBVUkwgcXVlcnkgc3RyaW5nLiBVc2UgdGhlIHF1ZXJ5IHN0cmluZyBwYXJhbXMgdG8gc3VibWl0IGEgcmVxdWVzdCB0byB0aGUgW1BVVCAvYWNjb3VudC9yZWNvdmVyeV0oaHR0cHM6Ly9hcHB3cml0ZS5pby9kb2NzL3JlZmVyZW5jZXMvY2xvdWQvY2xpZW50LXdlYi9hY2NvdW50I3VwZGF0ZVJlY292ZXJ5KSBlbmRwb2ludCB0byBjb21wbGV0ZSB0aGUgcHJvY2Vzcy4gVGhlIHZlcmlmaWNhdGlvbiBsaW5rIHNlbnQgdG8gdGhlIHVzZXImIzAzOTtzIGVtYWlsIGFkZHJlc3MgaXMgdmFsaWQgZm9yIDEgaG91ci5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBlbWFpbFxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSB1cmxcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTxNb2RlbHMuVG9rZW4+fVxuICAgICAqL1xuICAgIGNyZWF0ZVJlY292ZXJ5KGVtYWlsLCB1cmwpIHtcbiAgICAgICAgaWYgKHR5cGVvZiBlbWFpbCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwiZW1haWxcIicpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgdXJsID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJ1cmxcIicpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2FjY291bnQvcmVjb3ZlcnknO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGlmICh0eXBlb2YgZW1haWwgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydlbWFpbCddID0gZW1haWw7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiB1cmwgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWyd1cmwnXSA9IHVybDtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge1xuICAgICAgICAgICAgJ2NvbnRlbnQtdHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ3Bvc3QnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBVc2UgdGhpcyBlbmRwb2ludCB0byBjb21wbGV0ZSB0aGUgdXNlciBhY2NvdW50IHBhc3N3b3JkIHJlc2V0LiBCb3RoIHRoZSAqKnVzZXJJZCoqIGFuZCAqKnNlY3JldCoqIGFyZ3VtZW50cyB3aWxsIGJlIHBhc3NlZCBhcyBxdWVyeSBwYXJhbWV0ZXJzIHRvIHRoZSByZWRpcmVjdCBVUkwgeW91IGhhdmUgcHJvdmlkZWQgd2hlbiBzZW5kaW5nIHlvdXIgcmVxdWVzdCB0byB0aGUgW1BPU1QgL2FjY291bnQvcmVjb3ZlcnldKGh0dHBzOi8vYXBwd3JpdGUuaW8vZG9jcy9yZWZlcmVuY2VzL2Nsb3VkL2NsaWVudC13ZWIvYWNjb3VudCNjcmVhdGVSZWNvdmVyeSkgZW5kcG9pbnQuXG4gICAgICpcbiAgICAgKiBQbGVhc2Ugbm90ZSB0aGF0IGluIG9yZGVyIHRvIGF2b2lkIGEgW1JlZGlyZWN0IEF0dGFja10oaHR0cHM6Ly9naXRodWIuY29tL09XQVNQL0NoZWF0U2hlZXRTZXJpZXMvYmxvYi9tYXN0ZXIvY2hlYXRzaGVldHMvVW52YWxpZGF0ZWRfUmVkaXJlY3RzX2FuZF9Gb3J3YXJkc19DaGVhdF9TaGVldC5tZCkgdGhlIG9ubHkgdmFsaWQgcmVkaXJlY3QgVVJMcyBhcmUgdGhlIG9uZXMgZnJvbSBkb21haW5zIHlvdSBoYXZlIHNldCB3aGVuIGFkZGluZyB5b3VyIHBsYXRmb3JtcyBpbiB0aGUgY29uc29sZSBpbnRlcmZhY2UuXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gdXNlcklkXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IHNlY3JldFxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBwYXNzd29yZFxuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPE1vZGVscy5Ub2tlbj59XG4gICAgICovXG4gICAgdXBkYXRlUmVjb3ZlcnkodXNlcklkLCBzZWNyZXQsIHBhc3N3b3JkKSB7XG4gICAgICAgIGlmICh0eXBlb2YgdXNlcklkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJ1c2VySWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2Ygc2VjcmV0ID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJzZWNyZXRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgcGFzc3dvcmQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcInBhc3N3b3JkXCInKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy9hY2NvdW50L3JlY292ZXJ5JztcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBpZiAodHlwZW9mIHVzZXJJZCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3VzZXJJZCddID0gdXNlcklkO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2Ygc2VjcmV0ICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsnc2VjcmV0J10gPSBzZWNyZXQ7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBwYXNzd29yZCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3Bhc3N3b3JkJ10gPSBwYXNzd29yZDtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge1xuICAgICAgICAgICAgJ2NvbnRlbnQtdHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ3B1dCcsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIEdldCB0aGUgbGlzdCBvZiBhY3RpdmUgc2Vzc2lvbnMgYWNyb3NzIGRpZmZlcmVudCBkZXZpY2VzIGZvciB0aGUgY3VycmVudGx5IGxvZ2dlZCBpbiB1c2VyLlxuICAgICAqXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8TW9kZWxzLlNlc3Npb25MaXN0Pn1cbiAgICAgKi9cbiAgICBsaXN0U2Vzc2lvbnMoKSB7XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2FjY291bnQvc2Vzc2lvbnMnO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7fTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ2dldCcsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIERlbGV0ZSBhbGwgc2Vzc2lvbnMgZnJvbSB0aGUgdXNlciBhY2NvdW50IGFuZCByZW1vdmUgYW55IHNlc3Npb25zIGNvb2tpZXMgZnJvbSB0aGUgZW5kIGNsaWVudC5cbiAgICAgKlxuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPHt9Pn1cbiAgICAgKi9cbiAgICBkZWxldGVTZXNzaW9ucygpIHtcbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvYWNjb3VudC9zZXNzaW9ucyc7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgY29uc3QgYXBpSGVhZGVycyA9IHtcbiAgICAgICAgICAgICdjb250ZW50LXR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgIH07XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdkZWxldGUnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBVc2UgdGhpcyBlbmRwb2ludCB0byBhbGxvdyBhIG5ldyB1c2VyIHRvIHJlZ2lzdGVyIGFuIGFub255bW91cyBhY2NvdW50IGluIHlvdXIgcHJvamVjdC4gVGhpcyByb3V0ZSB3aWxsIGFsc28gY3JlYXRlIGEgbmV3IHNlc3Npb24gZm9yIHRoZSB1c2VyLiBUbyBhbGxvdyB0aGUgbmV3IHVzZXIgdG8gY29udmVydCBhbiBhbm9ueW1vdXMgYWNjb3VudCB0byBhIG5vcm1hbCBhY2NvdW50LCB5b3UgbmVlZCB0byB1cGRhdGUgaXRzIFtlbWFpbCBhbmQgcGFzc3dvcmRdKGh0dHBzOi8vYXBwd3JpdGUuaW8vZG9jcy9yZWZlcmVuY2VzL2Nsb3VkL2NsaWVudC13ZWIvYWNjb3VudCN1cGRhdGVFbWFpbCkgb3IgY3JlYXRlIGFuIFtPQXV0aDIgc2Vzc2lvbl0oaHR0cHM6Ly9hcHB3cml0ZS5pby9kb2NzL3JlZmVyZW5jZXMvY2xvdWQvY2xpZW50LXdlYi9hY2NvdW50I0NyZWF0ZU9BdXRoMlNlc3Npb24pLlxuICAgICAqXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8TW9kZWxzLlNlc3Npb24+fVxuICAgICAqL1xuICAgIGNyZWF0ZUFub255bW91c1Nlc3Npb24oKSB7XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2FjY291bnQvc2Vzc2lvbnMvYW5vbnltb3VzJztcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge1xuICAgICAgICAgICAgJ2NvbnRlbnQtdHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ3Bvc3QnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBBbGxvdyB0aGUgdXNlciB0byBsb2dpbiBpbnRvIHRoZWlyIGFjY291bnQgYnkgcHJvdmlkaW5nIGEgdmFsaWQgZW1haWwgYW5kIHBhc3N3b3JkIGNvbWJpbmF0aW9uLiBUaGlzIHJvdXRlIHdpbGwgY3JlYXRlIGEgbmV3IHNlc3Npb24gZm9yIHRoZSB1c2VyLlxuICAgICAqXG4gICAgICogQSB1c2VyIGlzIGxpbWl0ZWQgdG8gMTAgYWN0aXZlIHNlc3Npb25zIGF0IGEgdGltZSBieSBkZWZhdWx0LiBbTGVhcm4gbW9yZSBhYm91dCBzZXNzaW9uIGxpbWl0c10oaHR0cHM6Ly9hcHB3cml0ZS5pby9kb2NzL2F1dGhlbnRpY2F0aW9uLXNlY3VyaXR5I2xpbWl0cykuXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gZW1haWxcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gcGFzc3dvcmRcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTxNb2RlbHMuU2Vzc2lvbj59XG4gICAgICovXG4gICAgY3JlYXRlRW1haWxQYXNzd29yZFNlc3Npb24oZW1haWwsIHBhc3N3b3JkKSB7XG4gICAgICAgIGlmICh0eXBlb2YgZW1haWwgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcImVtYWlsXCInKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIHBhc3N3b3JkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJwYXNzd29yZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvYWNjb3VudC9zZXNzaW9ucy9lbWFpbCc7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgaWYgKHR5cGVvZiBlbWFpbCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ2VtYWlsJ10gPSBlbWFpbDtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIHBhc3N3b3JkICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsncGFzc3dvcmQnXSA9IHBhc3N3b3JkO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7XG4gICAgICAgICAgICAnY29udGVudC10eXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgncG9zdCcsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFVzZSB0aGlzIGVuZHBvaW50IHRvIGNyZWF0ZSBhIHNlc3Npb24gZnJvbSB0b2tlbi4gUHJvdmlkZSB0aGUgKip1c2VySWQqKiBhbmQgKipzZWNyZXQqKiBwYXJhbWV0ZXJzIGZyb20gdGhlIHN1Y2Nlc3NmdWwgcmVzcG9uc2Ugb2YgYXV0aGVudGljYXRpb24gZmxvd3MgaW5pdGlhdGVkIGJ5IHRva2VuIGNyZWF0aW9uLiBGb3IgZXhhbXBsZSwgbWFnaWMgVVJMIGFuZCBwaG9uZSBsb2dpbi5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSB1c2VySWRcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gc2VjcmV0XG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8TW9kZWxzLlNlc3Npb24+fVxuICAgICAqL1xuICAgIHVwZGF0ZU1hZ2ljVVJMU2Vzc2lvbih1c2VySWQsIHNlY3JldCkge1xuICAgICAgICBpZiAodHlwZW9mIHVzZXJJZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwidXNlcklkXCInKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIHNlY3JldCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwic2VjcmV0XCInKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy9hY2NvdW50L3Nlc3Npb25zL21hZ2ljLXVybCc7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgaWYgKHR5cGVvZiB1c2VySWQgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWyd1c2VySWQnXSA9IHVzZXJJZDtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIHNlY3JldCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3NlY3JldCddID0gc2VjcmV0O1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7XG4gICAgICAgICAgICAnY29udGVudC10eXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgncHV0JywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogQWxsb3cgdGhlIHVzZXIgdG8gbG9naW4gdG8gdGhlaXIgYWNjb3VudCB1c2luZyB0aGUgT0F1dGgyIHByb3ZpZGVyIG9mIHRoZWlyIGNob2ljZS4gRWFjaCBPQXV0aDIgcHJvdmlkZXIgc2hvdWxkIGJlIGVuYWJsZWQgZnJvbSB0aGUgQXBwd3JpdGUgY29uc29sZSBmaXJzdC4gVXNlIHRoZSBzdWNjZXNzIGFuZCBmYWlsdXJlIGFyZ3VtZW50cyB0byBwcm92aWRlIGEgcmVkaXJlY3QgVVJMJiMwMzk7cyBiYWNrIHRvIHlvdXIgYXBwIHdoZW4gbG9naW4gaXMgY29tcGxldGVkLlxuICAgICAqXG4gICAgICogSWYgdGhlcmUgaXMgYWxyZWFkeSBhbiBhY3RpdmUgc2Vzc2lvbiwgdGhlIG5ldyBzZXNzaW9uIHdpbGwgYmUgYXR0YWNoZWQgdG8gdGhlIGxvZ2dlZC1pbiBhY2NvdW50LiBJZiB0aGVyZSBhcmUgbm8gYWN0aXZlIHNlc3Npb25zLCB0aGUgc2VydmVyIHdpbGwgYXR0ZW1wdCB0byBsb29rIGZvciBhIHVzZXIgd2l0aCB0aGUgc2FtZSBlbWFpbCBhZGRyZXNzIGFzIHRoZSBlbWFpbCByZWNlaXZlZCBmcm9tIHRoZSBPQXV0aDIgcHJvdmlkZXIgYW5kIGF0dGFjaCB0aGUgbmV3IHNlc3Npb24gdG8gdGhlIGV4aXN0aW5nIHVzZXIuIElmIG5vIG1hdGNoaW5nIHVzZXIgaXMgZm91bmQgLSB0aGUgc2VydmVyIHdpbGwgY3JlYXRlIGEgbmV3IHVzZXIuXG4gICAgICpcbiAgICAgKiBBIHVzZXIgaXMgbGltaXRlZCB0byAxMCBhY3RpdmUgc2Vzc2lvbnMgYXQgYSB0aW1lIGJ5IGRlZmF1bHQuIFtMZWFybiBtb3JlIGFib3V0IHNlc3Npb24gbGltaXRzXShodHRwczovL2FwcHdyaXRlLmlvL2RvY3MvYXV0aGVudGljYXRpb24tc2VjdXJpdHkjbGltaXRzKS5cbiAgICAgKlxuICAgICAqXG4gICAgICogQHBhcmFtIHtPQXV0aFByb3ZpZGVyfSBwcm92aWRlclxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBzdWNjZXNzXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGZhaWx1cmVcbiAgICAgKiBAcGFyYW0ge3N0cmluZ1tdfSBzY29wZXNcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7dm9pZCB8IHN0cmluZ31cbiAgICAgKi9cbiAgICBjcmVhdGVPQXV0aDJTZXNzaW9uKHByb3ZpZGVyLCBzdWNjZXNzLCBmYWlsdXJlLCBzY29wZXMpIHtcbiAgICAgICAgaWYgKHR5cGVvZiBwcm92aWRlciA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwicHJvdmlkZXJcIicpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2FjY291bnQvc2Vzc2lvbnMvb2F1dGgyL3twcm92aWRlcn0nLnJlcGxhY2UoJ3twcm92aWRlcn0nLCBwcm92aWRlcik7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgaWYgKHR5cGVvZiBzdWNjZXNzICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsnc3VjY2VzcyddID0gc3VjY2VzcztcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIGZhaWx1cmUgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydmYWlsdXJlJ10gPSBmYWlsdXJlO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2Ygc2NvcGVzICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsnc2NvcGVzJ10gPSBzY29wZXM7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgcGF5bG9hZFsncHJvamVjdCddID0gdGhpcy5jbGllbnQuY29uZmlnLnByb2plY3Q7XG4gICAgICAgIGZvciAoY29uc3QgW2tleSwgdmFsdWVdIG9mIE9iamVjdC5lbnRyaWVzKFNlcnZpY2UuZmxhdHRlbihwYXlsb2FkKSkpIHtcbiAgICAgICAgICAgIHVyaS5zZWFyY2hQYXJhbXMuYXBwZW5kKGtleSwgdmFsdWUpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJyAmJiAod2luZG93ID09PSBudWxsIHx8IHdpbmRvdyA9PT0gdm9pZCAwID8gdm9pZCAwIDogd2luZG93LmxvY2F0aW9uKSkge1xuICAgICAgICAgICAgd2luZG93LmxvY2F0aW9uLmhyZWYgPSB1cmkudG9TdHJpbmcoKTtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIHJldHVybiB1cmkudG9TdHJpbmcoKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICAvKipcbiAgICAgKiBVc2UgdGhpcyBlbmRwb2ludCB0byBjcmVhdGUgYSBzZXNzaW9uIGZyb20gdG9rZW4uIFByb3ZpZGUgdGhlICoqdXNlcklkKiogYW5kICoqc2VjcmV0KiogcGFyYW1ldGVycyBmcm9tIHRoZSBzdWNjZXNzZnVsIHJlc3BvbnNlIG9mIGF1dGhlbnRpY2F0aW9uIGZsb3dzIGluaXRpYXRlZCBieSB0b2tlbiBjcmVhdGlvbi4gRm9yIGV4YW1wbGUsIG1hZ2ljIFVSTCBhbmQgcGhvbmUgbG9naW4uXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gdXNlcklkXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IHNlY3JldFxuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPE1vZGVscy5TZXNzaW9uPn1cbiAgICAgKi9cbiAgICB1cGRhdGVQaG9uZVNlc3Npb24odXNlcklkLCBzZWNyZXQpIHtcbiAgICAgICAgaWYgKHR5cGVvZiB1c2VySWQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcInVzZXJJZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBzZWNyZXQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcInNlY3JldFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvYWNjb3VudC9zZXNzaW9ucy9waG9uZSc7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgaWYgKHR5cGVvZiB1c2VySWQgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWyd1c2VySWQnXSA9IHVzZXJJZDtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIHNlY3JldCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3NlY3JldCddID0gc2VjcmV0O1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7XG4gICAgICAgICAgICAnY29udGVudC10eXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgncHV0JywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogVXNlIHRoaXMgZW5kcG9pbnQgdG8gY3JlYXRlIGEgc2Vzc2lvbiBmcm9tIHRva2VuLiBQcm92aWRlIHRoZSAqKnVzZXJJZCoqIGFuZCAqKnNlY3JldCoqIHBhcmFtZXRlcnMgZnJvbSB0aGUgc3VjY2Vzc2Z1bCByZXNwb25zZSBvZiBhdXRoZW50aWNhdGlvbiBmbG93cyBpbml0aWF0ZWQgYnkgdG9rZW4gY3JlYXRpb24uIEZvciBleGFtcGxlLCBtYWdpYyBVUkwgYW5kIHBob25lIGxvZ2luLlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IHVzZXJJZFxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBzZWNyZXRcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTxNb2RlbHMuU2Vzc2lvbj59XG4gICAgICovXG4gICAgY3JlYXRlU2Vzc2lvbih1c2VySWQsIHNlY3JldCkge1xuICAgICAgICBpZiAodHlwZW9mIHVzZXJJZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwidXNlcklkXCInKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIHNlY3JldCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwic2VjcmV0XCInKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy9hY2NvdW50L3Nlc3Npb25zL3Rva2VuJztcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBpZiAodHlwZW9mIHVzZXJJZCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3VzZXJJZCddID0gdXNlcklkO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2Ygc2VjcmV0ICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsnc2VjcmV0J10gPSBzZWNyZXQ7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgY29uc3QgYXBpSGVhZGVycyA9IHtcbiAgICAgICAgICAgICdjb250ZW50LXR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgIH07XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdwb3N0JywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogVXNlIHRoaXMgZW5kcG9pbnQgdG8gZ2V0IGEgbG9nZ2VkIGluIHVzZXImIzAzOTtzIHNlc3Npb24gdXNpbmcgYSBTZXNzaW9uIElELiBJbnB1dHRpbmcgJiMwMzk7Y3VycmVudCYjMDM5OyB3aWxsIHJldHVybiB0aGUgY3VycmVudCBzZXNzaW9uIGJlaW5nIHVzZWQuXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gc2Vzc2lvbklkXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8TW9kZWxzLlNlc3Npb24+fVxuICAgICAqL1xuICAgIGdldFNlc3Npb24oc2Vzc2lvbklkKSB7XG4gICAgICAgIGlmICh0eXBlb2Ygc2Vzc2lvbklkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJzZXNzaW9uSWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2FjY291bnQvc2Vzc2lvbnMve3Nlc3Npb25JZH0nLnJlcGxhY2UoJ3tzZXNzaW9uSWR9Jywgc2Vzc2lvbklkKTtcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge307XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdnZXQnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBVc2UgdGhpcyBlbmRwb2ludCB0byBleHRlbmQgYSBzZXNzaW9uJiMwMzk7cyBsZW5ndGguIEV4dGVuZGluZyBhIHNlc3Npb24gaXMgdXNlZnVsIHdoZW4gc2Vzc2lvbiBleHBpcnkgaXMgc2hvcnQuIElmIHRoZSBzZXNzaW9uIHdhcyBjcmVhdGVkIHVzaW5nIGFuIE9BdXRoIHByb3ZpZGVyLCB0aGlzIGVuZHBvaW50IHJlZnJlc2hlcyB0aGUgYWNjZXNzIHRva2VuIGZyb20gdGhlIHByb3ZpZGVyLlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IHNlc3Npb25JZFxuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPE1vZGVscy5TZXNzaW9uPn1cbiAgICAgKi9cbiAgICB1cGRhdGVTZXNzaW9uKHNlc3Npb25JZCkge1xuICAgICAgICBpZiAodHlwZW9mIHNlc3Npb25JZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwic2Vzc2lvbklkXCInKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy9hY2NvdW50L3Nlc3Npb25zL3tzZXNzaW9uSWR9Jy5yZXBsYWNlKCd7c2Vzc2lvbklkfScsIHNlc3Npb25JZCk7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgY29uc3QgYXBpSGVhZGVycyA9IHtcbiAgICAgICAgICAgICdjb250ZW50LXR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgIH07XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdwYXRjaCcsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIExvZ291dCB0aGUgdXNlci4gVXNlICYjMDM5O2N1cnJlbnQmIzAzOTsgYXMgdGhlIHNlc3Npb24gSUQgdG8gbG9nb3V0IG9uIHRoaXMgZGV2aWNlLCB1c2UgYSBzZXNzaW9uIElEIHRvIGxvZ291dCBvbiBhbm90aGVyIGRldmljZS4gSWYgeW91JiMwMzk7cmUgbG9va2luZyB0byBsb2dvdXQgdGhlIHVzZXIgb24gYWxsIGRldmljZXMsIHVzZSBbRGVsZXRlIFNlc3Npb25zXShodHRwczovL2FwcHdyaXRlLmlvL2RvY3MvcmVmZXJlbmNlcy9jbG91ZC9jbGllbnQtd2ViL2FjY291bnQjZGVsZXRlU2Vzc2lvbnMpIGluc3RlYWQuXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gc2Vzc2lvbklkXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8e30+fVxuICAgICAqL1xuICAgIGRlbGV0ZVNlc3Npb24oc2Vzc2lvbklkKSB7XG4gICAgICAgIGlmICh0eXBlb2Ygc2Vzc2lvbklkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJzZXNzaW9uSWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2FjY291bnQvc2Vzc2lvbnMve3Nlc3Npb25JZH0nLnJlcGxhY2UoJ3tzZXNzaW9uSWR9Jywgc2Vzc2lvbklkKTtcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge1xuICAgICAgICAgICAgJ2NvbnRlbnQtdHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ2RlbGV0ZScsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIEJsb2NrIHRoZSBjdXJyZW50bHkgbG9nZ2VkIGluIHVzZXIgYWNjb3VudC4gQmVoaW5kIHRoZSBzY2VuZSwgdGhlIHVzZXIgcmVjb3JkIGlzIG5vdCBkZWxldGVkIGJ1dCBwZXJtYW5lbnRseSBibG9ja2VkIGZyb20gYW55IGFjY2Vzcy4gVG8gY29tcGxldGVseSBkZWxldGUgYSB1c2VyLCB1c2UgdGhlIFVzZXJzIEFQSSBpbnN0ZWFkLlxuICAgICAqXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8TW9kZWxzLlVzZXI8UHJlZmVyZW5jZXM+Pn1cbiAgICAgKi9cbiAgICB1cGRhdGVTdGF0dXMoKSB7XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2FjY291bnQvc3RhdHVzJztcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge1xuICAgICAgICAgICAgJ2NvbnRlbnQtdHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ3BhdGNoJywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogVXNlIHRoaXMgZW5kcG9pbnQgdG8gcmVnaXN0ZXIgYSBkZXZpY2UgZm9yIHB1c2ggbm90aWZpY2F0aW9ucy4gUHJvdmlkZSBhIHRhcmdldCBJRCAoY3VzdG9tIG9yIGdlbmVyYXRlZCB1c2luZyBJRC51bmlxdWUoKSksIGEgZGV2aWNlIGlkZW50aWZpZXIgKHVzdWFsbHkgYSBkZXZpY2UgdG9rZW4pLCBhbmQgb3B0aW9uYWxseSBzcGVjaWZ5IHdoaWNoIHByb3ZpZGVyIHNob3VsZCBzZW5kIG5vdGlmaWNhdGlvbnMgdG8gdGhpcyB0YXJnZXQuIFRoZSB0YXJnZXQgaXMgYXV0b21hdGljYWxseSBsaW5rZWQgdG8gdGhlIGN1cnJlbnQgc2Vzc2lvbiBhbmQgaW5jbHVkZXMgZGV2aWNlIGluZm9ybWF0aW9uIGxpa2UgYnJhbmQgYW5kIG1vZGVsLlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IHRhcmdldElkXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGlkZW50aWZpZXJcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gcHJvdmlkZXJJZFxuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPE1vZGVscy5UYXJnZXQ+fVxuICAgICAqL1xuICAgIGNyZWF0ZVB1c2hUYXJnZXQodGFyZ2V0SWQsIGlkZW50aWZpZXIsIHByb3ZpZGVySWQpIHtcbiAgICAgICAgaWYgKHR5cGVvZiB0YXJnZXRJZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwidGFyZ2V0SWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgaWRlbnRpZmllciA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwiaWRlbnRpZmllclwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvYWNjb3VudC90YXJnZXRzL3B1c2gnO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGlmICh0eXBlb2YgdGFyZ2V0SWQgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWyd0YXJnZXRJZCddID0gdGFyZ2V0SWQ7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBpZGVudGlmaWVyICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsnaWRlbnRpZmllciddID0gaWRlbnRpZmllcjtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIHByb3ZpZGVySWQgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydwcm92aWRlcklkJ10gPSBwcm92aWRlcklkO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7XG4gICAgICAgICAgICAnY29udGVudC10eXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgncG9zdCcsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFVwZGF0ZSB0aGUgY3VycmVudGx5IGxvZ2dlZCBpbiB1c2VyJiMwMzk7cyBwdXNoIG5vdGlmaWNhdGlvbiB0YXJnZXQuIFlvdSBjYW4gbW9kaWZ5IHRoZSB0YXJnZXQmIzAzOTtzIGlkZW50aWZpZXIgKGRldmljZSB0b2tlbikgYW5kIHByb3ZpZGVyIElEICh0b2tlbiwgZW1haWwsIHBob25lIGV0Yy4pLiBUaGUgdGFyZ2V0IG11c3QgZXhpc3QgYW5kIGJlbG9uZyB0byB0aGUgY3VycmVudCB1c2VyLiBJZiB5b3UgY2hhbmdlIHRoZSBwcm92aWRlciBJRCwgbm90aWZpY2F0aW9ucyB3aWxsIGJlIHNlbnQgdGhyb3VnaCB0aGUgbmV3IG1lc3NhZ2luZyBwcm92aWRlciBpbnN0ZWFkLlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IHRhcmdldElkXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGlkZW50aWZpZXJcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTxNb2RlbHMuVGFyZ2V0Pn1cbiAgICAgKi9cbiAgICB1cGRhdGVQdXNoVGFyZ2V0KHRhcmdldElkLCBpZGVudGlmaWVyKSB7XG4gICAgICAgIGlmICh0eXBlb2YgdGFyZ2V0SWQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcInRhcmdldElkXCInKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIGlkZW50aWZpZXIgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcImlkZW50aWZpZXJcIicpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2FjY291bnQvdGFyZ2V0cy97dGFyZ2V0SWR9L3B1c2gnLnJlcGxhY2UoJ3t0YXJnZXRJZH0nLCB0YXJnZXRJZCk7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgaWYgKHR5cGVvZiBpZGVudGlmaWVyICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsnaWRlbnRpZmllciddID0gaWRlbnRpZmllcjtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge1xuICAgICAgICAgICAgJ2NvbnRlbnQtdHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ3B1dCcsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIERlbGV0ZSBhIHB1c2ggbm90aWZpY2F0aW9uIHRhcmdldCBmb3IgdGhlIGN1cnJlbnRseSBsb2dnZWQgaW4gdXNlci4gQWZ0ZXIgZGVsZXRpb24sIHRoZSBkZXZpY2Ugd2lsbCBubyBsb25nZXIgcmVjZWl2ZSBwdXNoIG5vdGlmaWNhdGlvbnMuIFRoZSB0YXJnZXQgbXVzdCBleGlzdCBhbmQgYmVsb25nIHRvIHRoZSBjdXJyZW50IHVzZXIuXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gdGFyZ2V0SWRcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTx7fT59XG4gICAgICovXG4gICAgZGVsZXRlUHVzaFRhcmdldCh0YXJnZXRJZCkge1xuICAgICAgICBpZiAodHlwZW9mIHRhcmdldElkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJ0YXJnZXRJZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvYWNjb3VudC90YXJnZXRzL3t0YXJnZXRJZH0vcHVzaCcucmVwbGFjZSgne3RhcmdldElkfScsIHRhcmdldElkKTtcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge1xuICAgICAgICAgICAgJ2NvbnRlbnQtdHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ2RlbGV0ZScsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFNlbmRzIHRoZSB1c2VyIGFuIGVtYWlsIHdpdGggYSBzZWNyZXQga2V5IGZvciBjcmVhdGluZyBhIHNlc3Npb24uIElmIHRoZSBwcm92aWRlZCB1c2VyIElEIGhhcyBub3QgYmUgcmVnaXN0ZXJlZCwgYSBuZXcgdXNlciB3aWxsIGJlIGNyZWF0ZWQuIFVzZSB0aGUgcmV0dXJuZWQgdXNlciBJRCBhbmQgc2VjcmV0IGFuZCBzdWJtaXQgYSByZXF1ZXN0IHRvIHRoZSBbUE9TVCAvdjEvYWNjb3VudC9zZXNzaW9ucy90b2tlbl0oaHR0cHM6Ly9hcHB3cml0ZS5pby9kb2NzL3JlZmVyZW5jZXMvY2xvdWQvY2xpZW50LXdlYi9hY2NvdW50I2NyZWF0ZVNlc3Npb24pIGVuZHBvaW50IHRvIGNvbXBsZXRlIHRoZSBsb2dpbiBwcm9jZXNzLiBUaGUgc2VjcmV0IHNlbnQgdG8gdGhlIHVzZXImIzAzOTtzIGVtYWlsIGlzIHZhbGlkIGZvciAxNSBtaW51dGVzLlxuICAgICAqXG4gICAgICogQSB1c2VyIGlzIGxpbWl0ZWQgdG8gMTAgYWN0aXZlIHNlc3Npb25zIGF0IGEgdGltZSBieSBkZWZhdWx0LiBbTGVhcm4gbW9yZSBhYm91dCBzZXNzaW9uIGxpbWl0c10oaHR0cHM6Ly9hcHB3cml0ZS5pby9kb2NzL2F1dGhlbnRpY2F0aW9uLXNlY3VyaXR5I2xpbWl0cykuXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gdXNlcklkXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGVtYWlsXG4gICAgICogQHBhcmFtIHtib29sZWFufSBwaHJhc2VcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTxNb2RlbHMuVG9rZW4+fVxuICAgICAqL1xuICAgIGNyZWF0ZUVtYWlsVG9rZW4odXNlcklkLCBlbWFpbCwgcGhyYXNlKSB7XG4gICAgICAgIGlmICh0eXBlb2YgdXNlcklkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJ1c2VySWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgZW1haWwgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcImVtYWlsXCInKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy9hY2NvdW50L3Rva2Vucy9lbWFpbCc7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgaWYgKHR5cGVvZiB1c2VySWQgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWyd1c2VySWQnXSA9IHVzZXJJZDtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIGVtYWlsICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsnZW1haWwnXSA9IGVtYWlsO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgcGhyYXNlICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsncGhyYXNlJ10gPSBwaHJhc2U7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgY29uc3QgYXBpSGVhZGVycyA9IHtcbiAgICAgICAgICAgICdjb250ZW50LXR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgIH07XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdwb3N0JywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogU2VuZHMgdGhlIHVzZXIgYW4gZW1haWwgd2l0aCBhIHNlY3JldCBrZXkgZm9yIGNyZWF0aW5nIGEgc2Vzc2lvbi4gSWYgdGhlIHByb3ZpZGVkIHVzZXIgSUQgaGFzIG5vdCBiZWVuIHJlZ2lzdGVyZWQsIGEgbmV3IHVzZXIgd2lsbCBiZSBjcmVhdGVkLiBXaGVuIHRoZSB1c2VyIGNsaWNrcyB0aGUgbGluayBpbiB0aGUgZW1haWwsIHRoZSB1c2VyIGlzIHJlZGlyZWN0ZWQgYmFjayB0byB0aGUgVVJMIHlvdSBwcm92aWRlZCB3aXRoIHRoZSBzZWNyZXQga2V5IGFuZCB1c2VySWQgdmFsdWVzIGF0dGFjaGVkIHRvIHRoZSBVUkwgcXVlcnkgc3RyaW5nLiBVc2UgdGhlIHF1ZXJ5IHN0cmluZyBwYXJhbWV0ZXJzIHRvIHN1Ym1pdCBhIHJlcXVlc3QgdG8gdGhlIFtQT1NUIC92MS9hY2NvdW50L3Nlc3Npb25zL3Rva2VuXShodHRwczovL2FwcHdyaXRlLmlvL2RvY3MvcmVmZXJlbmNlcy9jbG91ZC9jbGllbnQtd2ViL2FjY291bnQjY3JlYXRlU2Vzc2lvbikgZW5kcG9pbnQgdG8gY29tcGxldGUgdGhlIGxvZ2luIHByb2Nlc3MuIFRoZSBsaW5rIHNlbnQgdG8gdGhlIHVzZXImIzAzOTtzIGVtYWlsIGFkZHJlc3MgaXMgdmFsaWQgZm9yIDEgaG91ci5cbiAgICAgKlxuICAgICAqIEEgdXNlciBpcyBsaW1pdGVkIHRvIDEwIGFjdGl2ZSBzZXNzaW9ucyBhdCBhIHRpbWUgYnkgZGVmYXVsdC4gW0xlYXJuIG1vcmUgYWJvdXQgc2Vzc2lvbiBsaW1pdHNdKGh0dHBzOi8vYXBwd3JpdGUuaW8vZG9jcy9hdXRoZW50aWNhdGlvbi1zZWN1cml0eSNsaW1pdHMpLlxuICAgICAqXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gdXNlcklkXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGVtYWlsXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IHVybFxuICAgICAqIEBwYXJhbSB7Ym9vbGVhbn0gcGhyYXNlXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8TW9kZWxzLlRva2VuPn1cbiAgICAgKi9cbiAgICBjcmVhdGVNYWdpY1VSTFRva2VuKHVzZXJJZCwgZW1haWwsIHVybCwgcGhyYXNlKSB7XG4gICAgICAgIGlmICh0eXBlb2YgdXNlcklkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJ1c2VySWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgZW1haWwgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcImVtYWlsXCInKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy9hY2NvdW50L3Rva2Vucy9tYWdpYy11cmwnO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGlmICh0eXBlb2YgdXNlcklkICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsndXNlcklkJ10gPSB1c2VySWQ7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBlbWFpbCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ2VtYWlsJ10gPSBlbWFpbDtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIHVybCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3VybCddID0gdXJsO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgcGhyYXNlICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsncGhyYXNlJ10gPSBwaHJhc2U7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgY29uc3QgYXBpSGVhZGVycyA9IHtcbiAgICAgICAgICAgICdjb250ZW50LXR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgIH07XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdwb3N0JywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogQWxsb3cgdGhlIHVzZXIgdG8gbG9naW4gdG8gdGhlaXIgYWNjb3VudCB1c2luZyB0aGUgT0F1dGgyIHByb3ZpZGVyIG9mIHRoZWlyIGNob2ljZS4gRWFjaCBPQXV0aDIgcHJvdmlkZXIgc2hvdWxkIGJlIGVuYWJsZWQgZnJvbSB0aGUgQXBwd3JpdGUgY29uc29sZSBmaXJzdC4gVXNlIHRoZSBzdWNjZXNzIGFuZCBmYWlsdXJlIGFyZ3VtZW50cyB0byBwcm92aWRlIGEgcmVkaXJlY3QgVVJMJiMwMzk7cyBiYWNrIHRvIHlvdXIgYXBwIHdoZW4gbG9naW4gaXMgY29tcGxldGVkLlxuICAgICAqXG4gICAgICogSWYgYXV0aGVudGljYXRpb24gc3VjY2VlZHMsIGB1c2VySWRgIGFuZCBgc2VjcmV0YCBvZiBhIHRva2VuIHdpbGwgYmUgYXBwZW5kZWQgdG8gdGhlIHN1Y2Nlc3MgVVJMIGFzIHF1ZXJ5IHBhcmFtZXRlcnMuIFRoZXNlIGNhbiBiZSB1c2VkIHRvIGNyZWF0ZSBhIG5ldyBzZXNzaW9uIHVzaW5nIHRoZSBbQ3JlYXRlIHNlc3Npb25dKGh0dHBzOi8vYXBwd3JpdGUuaW8vZG9jcy9yZWZlcmVuY2VzL2Nsb3VkL2NsaWVudC13ZWIvYWNjb3VudCNjcmVhdGVTZXNzaW9uKSBlbmRwb2ludC5cbiAgICAgKlxuICAgICAqIEEgdXNlciBpcyBsaW1pdGVkIHRvIDEwIGFjdGl2ZSBzZXNzaW9ucyBhdCBhIHRpbWUgYnkgZGVmYXVsdC4gW0xlYXJuIG1vcmUgYWJvdXQgc2Vzc2lvbiBsaW1pdHNdKGh0dHBzOi8vYXBwd3JpdGUuaW8vZG9jcy9hdXRoZW50aWNhdGlvbi1zZWN1cml0eSNsaW1pdHMpLlxuICAgICAqXG4gICAgICogQHBhcmFtIHtPQXV0aFByb3ZpZGVyfSBwcm92aWRlclxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBzdWNjZXNzXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGZhaWx1cmVcbiAgICAgKiBAcGFyYW0ge3N0cmluZ1tdfSBzY29wZXNcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7dm9pZCB8IHN0cmluZ31cbiAgICAgKi9cbiAgICBjcmVhdGVPQXV0aDJUb2tlbihwcm92aWRlciwgc3VjY2VzcywgZmFpbHVyZSwgc2NvcGVzKSB7XG4gICAgICAgIGlmICh0eXBlb2YgcHJvdmlkZXIgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcInByb3ZpZGVyXCInKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy9hY2NvdW50L3Rva2Vucy9vYXV0aDIve3Byb3ZpZGVyfScucmVwbGFjZSgne3Byb3ZpZGVyfScsIHByb3ZpZGVyKTtcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBpZiAodHlwZW9mIHN1Y2Nlc3MgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydzdWNjZXNzJ10gPSBzdWNjZXNzO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgZmFpbHVyZSAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ2ZhaWx1cmUnXSA9IGZhaWx1cmU7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBzY29wZXMgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydzY29wZXMnXSA9IHNjb3BlcztcbiAgICAgICAgfVxuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBwYXlsb2FkWydwcm9qZWN0J10gPSB0aGlzLmNsaWVudC5jb25maWcucHJvamVjdDtcbiAgICAgICAgZm9yIChjb25zdCBba2V5LCB2YWx1ZV0gb2YgT2JqZWN0LmVudHJpZXMoU2VydmljZS5mbGF0dGVuKHBheWxvYWQpKSkge1xuICAgICAgICAgICAgdXJpLnNlYXJjaFBhcmFtcy5hcHBlbmQoa2V5LCB2YWx1ZSk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnICYmICh3aW5kb3cgPT09IG51bGwgfHwgd2luZG93ID09PSB2b2lkIDAgPyB2b2lkIDAgOiB3aW5kb3cubG9jYXRpb24pKSB7XG4gICAgICAgICAgICB3aW5kb3cubG9jYXRpb24uaHJlZiA9IHVyaS50b1N0cmluZygpO1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgcmV0dXJuIHVyaS50b1N0cmluZygpO1xuICAgICAgICB9XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFNlbmRzIHRoZSB1c2VyIGFuIFNNUyB3aXRoIGEgc2VjcmV0IGtleSBmb3IgY3JlYXRpbmcgYSBzZXNzaW9uLiBJZiB0aGUgcHJvdmlkZWQgdXNlciBJRCBoYXMgbm90IGJlIHJlZ2lzdGVyZWQsIGEgbmV3IHVzZXIgd2lsbCBiZSBjcmVhdGVkLiBVc2UgdGhlIHJldHVybmVkIHVzZXIgSUQgYW5kIHNlY3JldCBhbmQgc3VibWl0IGEgcmVxdWVzdCB0byB0aGUgW1BPU1QgL3YxL2FjY291bnQvc2Vzc2lvbnMvdG9rZW5dKGh0dHBzOi8vYXBwd3JpdGUuaW8vZG9jcy9yZWZlcmVuY2VzL2Nsb3VkL2NsaWVudC13ZWIvYWNjb3VudCNjcmVhdGVTZXNzaW9uKSBlbmRwb2ludCB0byBjb21wbGV0ZSB0aGUgbG9naW4gcHJvY2Vzcy4gVGhlIHNlY3JldCBzZW50IHRvIHRoZSB1c2VyJiMwMzk7cyBwaG9uZSBpcyB2YWxpZCBmb3IgMTUgbWludXRlcy5cbiAgICAgKlxuICAgICAqIEEgdXNlciBpcyBsaW1pdGVkIHRvIDEwIGFjdGl2ZSBzZXNzaW9ucyBhdCBhIHRpbWUgYnkgZGVmYXVsdC4gW0xlYXJuIG1vcmUgYWJvdXQgc2Vzc2lvbiBsaW1pdHNdKGh0dHBzOi8vYXBwd3JpdGUuaW8vZG9jcy9hdXRoZW50aWNhdGlvbi1zZWN1cml0eSNsaW1pdHMpLlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IHVzZXJJZFxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBwaG9uZVxuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPE1vZGVscy5Ub2tlbj59XG4gICAgICovXG4gICAgY3JlYXRlUGhvbmVUb2tlbih1c2VySWQsIHBob25lKSB7XG4gICAgICAgIGlmICh0eXBlb2YgdXNlcklkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJ1c2VySWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgcGhvbmUgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcInBob25lXCInKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy9hY2NvdW50L3Rva2Vucy9waG9uZSc7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgaWYgKHR5cGVvZiB1c2VySWQgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWyd1c2VySWQnXSA9IHVzZXJJZDtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIHBob25lICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsncGhvbmUnXSA9IHBob25lO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7XG4gICAgICAgICAgICAnY29udGVudC10eXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgncG9zdCcsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFVzZSB0aGlzIGVuZHBvaW50IHRvIHNlbmQgYSB2ZXJpZmljYXRpb24gbWVzc2FnZSB0byB5b3VyIHVzZXIgZW1haWwgYWRkcmVzcyB0byBjb25maXJtIHRoZXkgYXJlIHRoZSB2YWxpZCBvd25lcnMgb2YgdGhhdCBhZGRyZXNzLiBCb3RoIHRoZSAqKnVzZXJJZCoqIGFuZCAqKnNlY3JldCoqIGFyZ3VtZW50cyB3aWxsIGJlIHBhc3NlZCBhcyBxdWVyeSBwYXJhbWV0ZXJzIHRvIHRoZSBVUkwgeW91IGhhdmUgcHJvdmlkZWQgdG8gYmUgYXR0YWNoZWQgdG8gdGhlIHZlcmlmaWNhdGlvbiBlbWFpbC4gVGhlIHByb3ZpZGVkIFVSTCBzaG91bGQgcmVkaXJlY3QgdGhlIHVzZXIgYmFjayB0byB5b3VyIGFwcCBhbmQgYWxsb3cgeW91IHRvIGNvbXBsZXRlIHRoZSB2ZXJpZmljYXRpb24gcHJvY2VzcyBieSB2ZXJpZnlpbmcgYm90aCB0aGUgKip1c2VySWQqKiBhbmQgKipzZWNyZXQqKiBwYXJhbWV0ZXJzLiBMZWFybiBtb3JlIGFib3V0IGhvdyB0byBbY29tcGxldGUgdGhlIHZlcmlmaWNhdGlvbiBwcm9jZXNzXShodHRwczovL2FwcHdyaXRlLmlvL2RvY3MvcmVmZXJlbmNlcy9jbG91ZC9jbGllbnQtd2ViL2FjY291bnQjdXBkYXRlVmVyaWZpY2F0aW9uKS4gVGhlIHZlcmlmaWNhdGlvbiBsaW5rIHNlbnQgdG8gdGhlIHVzZXImIzAzOTtzIGVtYWlsIGFkZHJlc3MgaXMgdmFsaWQgZm9yIDcgZGF5cy5cbiAgICAgKlxuICAgICAqIFBsZWFzZSBub3RlIHRoYXQgaW4gb3JkZXIgdG8gYXZvaWQgYSBbUmVkaXJlY3QgQXR0YWNrXShodHRwczovL2dpdGh1Yi5jb20vT1dBU1AvQ2hlYXRTaGVldFNlcmllcy9ibG9iL21hc3Rlci9jaGVhdHNoZWV0cy9VbnZhbGlkYXRlZF9SZWRpcmVjdHNfYW5kX0ZvcndhcmRzX0NoZWF0X1NoZWV0Lm1kKSwgdGhlIG9ubHkgdmFsaWQgcmVkaXJlY3QgVVJMcyBhcmUgdGhlIG9uZXMgZnJvbSBkb21haW5zIHlvdSBoYXZlIHNldCB3aGVuIGFkZGluZyB5b3VyIHBsYXRmb3JtcyBpbiB0aGUgY29uc29sZSBpbnRlcmZhY2UuXG4gICAgICpcbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSB1cmxcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTxNb2RlbHMuVG9rZW4+fVxuICAgICAqL1xuICAgIGNyZWF0ZVZlcmlmaWNhdGlvbih1cmwpIHtcbiAgICAgICAgaWYgKHR5cGVvZiB1cmwgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcInVybFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvYWNjb3VudC92ZXJpZmljYXRpb24nO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGlmICh0eXBlb2YgdXJsICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsndXJsJ10gPSB1cmw7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgY29uc3QgYXBpSGVhZGVycyA9IHtcbiAgICAgICAgICAgICdjb250ZW50LXR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgIH07XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdwb3N0JywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogVXNlIHRoaXMgZW5kcG9pbnQgdG8gY29tcGxldGUgdGhlIHVzZXIgZW1haWwgdmVyaWZpY2F0aW9uIHByb2Nlc3MuIFVzZSBib3RoIHRoZSAqKnVzZXJJZCoqIGFuZCAqKnNlY3JldCoqIHBhcmFtZXRlcnMgdGhhdCB3ZXJlIGF0dGFjaGVkIHRvIHlvdXIgYXBwIFVSTCB0byB2ZXJpZnkgdGhlIHVzZXIgZW1haWwgb3duZXJzaGlwLiBJZiBjb25maXJtZWQgdGhpcyByb3V0ZSB3aWxsIHJldHVybiBhIDIwMCBzdGF0dXMgY29kZS5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSB1c2VySWRcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gc2VjcmV0XG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8TW9kZWxzLlRva2VuPn1cbiAgICAgKi9cbiAgICB1cGRhdGVWZXJpZmljYXRpb24odXNlcklkLCBzZWNyZXQpIHtcbiAgICAgICAgaWYgKHR5cGVvZiB1c2VySWQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcInVzZXJJZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBzZWNyZXQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcInNlY3JldFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvYWNjb3VudC92ZXJpZmljYXRpb24nO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGlmICh0eXBlb2YgdXNlcklkICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsndXNlcklkJ10gPSB1c2VySWQ7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBzZWNyZXQgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydzZWNyZXQnXSA9IHNlY3JldDtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge1xuICAgICAgICAgICAgJ2NvbnRlbnQtdHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ3B1dCcsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFVzZSB0aGlzIGVuZHBvaW50IHRvIHNlbmQgYSB2ZXJpZmljYXRpb24gU01TIHRvIHRoZSBjdXJyZW50bHkgbG9nZ2VkIGluIHVzZXIuIFRoaXMgZW5kcG9pbnQgaXMgbWVhbnQgZm9yIHVzZSBhZnRlciB1cGRhdGluZyBhIHVzZXImIzAzOTtzIHBob25lIG51bWJlciB1c2luZyB0aGUgW2FjY291bnRVcGRhdGVQaG9uZV0oaHR0cHM6Ly9hcHB3cml0ZS5pby9kb2NzL3JlZmVyZW5jZXMvY2xvdWQvY2xpZW50LXdlYi9hY2NvdW50I3VwZGF0ZVBob25lKSBlbmRwb2ludC4gTGVhcm4gbW9yZSBhYm91dCBob3cgdG8gW2NvbXBsZXRlIHRoZSB2ZXJpZmljYXRpb24gcHJvY2Vzc10oaHR0cHM6Ly9hcHB3cml0ZS5pby9kb2NzL3JlZmVyZW5jZXMvY2xvdWQvY2xpZW50LXdlYi9hY2NvdW50I3VwZGF0ZVBob25lVmVyaWZpY2F0aW9uKS4gVGhlIHZlcmlmaWNhdGlvbiBjb2RlIHNlbnQgdG8gdGhlIHVzZXImIzAzOTtzIHBob25lIG51bWJlciBpcyB2YWxpZCBmb3IgMTUgbWludXRlcy5cbiAgICAgKlxuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPE1vZGVscy5Ub2tlbj59XG4gICAgICovXG4gICAgY3JlYXRlUGhvbmVWZXJpZmljYXRpb24oKSB7XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2FjY291bnQvdmVyaWZpY2F0aW9uL3Bob25lJztcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge1xuICAgICAgICAgICAgJ2NvbnRlbnQtdHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ3Bvc3QnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBVc2UgdGhpcyBlbmRwb2ludCB0byBjb21wbGV0ZSB0aGUgdXNlciBwaG9uZSB2ZXJpZmljYXRpb24gcHJvY2Vzcy4gVXNlIHRoZSAqKnVzZXJJZCoqIGFuZCAqKnNlY3JldCoqIHRoYXQgd2VyZSBzZW50IHRvIHlvdXIgdXNlciYjMDM5O3MgcGhvbmUgbnVtYmVyIHRvIHZlcmlmeSB0aGUgdXNlciBlbWFpbCBvd25lcnNoaXAuIElmIGNvbmZpcm1lZCB0aGlzIHJvdXRlIHdpbGwgcmV0dXJuIGEgMjAwIHN0YXR1cyBjb2RlLlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IHVzZXJJZFxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBzZWNyZXRcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTxNb2RlbHMuVG9rZW4+fVxuICAgICAqL1xuICAgIHVwZGF0ZVBob25lVmVyaWZpY2F0aW9uKHVzZXJJZCwgc2VjcmV0KSB7XG4gICAgICAgIGlmICh0eXBlb2YgdXNlcklkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJ1c2VySWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2Ygc2VjcmV0ID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJzZWNyZXRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2FjY291bnQvdmVyaWZpY2F0aW9uL3Bob25lJztcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBpZiAodHlwZW9mIHVzZXJJZCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3VzZXJJZCddID0gdXNlcklkO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2Ygc2VjcmV0ICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsnc2VjcmV0J10gPSBzZWNyZXQ7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgY29uc3QgYXBpSGVhZGVycyA9IHtcbiAgICAgICAgICAgICdjb250ZW50LXR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgIH07XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdwdXQnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbn1cblxuY2xhc3MgQXZhdGFycyB7XG4gICAgY29uc3RydWN0b3IoY2xpZW50KSB7XG4gICAgICAgIHRoaXMuY2xpZW50ID0gY2xpZW50O1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBZb3UgY2FuIHVzZSB0aGlzIGVuZHBvaW50IHRvIHNob3cgZGlmZmVyZW50IGJyb3dzZXIgaWNvbnMgdG8geW91ciB1c2Vycy4gVGhlIGNvZGUgYXJndW1lbnQgcmVjZWl2ZXMgdGhlIGJyb3dzZXIgY29kZSBhcyBpdCBhcHBlYXJzIGluIHlvdXIgdXNlciBbR0VUIC9hY2NvdW50L3Nlc3Npb25zXShodHRwczovL2FwcHdyaXRlLmlvL2RvY3MvcmVmZXJlbmNlcy9jbG91ZC9jbGllbnQtd2ViL2FjY291bnQjZ2V0U2Vzc2lvbnMpIGVuZHBvaW50LiBVc2Ugd2lkdGgsIGhlaWdodCBhbmQgcXVhbGl0eSBhcmd1bWVudHMgdG8gY2hhbmdlIHRoZSBvdXRwdXQgc2V0dGluZ3MuXG4gICAgICpcbiAgICAgKiBXaGVuIG9uZSBkaW1lbnNpb24gaXMgc3BlY2lmaWVkIGFuZCB0aGUgb3RoZXIgaXMgMCwgdGhlIGltYWdlIGlzIHNjYWxlZCB3aXRoIHByZXNlcnZlZCBhc3BlY3QgcmF0aW8uIElmIGJvdGggZGltZW5zaW9ucyBhcmUgMCwgdGhlIEFQSSBwcm92aWRlcyBhbiBpbWFnZSBhdCBzb3VyY2UgcXVhbGl0eS4gSWYgZGltZW5zaW9ucyBhcmUgbm90IHNwZWNpZmllZCwgdGhlIGRlZmF1bHQgc2l6ZSBvZiBpbWFnZSByZXR1cm5lZCBpcyAxMDB4MTAwcHguXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge0Jyb3dzZXJ9IGNvZGVcbiAgICAgKiBAcGFyYW0ge251bWJlcn0gd2lkdGhcbiAgICAgKiBAcGFyYW0ge251bWJlcn0gaGVpZ2h0XG4gICAgICogQHBhcmFtIHtudW1iZXJ9IHF1YWxpdHlcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7c3RyaW5nfVxuICAgICAqL1xuICAgIGdldEJyb3dzZXIoY29kZSwgd2lkdGgsIGhlaWdodCwgcXVhbGl0eSkge1xuICAgICAgICBpZiAodHlwZW9mIGNvZGUgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcImNvZGVcIicpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2F2YXRhcnMvYnJvd3NlcnMve2NvZGV9Jy5yZXBsYWNlKCd7Y29kZX0nLCBjb2RlKTtcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBpZiAodHlwZW9mIHdpZHRoICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsnd2lkdGgnXSA9IHdpZHRoO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgaGVpZ2h0ICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsnaGVpZ2h0J10gPSBoZWlnaHQ7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBxdWFsaXR5ICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsncXVhbGl0eSddID0gcXVhbGl0eTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBwYXlsb2FkWydwcm9qZWN0J10gPSB0aGlzLmNsaWVudC5jb25maWcucHJvamVjdDtcbiAgICAgICAgZm9yIChjb25zdCBba2V5LCB2YWx1ZV0gb2YgT2JqZWN0LmVudHJpZXMoU2VydmljZS5mbGF0dGVuKHBheWxvYWQpKSkge1xuICAgICAgICAgICAgdXJpLnNlYXJjaFBhcmFtcy5hcHBlbmQoa2V5LCB2YWx1ZSk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHVyaS50b1N0cmluZygpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBUaGUgY3JlZGl0IGNhcmQgZW5kcG9pbnQgd2lsbCByZXR1cm4geW91IHRoZSBpY29uIG9mIHRoZSBjcmVkaXQgY2FyZCBwcm92aWRlciB5b3UgbmVlZC4gVXNlIHdpZHRoLCBoZWlnaHQgYW5kIHF1YWxpdHkgYXJndW1lbnRzIHRvIGNoYW5nZSB0aGUgb3V0cHV0IHNldHRpbmdzLlxuICAgICAqXG4gICAgICogV2hlbiBvbmUgZGltZW5zaW9uIGlzIHNwZWNpZmllZCBhbmQgdGhlIG90aGVyIGlzIDAsIHRoZSBpbWFnZSBpcyBzY2FsZWQgd2l0aCBwcmVzZXJ2ZWQgYXNwZWN0IHJhdGlvLiBJZiBib3RoIGRpbWVuc2lvbnMgYXJlIDAsIHRoZSBBUEkgcHJvdmlkZXMgYW4gaW1hZ2UgYXQgc291cmNlIHF1YWxpdHkuIElmIGRpbWVuc2lvbnMgYXJlIG5vdCBzcGVjaWZpZWQsIHRoZSBkZWZhdWx0IHNpemUgb2YgaW1hZ2UgcmV0dXJuZWQgaXMgMTAweDEwMHB4LlxuICAgICAqXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge0NyZWRpdENhcmR9IGNvZGVcbiAgICAgKiBAcGFyYW0ge251bWJlcn0gd2lkdGhcbiAgICAgKiBAcGFyYW0ge251bWJlcn0gaGVpZ2h0XG4gICAgICogQHBhcmFtIHtudW1iZXJ9IHF1YWxpdHlcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7c3RyaW5nfVxuICAgICAqL1xuICAgIGdldENyZWRpdENhcmQoY29kZSwgd2lkdGgsIGhlaWdodCwgcXVhbGl0eSkge1xuICAgICAgICBpZiAodHlwZW9mIGNvZGUgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcImNvZGVcIicpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2F2YXRhcnMvY3JlZGl0LWNhcmRzL3tjb2RlfScucmVwbGFjZSgne2NvZGV9JywgY29kZSk7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgaWYgKHR5cGVvZiB3aWR0aCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3dpZHRoJ10gPSB3aWR0aDtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIGhlaWdodCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ2hlaWdodCddID0gaGVpZ2h0O1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgcXVhbGl0eSAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3F1YWxpdHknXSA9IHF1YWxpdHk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgcGF5bG9hZFsncHJvamVjdCddID0gdGhpcy5jbGllbnQuY29uZmlnLnByb2plY3Q7XG4gICAgICAgIGZvciAoY29uc3QgW2tleSwgdmFsdWVdIG9mIE9iamVjdC5lbnRyaWVzKFNlcnZpY2UuZmxhdHRlbihwYXlsb2FkKSkpIHtcbiAgICAgICAgICAgIHVyaS5zZWFyY2hQYXJhbXMuYXBwZW5kKGtleSwgdmFsdWUpO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiB1cmkudG9TdHJpbmcoKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogVXNlIHRoaXMgZW5kcG9pbnQgdG8gZmV0Y2ggdGhlIGZhdm9yaXRlIGljb24gKEFLQSBmYXZpY29uKSBvZiBhbnkgcmVtb3RlIHdlYnNpdGUgVVJMLlxuICAgICAqXG4gICAgICogVGhpcyBlbmRwb2ludCBkb2VzIG5vdCBmb2xsb3cgSFRUUCByZWRpcmVjdHMuXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gdXJsXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge3N0cmluZ31cbiAgICAgKi9cbiAgICBnZXRGYXZpY29uKHVybCkge1xuICAgICAgICBpZiAodHlwZW9mIHVybCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwidXJsXCInKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy9hdmF0YXJzL2Zhdmljb24nO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGlmICh0eXBlb2YgdXJsICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsndXJsJ10gPSB1cmw7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgcGF5bG9hZFsncHJvamVjdCddID0gdGhpcy5jbGllbnQuY29uZmlnLnByb2plY3Q7XG4gICAgICAgIGZvciAoY29uc3QgW2tleSwgdmFsdWVdIG9mIE9iamVjdC5lbnRyaWVzKFNlcnZpY2UuZmxhdHRlbihwYXlsb2FkKSkpIHtcbiAgICAgICAgICAgIHVyaS5zZWFyY2hQYXJhbXMuYXBwZW5kKGtleSwgdmFsdWUpO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiB1cmkudG9TdHJpbmcoKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogWW91IGNhbiB1c2UgdGhpcyBlbmRwb2ludCB0byBzaG93IGRpZmZlcmVudCBjb3VudHJ5IGZsYWdzIGljb25zIHRvIHlvdXIgdXNlcnMuIFRoZSBjb2RlIGFyZ3VtZW50IHJlY2VpdmVzIHRoZSAyIGxldHRlciBjb3VudHJ5IGNvZGUuIFVzZSB3aWR0aCwgaGVpZ2h0IGFuZCBxdWFsaXR5IGFyZ3VtZW50cyB0byBjaGFuZ2UgdGhlIG91dHB1dCBzZXR0aW5ncy4gQ291bnRyeSBjb2RlcyBmb2xsb3cgdGhlIFtJU08gMzE2Ni0xXShodHRwczovL2VuLndpa2lwZWRpYS5vcmcvd2lraS9JU09fMzE2Ni0xKSBzdGFuZGFyZC5cbiAgICAgKlxuICAgICAqIFdoZW4gb25lIGRpbWVuc2lvbiBpcyBzcGVjaWZpZWQgYW5kIHRoZSBvdGhlciBpcyAwLCB0aGUgaW1hZ2UgaXMgc2NhbGVkIHdpdGggcHJlc2VydmVkIGFzcGVjdCByYXRpby4gSWYgYm90aCBkaW1lbnNpb25zIGFyZSAwLCB0aGUgQVBJIHByb3ZpZGVzIGFuIGltYWdlIGF0IHNvdXJjZSBxdWFsaXR5LiBJZiBkaW1lbnNpb25zIGFyZSBub3Qgc3BlY2lmaWVkLCB0aGUgZGVmYXVsdCBzaXplIG9mIGltYWdlIHJldHVybmVkIGlzIDEwMHgxMDBweC5cbiAgICAgKlxuICAgICAqXG4gICAgICogQHBhcmFtIHtGbGFnfSBjb2RlXG4gICAgICogQHBhcmFtIHtudW1iZXJ9IHdpZHRoXG4gICAgICogQHBhcmFtIHtudW1iZXJ9IGhlaWdodFxuICAgICAqIEBwYXJhbSB7bnVtYmVyfSBxdWFsaXR5XG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge3N0cmluZ31cbiAgICAgKi9cbiAgICBnZXRGbGFnKGNvZGUsIHdpZHRoLCBoZWlnaHQsIHF1YWxpdHkpIHtcbiAgICAgICAgaWYgKHR5cGVvZiBjb2RlID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJjb2RlXCInKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy9hdmF0YXJzL2ZsYWdzL3tjb2RlfScucmVwbGFjZSgne2NvZGV9JywgY29kZSk7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgaWYgKHR5cGVvZiB3aWR0aCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3dpZHRoJ10gPSB3aWR0aDtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIGhlaWdodCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ2hlaWdodCddID0gaGVpZ2h0O1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgcXVhbGl0eSAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3F1YWxpdHknXSA9IHF1YWxpdHk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgcGF5bG9hZFsncHJvamVjdCddID0gdGhpcy5jbGllbnQuY29uZmlnLnByb2plY3Q7XG4gICAgICAgIGZvciAoY29uc3QgW2tleSwgdmFsdWVdIG9mIE9iamVjdC5lbnRyaWVzKFNlcnZpY2UuZmxhdHRlbihwYXlsb2FkKSkpIHtcbiAgICAgICAgICAgIHVyaS5zZWFyY2hQYXJhbXMuYXBwZW5kKGtleSwgdmFsdWUpO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiB1cmkudG9TdHJpbmcoKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogVXNlIHRoaXMgZW5kcG9pbnQgdG8gZmV0Y2ggYSByZW1vdGUgaW1hZ2UgVVJMIGFuZCBjcm9wIGl0IHRvIGFueSBpbWFnZSBzaXplIHlvdSB3YW50LiBUaGlzIGVuZHBvaW50IGlzIHZlcnkgdXNlZnVsIGlmIHlvdSBuZWVkIHRvIGNyb3AgYW5kIGRpc3BsYXkgcmVtb3RlIGltYWdlcyBpbiB5b3VyIGFwcCBvciBpbiBjYXNlIHlvdSB3YW50IHRvIG1ha2Ugc3VyZSBhIDNyZCBwYXJ0eSBpbWFnZSBpcyBwcm9wZXJseSBzZXJ2ZWQgdXNpbmcgYSBUTFMgcHJvdG9jb2wuXG4gICAgICpcbiAgICAgKiBXaGVuIG9uZSBkaW1lbnNpb24gaXMgc3BlY2lmaWVkIGFuZCB0aGUgb3RoZXIgaXMgMCwgdGhlIGltYWdlIGlzIHNjYWxlZCB3aXRoIHByZXNlcnZlZCBhc3BlY3QgcmF0aW8uIElmIGJvdGggZGltZW5zaW9ucyBhcmUgMCwgdGhlIEFQSSBwcm92aWRlcyBhbiBpbWFnZSBhdCBzb3VyY2UgcXVhbGl0eS4gSWYgZGltZW5zaW9ucyBhcmUgbm90IHNwZWNpZmllZCwgdGhlIGRlZmF1bHQgc2l6ZSBvZiBpbWFnZSByZXR1cm5lZCBpcyA0MDB4NDAwcHguXG4gICAgICpcbiAgICAgKiBUaGlzIGVuZHBvaW50IGRvZXMgbm90IGZvbGxvdyBIVFRQIHJlZGlyZWN0cy5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSB1cmxcbiAgICAgKiBAcGFyYW0ge251bWJlcn0gd2lkdGhcbiAgICAgKiBAcGFyYW0ge251bWJlcn0gaGVpZ2h0XG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge3N0cmluZ31cbiAgICAgKi9cbiAgICBnZXRJbWFnZSh1cmwsIHdpZHRoLCBoZWlnaHQpIHtcbiAgICAgICAgaWYgKHR5cGVvZiB1cmwgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcInVybFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvYXZhdGFycy9pbWFnZSc7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgaWYgKHR5cGVvZiB1cmwgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWyd1cmwnXSA9IHVybDtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIHdpZHRoICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsnd2lkdGgnXSA9IHdpZHRoO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgaGVpZ2h0ICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsnaGVpZ2h0J10gPSBoZWlnaHQ7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgcGF5bG9hZFsncHJvamVjdCddID0gdGhpcy5jbGllbnQuY29uZmlnLnByb2plY3Q7XG4gICAgICAgIGZvciAoY29uc3QgW2tleSwgdmFsdWVdIG9mIE9iamVjdC5lbnRyaWVzKFNlcnZpY2UuZmxhdHRlbihwYXlsb2FkKSkpIHtcbiAgICAgICAgICAgIHVyaS5zZWFyY2hQYXJhbXMuYXBwZW5kKGtleSwgdmFsdWUpO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiB1cmkudG9TdHJpbmcoKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogVXNlIHRoaXMgZW5kcG9pbnQgdG8gc2hvdyB5b3VyIHVzZXIgaW5pdGlhbHMgYXZhdGFyIGljb24gb24geW91ciB3ZWJzaXRlIG9yIGFwcC4gQnkgZGVmYXVsdCwgdGhpcyByb3V0ZSB3aWxsIHRyeSB0byBwcmludCB5b3VyIGxvZ2dlZC1pbiB1c2VyIG5hbWUgb3IgZW1haWwgaW5pdGlhbHMuIFlvdSBjYW4gYWxzbyBvdmVyd3JpdGUgdGhlIHVzZXIgbmFtZSBpZiB5b3UgcGFzcyB0aGUgJiMwMzk7bmFtZSYjMDM5OyBwYXJhbWV0ZXIuIElmIG5vIG5hbWUgaXMgZ2l2ZW4gYW5kIG5vIHVzZXIgaXMgbG9nZ2VkLCBhbiBlbXB0eSBhdmF0YXIgd2lsbCBiZSByZXR1cm5lZC5cbiAgICAgKlxuICAgICAqIFlvdSBjYW4gdXNlIHRoZSBjb2xvciBhbmQgYmFja2dyb3VuZCBwYXJhbXMgdG8gY2hhbmdlIHRoZSBhdmF0YXIgY29sb3JzLiBCeSBkZWZhdWx0LCBhIHJhbmRvbSB0aGVtZSB3aWxsIGJlIHNlbGVjdGVkLiBUaGUgcmFuZG9tIHRoZW1lIHdpbGwgcGVyc2lzdCBmb3IgdGhlIHVzZXImIzAzOTtzIGluaXRpYWxzIHdoZW4gcmVsb2FkaW5nIHRoZSBzYW1lIHRoZW1lIHdpbGwgYWx3YXlzIHJldHVybiBmb3IgdGhlIHNhbWUgaW5pdGlhbHMuXG4gICAgICpcbiAgICAgKiBXaGVuIG9uZSBkaW1lbnNpb24gaXMgc3BlY2lmaWVkIGFuZCB0aGUgb3RoZXIgaXMgMCwgdGhlIGltYWdlIGlzIHNjYWxlZCB3aXRoIHByZXNlcnZlZCBhc3BlY3QgcmF0aW8uIElmIGJvdGggZGltZW5zaW9ucyBhcmUgMCwgdGhlIEFQSSBwcm92aWRlcyBhbiBpbWFnZSBhdCBzb3VyY2UgcXVhbGl0eS4gSWYgZGltZW5zaW9ucyBhcmUgbm90IHNwZWNpZmllZCwgdGhlIGRlZmF1bHQgc2l6ZSBvZiBpbWFnZSByZXR1cm5lZCBpcyAxMDB4MTAwcHguXG4gICAgICpcbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBuYW1lXG4gICAgICogQHBhcmFtIHtudW1iZXJ9IHdpZHRoXG4gICAgICogQHBhcmFtIHtudW1iZXJ9IGhlaWdodFxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBiYWNrZ3JvdW5kXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge3N0cmluZ31cbiAgICAgKi9cbiAgICBnZXRJbml0aWFscyhuYW1lLCB3aWR0aCwgaGVpZ2h0LCBiYWNrZ3JvdW5kKSB7XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2F2YXRhcnMvaW5pdGlhbHMnO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGlmICh0eXBlb2YgbmFtZSAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ25hbWUnXSA9IG5hbWU7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiB3aWR0aCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3dpZHRoJ10gPSB3aWR0aDtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIGhlaWdodCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ2hlaWdodCddID0gaGVpZ2h0O1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgYmFja2dyb3VuZCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ2JhY2tncm91bmQnXSA9IGJhY2tncm91bmQ7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgcGF5bG9hZFsncHJvamVjdCddID0gdGhpcy5jbGllbnQuY29uZmlnLnByb2plY3Q7XG4gICAgICAgIGZvciAoY29uc3QgW2tleSwgdmFsdWVdIG9mIE9iamVjdC5lbnRyaWVzKFNlcnZpY2UuZmxhdHRlbihwYXlsb2FkKSkpIHtcbiAgICAgICAgICAgIHVyaS5zZWFyY2hQYXJhbXMuYXBwZW5kKGtleSwgdmFsdWUpO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiB1cmkudG9TdHJpbmcoKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogQ29udmVydHMgYSBnaXZlbiBwbGFpbiB0ZXh0IHRvIGEgUVIgY29kZSBpbWFnZS4gWW91IGNhbiB1c2UgdGhlIHF1ZXJ5IHBhcmFtZXRlcnMgdG8gY2hhbmdlIHRoZSBzaXplIGFuZCBzdHlsZSBvZiB0aGUgcmVzdWx0aW5nIGltYWdlLlxuICAgICAqXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gdGV4dFxuICAgICAqIEBwYXJhbSB7bnVtYmVyfSBzaXplXG4gICAgICogQHBhcmFtIHtudW1iZXJ9IG1hcmdpblxuICAgICAqIEBwYXJhbSB7Ym9vbGVhbn0gZG93bmxvYWRcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7c3RyaW5nfVxuICAgICAqL1xuICAgIGdldFFSKHRleHQsIHNpemUsIG1hcmdpbiwgZG93bmxvYWQpIHtcbiAgICAgICAgaWYgKHR5cGVvZiB0ZXh0ID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJ0ZXh0XCInKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy9hdmF0YXJzL3FyJztcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBpZiAodHlwZW9mIHRleHQgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWyd0ZXh0J10gPSB0ZXh0O1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2Ygc2l6ZSAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3NpemUnXSA9IHNpemU7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBtYXJnaW4gIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydtYXJnaW4nXSA9IG1hcmdpbjtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIGRvd25sb2FkICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsnZG93bmxvYWQnXSA9IGRvd25sb2FkO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIHBheWxvYWRbJ3Byb2plY3QnXSA9IHRoaXMuY2xpZW50LmNvbmZpZy5wcm9qZWN0O1xuICAgICAgICBmb3IgKGNvbnN0IFtrZXksIHZhbHVlXSBvZiBPYmplY3QuZW50cmllcyhTZXJ2aWNlLmZsYXR0ZW4ocGF5bG9hZCkpKSB7XG4gICAgICAgICAgICB1cmkuc2VhcmNoUGFyYW1zLmFwcGVuZChrZXksIHZhbHVlKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gdXJpLnRvU3RyaW5nKCk7XG4gICAgfVxufVxuXG5jbGFzcyBEYXRhYmFzZXMge1xuICAgIGNvbnN0cnVjdG9yKGNsaWVudCkge1xuICAgICAgICB0aGlzLmNsaWVudCA9IGNsaWVudDtcbiAgICB9XG4gICAgLyoqXG4gICAgICogR2V0IGEgbGlzdCBvZiBhbGwgdGhlIHVzZXImIzAzOTtzIGRvY3VtZW50cyBpbiBhIGdpdmVuIGNvbGxlY3Rpb24uIFlvdSBjYW4gdXNlIHRoZSBxdWVyeSBwYXJhbXMgdG8gZmlsdGVyIHlvdXIgcmVzdWx0cy5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBkYXRhYmFzZUlkXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGNvbGxlY3Rpb25JZFxuICAgICAqIEBwYXJhbSB7c3RyaW5nW119IHF1ZXJpZXNcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTxNb2RlbHMuRG9jdW1lbnRMaXN0PERvY3VtZW50Pj59XG4gICAgICovXG4gICAgbGlzdERvY3VtZW50cyhkYXRhYmFzZUlkLCBjb2xsZWN0aW9uSWQsIHF1ZXJpZXMpIHtcbiAgICAgICAgaWYgKHR5cGVvZiBkYXRhYmFzZUlkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJkYXRhYmFzZUlkXCInKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIGNvbGxlY3Rpb25JZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwiY29sbGVjdGlvbklkXCInKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy9kYXRhYmFzZXMve2RhdGFiYXNlSWR9L2NvbGxlY3Rpb25zL3tjb2xsZWN0aW9uSWR9L2RvY3VtZW50cycucmVwbGFjZSgne2RhdGFiYXNlSWR9JywgZGF0YWJhc2VJZCkucmVwbGFjZSgne2NvbGxlY3Rpb25JZH0nLCBjb2xsZWN0aW9uSWQpO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGlmICh0eXBlb2YgcXVlcmllcyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3F1ZXJpZXMnXSA9IHF1ZXJpZXM7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgY29uc3QgYXBpSGVhZGVycyA9IHt9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgnZ2V0JywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogQ3JlYXRlIGEgbmV3IERvY3VtZW50LiBCZWZvcmUgdXNpbmcgdGhpcyByb3V0ZSwgeW91IHNob3VsZCBjcmVhdGUgYSBuZXcgY29sbGVjdGlvbiByZXNvdXJjZSB1c2luZyBlaXRoZXIgYSBbc2VydmVyIGludGVncmF0aW9uXShodHRwczovL2FwcHdyaXRlLmlvL2RvY3Mvc2VydmVyL2RhdGFiYXNlcyNkYXRhYmFzZXNDcmVhdGVDb2xsZWN0aW9uKSBBUEkgb3IgZGlyZWN0bHkgZnJvbSB5b3VyIGRhdGFiYXNlIGNvbnNvbGUuXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gZGF0YWJhc2VJZFxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBjb2xsZWN0aW9uSWRcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gZG9jdW1lbnRJZFxuICAgICAqIEBwYXJhbSB7RG9jdW1lbnQgZXh0ZW5kcyBNb2RlbHMuRGVmYXVsdERvY3VtZW50ID8gTW9kZWxzLkRhdGFXaXRob3V0RG9jdW1lbnRLZXlzIDogT21pdDxEb2N1bWVudCwga2V5b2YgTW9kZWxzLkRvY3VtZW50Pn0gZGF0YVxuICAgICAqIEBwYXJhbSB7c3RyaW5nW119IHBlcm1pc3Npb25zXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8RG9jdW1lbnQ+fVxuICAgICAqL1xuICAgIGNyZWF0ZURvY3VtZW50KGRhdGFiYXNlSWQsIGNvbGxlY3Rpb25JZCwgZG9jdW1lbnRJZCwgZGF0YSwgcGVybWlzc2lvbnMpIHtcbiAgICAgICAgaWYgKHR5cGVvZiBkYXRhYmFzZUlkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJkYXRhYmFzZUlkXCInKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIGNvbGxlY3Rpb25JZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwiY29sbGVjdGlvbklkXCInKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIGRvY3VtZW50SWQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcImRvY3VtZW50SWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgZGF0YSA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwiZGF0YVwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvZGF0YWJhc2VzL3tkYXRhYmFzZUlkfS9jb2xsZWN0aW9ucy97Y29sbGVjdGlvbklkfS9kb2N1bWVudHMnLnJlcGxhY2UoJ3tkYXRhYmFzZUlkfScsIGRhdGFiYXNlSWQpLnJlcGxhY2UoJ3tjb2xsZWN0aW9uSWR9JywgY29sbGVjdGlvbklkKTtcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBpZiAodHlwZW9mIGRvY3VtZW50SWQgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydkb2N1bWVudElkJ10gPSBkb2N1bWVudElkO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgZGF0YSAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ2RhdGEnXSA9IGRhdGE7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBwZXJtaXNzaW9ucyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3Blcm1pc3Npb25zJ10gPSBwZXJtaXNzaW9ucztcbiAgICAgICAgfVxuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge1xuICAgICAgICAgICAgJ2NvbnRlbnQtdHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ3Bvc3QnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBHZXQgYSBkb2N1bWVudCBieSBpdHMgdW5pcXVlIElELiBUaGlzIGVuZHBvaW50IHJlc3BvbnNlIHJldHVybnMgYSBKU09OIG9iamVjdCB3aXRoIHRoZSBkb2N1bWVudCBkYXRhLlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGRhdGFiYXNlSWRcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gY29sbGVjdGlvbklkXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGRvY3VtZW50SWRcbiAgICAgKiBAcGFyYW0ge3N0cmluZ1tdfSBxdWVyaWVzXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8RG9jdW1lbnQ+fVxuICAgICAqL1xuICAgIGdldERvY3VtZW50KGRhdGFiYXNlSWQsIGNvbGxlY3Rpb25JZCwgZG9jdW1lbnRJZCwgcXVlcmllcykge1xuICAgICAgICBpZiAodHlwZW9mIGRhdGFiYXNlSWQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcImRhdGFiYXNlSWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgY29sbGVjdGlvbklkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJjb2xsZWN0aW9uSWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgZG9jdW1lbnRJZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwiZG9jdW1lbnRJZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvZGF0YWJhc2VzL3tkYXRhYmFzZUlkfS9jb2xsZWN0aW9ucy97Y29sbGVjdGlvbklkfS9kb2N1bWVudHMve2RvY3VtZW50SWR9Jy5yZXBsYWNlKCd7ZGF0YWJhc2VJZH0nLCBkYXRhYmFzZUlkKS5yZXBsYWNlKCd7Y29sbGVjdGlvbklkfScsIGNvbGxlY3Rpb25JZCkucmVwbGFjZSgne2RvY3VtZW50SWR9JywgZG9jdW1lbnRJZCk7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgaWYgKHR5cGVvZiBxdWVyaWVzICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsncXVlcmllcyddID0gcXVlcmllcztcbiAgICAgICAgfVxuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge307XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdnZXQnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiAqKldBUk5JTkc6IEV4cGVyaW1lbnRhbCBGZWF0dXJlKiogLSBUaGlzIGVuZHBvaW50IGlzIGV4cGVyaW1lbnRhbCBhbmQgbm90IHlldCBvZmZpY2lhbGx5IHN1cHBvcnRlZC4gSXQgbWF5IGJlIHN1YmplY3QgdG8gYnJlYWtpbmcgY2hhbmdlcyBvciByZW1vdmFsIGluIGZ1dHVyZSB2ZXJzaW9ucy5cbiAgICAgKlxuICAgICAqIENyZWF0ZSBvciB1cGRhdGUgYSBEb2N1bWVudC4gQmVmb3JlIHVzaW5nIHRoaXMgcm91dGUsIHlvdSBzaG91bGQgY3JlYXRlIGEgbmV3IGNvbGxlY3Rpb24gcmVzb3VyY2UgdXNpbmcgZWl0aGVyIGEgW3NlcnZlciBpbnRlZ3JhdGlvbl0oaHR0cHM6Ly9hcHB3cml0ZS5pby9kb2NzL3NlcnZlci9kYXRhYmFzZXMjZGF0YWJhc2VzQ3JlYXRlQ29sbGVjdGlvbikgQVBJIG9yIGRpcmVjdGx5IGZyb20geW91ciBkYXRhYmFzZSBjb25zb2xlLlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGRhdGFiYXNlSWRcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gY29sbGVjdGlvbklkXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGRvY3VtZW50SWRcbiAgICAgKiBAcGFyYW0ge29iamVjdH0gZGF0YVxuICAgICAqIEBwYXJhbSB7c3RyaW5nW119IHBlcm1pc3Npb25zXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8RG9jdW1lbnQ+fVxuICAgICAqL1xuICAgIHVwc2VydERvY3VtZW50KGRhdGFiYXNlSWQsIGNvbGxlY3Rpb25JZCwgZG9jdW1lbnRJZCwgZGF0YSwgcGVybWlzc2lvbnMpIHtcbiAgICAgICAgaWYgKHR5cGVvZiBkYXRhYmFzZUlkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJkYXRhYmFzZUlkXCInKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIGNvbGxlY3Rpb25JZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwiY29sbGVjdGlvbklkXCInKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIGRvY3VtZW50SWQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcImRvY3VtZW50SWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgZGF0YSA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwiZGF0YVwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvZGF0YWJhc2VzL3tkYXRhYmFzZUlkfS9jb2xsZWN0aW9ucy97Y29sbGVjdGlvbklkfS9kb2N1bWVudHMve2RvY3VtZW50SWR9Jy5yZXBsYWNlKCd7ZGF0YWJhc2VJZH0nLCBkYXRhYmFzZUlkKS5yZXBsYWNlKCd7Y29sbGVjdGlvbklkfScsIGNvbGxlY3Rpb25JZCkucmVwbGFjZSgne2RvY3VtZW50SWR9JywgZG9jdW1lbnRJZCk7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgaWYgKHR5cGVvZiBkYXRhICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsnZGF0YSddID0gZGF0YTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIHBlcm1pc3Npb25zICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsncGVybWlzc2lvbnMnXSA9IHBlcm1pc3Npb25zO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7XG4gICAgICAgICAgICAnY29udGVudC10eXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgncHV0JywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogVXBkYXRlIGEgZG9jdW1lbnQgYnkgaXRzIHVuaXF1ZSBJRC4gVXNpbmcgdGhlIHBhdGNoIG1ldGhvZCB5b3UgY2FuIHBhc3Mgb25seSBzcGVjaWZpYyBmaWVsZHMgdGhhdCB3aWxsIGdldCB1cGRhdGVkLlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGRhdGFiYXNlSWRcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gY29sbGVjdGlvbklkXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGRvY3VtZW50SWRcbiAgICAgKiBAcGFyYW0ge1BhcnRpYWw8RG9jdW1lbnQgZXh0ZW5kcyBNb2RlbHMuRGVmYXVsdERvY3VtZW50ID8gTW9kZWxzLkRhdGFXaXRob3V0RG9jdW1lbnRLZXlzIDogT21pdDxEb2N1bWVudCwga2V5b2YgTW9kZWxzLkRvY3VtZW50Pj59IGRhdGFcbiAgICAgKiBAcGFyYW0ge3N0cmluZ1tdfSBwZXJtaXNzaW9uc1xuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPERvY3VtZW50Pn1cbiAgICAgKi9cbiAgICB1cGRhdGVEb2N1bWVudChkYXRhYmFzZUlkLCBjb2xsZWN0aW9uSWQsIGRvY3VtZW50SWQsIGRhdGEsIHBlcm1pc3Npb25zKSB7XG4gICAgICAgIGlmICh0eXBlb2YgZGF0YWJhc2VJZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwiZGF0YWJhc2VJZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBjb2xsZWN0aW9uSWQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcImNvbGxlY3Rpb25JZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBkb2N1bWVudElkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJkb2N1bWVudElkXCInKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy9kYXRhYmFzZXMve2RhdGFiYXNlSWR9L2NvbGxlY3Rpb25zL3tjb2xsZWN0aW9uSWR9L2RvY3VtZW50cy97ZG9jdW1lbnRJZH0nLnJlcGxhY2UoJ3tkYXRhYmFzZUlkfScsIGRhdGFiYXNlSWQpLnJlcGxhY2UoJ3tjb2xsZWN0aW9uSWR9JywgY29sbGVjdGlvbklkKS5yZXBsYWNlKCd7ZG9jdW1lbnRJZH0nLCBkb2N1bWVudElkKTtcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBpZiAodHlwZW9mIGRhdGEgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydkYXRhJ10gPSBkYXRhO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgcGVybWlzc2lvbnMgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydwZXJtaXNzaW9ucyddID0gcGVybWlzc2lvbnM7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgY29uc3QgYXBpSGVhZGVycyA9IHtcbiAgICAgICAgICAgICdjb250ZW50LXR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgIH07XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdwYXRjaCcsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIERlbGV0ZSBhIGRvY3VtZW50IGJ5IGl0cyB1bmlxdWUgSUQuXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gZGF0YWJhc2VJZFxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBjb2xsZWN0aW9uSWRcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gZG9jdW1lbnRJZFxuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPHt9Pn1cbiAgICAgKi9cbiAgICBkZWxldGVEb2N1bWVudChkYXRhYmFzZUlkLCBjb2xsZWN0aW9uSWQsIGRvY3VtZW50SWQpIHtcbiAgICAgICAgaWYgKHR5cGVvZiBkYXRhYmFzZUlkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJkYXRhYmFzZUlkXCInKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIGNvbGxlY3Rpb25JZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwiY29sbGVjdGlvbklkXCInKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIGRvY3VtZW50SWQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcImRvY3VtZW50SWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2RhdGFiYXNlcy97ZGF0YWJhc2VJZH0vY29sbGVjdGlvbnMve2NvbGxlY3Rpb25JZH0vZG9jdW1lbnRzL3tkb2N1bWVudElkfScucmVwbGFjZSgne2RhdGFiYXNlSWR9JywgZGF0YWJhc2VJZCkucmVwbGFjZSgne2NvbGxlY3Rpb25JZH0nLCBjb2xsZWN0aW9uSWQpLnJlcGxhY2UoJ3tkb2N1bWVudElkfScsIGRvY3VtZW50SWQpO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7XG4gICAgICAgICAgICAnY29udGVudC10eXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgnZGVsZXRlJywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogRGVjcmVtZW50IGEgc3BlY2lmaWMgYXR0cmlidXRlIG9mIGEgZG9jdW1lbnQgYnkgYSBnaXZlbiB2YWx1ZS5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBkYXRhYmFzZUlkXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGNvbGxlY3Rpb25JZFxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBkb2N1bWVudElkXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGF0dHJpYnV0ZVxuICAgICAqIEBwYXJhbSB7bnVtYmVyfSB2YWx1ZVxuICAgICAqIEBwYXJhbSB7bnVtYmVyfSBtaW5cbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTxEb2N1bWVudD59XG4gICAgICovXG4gICAgZGVjcmVtZW50RG9jdW1lbnRBdHRyaWJ1dGUoZGF0YWJhc2VJZCwgY29sbGVjdGlvbklkLCBkb2N1bWVudElkLCBhdHRyaWJ1dGUsIHZhbHVlLCBtaW4pIHtcbiAgICAgICAgaWYgKHR5cGVvZiBkYXRhYmFzZUlkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJkYXRhYmFzZUlkXCInKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIGNvbGxlY3Rpb25JZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwiY29sbGVjdGlvbklkXCInKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIGRvY3VtZW50SWQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcImRvY3VtZW50SWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgYXR0cmlidXRlID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJhdHRyaWJ1dGVcIicpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2RhdGFiYXNlcy97ZGF0YWJhc2VJZH0vY29sbGVjdGlvbnMve2NvbGxlY3Rpb25JZH0vZG9jdW1lbnRzL3tkb2N1bWVudElkfS97YXR0cmlidXRlfS9kZWNyZW1lbnQnLnJlcGxhY2UoJ3tkYXRhYmFzZUlkfScsIGRhdGFiYXNlSWQpLnJlcGxhY2UoJ3tjb2xsZWN0aW9uSWR9JywgY29sbGVjdGlvbklkKS5yZXBsYWNlKCd7ZG9jdW1lbnRJZH0nLCBkb2N1bWVudElkKS5yZXBsYWNlKCd7YXR0cmlidXRlfScsIGF0dHJpYnV0ZSk7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgaWYgKHR5cGVvZiB2YWx1ZSAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3ZhbHVlJ10gPSB2YWx1ZTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIG1pbiAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ21pbiddID0gbWluO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7XG4gICAgICAgICAgICAnY29udGVudC10eXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgncGF0Y2gnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBJbmNyZW1lbnQgYSBzcGVjaWZpYyBhdHRyaWJ1dGUgb2YgYSBkb2N1bWVudCBieSBhIGdpdmVuIHZhbHVlLlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGRhdGFiYXNlSWRcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gY29sbGVjdGlvbklkXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGRvY3VtZW50SWRcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gYXR0cmlidXRlXG4gICAgICogQHBhcmFtIHtudW1iZXJ9IHZhbHVlXG4gICAgICogQHBhcmFtIHtudW1iZXJ9IG1heFxuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPERvY3VtZW50Pn1cbiAgICAgKi9cbiAgICBpbmNyZW1lbnREb2N1bWVudEF0dHJpYnV0ZShkYXRhYmFzZUlkLCBjb2xsZWN0aW9uSWQsIGRvY3VtZW50SWQsIGF0dHJpYnV0ZSwgdmFsdWUsIG1heCkge1xuICAgICAgICBpZiAodHlwZW9mIGRhdGFiYXNlSWQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcImRhdGFiYXNlSWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgY29sbGVjdGlvbklkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJjb2xsZWN0aW9uSWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgZG9jdW1lbnRJZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwiZG9jdW1lbnRJZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBhdHRyaWJ1dGUgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcImF0dHJpYnV0ZVwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvZGF0YWJhc2VzL3tkYXRhYmFzZUlkfS9jb2xsZWN0aW9ucy97Y29sbGVjdGlvbklkfS9kb2N1bWVudHMve2RvY3VtZW50SWR9L3thdHRyaWJ1dGV9L2luY3JlbWVudCcucmVwbGFjZSgne2RhdGFiYXNlSWR9JywgZGF0YWJhc2VJZCkucmVwbGFjZSgne2NvbGxlY3Rpb25JZH0nLCBjb2xsZWN0aW9uSWQpLnJlcGxhY2UoJ3tkb2N1bWVudElkfScsIGRvY3VtZW50SWQpLnJlcGxhY2UoJ3thdHRyaWJ1dGV9JywgYXR0cmlidXRlKTtcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBpZiAodHlwZW9mIHZhbHVlICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsndmFsdWUnXSA9IHZhbHVlO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgbWF4ICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsnbWF4J10gPSBtYXg7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgY29uc3QgYXBpSGVhZGVycyA9IHtcbiAgICAgICAgICAgICdjb250ZW50LXR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgIH07XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdwYXRjaCcsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxufVxuXG5jbGFzcyBGdW5jdGlvbnMge1xuICAgIGNvbnN0cnVjdG9yKGNsaWVudCkge1xuICAgICAgICB0aGlzLmNsaWVudCA9IGNsaWVudDtcbiAgICB9XG4gICAgLyoqXG4gICAgICogR2V0IGEgbGlzdCBvZiBhbGwgdGhlIGN1cnJlbnQgdXNlciBmdW5jdGlvbiBleGVjdXRpb24gbG9ncy4gWW91IGNhbiB1c2UgdGhlIHF1ZXJ5IHBhcmFtcyB0byBmaWx0ZXIgeW91ciByZXN1bHRzLlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGZ1bmN0aW9uSWRcbiAgICAgKiBAcGFyYW0ge3N0cmluZ1tdfSBxdWVyaWVzXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8TW9kZWxzLkV4ZWN1dGlvbkxpc3Q+fVxuICAgICAqL1xuICAgIGxpc3RFeGVjdXRpb25zKGZ1bmN0aW9uSWQsIHF1ZXJpZXMpIHtcbiAgICAgICAgaWYgKHR5cGVvZiBmdW5jdGlvbklkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJmdW5jdGlvbklkXCInKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy9mdW5jdGlvbnMve2Z1bmN0aW9uSWR9L2V4ZWN1dGlvbnMnLnJlcGxhY2UoJ3tmdW5jdGlvbklkfScsIGZ1bmN0aW9uSWQpO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGlmICh0eXBlb2YgcXVlcmllcyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3F1ZXJpZXMnXSA9IHF1ZXJpZXM7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgY29uc3QgYXBpSGVhZGVycyA9IHt9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgnZ2V0JywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogVHJpZ2dlciBhIGZ1bmN0aW9uIGV4ZWN1dGlvbi4gVGhlIHJldHVybmVkIG9iamVjdCB3aWxsIHJldHVybiB5b3UgdGhlIGN1cnJlbnQgZXhlY3V0aW9uIHN0YXR1cy4gWW91IGNhbiBwaW5nIHRoZSBgR2V0IEV4ZWN1dGlvbmAgZW5kcG9pbnQgdG8gZ2V0IHVwZGF0ZXMgb24gdGhlIGN1cnJlbnQgZXhlY3V0aW9uIHN0YXR1cy4gT25jZSB0aGlzIGVuZHBvaW50IGlzIGNhbGxlZCwgeW91ciBmdW5jdGlvbiBleGVjdXRpb24gcHJvY2VzcyB3aWxsIHN0YXJ0IGFzeW5jaHJvbm91c2x5LlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGZ1bmN0aW9uSWRcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gYm9keVxuICAgICAqIEBwYXJhbSB7Ym9vbGVhbn0gYXN5bmNcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30geHBhdGhcbiAgICAgKiBAcGFyYW0ge0V4ZWN1dGlvbk1ldGhvZH0gbWV0aG9kXG4gICAgICogQHBhcmFtIHtvYmplY3R9IGhlYWRlcnNcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gc2NoZWR1bGVkQXRcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTxNb2RlbHMuRXhlY3V0aW9uPn1cbiAgICAgKi9cbiAgICBjcmVhdGVFeGVjdXRpb24oZnVuY3Rpb25JZCwgYm9keSwgYXN5bmMsIHhwYXRoLCBtZXRob2QsIGhlYWRlcnMsIHNjaGVkdWxlZEF0KSB7XG4gICAgICAgIGlmICh0eXBlb2YgZnVuY3Rpb25JZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwiZnVuY3Rpb25JZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvZnVuY3Rpb25zL3tmdW5jdGlvbklkfS9leGVjdXRpb25zJy5yZXBsYWNlKCd7ZnVuY3Rpb25JZH0nLCBmdW5jdGlvbklkKTtcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBpZiAodHlwZW9mIGJvZHkgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydib2R5J10gPSBib2R5O1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgYXN5bmMgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydhc3luYyddID0gYXN5bmM7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiB4cGF0aCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3BhdGgnXSA9IHhwYXRoO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgbWV0aG9kICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsnbWV0aG9kJ10gPSBtZXRob2Q7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBoZWFkZXJzICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsnaGVhZGVycyddID0gaGVhZGVycztcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIHNjaGVkdWxlZEF0ICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsnc2NoZWR1bGVkQXQnXSA9IHNjaGVkdWxlZEF0O1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7XG4gICAgICAgICAgICAnY29udGVudC10eXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgncG9zdCcsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIEdldCBhIGZ1bmN0aW9uIGV4ZWN1dGlvbiBsb2cgYnkgaXRzIHVuaXF1ZSBJRC5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBmdW5jdGlvbklkXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGV4ZWN1dGlvbklkXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8TW9kZWxzLkV4ZWN1dGlvbj59XG4gICAgICovXG4gICAgZ2V0RXhlY3V0aW9uKGZ1bmN0aW9uSWQsIGV4ZWN1dGlvbklkKSB7XG4gICAgICAgIGlmICh0eXBlb2YgZnVuY3Rpb25JZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwiZnVuY3Rpb25JZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBleGVjdXRpb25JZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwiZXhlY3V0aW9uSWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2Z1bmN0aW9ucy97ZnVuY3Rpb25JZH0vZXhlY3V0aW9ucy97ZXhlY3V0aW9uSWR9Jy5yZXBsYWNlKCd7ZnVuY3Rpb25JZH0nLCBmdW5jdGlvbklkKS5yZXBsYWNlKCd7ZXhlY3V0aW9uSWR9JywgZXhlY3V0aW9uSWQpO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7fTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ2dldCcsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxufVxuXG5jbGFzcyBHcmFwaHFsIHtcbiAgICBjb25zdHJ1Y3RvcihjbGllbnQpIHtcbiAgICAgICAgdGhpcy5jbGllbnQgPSBjbGllbnQ7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIEV4ZWN1dGUgYSBHcmFwaFFMIG11dGF0aW9uLlxuICAgICAqXG4gICAgICogQHBhcmFtIHtvYmplY3R9IHF1ZXJ5XG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8e30+fVxuICAgICAqL1xuICAgIHF1ZXJ5KHF1ZXJ5KSB7XG4gICAgICAgIGlmICh0eXBlb2YgcXVlcnkgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcInF1ZXJ5XCInKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy9ncmFwaHFsJztcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBpZiAodHlwZW9mIHF1ZXJ5ICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsncXVlcnknXSA9IHF1ZXJ5O1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7XG4gICAgICAgICAgICAneC1zZGstZ3JhcGhxbCc6ICd0cnVlJyxcbiAgICAgICAgICAgICdjb250ZW50LXR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgIH07XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdwb3N0JywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogRXhlY3V0ZSBhIEdyYXBoUUwgbXV0YXRpb24uXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge29iamVjdH0gcXVlcnlcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTx7fT59XG4gICAgICovXG4gICAgbXV0YXRpb24ocXVlcnkpIHtcbiAgICAgICAgaWYgKHR5cGVvZiBxdWVyeSA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwicXVlcnlcIicpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2dyYXBocWwvbXV0YXRpb24nO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGlmICh0eXBlb2YgcXVlcnkgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydxdWVyeSddID0gcXVlcnk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgY29uc3QgYXBpSGVhZGVycyA9IHtcbiAgICAgICAgICAgICd4LXNkay1ncmFwaHFsJzogJ3RydWUnLFxuICAgICAgICAgICAgJ2NvbnRlbnQtdHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ3Bvc3QnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbn1cblxuY2xhc3MgTG9jYWxlIHtcbiAgICBjb25zdHJ1Y3RvcihjbGllbnQpIHtcbiAgICAgICAgdGhpcy5jbGllbnQgPSBjbGllbnQ7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIEdldCB0aGUgY3VycmVudCB1c2VyIGxvY2F0aW9uIGJhc2VkIG9uIElQLiBSZXR1cm5zIGFuIG9iamVjdCB3aXRoIHVzZXIgY291bnRyeSBjb2RlLCBjb3VudHJ5IG5hbWUsIGNvbnRpbmVudCBuYW1lLCBjb250aW5lbnQgY29kZSwgaXAgYWRkcmVzcyBhbmQgc3VnZ2VzdGVkIGN1cnJlbmN5LiBZb3UgY2FuIHVzZSB0aGUgbG9jYWxlIGhlYWRlciB0byBnZXQgdGhlIGRhdGEgaW4gYSBzdXBwb3J0ZWQgbGFuZ3VhZ2UuXG4gICAgICpcbiAgICAgKiAoW0lQIEdlb2xvY2F0aW9uIGJ5IERCLUlQXShodHRwczovL2RiLWlwLmNvbSkpXG4gICAgICpcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTxNb2RlbHMuTG9jYWxlPn1cbiAgICAgKi9cbiAgICBnZXQoKSB7XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2xvY2FsZSc7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgY29uc3QgYXBpSGVhZGVycyA9IHt9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgnZ2V0JywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogTGlzdCBvZiBhbGwgbG9jYWxlIGNvZGVzIGluIFtJU08gNjM5LTFdKGh0dHBzOi8vZW4ud2lraXBlZGlhLm9yZy93aWtpL0xpc3Rfb2ZfSVNPXzYzOS0xX2NvZGVzKS5cbiAgICAgKlxuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPE1vZGVscy5Mb2NhbGVDb2RlTGlzdD59XG4gICAgICovXG4gICAgbGlzdENvZGVzKCkge1xuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy9sb2NhbGUvY29kZXMnO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7fTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ2dldCcsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIExpc3Qgb2YgYWxsIGNvbnRpbmVudHMuIFlvdSBjYW4gdXNlIHRoZSBsb2NhbGUgaGVhZGVyIHRvIGdldCB0aGUgZGF0YSBpbiBhIHN1cHBvcnRlZCBsYW5ndWFnZS5cbiAgICAgKlxuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPE1vZGVscy5Db250aW5lbnRMaXN0Pn1cbiAgICAgKi9cbiAgICBsaXN0Q29udGluZW50cygpIHtcbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvbG9jYWxlL2NvbnRpbmVudHMnO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7fTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ2dldCcsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIExpc3Qgb2YgYWxsIGNvdW50cmllcy4gWW91IGNhbiB1c2UgdGhlIGxvY2FsZSBoZWFkZXIgdG8gZ2V0IHRoZSBkYXRhIGluIGEgc3VwcG9ydGVkIGxhbmd1YWdlLlxuICAgICAqXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8TW9kZWxzLkNvdW50cnlMaXN0Pn1cbiAgICAgKi9cbiAgICBsaXN0Q291bnRyaWVzKCkge1xuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy9sb2NhbGUvY291bnRyaWVzJztcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge307XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdnZXQnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBMaXN0IG9mIGFsbCBjb3VudHJpZXMgdGhhdCBhcmUgY3VycmVudGx5IG1lbWJlcnMgb2YgdGhlIEVVLiBZb3UgY2FuIHVzZSB0aGUgbG9jYWxlIGhlYWRlciB0byBnZXQgdGhlIGRhdGEgaW4gYSBzdXBwb3J0ZWQgbGFuZ3VhZ2UuXG4gICAgICpcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTxNb2RlbHMuQ291bnRyeUxpc3Q+fVxuICAgICAqL1xuICAgIGxpc3RDb3VudHJpZXNFVSgpIHtcbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvbG9jYWxlL2NvdW50cmllcy9ldSc7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgY29uc3QgYXBpSGVhZGVycyA9IHt9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgnZ2V0JywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogTGlzdCBvZiBhbGwgY291bnRyaWVzIHBob25lIGNvZGVzLiBZb3UgY2FuIHVzZSB0aGUgbG9jYWxlIGhlYWRlciB0byBnZXQgdGhlIGRhdGEgaW4gYSBzdXBwb3J0ZWQgbGFuZ3VhZ2UuXG4gICAgICpcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTxNb2RlbHMuUGhvbmVMaXN0Pn1cbiAgICAgKi9cbiAgICBsaXN0Q291bnRyaWVzUGhvbmVzKCkge1xuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy9sb2NhbGUvY291bnRyaWVzL3Bob25lcyc7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgY29uc3QgYXBpSGVhZGVycyA9IHt9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgnZ2V0JywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogTGlzdCBvZiBhbGwgY3VycmVuY2llcywgaW5jbHVkaW5nIGN1cnJlbmN5IHN5bWJvbCwgbmFtZSwgcGx1cmFsLCBhbmQgZGVjaW1hbCBkaWdpdHMgZm9yIGFsbCBtYWpvciBhbmQgbWlub3IgY3VycmVuY2llcy4gWW91IGNhbiB1c2UgdGhlIGxvY2FsZSBoZWFkZXIgdG8gZ2V0IHRoZSBkYXRhIGluIGEgc3VwcG9ydGVkIGxhbmd1YWdlLlxuICAgICAqXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8TW9kZWxzLkN1cnJlbmN5TGlzdD59XG4gICAgICovXG4gICAgbGlzdEN1cnJlbmNpZXMoKSB7XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2xvY2FsZS9jdXJyZW5jaWVzJztcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge307XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdnZXQnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBMaXN0IG9mIGFsbCBsYW5ndWFnZXMgY2xhc3NpZmllZCBieSBJU08gNjM5LTEgaW5jbHVkaW5nIDItbGV0dGVyIGNvZGUsIG5hbWUgaW4gRW5nbGlzaCwgYW5kIG5hbWUgaW4gdGhlIHJlc3BlY3RpdmUgbGFuZ3VhZ2UuXG4gICAgICpcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTxNb2RlbHMuTGFuZ3VhZ2VMaXN0Pn1cbiAgICAgKi9cbiAgICBsaXN0TGFuZ3VhZ2VzKCkge1xuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy9sb2NhbGUvbGFuZ3VhZ2VzJztcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge307XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdnZXQnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbn1cblxuY2xhc3MgTWVzc2FnaW5nIHtcbiAgICBjb25zdHJ1Y3RvcihjbGllbnQpIHtcbiAgICAgICAgdGhpcy5jbGllbnQgPSBjbGllbnQ7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIENyZWF0ZSBhIG5ldyBzdWJzY3JpYmVyLlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IHRvcGljSWRcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gc3Vic2NyaWJlcklkXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IHRhcmdldElkXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8TW9kZWxzLlN1YnNjcmliZXI+fVxuICAgICAqL1xuICAgIGNyZWF0ZVN1YnNjcmliZXIodG9waWNJZCwgc3Vic2NyaWJlcklkLCB0YXJnZXRJZCkge1xuICAgICAgICBpZiAodHlwZW9mIHRvcGljSWQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcInRvcGljSWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2Ygc3Vic2NyaWJlcklkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJzdWJzY3JpYmVySWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgdGFyZ2V0SWQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcInRhcmdldElkXCInKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy9tZXNzYWdpbmcvdG9waWNzL3t0b3BpY0lkfS9zdWJzY3JpYmVycycucmVwbGFjZSgne3RvcGljSWR9JywgdG9waWNJZCk7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgaWYgKHR5cGVvZiBzdWJzY3JpYmVySWQgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydzdWJzY3JpYmVySWQnXSA9IHN1YnNjcmliZXJJZDtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIHRhcmdldElkICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsndGFyZ2V0SWQnXSA9IHRhcmdldElkO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7XG4gICAgICAgICAgICAnY29udGVudC10eXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgncG9zdCcsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIERlbGV0ZSBhIHN1YnNjcmliZXIgYnkgaXRzIHVuaXF1ZSBJRC5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSB0b3BpY0lkXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IHN1YnNjcmliZXJJZFxuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPHt9Pn1cbiAgICAgKi9cbiAgICBkZWxldGVTdWJzY3JpYmVyKHRvcGljSWQsIHN1YnNjcmliZXJJZCkge1xuICAgICAgICBpZiAodHlwZW9mIHRvcGljSWQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcInRvcGljSWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2Ygc3Vic2NyaWJlcklkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJzdWJzY3JpYmVySWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL21lc3NhZ2luZy90b3BpY3Mve3RvcGljSWR9L3N1YnNjcmliZXJzL3tzdWJzY3JpYmVySWR9Jy5yZXBsYWNlKCd7dG9waWNJZH0nLCB0b3BpY0lkKS5yZXBsYWNlKCd7c3Vic2NyaWJlcklkfScsIHN1YnNjcmliZXJJZCk7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgY29uc3QgYXBpSGVhZGVycyA9IHtcbiAgICAgICAgICAgICdjb250ZW50LXR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgIH07XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdkZWxldGUnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbn1cblxuY2xhc3MgU3RvcmFnZSB7XG4gICAgY29uc3RydWN0b3IoY2xpZW50KSB7XG4gICAgICAgIHRoaXMuY2xpZW50ID0gY2xpZW50O1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBHZXQgYSBsaXN0IG9mIGFsbCB0aGUgdXNlciBmaWxlcy4gWW91IGNhbiB1c2UgdGhlIHF1ZXJ5IHBhcmFtcyB0byBmaWx0ZXIgeW91ciByZXN1bHRzLlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGJ1Y2tldElkXG4gICAgICogQHBhcmFtIHtzdHJpbmdbXX0gcXVlcmllc1xuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBzZWFyY2hcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTxNb2RlbHMuRmlsZUxpc3Q+fVxuICAgICAqL1xuICAgIGxpc3RGaWxlcyhidWNrZXRJZCwgcXVlcmllcywgc2VhcmNoKSB7XG4gICAgICAgIGlmICh0eXBlb2YgYnVja2V0SWQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcImJ1Y2tldElkXCInKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy9zdG9yYWdlL2J1Y2tldHMve2J1Y2tldElkfS9maWxlcycucmVwbGFjZSgne2J1Y2tldElkfScsIGJ1Y2tldElkKTtcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBpZiAodHlwZW9mIHF1ZXJpZXMgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydxdWVyaWVzJ10gPSBxdWVyaWVzO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2Ygc2VhcmNoICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsnc2VhcmNoJ10gPSBzZWFyY2g7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgY29uc3QgYXBpSGVhZGVycyA9IHt9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgnZ2V0JywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogQ3JlYXRlIGEgbmV3IGZpbGUuIEJlZm9yZSB1c2luZyB0aGlzIHJvdXRlLCB5b3Ugc2hvdWxkIGNyZWF0ZSBhIG5ldyBidWNrZXQgcmVzb3VyY2UgdXNpbmcgZWl0aGVyIGEgW3NlcnZlciBpbnRlZ3JhdGlvbl0oaHR0cHM6Ly9hcHB3cml0ZS5pby9kb2NzL3NlcnZlci9zdG9yYWdlI3N0b3JhZ2VDcmVhdGVCdWNrZXQpIEFQSSBvciBkaXJlY3RseSBmcm9tIHlvdXIgQXBwd3JpdGUgY29uc29sZS5cbiAgICAgKlxuICAgICAqIExhcmdlciBmaWxlcyBzaG91bGQgYmUgdXBsb2FkZWQgdXNpbmcgbXVsdGlwbGUgcmVxdWVzdHMgd2l0aCB0aGUgW2NvbnRlbnQtcmFuZ2VdKGh0dHBzOi8vZGV2ZWxvcGVyLm1vemlsbGEub3JnL2VuLVVTL2RvY3MvV2ViL0hUVFAvSGVhZGVycy9Db250ZW50LVJhbmdlKSBoZWFkZXIgdG8gc2VuZCBhIHBhcnRpYWwgcmVxdWVzdCB3aXRoIGEgbWF4aW11bSBzdXBwb3J0ZWQgY2h1bmsgb2YgYDVNQmAuIFRoZSBgY29udGVudC1yYW5nZWAgaGVhZGVyIHZhbHVlcyBzaG91bGQgYWx3YXlzIGJlIGluIGJ5dGVzLlxuICAgICAqXG4gICAgICogV2hlbiB0aGUgZmlyc3QgcmVxdWVzdCBpcyBzZW50LCB0aGUgc2VydmVyIHdpbGwgcmV0dXJuIHRoZSAqKkZpbGUqKiBvYmplY3QsIGFuZCB0aGUgc3Vic2VxdWVudCBwYXJ0IHJlcXVlc3QgbXVzdCBpbmNsdWRlIHRoZSBmaWxlJiMwMzk7cyAqKmlkKiogaW4gYHgtYXBwd3JpdGUtaWRgIGhlYWRlciB0byBhbGxvdyB0aGUgc2VydmVyIHRvIGtub3cgdGhhdCB0aGUgcGFydGlhbCB1cGxvYWQgaXMgZm9yIHRoZSBleGlzdGluZyBmaWxlIGFuZCBub3QgZm9yIGEgbmV3IG9uZS5cbiAgICAgKlxuICAgICAqIElmIHlvdSYjMDM5O3JlIGNyZWF0aW5nIGEgbmV3IGZpbGUgdXNpbmcgb25lIG9mIHRoZSBBcHB3cml0ZSBTREtzLCBhbGwgdGhlIGNodW5raW5nIGxvZ2ljIHdpbGwgYmUgbWFuYWdlZCBieSB0aGUgU0RLIGludGVybmFsbHkuXG4gICAgICpcbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBidWNrZXRJZFxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBmaWxlSWRcbiAgICAgKiBAcGFyYW0ge0ZpbGV9IGZpbGVcbiAgICAgKiBAcGFyYW0ge3N0cmluZ1tdfSBwZXJtaXNzaW9uc1xuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPE1vZGVscy5GaWxlPn1cbiAgICAgKi9cbiAgICBjcmVhdGVGaWxlKGJ1Y2tldElkLCBmaWxlSWQsIGZpbGUsIHBlcm1pc3Npb25zLCBvblByb2dyZXNzID0gKHByb2dyZXNzKSA9PiB7IH0pIHtcbiAgICAgICAgaWYgKHR5cGVvZiBidWNrZXRJZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwiYnVja2V0SWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgZmlsZUlkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJmaWxlSWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgZmlsZSA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwiZmlsZVwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvc3RvcmFnZS9idWNrZXRzL3tidWNrZXRJZH0vZmlsZXMnLnJlcGxhY2UoJ3tidWNrZXRJZH0nLCBidWNrZXRJZCk7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgaWYgKHR5cGVvZiBmaWxlSWQgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydmaWxlSWQnXSA9IGZpbGVJZDtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIGZpbGUgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydmaWxlJ10gPSBmaWxlO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgcGVybWlzc2lvbnMgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydwZXJtaXNzaW9ucyddID0gcGVybWlzc2lvbnM7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgY29uc3QgYXBpSGVhZGVycyA9IHtcbiAgICAgICAgICAgICdjb250ZW50LXR5cGUnOiAnbXVsdGlwYXJ0L2Zvcm0tZGF0YScsXG4gICAgICAgIH07XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jaHVua2VkVXBsb2FkKCdwb3N0JywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkLCBvblByb2dyZXNzKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogR2V0IGEgZmlsZSBieSBpdHMgdW5pcXVlIElELiBUaGlzIGVuZHBvaW50IHJlc3BvbnNlIHJldHVybnMgYSBKU09OIG9iamVjdCB3aXRoIHRoZSBmaWxlIG1ldGFkYXRhLlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGJ1Y2tldElkXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGZpbGVJZFxuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPE1vZGVscy5GaWxlPn1cbiAgICAgKi9cbiAgICBnZXRGaWxlKGJ1Y2tldElkLCBmaWxlSWQpIHtcbiAgICAgICAgaWYgKHR5cGVvZiBidWNrZXRJZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwiYnVja2V0SWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgZmlsZUlkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJmaWxlSWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL3N0b3JhZ2UvYnVja2V0cy97YnVja2V0SWR9L2ZpbGVzL3tmaWxlSWR9Jy5yZXBsYWNlKCd7YnVja2V0SWR9JywgYnVja2V0SWQpLnJlcGxhY2UoJ3tmaWxlSWR9JywgZmlsZUlkKTtcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge307XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdnZXQnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBVcGRhdGUgYSBmaWxlIGJ5IGl0cyB1bmlxdWUgSUQuIE9ubHkgdXNlcnMgd2l0aCB3cml0ZSBwZXJtaXNzaW9ucyBoYXZlIGFjY2VzcyB0byB1cGRhdGUgdGhpcyByZXNvdXJjZS5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBidWNrZXRJZFxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBmaWxlSWRcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gbmFtZVxuICAgICAqIEBwYXJhbSB7c3RyaW5nW119IHBlcm1pc3Npb25zXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8TW9kZWxzLkZpbGU+fVxuICAgICAqL1xuICAgIHVwZGF0ZUZpbGUoYnVja2V0SWQsIGZpbGVJZCwgbmFtZSwgcGVybWlzc2lvbnMpIHtcbiAgICAgICAgaWYgKHR5cGVvZiBidWNrZXRJZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwiYnVja2V0SWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgZmlsZUlkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJmaWxlSWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL3N0b3JhZ2UvYnVja2V0cy97YnVja2V0SWR9L2ZpbGVzL3tmaWxlSWR9Jy5yZXBsYWNlKCd7YnVja2V0SWR9JywgYnVja2V0SWQpLnJlcGxhY2UoJ3tmaWxlSWR9JywgZmlsZUlkKTtcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBpZiAodHlwZW9mIG5hbWUgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWyduYW1lJ10gPSBuYW1lO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgcGVybWlzc2lvbnMgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydwZXJtaXNzaW9ucyddID0gcGVybWlzc2lvbnM7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgY29uc3QgYXBpSGVhZGVycyA9IHtcbiAgICAgICAgICAgICdjb250ZW50LXR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgIH07XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdwdXQnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBEZWxldGUgYSBmaWxlIGJ5IGl0cyB1bmlxdWUgSUQuIE9ubHkgdXNlcnMgd2l0aCB3cml0ZSBwZXJtaXNzaW9ucyBoYXZlIGFjY2VzcyB0byBkZWxldGUgdGhpcyByZXNvdXJjZS5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBidWNrZXRJZFxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBmaWxlSWRcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTx7fT59XG4gICAgICovXG4gICAgZGVsZXRlRmlsZShidWNrZXRJZCwgZmlsZUlkKSB7XG4gICAgICAgIGlmICh0eXBlb2YgYnVja2V0SWQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcImJ1Y2tldElkXCInKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIGZpbGVJZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwiZmlsZUlkXCInKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy9zdG9yYWdlL2J1Y2tldHMve2J1Y2tldElkfS9maWxlcy97ZmlsZUlkfScucmVwbGFjZSgne2J1Y2tldElkfScsIGJ1Y2tldElkKS5yZXBsYWNlKCd7ZmlsZUlkfScsIGZpbGVJZCk7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgY29uc3QgYXBpSGVhZGVycyA9IHtcbiAgICAgICAgICAgICdjb250ZW50LXR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgIH07XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdkZWxldGUnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBHZXQgYSBmaWxlIGNvbnRlbnQgYnkgaXRzIHVuaXF1ZSBJRC4gVGhlIGVuZHBvaW50IHJlc3BvbnNlIHJldHVybiB3aXRoIGEgJiMwMzk7Q29udGVudC1EaXNwb3NpdGlvbjogYXR0YWNobWVudCYjMDM5OyBoZWFkZXIgdGhhdCB0ZWxscyB0aGUgYnJvd3NlciB0byBzdGFydCBkb3dubG9hZGluZyB0aGUgZmlsZSB0byB1c2VyIGRvd25sb2FkcyBkaXJlY3RvcnkuXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gYnVja2V0SWRcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gZmlsZUlkXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IHRva2VuXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge3N0cmluZ31cbiAgICAgKi9cbiAgICBnZXRGaWxlRG93bmxvYWQoYnVja2V0SWQsIGZpbGVJZCwgdG9rZW4pIHtcbiAgICAgICAgaWYgKHR5cGVvZiBidWNrZXRJZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwiYnVja2V0SWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgZmlsZUlkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJmaWxlSWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL3N0b3JhZ2UvYnVja2V0cy97YnVja2V0SWR9L2ZpbGVzL3tmaWxlSWR9L2Rvd25sb2FkJy5yZXBsYWNlKCd7YnVja2V0SWR9JywgYnVja2V0SWQpLnJlcGxhY2UoJ3tmaWxlSWR9JywgZmlsZUlkKTtcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBpZiAodHlwZW9mIHRva2VuICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsndG9rZW4nXSA9IHRva2VuO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIHBheWxvYWRbJ3Byb2plY3QnXSA9IHRoaXMuY2xpZW50LmNvbmZpZy5wcm9qZWN0O1xuICAgICAgICBmb3IgKGNvbnN0IFtrZXksIHZhbHVlXSBvZiBPYmplY3QuZW50cmllcyhTZXJ2aWNlLmZsYXR0ZW4ocGF5bG9hZCkpKSB7XG4gICAgICAgICAgICB1cmkuc2VhcmNoUGFyYW1zLmFwcGVuZChrZXksIHZhbHVlKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gdXJpLnRvU3RyaW5nKCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIEdldCBhIGZpbGUgcHJldmlldyBpbWFnZS4gQ3VycmVudGx5LCB0aGlzIG1ldGhvZCBzdXBwb3J0cyBwcmV2aWV3IGZvciBpbWFnZSBmaWxlcyAoanBnLCBwbmcsIGFuZCBnaWYpLCBvdGhlciBzdXBwb3J0ZWQgZm9ybWF0cywgbGlrZSBwZGYsIGRvY3MsIHNsaWRlcywgYW5kIHNwcmVhZHNoZWV0cywgd2lsbCByZXR1cm4gdGhlIGZpbGUgaWNvbiBpbWFnZS4gWW91IGNhbiBhbHNvIHBhc3MgcXVlcnkgc3RyaW5nIGFyZ3VtZW50cyBmb3IgY3V0dGluZyBhbmQgcmVzaXppbmcgeW91ciBwcmV2aWV3IGltYWdlLiBQcmV2aWV3IGlzIHN1cHBvcnRlZCBvbmx5IGZvciBpbWFnZSBmaWxlcyBzbWFsbGVyIHRoYW4gMTBNQi5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBidWNrZXRJZFxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBmaWxlSWRcbiAgICAgKiBAcGFyYW0ge251bWJlcn0gd2lkdGhcbiAgICAgKiBAcGFyYW0ge251bWJlcn0gaGVpZ2h0XG4gICAgICogQHBhcmFtIHtJbWFnZUdyYXZpdHl9IGdyYXZpdHlcbiAgICAgKiBAcGFyYW0ge251bWJlcn0gcXVhbGl0eVxuICAgICAqIEBwYXJhbSB7bnVtYmVyfSBib3JkZXJXaWR0aFxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBib3JkZXJDb2xvclxuICAgICAqIEBwYXJhbSB7bnVtYmVyfSBib3JkZXJSYWRpdXNcbiAgICAgKiBAcGFyYW0ge251bWJlcn0gb3BhY2l0eVxuICAgICAqIEBwYXJhbSB7bnVtYmVyfSByb3RhdGlvblxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBiYWNrZ3JvdW5kXG4gICAgICogQHBhcmFtIHtJbWFnZUZvcm1hdH0gb3V0cHV0XG4gICAgICogQHBhcmFtIHtzdHJpbmd9IHRva2VuXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge3N0cmluZ31cbiAgICAgKi9cbiAgICBnZXRGaWxlUHJldmlldyhidWNrZXRJZCwgZmlsZUlkLCB3aWR0aCwgaGVpZ2h0LCBncmF2aXR5LCBxdWFsaXR5LCBib3JkZXJXaWR0aCwgYm9yZGVyQ29sb3IsIGJvcmRlclJhZGl1cywgb3BhY2l0eSwgcm90YXRpb24sIGJhY2tncm91bmQsIG91dHB1dCwgdG9rZW4pIHtcbiAgICAgICAgaWYgKHR5cGVvZiBidWNrZXRJZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwiYnVja2V0SWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgZmlsZUlkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJmaWxlSWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL3N0b3JhZ2UvYnVja2V0cy97YnVja2V0SWR9L2ZpbGVzL3tmaWxlSWR9L3ByZXZpZXcnLnJlcGxhY2UoJ3tidWNrZXRJZH0nLCBidWNrZXRJZCkucmVwbGFjZSgne2ZpbGVJZH0nLCBmaWxlSWQpO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGlmICh0eXBlb2Ygd2lkdGggIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWyd3aWR0aCddID0gd2lkdGg7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBoZWlnaHQgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydoZWlnaHQnXSA9IGhlaWdodDtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIGdyYXZpdHkgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydncmF2aXR5J10gPSBncmF2aXR5O1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgcXVhbGl0eSAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3F1YWxpdHknXSA9IHF1YWxpdHk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBib3JkZXJXaWR0aCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ2JvcmRlcldpZHRoJ10gPSBib3JkZXJXaWR0aDtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIGJvcmRlckNvbG9yICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsnYm9yZGVyQ29sb3InXSA9IGJvcmRlckNvbG9yO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgYm9yZGVyUmFkaXVzICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsnYm9yZGVyUmFkaXVzJ10gPSBib3JkZXJSYWRpdXM7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBvcGFjaXR5ICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsnb3BhY2l0eSddID0gb3BhY2l0eTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIHJvdGF0aW9uICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsncm90YXRpb24nXSA9IHJvdGF0aW9uO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgYmFja2dyb3VuZCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ2JhY2tncm91bmQnXSA9IGJhY2tncm91bmQ7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBvdXRwdXQgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydvdXRwdXQnXSA9IG91dHB1dDtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIHRva2VuICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsndG9rZW4nXSA9IHRva2VuO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIHBheWxvYWRbJ3Byb2plY3QnXSA9IHRoaXMuY2xpZW50LmNvbmZpZy5wcm9qZWN0O1xuICAgICAgICBmb3IgKGNvbnN0IFtrZXksIHZhbHVlXSBvZiBPYmplY3QuZW50cmllcyhTZXJ2aWNlLmZsYXR0ZW4ocGF5bG9hZCkpKSB7XG4gICAgICAgICAgICB1cmkuc2VhcmNoUGFyYW1zLmFwcGVuZChrZXksIHZhbHVlKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gdXJpLnRvU3RyaW5nKCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIEdldCBhIGZpbGUgY29udGVudCBieSBpdHMgdW5pcXVlIElELiBUaGlzIGVuZHBvaW50IGlzIHNpbWlsYXIgdG8gdGhlIGRvd25sb2FkIG1ldGhvZCBidXQgcmV0dXJucyB3aXRoIG5vICAmIzAzOTtDb250ZW50LURpc3Bvc2l0aW9uOiBhdHRhY2htZW50JiMwMzk7IGhlYWRlci5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBidWNrZXRJZFxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBmaWxlSWRcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gdG9rZW5cbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7c3RyaW5nfVxuICAgICAqL1xuICAgIGdldEZpbGVWaWV3KGJ1Y2tldElkLCBmaWxlSWQsIHRva2VuKSB7XG4gICAgICAgIGlmICh0eXBlb2YgYnVja2V0SWQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcImJ1Y2tldElkXCInKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIGZpbGVJZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwiZmlsZUlkXCInKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy9zdG9yYWdlL2J1Y2tldHMve2J1Y2tldElkfS9maWxlcy97ZmlsZUlkfS92aWV3Jy5yZXBsYWNlKCd7YnVja2V0SWR9JywgYnVja2V0SWQpLnJlcGxhY2UoJ3tmaWxlSWR9JywgZmlsZUlkKTtcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBpZiAodHlwZW9mIHRva2VuICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsndG9rZW4nXSA9IHRva2VuO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIHBheWxvYWRbJ3Byb2plY3QnXSA9IHRoaXMuY2xpZW50LmNvbmZpZy5wcm9qZWN0O1xuICAgICAgICBmb3IgKGNvbnN0IFtrZXksIHZhbHVlXSBvZiBPYmplY3QuZW50cmllcyhTZXJ2aWNlLmZsYXR0ZW4ocGF5bG9hZCkpKSB7XG4gICAgICAgICAgICB1cmkuc2VhcmNoUGFyYW1zLmFwcGVuZChrZXksIHZhbHVlKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gdXJpLnRvU3RyaW5nKCk7XG4gICAgfVxufVxuXG5jbGFzcyBUZWFtcyB7XG4gICAgY29uc3RydWN0b3IoY2xpZW50KSB7XG4gICAgICAgIHRoaXMuY2xpZW50ID0gY2xpZW50O1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBHZXQgYSBsaXN0IG9mIGFsbCB0aGUgdGVhbXMgaW4gd2hpY2ggdGhlIGN1cnJlbnQgdXNlciBpcyBhIG1lbWJlci4gWW91IGNhbiB1c2UgdGhlIHBhcmFtZXRlcnMgdG8gZmlsdGVyIHlvdXIgcmVzdWx0cy5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nW119IHF1ZXJpZXNcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gc2VhcmNoXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8TW9kZWxzLlRlYW1MaXN0PFByZWZlcmVuY2VzPj59XG4gICAgICovXG4gICAgbGlzdChxdWVyaWVzLCBzZWFyY2gpIHtcbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvdGVhbXMnO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGlmICh0eXBlb2YgcXVlcmllcyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3F1ZXJpZXMnXSA9IHF1ZXJpZXM7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBzZWFyY2ggIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydzZWFyY2gnXSA9IHNlYXJjaDtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge307XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdnZXQnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBDcmVhdGUgYSBuZXcgdGVhbS4gVGhlIHVzZXIgd2hvIGNyZWF0ZXMgdGhlIHRlYW0gd2lsbCBhdXRvbWF0aWNhbGx5IGJlIGFzc2lnbmVkIGFzIHRoZSBvd25lciBvZiB0aGUgdGVhbS4gT25seSB0aGUgdXNlcnMgd2l0aCB0aGUgb3duZXIgcm9sZSBjYW4gaW52aXRlIG5ldyBtZW1iZXJzLCBhZGQgbmV3IG93bmVycyBhbmQgZGVsZXRlIG9yIHVwZGF0ZSB0aGUgdGVhbS5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSB0ZWFtSWRcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gbmFtZVxuICAgICAqIEBwYXJhbSB7c3RyaW5nW119IHJvbGVzXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8TW9kZWxzLlRlYW08UHJlZmVyZW5jZXM+Pn1cbiAgICAgKi9cbiAgICBjcmVhdGUodGVhbUlkLCBuYW1lLCByb2xlcykge1xuICAgICAgICBpZiAodHlwZW9mIHRlYW1JZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwidGVhbUlkXCInKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIG5hbWUgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcIm5hbWVcIicpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL3RlYW1zJztcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBpZiAodHlwZW9mIHRlYW1JZCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3RlYW1JZCddID0gdGVhbUlkO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgbmFtZSAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ25hbWUnXSA9IG5hbWU7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiByb2xlcyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3JvbGVzJ10gPSByb2xlcztcbiAgICAgICAgfVxuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge1xuICAgICAgICAgICAgJ2NvbnRlbnQtdHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ3Bvc3QnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBHZXQgYSB0ZWFtIGJ5IGl0cyBJRC4gQWxsIHRlYW0gbWVtYmVycyBoYXZlIHJlYWQgYWNjZXNzIGZvciB0aGlzIHJlc291cmNlLlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IHRlYW1JZFxuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPE1vZGVscy5UZWFtPFByZWZlcmVuY2VzPj59XG4gICAgICovXG4gICAgZ2V0KHRlYW1JZCkge1xuICAgICAgICBpZiAodHlwZW9mIHRlYW1JZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwidGVhbUlkXCInKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy90ZWFtcy97dGVhbUlkfScucmVwbGFjZSgne3RlYW1JZH0nLCB0ZWFtSWQpO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7fTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ2dldCcsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFVwZGF0ZSB0aGUgdGVhbSYjMDM5O3MgbmFtZSBieSBpdHMgdW5pcXVlIElELlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IHRlYW1JZFxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBuYW1lXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8TW9kZWxzLlRlYW08UHJlZmVyZW5jZXM+Pn1cbiAgICAgKi9cbiAgICB1cGRhdGVOYW1lKHRlYW1JZCwgbmFtZSkge1xuICAgICAgICBpZiAodHlwZW9mIHRlYW1JZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwidGVhbUlkXCInKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIG5hbWUgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcIm5hbWVcIicpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL3RlYW1zL3t0ZWFtSWR9Jy5yZXBsYWNlKCd7dGVhbUlkfScsIHRlYW1JZCk7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgaWYgKHR5cGVvZiBuYW1lICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsnbmFtZSddID0gbmFtZTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge1xuICAgICAgICAgICAgJ2NvbnRlbnQtdHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ3B1dCcsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIERlbGV0ZSBhIHRlYW0gdXNpbmcgaXRzIElELiBPbmx5IHRlYW0gbWVtYmVycyB3aXRoIHRoZSBvd25lciByb2xlIGNhbiBkZWxldGUgdGhlIHRlYW0uXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gdGVhbUlkXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8e30+fVxuICAgICAqL1xuICAgIGRlbGV0ZSh0ZWFtSWQpIHtcbiAgICAgICAgaWYgKHR5cGVvZiB0ZWFtSWQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcInRlYW1JZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvdGVhbXMve3RlYW1JZH0nLnJlcGxhY2UoJ3t0ZWFtSWR9JywgdGVhbUlkKTtcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge1xuICAgICAgICAgICAgJ2NvbnRlbnQtdHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ2RlbGV0ZScsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFVzZSB0aGlzIGVuZHBvaW50IHRvIGxpc3QgYSB0ZWFtJiMwMzk7cyBtZW1iZXJzIHVzaW5nIHRoZSB0ZWFtJiMwMzk7cyBJRC4gQWxsIHRlYW0gbWVtYmVycyBoYXZlIHJlYWQgYWNjZXNzIHRvIHRoaXMgZW5kcG9pbnQuIEhpZGUgc2Vuc2l0aXZlIGF0dHJpYnV0ZXMgZnJvbSB0aGUgcmVzcG9uc2UgYnkgdG9nZ2xpbmcgbWVtYmVyc2hpcCBwcml2YWN5IGluIHRoZSBDb25zb2xlLlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IHRlYW1JZFxuICAgICAqIEBwYXJhbSB7c3RyaW5nW119IHF1ZXJpZXNcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gc2VhcmNoXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8TW9kZWxzLk1lbWJlcnNoaXBMaXN0Pn1cbiAgICAgKi9cbiAgICBsaXN0TWVtYmVyc2hpcHModGVhbUlkLCBxdWVyaWVzLCBzZWFyY2gpIHtcbiAgICAgICAgaWYgKHR5cGVvZiB0ZWFtSWQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcInRlYW1JZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvdGVhbXMve3RlYW1JZH0vbWVtYmVyc2hpcHMnLnJlcGxhY2UoJ3t0ZWFtSWR9JywgdGVhbUlkKTtcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBpZiAodHlwZW9mIHF1ZXJpZXMgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydxdWVyaWVzJ10gPSBxdWVyaWVzO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2Ygc2VhcmNoICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsnc2VhcmNoJ10gPSBzZWFyY2g7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgY29uc3QgYXBpSGVhZGVycyA9IHt9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgnZ2V0JywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogSW52aXRlIGEgbmV3IG1lbWJlciB0byBqb2luIHlvdXIgdGVhbS4gUHJvdmlkZSBhbiBJRCBmb3IgZXhpc3RpbmcgdXNlcnMsIG9yIGludml0ZSB1bnJlZ2lzdGVyZWQgdXNlcnMgdXNpbmcgYW4gZW1haWwgb3IgcGhvbmUgbnVtYmVyLiBJZiBpbml0aWF0ZWQgZnJvbSBhIENsaWVudCBTREssIEFwcHdyaXRlIHdpbGwgc2VuZCBhbiBlbWFpbCBvciBzbXMgd2l0aCBhIGxpbmsgdG8gam9pbiB0aGUgdGVhbSB0byB0aGUgaW52aXRlZCB1c2VyLCBhbmQgYW4gYWNjb3VudCB3aWxsIGJlIGNyZWF0ZWQgZm9yIHRoZW0gaWYgb25lIGRvZXNuJiMwMzk7dCBleGlzdC4gSWYgaW5pdGlhdGVkIGZyb20gYSBTZXJ2ZXIgU0RLLCB0aGUgbmV3IG1lbWJlciB3aWxsIGJlIGFkZGVkIGF1dG9tYXRpY2FsbHkgdG8gdGhlIHRlYW0uXG4gICAgICpcbiAgICAgKiBZb3Ugb25seSBuZWVkIHRvIHByb3ZpZGUgb25lIG9mIGEgdXNlciBJRCwgZW1haWwsIG9yIHBob25lIG51bWJlci4gQXBwd3JpdGUgd2lsbCBwcmlvcml0aXplIGFjY2VwdGluZyB0aGUgdXNlciBJRCAmZ3Q7IGVtYWlsICZndDsgcGhvbmUgbnVtYmVyIGlmIHlvdSBwcm92aWRlIG1vcmUgdGhhbiBvbmUgb2YgdGhlc2UgcGFyYW1ldGVycy5cbiAgICAgKlxuICAgICAqIFVzZSB0aGUgYHVybGAgcGFyYW1ldGVyIHRvIHJlZGlyZWN0IHRoZSB1c2VyIGZyb20gdGhlIGludml0YXRpb24gZW1haWwgdG8geW91ciBhcHAuIEFmdGVyIHRoZSB1c2VyIGlzIHJlZGlyZWN0ZWQsIHVzZSB0aGUgW1VwZGF0ZSBUZWFtIE1lbWJlcnNoaXAgU3RhdHVzXShodHRwczovL2FwcHdyaXRlLmlvL2RvY3MvcmVmZXJlbmNlcy9jbG91ZC9jbGllbnQtd2ViL3RlYW1zI3VwZGF0ZU1lbWJlcnNoaXBTdGF0dXMpIGVuZHBvaW50IHRvIGFsbG93IHRoZSB1c2VyIHRvIGFjY2VwdCB0aGUgaW52aXRhdGlvbiB0byB0aGUgdGVhbS5cbiAgICAgKlxuICAgICAqIFBsZWFzZSBub3RlIHRoYXQgdG8gYXZvaWQgYSBbUmVkaXJlY3QgQXR0YWNrXShodHRwczovL2dpdGh1Yi5jb20vT1dBU1AvQ2hlYXRTaGVldFNlcmllcy9ibG9iL21hc3Rlci9jaGVhdHNoZWV0cy9VbnZhbGlkYXRlZF9SZWRpcmVjdHNfYW5kX0ZvcndhcmRzX0NoZWF0X1NoZWV0Lm1kKSBBcHB3cml0ZSB3aWxsIGFjY2VwdCB0aGUgb25seSByZWRpcmVjdCBVUkxzIHVuZGVyIHRoZSBkb21haW5zIHlvdSBoYXZlIGFkZGVkIGFzIGEgcGxhdGZvcm0gb24gdGhlIEFwcHdyaXRlIENvbnNvbGUuXG4gICAgICpcbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSB0ZWFtSWRcbiAgICAgKiBAcGFyYW0ge3N0cmluZ1tdfSByb2xlc1xuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBlbWFpbFxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSB1c2VySWRcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gcGhvbmVcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gdXJsXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IG5hbWVcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTxNb2RlbHMuTWVtYmVyc2hpcD59XG4gICAgICovXG4gICAgY3JlYXRlTWVtYmVyc2hpcCh0ZWFtSWQsIHJvbGVzLCBlbWFpbCwgdXNlcklkLCBwaG9uZSwgdXJsLCBuYW1lKSB7XG4gICAgICAgIGlmICh0eXBlb2YgdGVhbUlkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJ0ZWFtSWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2Ygcm9sZXMgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcInJvbGVzXCInKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy90ZWFtcy97dGVhbUlkfS9tZW1iZXJzaGlwcycucmVwbGFjZSgne3RlYW1JZH0nLCB0ZWFtSWQpO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGlmICh0eXBlb2YgZW1haWwgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydlbWFpbCddID0gZW1haWw7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiB1c2VySWQgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWyd1c2VySWQnXSA9IHVzZXJJZDtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIHBob25lICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsncGhvbmUnXSA9IHBob25lO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2Ygcm9sZXMgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydyb2xlcyddID0gcm9sZXM7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiB1cmwgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWyd1cmwnXSA9IHVybDtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIG5hbWUgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWyduYW1lJ10gPSBuYW1lO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7XG4gICAgICAgICAgICAnY29udGVudC10eXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgncG9zdCcsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIEdldCBhIHRlYW0gbWVtYmVyIGJ5IHRoZSBtZW1iZXJzaGlwIHVuaXF1ZSBpZC4gQWxsIHRlYW0gbWVtYmVycyBoYXZlIHJlYWQgYWNjZXNzIGZvciB0aGlzIHJlc291cmNlLiBIaWRlIHNlbnNpdGl2ZSBhdHRyaWJ1dGVzIGZyb20gdGhlIHJlc3BvbnNlIGJ5IHRvZ2dsaW5nIG1lbWJlcnNoaXAgcHJpdmFjeSBpbiB0aGUgQ29uc29sZS5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSB0ZWFtSWRcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gbWVtYmVyc2hpcElkXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8TW9kZWxzLk1lbWJlcnNoaXA+fVxuICAgICAqL1xuICAgIGdldE1lbWJlcnNoaXAodGVhbUlkLCBtZW1iZXJzaGlwSWQpIHtcbiAgICAgICAgaWYgKHR5cGVvZiB0ZWFtSWQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcInRlYW1JZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBtZW1iZXJzaGlwSWQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcIm1lbWJlcnNoaXBJZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvdGVhbXMve3RlYW1JZH0vbWVtYmVyc2hpcHMve21lbWJlcnNoaXBJZH0nLnJlcGxhY2UoJ3t0ZWFtSWR9JywgdGVhbUlkKS5yZXBsYWNlKCd7bWVtYmVyc2hpcElkfScsIG1lbWJlcnNoaXBJZCk7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgY29uc3QgYXBpSGVhZGVycyA9IHt9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgnZ2V0JywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogTW9kaWZ5IHRoZSByb2xlcyBvZiBhIHRlYW0gbWVtYmVyLiBPbmx5IHRlYW0gbWVtYmVycyB3aXRoIHRoZSBvd25lciByb2xlIGhhdmUgYWNjZXNzIHRvIHRoaXMgZW5kcG9pbnQuIExlYXJuIG1vcmUgYWJvdXQgW3JvbGVzIGFuZCBwZXJtaXNzaW9uc10oaHR0cHM6Ly9hcHB3cml0ZS5pby9kb2NzL3Blcm1pc3Npb25zKS5cbiAgICAgKlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IHRlYW1JZFxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBtZW1iZXJzaGlwSWRcbiAgICAgKiBAcGFyYW0ge3N0cmluZ1tdfSByb2xlc1xuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPE1vZGVscy5NZW1iZXJzaGlwPn1cbiAgICAgKi9cbiAgICB1cGRhdGVNZW1iZXJzaGlwKHRlYW1JZCwgbWVtYmVyc2hpcElkLCByb2xlcykge1xuICAgICAgICBpZiAodHlwZW9mIHRlYW1JZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwidGVhbUlkXCInKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIG1lbWJlcnNoaXBJZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwibWVtYmVyc2hpcElkXCInKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIHJvbGVzID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJyb2xlc1wiJyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvdGVhbXMve3RlYW1JZH0vbWVtYmVyc2hpcHMve21lbWJlcnNoaXBJZH0nLnJlcGxhY2UoJ3t0ZWFtSWR9JywgdGVhbUlkKS5yZXBsYWNlKCd7bWVtYmVyc2hpcElkfScsIG1lbWJlcnNoaXBJZCk7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgaWYgKHR5cGVvZiByb2xlcyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3JvbGVzJ10gPSByb2xlcztcbiAgICAgICAgfVxuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge1xuICAgICAgICAgICAgJ2NvbnRlbnQtdHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ3BhdGNoJywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogVGhpcyBlbmRwb2ludCBhbGxvd3MgYSB1c2VyIHRvIGxlYXZlIGEgdGVhbSBvciBmb3IgYSB0ZWFtIG93bmVyIHRvIGRlbGV0ZSB0aGUgbWVtYmVyc2hpcCBvZiBhbnkgb3RoZXIgdGVhbSBtZW1iZXIuIFlvdSBjYW4gYWxzbyB1c2UgdGhpcyBlbmRwb2ludCB0byBkZWxldGUgYSB1c2VyIG1lbWJlcnNoaXAgZXZlbiBpZiBpdCBpcyBub3QgYWNjZXB0ZWQuXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gdGVhbUlkXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IG1lbWJlcnNoaXBJZFxuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPHt9Pn1cbiAgICAgKi9cbiAgICBkZWxldGVNZW1iZXJzaGlwKHRlYW1JZCwgbWVtYmVyc2hpcElkKSB7XG4gICAgICAgIGlmICh0eXBlb2YgdGVhbUlkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJ0ZWFtSWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgbWVtYmVyc2hpcElkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJtZW1iZXJzaGlwSWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL3RlYW1zL3t0ZWFtSWR9L21lbWJlcnNoaXBzL3ttZW1iZXJzaGlwSWR9Jy5yZXBsYWNlKCd7dGVhbUlkfScsIHRlYW1JZCkucmVwbGFjZSgne21lbWJlcnNoaXBJZH0nLCBtZW1iZXJzaGlwSWQpO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7XG4gICAgICAgICAgICAnY29udGVudC10eXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgnZGVsZXRlJywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogVXNlIHRoaXMgZW5kcG9pbnQgdG8gYWxsb3cgYSB1c2VyIHRvIGFjY2VwdCBhbiBpbnZpdGF0aW9uIHRvIGpvaW4gYSB0ZWFtIGFmdGVyIGJlaW5nIHJlZGlyZWN0ZWQgYmFjayB0byB5b3VyIGFwcCBmcm9tIHRoZSBpbnZpdGF0aW9uIGVtYWlsIHJlY2VpdmVkIGJ5IHRoZSB1c2VyLlxuICAgICAqXG4gICAgICogSWYgdGhlIHJlcXVlc3QgaXMgc3VjY2Vzc2Z1bCwgYSBzZXNzaW9uIGZvciB0aGUgdXNlciBpcyBhdXRvbWF0aWNhbGx5IGNyZWF0ZWQuXG4gICAgICpcbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSB0ZWFtSWRcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gbWVtYmVyc2hpcElkXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IHVzZXJJZFxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBzZWNyZXRcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTxNb2RlbHMuTWVtYmVyc2hpcD59XG4gICAgICovXG4gICAgdXBkYXRlTWVtYmVyc2hpcFN0YXR1cyh0ZWFtSWQsIG1lbWJlcnNoaXBJZCwgdXNlcklkLCBzZWNyZXQpIHtcbiAgICAgICAgaWYgKHR5cGVvZiB0ZWFtSWQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcInRlYW1JZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBtZW1iZXJzaGlwSWQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcIm1lbWJlcnNoaXBJZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiB1c2VySWQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcInVzZXJJZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBzZWNyZXQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcInNlY3JldFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvdGVhbXMve3RlYW1JZH0vbWVtYmVyc2hpcHMve21lbWJlcnNoaXBJZH0vc3RhdHVzJy5yZXBsYWNlKCd7dGVhbUlkfScsIHRlYW1JZCkucmVwbGFjZSgne21lbWJlcnNoaXBJZH0nLCBtZW1iZXJzaGlwSWQpO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGlmICh0eXBlb2YgdXNlcklkICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsndXNlcklkJ10gPSB1c2VySWQ7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBzZWNyZXQgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydzZWNyZXQnXSA9IHNlY3JldDtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge1xuICAgICAgICAgICAgJ2NvbnRlbnQtdHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ3BhdGNoJywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogR2V0IHRoZSB0ZWFtJiMwMzk7cyBzaGFyZWQgcHJlZmVyZW5jZXMgYnkgaXRzIHVuaXF1ZSBJRC4gSWYgYSBwcmVmZXJlbmNlIGRvZXNuJiMwMzk7dCBuZWVkIHRvIGJlIHNoYXJlZCBieSBhbGwgdGVhbSBtZW1iZXJzLCBwcmVmZXIgc3RvcmluZyB0aGVtIGluIFt1c2VyIHByZWZlcmVuY2VzXShodHRwczovL2FwcHdyaXRlLmlvL2RvY3MvcmVmZXJlbmNlcy9jbG91ZC9jbGllbnQtd2ViL2FjY291bnQjZ2V0UHJlZnMpLlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IHRlYW1JZFxuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPFByZWZlcmVuY2VzPn1cbiAgICAgKi9cbiAgICBnZXRQcmVmcyh0ZWFtSWQpIHtcbiAgICAgICAgaWYgKHR5cGVvZiB0ZWFtSWQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcInRlYW1JZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvdGVhbXMve3RlYW1JZH0vcHJlZnMnLnJlcGxhY2UoJ3t0ZWFtSWR9JywgdGVhbUlkKTtcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge307XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdnZXQnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBVcGRhdGUgdGhlIHRlYW0mIzAzOTtzIHByZWZlcmVuY2VzIGJ5IGl0cyB1bmlxdWUgSUQuIFRoZSBvYmplY3QgeW91IHBhc3MgaXMgc3RvcmVkIGFzIGlzIGFuZCByZXBsYWNlcyBhbnkgcHJldmlvdXMgdmFsdWUuIFRoZSBtYXhpbXVtIGFsbG93ZWQgcHJlZnMgc2l6ZSBpcyA2NGtCIGFuZCB0aHJvd3MgYW4gZXJyb3IgaWYgZXhjZWVkZWQuXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gdGVhbUlkXG4gICAgICogQHBhcmFtIHtvYmplY3R9IHByZWZzXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8UHJlZmVyZW5jZXM+fVxuICAgICAqL1xuICAgIHVwZGF0ZVByZWZzKHRlYW1JZCwgcHJlZnMpIHtcbiAgICAgICAgaWYgKHR5cGVvZiB0ZWFtSWQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcInRlYW1JZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBwcmVmcyA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwicHJlZnNcIicpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL3RlYW1zL3t0ZWFtSWR9L3ByZWZzJy5yZXBsYWNlKCd7dGVhbUlkfScsIHRlYW1JZCk7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgaWYgKHR5cGVvZiBwcmVmcyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3ByZWZzJ10gPSBwcmVmcztcbiAgICAgICAgfVxuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge1xuICAgICAgICAgICAgJ2NvbnRlbnQtdHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ3B1dCcsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxufVxuXG4vKipcbiAqIEhlbHBlciBjbGFzcyB0byBnZW5lcmF0ZSBwZXJtaXNzaW9uIHN0cmluZ3MgZm9yIHJlc291cmNlcy5cbiAqL1xuY2xhc3MgUGVybWlzc2lvbiB7XG59XG4vKipcbiAqIEdlbmVyYXRlIHJlYWQgcGVybWlzc2lvbiBzdHJpbmcgZm9yIHRoZSBwcm92aWRlZCByb2xlLlxuICpcbiAqIEBwYXJhbSB7c3RyaW5nfSByb2xlXG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICovXG5QZXJtaXNzaW9uLnJlYWQgPSAocm9sZSkgPT4ge1xuICAgIHJldHVybiBgcmVhZChcIiR7cm9sZX1cIilgO1xufTtcbi8qKlxuICogR2VuZXJhdGUgd3JpdGUgcGVybWlzc2lvbiBzdHJpbmcgZm9yIHRoZSBwcm92aWRlZCByb2xlLlxuICpcbiAqIFRoaXMgaXMgYW4gYWxpYXMgb2YgdXBkYXRlLCBkZWxldGUsIGFuZCBwb3NzaWJseSBjcmVhdGUuXG4gKiBEb24ndCB1c2Ugd3JpdGUgaW4gY29tYmluYXRpb24gd2l0aCB1cGRhdGUsIGRlbGV0ZSwgb3IgY3JlYXRlLlxuICpcbiAqIEBwYXJhbSB7c3RyaW5nfSByb2xlXG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICovXG5QZXJtaXNzaW9uLndyaXRlID0gKHJvbGUpID0+IHtcbiAgICByZXR1cm4gYHdyaXRlKFwiJHtyb2xlfVwiKWA7XG59O1xuLyoqXG4gKiBHZW5lcmF0ZSBjcmVhdGUgcGVybWlzc2lvbiBzdHJpbmcgZm9yIHRoZSBwcm92aWRlZCByb2xlLlxuICpcbiAqIEBwYXJhbSB7c3RyaW5nfSByb2xlXG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICovXG5QZXJtaXNzaW9uLmNyZWF0ZSA9IChyb2xlKSA9PiB7XG4gICAgcmV0dXJuIGBjcmVhdGUoXCIke3JvbGV9XCIpYDtcbn07XG4vKipcbiAqIEdlbmVyYXRlIHVwZGF0ZSBwZXJtaXNzaW9uIHN0cmluZyBmb3IgdGhlIHByb3ZpZGVkIHJvbGUuXG4gKlxuICogQHBhcmFtIHtzdHJpbmd9IHJvbGVcbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKi9cblBlcm1pc3Npb24udXBkYXRlID0gKHJvbGUpID0+IHtcbiAgICByZXR1cm4gYHVwZGF0ZShcIiR7cm9sZX1cIilgO1xufTtcbi8qKlxuICogR2VuZXJhdGUgZGVsZXRlIHBlcm1pc3Npb24gc3RyaW5nIGZvciB0aGUgcHJvdmlkZWQgcm9sZS5cbiAqXG4gKiBAcGFyYW0ge3N0cmluZ30gcm9sZVxuICogQHJldHVybnMge3N0cmluZ31cbiAqL1xuUGVybWlzc2lvbi5kZWxldGUgPSAocm9sZSkgPT4ge1xuICAgIHJldHVybiBgZGVsZXRlKFwiJHtyb2xlfVwiKWA7XG59O1xuXG4vKipcbiAqIEhlbHBlciBjbGFzcyB0byBnZW5lcmF0ZSByb2xlIHN0cmluZ3MgZm9yIGBQZXJtaXNzaW9uYC5cbiAqL1xuY2xhc3MgUm9sZSB7XG4gICAgLyoqXG4gICAgICogR3JhbnRzIGFjY2VzcyB0byBhbnlvbmUuXG4gICAgICpcbiAgICAgKiBUaGlzIGluY2x1ZGVzIGF1dGhlbnRpY2F0ZWQgYW5kIHVuYXV0aGVudGljYXRlZCB1c2Vycy5cbiAgICAgKlxuICAgICAqIEByZXR1cm5zIHtzdHJpbmd9XG4gICAgICovXG4gICAgc3RhdGljIGFueSgpIHtcbiAgICAgICAgcmV0dXJuICdhbnknO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBHcmFudHMgYWNjZXNzIHRvIGEgc3BlY2lmaWMgdXNlciBieSB1c2VyIElELlxuICAgICAqXG4gICAgICogWW91IGNhbiBvcHRpb25hbGx5IHBhc3MgdmVyaWZpZWQgb3IgdW52ZXJpZmllZCBmb3JcbiAgICAgKiBgc3RhdHVzYCB0byB0YXJnZXQgc3BlY2lmaWMgdHlwZXMgb2YgdXNlcnMuXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gaWRcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gc3RhdHVzXG4gICAgICogQHJldHVybnMge3N0cmluZ31cbiAgICAgKi9cbiAgICBzdGF0aWMgdXNlcihpZCwgc3RhdHVzID0gJycpIHtcbiAgICAgICAgaWYgKHN0YXR1cyA9PT0gJycpIHtcbiAgICAgICAgICAgIHJldHVybiBgdXNlcjoke2lkfWA7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIGB1c2VyOiR7aWR9LyR7c3RhdHVzfWA7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIEdyYW50cyBhY2Nlc3MgdG8gYW55IGF1dGhlbnRpY2F0ZWQgb3IgYW5vbnltb3VzIHVzZXIuXG4gICAgICpcbiAgICAgKiBZb3UgY2FuIG9wdGlvbmFsbHkgcGFzcyB2ZXJpZmllZCBvciB1bnZlcmlmaWVkIGZvclxuICAgICAqIGBzdGF0dXNgIHRvIHRhcmdldCBzcGVjaWZpYyB0eXBlcyBvZiB1c2Vycy5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBzdGF0dXNcbiAgICAgKiBAcmV0dXJucyB7c3RyaW5nfVxuICAgICAqL1xuICAgIHN0YXRpYyB1c2VycyhzdGF0dXMgPSAnJykge1xuICAgICAgICBpZiAoc3RhdHVzID09PSAnJykge1xuICAgICAgICAgICAgcmV0dXJuICd1c2Vycyc7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIGB1c2Vycy8ke3N0YXR1c31gO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBHcmFudHMgYWNjZXNzIHRvIGFueSBndWVzdCB1c2VyIHdpdGhvdXQgYSBzZXNzaW9uLlxuICAgICAqXG4gICAgICogQXV0aGVudGljYXRlZCB1c2VycyBkb24ndCBoYXZlIGFjY2VzcyB0byB0aGlzIHJvbGUuXG4gICAgICpcbiAgICAgKiBAcmV0dXJucyB7c3RyaW5nfVxuICAgICAqL1xuICAgIHN0YXRpYyBndWVzdHMoKSB7XG4gICAgICAgIHJldHVybiAnZ3Vlc3RzJztcbiAgICB9XG4gICAgLyoqXG4gICAgICogR3JhbnRzIGFjY2VzcyB0byBhIHRlYW0gYnkgdGVhbSBJRC5cbiAgICAgKlxuICAgICAqIFlvdSBjYW4gb3B0aW9uYWxseSBwYXNzIGEgcm9sZSBmb3IgYHJvbGVgIHRvIHRhcmdldFxuICAgICAqIHRlYW0gbWVtYmVycyB3aXRoIHRoZSBzcGVjaWZpZWQgcm9sZS5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBpZFxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSByb2xlXG4gICAgICogQHJldHVybnMge3N0cmluZ31cbiAgICAgKi9cbiAgICBzdGF0aWMgdGVhbShpZCwgcm9sZSA9ICcnKSB7XG4gICAgICAgIGlmIChyb2xlID09PSAnJykge1xuICAgICAgICAgICAgcmV0dXJuIGB0ZWFtOiR7aWR9YDtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gYHRlYW06JHtpZH0vJHtyb2xlfWA7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIEdyYW50cyBhY2Nlc3MgdG8gYSBzcGVjaWZpYyBtZW1iZXIgb2YgYSB0ZWFtLlxuICAgICAqXG4gICAgICogV2hlbiB0aGUgbWVtYmVyIGlzIHJlbW92ZWQgZnJvbSB0aGUgdGVhbSwgdGhleSB3aWxsXG4gICAgICogbm8gbG9uZ2VyIGhhdmUgYWNjZXNzLlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGlkXG4gICAgICogQHJldHVybnMge3N0cmluZ31cbiAgICAgKi9cbiAgICBzdGF0aWMgbWVtYmVyKGlkKSB7XG4gICAgICAgIHJldHVybiBgbWVtYmVyOiR7aWR9YDtcbiAgICB9XG4gICAgLyoqXG4gICAgICogR3JhbnRzIGFjY2VzcyB0byBhIHVzZXIgd2l0aCB0aGUgc3BlY2lmaWVkIGxhYmVsLlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IG5hbWVcbiAgICAgKiBAcmV0dXJucyAge3N0cmluZ31cbiAgICAgKi9cbiAgICBzdGF0aWMgbGFiZWwobmFtZSkge1xuICAgICAgICByZXR1cm4gYGxhYmVsOiR7bmFtZX1gO1xuICAgIH1cbn1cblxudmFyIF9hLCBfSURfaGV4VGltZXN0YW1wO1xuLyoqXG4gKiBIZWxwZXIgY2xhc3MgdG8gZ2VuZXJhdGUgSUQgc3RyaW5ncyBmb3IgcmVzb3VyY2VzLlxuICovXG5jbGFzcyBJRCB7XG4gICAgLyoqXG4gICAgICogVXNlcyB0aGUgcHJvdmlkZWQgSUQgYXMgdGhlIElEIGZvciB0aGUgcmVzb3VyY2UuXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gaWRcbiAgICAgKiBAcmV0dXJucyB7c3RyaW5nfVxuICAgICAqL1xuICAgIHN0YXRpYyBjdXN0b20oaWQpIHtcbiAgICAgICAgcmV0dXJuIGlkO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBIYXZlIEFwcHdyaXRlIGdlbmVyYXRlIGEgdW5pcXVlIElEIGZvciB5b3UuXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge251bWJlcn0gcGFkZGluZy4gRGVmYXVsdCBpcyA3LlxuICAgICAqIEByZXR1cm5zIHtzdHJpbmd9XG4gICAgICovXG4gICAgc3RhdGljIHVuaXF1ZShwYWRkaW5nID0gNykge1xuICAgICAgICAvLyBHZW5lcmF0ZSBhIHVuaXF1ZSBJRCB3aXRoIHBhZGRpbmcgdG8gaGF2ZSBhIGxvbmdlciBJRFxuICAgICAgICBjb25zdCBiYXNlSWQgPSBfX2NsYXNzUHJpdmF0ZUZpZWxkR2V0KElELCBfYSwgXCJtXCIsIF9JRF9oZXhUaW1lc3RhbXApLmNhbGwoSUQpO1xuICAgICAgICBsZXQgcmFuZG9tUGFkZGluZyA9ICcnO1xuICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IHBhZGRpbmc7IGkrKykge1xuICAgICAgICAgICAgY29uc3QgcmFuZG9tSGV4RGlnaXQgPSBNYXRoLmZsb29yKE1hdGgucmFuZG9tKCkgKiAxNikudG9TdHJpbmcoMTYpO1xuICAgICAgICAgICAgcmFuZG9tUGFkZGluZyArPSByYW5kb21IZXhEaWdpdDtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gYmFzZUlkICsgcmFuZG9tUGFkZGluZztcbiAgICB9XG59XG5fYSA9IElELCBfSURfaGV4VGltZXN0YW1wID0gZnVuY3Rpb24gX0lEX2hleFRpbWVzdGFtcCgpIHtcbiAgICBjb25zdCBub3cgPSBuZXcgRGF0ZSgpO1xuICAgIGNvbnN0IHNlYyA9IE1hdGguZmxvb3Iobm93LmdldFRpbWUoKSAvIDEwMDApO1xuICAgIGNvbnN0IG1zZWMgPSBub3cuZ2V0TWlsbGlzZWNvbmRzKCk7XG4gICAgLy8gQ29udmVydCB0byBoZXhhZGVjaW1hbFxuICAgIGNvbnN0IGhleFRpbWVzdGFtcCA9IHNlYy50b1N0cmluZygxNikgKyBtc2VjLnRvU3RyaW5nKDE2KS5wYWRTdGFydCg1LCAnMCcpO1xuICAgIHJldHVybiBoZXhUaW1lc3RhbXA7XG59O1xuXG52YXIgQXV0aGVudGljYXRvclR5cGU7XG4oZnVuY3Rpb24gKEF1dGhlbnRpY2F0b3JUeXBlKSB7XG4gICAgQXV0aGVudGljYXRvclR5cGVbXCJUb3RwXCJdID0gXCJ0b3RwXCI7XG59KShBdXRoZW50aWNhdG9yVHlwZSB8fCAoQXV0aGVudGljYXRvclR5cGUgPSB7fSkpO1xuXG52YXIgQXV0aGVudGljYXRpb25GYWN0b3I7XG4oZnVuY3Rpb24gKEF1dGhlbnRpY2F0aW9uRmFjdG9yKSB7XG4gICAgQXV0aGVudGljYXRpb25GYWN0b3JbXCJFbWFpbFwiXSA9IFwiZW1haWxcIjtcbiAgICBBdXRoZW50aWNhdGlvbkZhY3RvcltcIlBob25lXCJdID0gXCJwaG9uZVwiO1xuICAgIEF1dGhlbnRpY2F0aW9uRmFjdG9yW1wiVG90cFwiXSA9IFwidG90cFwiO1xuICAgIEF1dGhlbnRpY2F0aW9uRmFjdG9yW1wiUmVjb3Zlcnljb2RlXCJdID0gXCJyZWNvdmVyeWNvZGVcIjtcbn0pKEF1dGhlbnRpY2F0aW9uRmFjdG9yIHx8IChBdXRoZW50aWNhdGlvbkZhY3RvciA9IHt9KSk7XG5cbnZhciBPQXV0aFByb3ZpZGVyO1xuKGZ1bmN0aW9uIChPQXV0aFByb3ZpZGVyKSB7XG4gICAgT0F1dGhQcm92aWRlcltcIkFtYXpvblwiXSA9IFwiYW1hem9uXCI7XG4gICAgT0F1dGhQcm92aWRlcltcIkFwcGxlXCJdID0gXCJhcHBsZVwiO1xuICAgIE9BdXRoUHJvdmlkZXJbXCJBdXRoMFwiXSA9IFwiYXV0aDBcIjtcbiAgICBPQXV0aFByb3ZpZGVyW1wiQXV0aGVudGlrXCJdID0gXCJhdXRoZW50aWtcIjtcbiAgICBPQXV0aFByb3ZpZGVyW1wiQXV0b2Rlc2tcIl0gPSBcImF1dG9kZXNrXCI7XG4gICAgT0F1dGhQcm92aWRlcltcIkJpdGJ1Y2tldFwiXSA9IFwiYml0YnVja2V0XCI7XG4gICAgT0F1dGhQcm92aWRlcltcIkJpdGx5XCJdID0gXCJiaXRseVwiO1xuICAgIE9BdXRoUHJvdmlkZXJbXCJCb3hcIl0gPSBcImJveFwiO1xuICAgIE9BdXRoUHJvdmlkZXJbXCJEYWlseW1vdGlvblwiXSA9IFwiZGFpbHltb3Rpb25cIjtcbiAgICBPQXV0aFByb3ZpZGVyW1wiRGlzY29yZFwiXSA9IFwiZGlzY29yZFwiO1xuICAgIE9BdXRoUHJvdmlkZXJbXCJEaXNxdXNcIl0gPSBcImRpc3F1c1wiO1xuICAgIE9BdXRoUHJvdmlkZXJbXCJEcm9wYm94XCJdID0gXCJkcm9wYm94XCI7XG4gICAgT0F1dGhQcm92aWRlcltcIkV0c3lcIl0gPSBcImV0c3lcIjtcbiAgICBPQXV0aFByb3ZpZGVyW1wiRmFjZWJvb2tcIl0gPSBcImZhY2Vib29rXCI7XG4gICAgT0F1dGhQcm92aWRlcltcIkZpZ21hXCJdID0gXCJmaWdtYVwiO1xuICAgIE9BdXRoUHJvdmlkZXJbXCJHaXRodWJcIl0gPSBcImdpdGh1YlwiO1xuICAgIE9BdXRoUHJvdmlkZXJbXCJHaXRsYWJcIl0gPSBcImdpdGxhYlwiO1xuICAgIE9BdXRoUHJvdmlkZXJbXCJHb29nbGVcIl0gPSBcImdvb2dsZVwiO1xuICAgIE9BdXRoUHJvdmlkZXJbXCJMaW5rZWRpblwiXSA9IFwibGlua2VkaW5cIjtcbiAgICBPQXV0aFByb3ZpZGVyW1wiTWljcm9zb2Z0XCJdID0gXCJtaWNyb3NvZnRcIjtcbiAgICBPQXV0aFByb3ZpZGVyW1wiTm90aW9uXCJdID0gXCJub3Rpb25cIjtcbiAgICBPQXV0aFByb3ZpZGVyW1wiT2lkY1wiXSA9IFwib2lkY1wiO1xuICAgIE9BdXRoUHJvdmlkZXJbXCJPa3RhXCJdID0gXCJva3RhXCI7XG4gICAgT0F1dGhQcm92aWRlcltcIlBheXBhbFwiXSA9IFwicGF5cGFsXCI7XG4gICAgT0F1dGhQcm92aWRlcltcIlBheXBhbFNhbmRib3hcIl0gPSBcInBheXBhbFNhbmRib3hcIjtcbiAgICBPQXV0aFByb3ZpZGVyW1wiUG9kaW9cIl0gPSBcInBvZGlvXCI7XG4gICAgT0F1dGhQcm92aWRlcltcIlNhbGVzZm9yY2VcIl0gPSBcInNhbGVzZm9yY2VcIjtcbiAgICBPQXV0aFByb3ZpZGVyW1wiU2xhY2tcIl0gPSBcInNsYWNrXCI7XG4gICAgT0F1dGhQcm92aWRlcltcIlNwb3RpZnlcIl0gPSBcInNwb3RpZnlcIjtcbiAgICBPQXV0aFByb3ZpZGVyW1wiU3RyaXBlXCJdID0gXCJzdHJpcGVcIjtcbiAgICBPQXV0aFByb3ZpZGVyW1wiVHJhZGVzaGlmdFwiXSA9IFwidHJhZGVzaGlmdFwiO1xuICAgIE9BdXRoUHJvdmlkZXJbXCJUcmFkZXNoaWZ0Qm94XCJdID0gXCJ0cmFkZXNoaWZ0Qm94XCI7XG4gICAgT0F1dGhQcm92aWRlcltcIlR3aXRjaFwiXSA9IFwidHdpdGNoXCI7XG4gICAgT0F1dGhQcm92aWRlcltcIldvcmRwcmVzc1wiXSA9IFwid29yZHByZXNzXCI7XG4gICAgT0F1dGhQcm92aWRlcltcIllhaG9vXCJdID0gXCJ5YWhvb1wiO1xuICAgIE9BdXRoUHJvdmlkZXJbXCJZYW1tZXJcIl0gPSBcInlhbW1lclwiO1xuICAgIE9BdXRoUHJvdmlkZXJbXCJZYW5kZXhcIl0gPSBcInlhbmRleFwiO1xuICAgIE9BdXRoUHJvdmlkZXJbXCJab2hvXCJdID0gXCJ6b2hvXCI7XG4gICAgT0F1dGhQcm92aWRlcltcIlpvb21cIl0gPSBcInpvb21cIjtcbiAgICBPQXV0aFByb3ZpZGVyW1wiTW9ja1wiXSA9IFwibW9ja1wiO1xufSkoT0F1dGhQcm92aWRlciB8fCAoT0F1dGhQcm92aWRlciA9IHt9KSk7XG5cbnZhciBCcm93c2VyO1xuKGZ1bmN0aW9uIChCcm93c2VyKSB7XG4gICAgQnJvd3NlcltcIkF2YW50QnJvd3NlclwiXSA9IFwiYWFcIjtcbiAgICBCcm93c2VyW1wiQW5kcm9pZFdlYlZpZXdCZXRhXCJdID0gXCJhblwiO1xuICAgIEJyb3dzZXJbXCJHb29nbGVDaHJvbWVcIl0gPSBcImNoXCI7XG4gICAgQnJvd3NlcltcIkdvb2dsZUNocm9tZUlPU1wiXSA9IFwiY2lcIjtcbiAgICBCcm93c2VyW1wiR29vZ2xlQ2hyb21lTW9iaWxlXCJdID0gXCJjbVwiO1xuICAgIEJyb3dzZXJbXCJDaHJvbWl1bVwiXSA9IFwiY3JcIjtcbiAgICBCcm93c2VyW1wiTW96aWxsYUZpcmVmb3hcIl0gPSBcImZmXCI7XG4gICAgQnJvd3NlcltcIlNhZmFyaVwiXSA9IFwic2ZcIjtcbiAgICBCcm93c2VyW1wiTW9iaWxlU2FmYXJpXCJdID0gXCJtZlwiO1xuICAgIEJyb3dzZXJbXCJNaWNyb3NvZnRFZGdlXCJdID0gXCJwc1wiO1xuICAgIEJyb3dzZXJbXCJNaWNyb3NvZnRFZGdlSU9TXCJdID0gXCJvaVwiO1xuICAgIEJyb3dzZXJbXCJPcGVyYU1pbmlcIl0gPSBcIm9tXCI7XG4gICAgQnJvd3NlcltcIk9wZXJhXCJdID0gXCJvcFwiO1xuICAgIEJyb3dzZXJbXCJPcGVyYU5leHRcIl0gPSBcIm9uXCI7XG59KShCcm93c2VyIHx8IChCcm93c2VyID0ge30pKTtcblxudmFyIENyZWRpdENhcmQ7XG4oZnVuY3Rpb24gKENyZWRpdENhcmQpIHtcbiAgICBDcmVkaXRDYXJkW1wiQW1lcmljYW5FeHByZXNzXCJdID0gXCJhbWV4XCI7XG4gICAgQ3JlZGl0Q2FyZFtcIkFyZ2VuY2FyZFwiXSA9IFwiYXJnZW5jYXJkXCI7XG4gICAgQ3JlZGl0Q2FyZFtcIkNhYmFsXCJdID0gXCJjYWJhbFwiO1xuICAgIENyZWRpdENhcmRbXCJDZW5jb3N1ZFwiXSA9IFwiY2VuY29zdWRcIjtcbiAgICBDcmVkaXRDYXJkW1wiRGluZXJzQ2x1YlwiXSA9IFwiZGluZXJzXCI7XG4gICAgQ3JlZGl0Q2FyZFtcIkRpc2NvdmVyXCJdID0gXCJkaXNjb3ZlclwiO1xuICAgIENyZWRpdENhcmRbXCJFbG9cIl0gPSBcImVsb1wiO1xuICAgIENyZWRpdENhcmRbXCJIaXBlcmNhcmRcIl0gPSBcImhpcGVyY2FyZFwiO1xuICAgIENyZWRpdENhcmRbXCJKQ0JcIl0gPSBcImpjYlwiO1xuICAgIENyZWRpdENhcmRbXCJNYXN0ZXJjYXJkXCJdID0gXCJtYXN0ZXJjYXJkXCI7XG4gICAgQ3JlZGl0Q2FyZFtcIk5hcmFuamFcIl0gPSBcIm5hcmFuamFcIjtcbiAgICBDcmVkaXRDYXJkW1wiVGFyamV0YVNob3BwaW5nXCJdID0gXCJ0YXJnZXRhLXNob3BwaW5nXCI7XG4gICAgQ3JlZGl0Q2FyZFtcIlVuaW9uQ2hpbmFQYXlcIl0gPSBcInVuaW9uLWNoaW5hLXBheVwiO1xuICAgIENyZWRpdENhcmRbXCJWaXNhXCJdID0gXCJ2aXNhXCI7XG4gICAgQ3JlZGl0Q2FyZFtcIk1JUlwiXSA9IFwibWlyXCI7XG4gICAgQ3JlZGl0Q2FyZFtcIk1hZXN0cm9cIl0gPSBcIm1hZXN0cm9cIjtcbiAgICBDcmVkaXRDYXJkW1wiUnVwYXlcIl0gPSBcInJ1cGF5XCI7XG59KShDcmVkaXRDYXJkIHx8IChDcmVkaXRDYXJkID0ge30pKTtcblxudmFyIEZsYWc7XG4oZnVuY3Rpb24gKEZsYWcpIHtcbiAgICBGbGFnW1wiQWZnaGFuaXN0YW5cIl0gPSBcImFmXCI7XG4gICAgRmxhZ1tcIkFuZ29sYVwiXSA9IFwiYW9cIjtcbiAgICBGbGFnW1wiQWxiYW5pYVwiXSA9IFwiYWxcIjtcbiAgICBGbGFnW1wiQW5kb3JyYVwiXSA9IFwiYWRcIjtcbiAgICBGbGFnW1wiVW5pdGVkQXJhYkVtaXJhdGVzXCJdID0gXCJhZVwiO1xuICAgIEZsYWdbXCJBcmdlbnRpbmFcIl0gPSBcImFyXCI7XG4gICAgRmxhZ1tcIkFybWVuaWFcIl0gPSBcImFtXCI7XG4gICAgRmxhZ1tcIkFudGlndWFBbmRCYXJidWRhXCJdID0gXCJhZ1wiO1xuICAgIEZsYWdbXCJBdXN0cmFsaWFcIl0gPSBcImF1XCI7XG4gICAgRmxhZ1tcIkF1c3RyaWFcIl0gPSBcImF0XCI7XG4gICAgRmxhZ1tcIkF6ZXJiYWlqYW5cIl0gPSBcImF6XCI7XG4gICAgRmxhZ1tcIkJ1cnVuZGlcIl0gPSBcImJpXCI7XG4gICAgRmxhZ1tcIkJlbGdpdW1cIl0gPSBcImJlXCI7XG4gICAgRmxhZ1tcIkJlbmluXCJdID0gXCJialwiO1xuICAgIEZsYWdbXCJCdXJraW5hRmFzb1wiXSA9IFwiYmZcIjtcbiAgICBGbGFnW1wiQmFuZ2xhZGVzaFwiXSA9IFwiYmRcIjtcbiAgICBGbGFnW1wiQnVsZ2FyaWFcIl0gPSBcImJnXCI7XG4gICAgRmxhZ1tcIkJhaHJhaW5cIl0gPSBcImJoXCI7XG4gICAgRmxhZ1tcIkJhaGFtYXNcIl0gPSBcImJzXCI7XG4gICAgRmxhZ1tcIkJvc25pYUFuZEhlcnplZ292aW5hXCJdID0gXCJiYVwiO1xuICAgIEZsYWdbXCJCZWxhcnVzXCJdID0gXCJieVwiO1xuICAgIEZsYWdbXCJCZWxpemVcIl0gPSBcImJ6XCI7XG4gICAgRmxhZ1tcIkJvbGl2aWFcIl0gPSBcImJvXCI7XG4gICAgRmxhZ1tcIkJyYXppbFwiXSA9IFwiYnJcIjtcbiAgICBGbGFnW1wiQmFyYmFkb3NcIl0gPSBcImJiXCI7XG4gICAgRmxhZ1tcIkJydW5laURhcnVzc2FsYW1cIl0gPSBcImJuXCI7XG4gICAgRmxhZ1tcIkJodXRhblwiXSA9IFwiYnRcIjtcbiAgICBGbGFnW1wiQm90c3dhbmFcIl0gPSBcImJ3XCI7XG4gICAgRmxhZ1tcIkNlbnRyYWxBZnJpY2FuUmVwdWJsaWNcIl0gPSBcImNmXCI7XG4gICAgRmxhZ1tcIkNhbmFkYVwiXSA9IFwiY2FcIjtcbiAgICBGbGFnW1wiU3dpdHplcmxhbmRcIl0gPSBcImNoXCI7XG4gICAgRmxhZ1tcIkNoaWxlXCJdID0gXCJjbFwiO1xuICAgIEZsYWdbXCJDaGluYVwiXSA9IFwiY25cIjtcbiAgICBGbGFnW1wiQ290ZURJdm9pcmVcIl0gPSBcImNpXCI7XG4gICAgRmxhZ1tcIkNhbWVyb29uXCJdID0gXCJjbVwiO1xuICAgIEZsYWdbXCJEZW1vY3JhdGljUmVwdWJsaWNPZlRoZUNvbmdvXCJdID0gXCJjZFwiO1xuICAgIEZsYWdbXCJSZXB1YmxpY09mVGhlQ29uZ29cIl0gPSBcImNnXCI7XG4gICAgRmxhZ1tcIkNvbG9tYmlhXCJdID0gXCJjb1wiO1xuICAgIEZsYWdbXCJDb21vcm9zXCJdID0gXCJrbVwiO1xuICAgIEZsYWdbXCJDYXBlVmVyZGVcIl0gPSBcImN2XCI7XG4gICAgRmxhZ1tcIkNvc3RhUmljYVwiXSA9IFwiY3JcIjtcbiAgICBGbGFnW1wiQ3ViYVwiXSA9IFwiY3VcIjtcbiAgICBGbGFnW1wiQ3lwcnVzXCJdID0gXCJjeVwiO1xuICAgIEZsYWdbXCJDemVjaFJlcHVibGljXCJdID0gXCJjelwiO1xuICAgIEZsYWdbXCJHZXJtYW55XCJdID0gXCJkZVwiO1xuICAgIEZsYWdbXCJEamlib3V0aVwiXSA9IFwiZGpcIjtcbiAgICBGbGFnW1wiRG9taW5pY2FcIl0gPSBcImRtXCI7XG4gICAgRmxhZ1tcIkRlbm1hcmtcIl0gPSBcImRrXCI7XG4gICAgRmxhZ1tcIkRvbWluaWNhblJlcHVibGljXCJdID0gXCJkb1wiO1xuICAgIEZsYWdbXCJBbGdlcmlhXCJdID0gXCJkelwiO1xuICAgIEZsYWdbXCJFY3VhZG9yXCJdID0gXCJlY1wiO1xuICAgIEZsYWdbXCJFZ3lwdFwiXSA9IFwiZWdcIjtcbiAgICBGbGFnW1wiRXJpdHJlYVwiXSA9IFwiZXJcIjtcbiAgICBGbGFnW1wiU3BhaW5cIl0gPSBcImVzXCI7XG4gICAgRmxhZ1tcIkVzdG9uaWFcIl0gPSBcImVlXCI7XG4gICAgRmxhZ1tcIkV0aGlvcGlhXCJdID0gXCJldFwiO1xuICAgIEZsYWdbXCJGaW5sYW5kXCJdID0gXCJmaVwiO1xuICAgIEZsYWdbXCJGaWppXCJdID0gXCJmalwiO1xuICAgIEZsYWdbXCJGcmFuY2VcIl0gPSBcImZyXCI7XG4gICAgRmxhZ1tcIk1pY3JvbmVzaWFGZWRlcmF0ZWRTdGF0ZXNPZlwiXSA9IFwiZm1cIjtcbiAgICBGbGFnW1wiR2Fib25cIl0gPSBcImdhXCI7XG4gICAgRmxhZ1tcIlVuaXRlZEtpbmdkb21cIl0gPSBcImdiXCI7XG4gICAgRmxhZ1tcIkdlb3JnaWFcIl0gPSBcImdlXCI7XG4gICAgRmxhZ1tcIkdoYW5hXCJdID0gXCJnaFwiO1xuICAgIEZsYWdbXCJHdWluZWFcIl0gPSBcImduXCI7XG4gICAgRmxhZ1tcIkdhbWJpYVwiXSA9IFwiZ21cIjtcbiAgICBGbGFnW1wiR3VpbmVhQmlzc2F1XCJdID0gXCJnd1wiO1xuICAgIEZsYWdbXCJFcXVhdG9yaWFsR3VpbmVhXCJdID0gXCJncVwiO1xuICAgIEZsYWdbXCJHcmVlY2VcIl0gPSBcImdyXCI7XG4gICAgRmxhZ1tcIkdyZW5hZGFcIl0gPSBcImdkXCI7XG4gICAgRmxhZ1tcIkd1YXRlbWFsYVwiXSA9IFwiZ3RcIjtcbiAgICBGbGFnW1wiR3V5YW5hXCJdID0gXCJneVwiO1xuICAgIEZsYWdbXCJIb25kdXJhc1wiXSA9IFwiaG5cIjtcbiAgICBGbGFnW1wiQ3JvYXRpYVwiXSA9IFwiaHJcIjtcbiAgICBGbGFnW1wiSGFpdGlcIl0gPSBcImh0XCI7XG4gICAgRmxhZ1tcIkh1bmdhcnlcIl0gPSBcImh1XCI7XG4gICAgRmxhZ1tcIkluZG9uZXNpYVwiXSA9IFwiaWRcIjtcbiAgICBGbGFnW1wiSW5kaWFcIl0gPSBcImluXCI7XG4gICAgRmxhZ1tcIklyZWxhbmRcIl0gPSBcImllXCI7XG4gICAgRmxhZ1tcIklyYW5Jc2xhbWljUmVwdWJsaWNPZlwiXSA9IFwiaXJcIjtcbiAgICBGbGFnW1wiSXJhcVwiXSA9IFwiaXFcIjtcbiAgICBGbGFnW1wiSWNlbGFuZFwiXSA9IFwiaXNcIjtcbiAgICBGbGFnW1wiSXNyYWVsXCJdID0gXCJpbFwiO1xuICAgIEZsYWdbXCJJdGFseVwiXSA9IFwiaXRcIjtcbiAgICBGbGFnW1wiSmFtYWljYVwiXSA9IFwiam1cIjtcbiAgICBGbGFnW1wiSm9yZGFuXCJdID0gXCJqb1wiO1xuICAgIEZsYWdbXCJKYXBhblwiXSA9IFwianBcIjtcbiAgICBGbGFnW1wiS2F6YWtoc3RhblwiXSA9IFwia3pcIjtcbiAgICBGbGFnW1wiS2VueWFcIl0gPSBcImtlXCI7XG4gICAgRmxhZ1tcIkt5cmd5enN0YW5cIl0gPSBcImtnXCI7XG4gICAgRmxhZ1tcIkNhbWJvZGlhXCJdID0gXCJraFwiO1xuICAgIEZsYWdbXCJLaXJpYmF0aVwiXSA9IFwia2lcIjtcbiAgICBGbGFnW1wiU2FpbnRLaXR0c0FuZE5ldmlzXCJdID0gXCJrblwiO1xuICAgIEZsYWdbXCJTb3V0aEtvcmVhXCJdID0gXCJrclwiO1xuICAgIEZsYWdbXCJLdXdhaXRcIl0gPSBcImt3XCI7XG4gICAgRmxhZ1tcIkxhb1Blb3BsZVNEZW1vY3JhdGljUmVwdWJsaWNcIl0gPSBcImxhXCI7XG4gICAgRmxhZ1tcIkxlYmFub25cIl0gPSBcImxiXCI7XG4gICAgRmxhZ1tcIkxpYmVyaWFcIl0gPSBcImxyXCI7XG4gICAgRmxhZ1tcIkxpYnlhXCJdID0gXCJseVwiO1xuICAgIEZsYWdbXCJTYWludEx1Y2lhXCJdID0gXCJsY1wiO1xuICAgIEZsYWdbXCJMaWVjaHRlbnN0ZWluXCJdID0gXCJsaVwiO1xuICAgIEZsYWdbXCJTcmlMYW5rYVwiXSA9IFwibGtcIjtcbiAgICBGbGFnW1wiTGVzb3Rob1wiXSA9IFwibHNcIjtcbiAgICBGbGFnW1wiTGl0aHVhbmlhXCJdID0gXCJsdFwiO1xuICAgIEZsYWdbXCJMdXhlbWJvdXJnXCJdID0gXCJsdVwiO1xuICAgIEZsYWdbXCJMYXR2aWFcIl0gPSBcImx2XCI7XG4gICAgRmxhZ1tcIk1vcm9jY29cIl0gPSBcIm1hXCI7XG4gICAgRmxhZ1tcIk1vbmFjb1wiXSA9IFwibWNcIjtcbiAgICBGbGFnW1wiTW9sZG92YVwiXSA9IFwibWRcIjtcbiAgICBGbGFnW1wiTWFkYWdhc2NhclwiXSA9IFwibWdcIjtcbiAgICBGbGFnW1wiTWFsZGl2ZXNcIl0gPSBcIm12XCI7XG4gICAgRmxhZ1tcIk1leGljb1wiXSA9IFwibXhcIjtcbiAgICBGbGFnW1wiTWFyc2hhbGxJc2xhbmRzXCJdID0gXCJtaFwiO1xuICAgIEZsYWdbXCJOb3J0aE1hY2Vkb25pYVwiXSA9IFwibWtcIjtcbiAgICBGbGFnW1wiTWFsaVwiXSA9IFwibWxcIjtcbiAgICBGbGFnW1wiTWFsdGFcIl0gPSBcIm10XCI7XG4gICAgRmxhZ1tcIk15YW5tYXJcIl0gPSBcIm1tXCI7XG4gICAgRmxhZ1tcIk1vbnRlbmVncm9cIl0gPSBcIm1lXCI7XG4gICAgRmxhZ1tcIk1vbmdvbGlhXCJdID0gXCJtblwiO1xuICAgIEZsYWdbXCJNb3phbWJpcXVlXCJdID0gXCJtelwiO1xuICAgIEZsYWdbXCJNYXVyaXRhbmlhXCJdID0gXCJtclwiO1xuICAgIEZsYWdbXCJNYXVyaXRpdXNcIl0gPSBcIm11XCI7XG4gICAgRmxhZ1tcIk1hbGF3aVwiXSA9IFwibXdcIjtcbiAgICBGbGFnW1wiTWFsYXlzaWFcIl0gPSBcIm15XCI7XG4gICAgRmxhZ1tcIk5hbWliaWFcIl0gPSBcIm5hXCI7XG4gICAgRmxhZ1tcIk5pZ2VyXCJdID0gXCJuZVwiO1xuICAgIEZsYWdbXCJOaWdlcmlhXCJdID0gXCJuZ1wiO1xuICAgIEZsYWdbXCJOaWNhcmFndWFcIl0gPSBcIm5pXCI7XG4gICAgRmxhZ1tcIk5ldGhlcmxhbmRzXCJdID0gXCJubFwiO1xuICAgIEZsYWdbXCJOb3J3YXlcIl0gPSBcIm5vXCI7XG4gICAgRmxhZ1tcIk5lcGFsXCJdID0gXCJucFwiO1xuICAgIEZsYWdbXCJOYXVydVwiXSA9IFwibnJcIjtcbiAgICBGbGFnW1wiTmV3WmVhbGFuZFwiXSA9IFwibnpcIjtcbiAgICBGbGFnW1wiT21hblwiXSA9IFwib21cIjtcbiAgICBGbGFnW1wiUGFraXN0YW5cIl0gPSBcInBrXCI7XG4gICAgRmxhZ1tcIlBhbmFtYVwiXSA9IFwicGFcIjtcbiAgICBGbGFnW1wiUGVydVwiXSA9IFwicGVcIjtcbiAgICBGbGFnW1wiUGhpbGlwcGluZXNcIl0gPSBcInBoXCI7XG4gICAgRmxhZ1tcIlBhbGF1XCJdID0gXCJwd1wiO1xuICAgIEZsYWdbXCJQYXB1YU5ld0d1aW5lYVwiXSA9IFwicGdcIjtcbiAgICBGbGFnW1wiUG9sYW5kXCJdID0gXCJwbFwiO1xuICAgIEZsYWdbXCJGcmVuY2hQb2x5bmVzaWFcIl0gPSBcInBmXCI7XG4gICAgRmxhZ1tcIk5vcnRoS29yZWFcIl0gPSBcImtwXCI7XG4gICAgRmxhZ1tcIlBvcnR1Z2FsXCJdID0gXCJwdFwiO1xuICAgIEZsYWdbXCJQYXJhZ3VheVwiXSA9IFwicHlcIjtcbiAgICBGbGFnW1wiUWF0YXJcIl0gPSBcInFhXCI7XG4gICAgRmxhZ1tcIlJvbWFuaWFcIl0gPSBcInJvXCI7XG4gICAgRmxhZ1tcIlJ1c3NpYVwiXSA9IFwicnVcIjtcbiAgICBGbGFnW1wiUndhbmRhXCJdID0gXCJyd1wiO1xuICAgIEZsYWdbXCJTYXVkaUFyYWJpYVwiXSA9IFwic2FcIjtcbiAgICBGbGFnW1wiU3VkYW5cIl0gPSBcInNkXCI7XG4gICAgRmxhZ1tcIlNlbmVnYWxcIl0gPSBcInNuXCI7XG4gICAgRmxhZ1tcIlNpbmdhcG9yZVwiXSA9IFwic2dcIjtcbiAgICBGbGFnW1wiU29sb21vbklzbGFuZHNcIl0gPSBcInNiXCI7XG4gICAgRmxhZ1tcIlNpZXJyYUxlb25lXCJdID0gXCJzbFwiO1xuICAgIEZsYWdbXCJFbFNhbHZhZG9yXCJdID0gXCJzdlwiO1xuICAgIEZsYWdbXCJTYW5NYXJpbm9cIl0gPSBcInNtXCI7XG4gICAgRmxhZ1tcIlNvbWFsaWFcIl0gPSBcInNvXCI7XG4gICAgRmxhZ1tcIlNlcmJpYVwiXSA9IFwicnNcIjtcbiAgICBGbGFnW1wiU291dGhTdWRhblwiXSA9IFwic3NcIjtcbiAgICBGbGFnW1wiU2FvVG9tZUFuZFByaW5jaXBlXCJdID0gXCJzdFwiO1xuICAgIEZsYWdbXCJTdXJpbmFtZVwiXSA9IFwic3JcIjtcbiAgICBGbGFnW1wiU2xvdmFraWFcIl0gPSBcInNrXCI7XG4gICAgRmxhZ1tcIlNsb3ZlbmlhXCJdID0gXCJzaVwiO1xuICAgIEZsYWdbXCJTd2VkZW5cIl0gPSBcInNlXCI7XG4gICAgRmxhZ1tcIkVzd2F0aW5pXCJdID0gXCJzelwiO1xuICAgIEZsYWdbXCJTZXljaGVsbGVzXCJdID0gXCJzY1wiO1xuICAgIEZsYWdbXCJTeXJpYVwiXSA9IFwic3lcIjtcbiAgICBGbGFnW1wiQ2hhZFwiXSA9IFwidGRcIjtcbiAgICBGbGFnW1wiVG9nb1wiXSA9IFwidGdcIjtcbiAgICBGbGFnW1wiVGhhaWxhbmRcIl0gPSBcInRoXCI7XG4gICAgRmxhZ1tcIlRhamlraXN0YW5cIl0gPSBcInRqXCI7XG4gICAgRmxhZ1tcIlR1cmttZW5pc3RhblwiXSA9IFwidG1cIjtcbiAgICBGbGFnW1wiVGltb3JMZXN0ZVwiXSA9IFwidGxcIjtcbiAgICBGbGFnW1wiVG9uZ2FcIl0gPSBcInRvXCI7XG4gICAgRmxhZ1tcIlRyaW5pZGFkQW5kVG9iYWdvXCJdID0gXCJ0dFwiO1xuICAgIEZsYWdbXCJUdW5pc2lhXCJdID0gXCJ0blwiO1xuICAgIEZsYWdbXCJUdXJrZXlcIl0gPSBcInRyXCI7XG4gICAgRmxhZ1tcIlR1dmFsdVwiXSA9IFwidHZcIjtcbiAgICBGbGFnW1wiVGFuemFuaWFcIl0gPSBcInR6XCI7XG4gICAgRmxhZ1tcIlVnYW5kYVwiXSA9IFwidWdcIjtcbiAgICBGbGFnW1wiVWtyYWluZVwiXSA9IFwidWFcIjtcbiAgICBGbGFnW1wiVXJ1Z3VheVwiXSA9IFwidXlcIjtcbiAgICBGbGFnW1wiVW5pdGVkU3RhdGVzXCJdID0gXCJ1c1wiO1xuICAgIEZsYWdbXCJVemJla2lzdGFuXCJdID0gXCJ1elwiO1xuICAgIEZsYWdbXCJWYXRpY2FuQ2l0eVwiXSA9IFwidmFcIjtcbiAgICBGbGFnW1wiU2FpbnRWaW5jZW50QW5kVGhlR3JlbmFkaW5lc1wiXSA9IFwidmNcIjtcbiAgICBGbGFnW1wiVmVuZXp1ZWxhXCJdID0gXCJ2ZVwiO1xuICAgIEZsYWdbXCJWaWV0bmFtXCJdID0gXCJ2blwiO1xuICAgIEZsYWdbXCJWYW51YXR1XCJdID0gXCJ2dVwiO1xuICAgIEZsYWdbXCJTYW1vYVwiXSA9IFwid3NcIjtcbiAgICBGbGFnW1wiWWVtZW5cIl0gPSBcInllXCI7XG4gICAgRmxhZ1tcIlNvdXRoQWZyaWNhXCJdID0gXCJ6YVwiO1xuICAgIEZsYWdbXCJaYW1iaWFcIl0gPSBcInptXCI7XG4gICAgRmxhZ1tcIlppbWJhYndlXCJdID0gXCJ6d1wiO1xufSkoRmxhZyB8fCAoRmxhZyA9IHt9KSk7XG5cbnZhciBFeGVjdXRpb25NZXRob2Q7XG4oZnVuY3Rpb24gKEV4ZWN1dGlvbk1ldGhvZCkge1xuICAgIEV4ZWN1dGlvbk1ldGhvZFtcIkdFVFwiXSA9IFwiR0VUXCI7XG4gICAgRXhlY3V0aW9uTWV0aG9kW1wiUE9TVFwiXSA9IFwiUE9TVFwiO1xuICAgIEV4ZWN1dGlvbk1ldGhvZFtcIlBVVFwiXSA9IFwiUFVUXCI7XG4gICAgRXhlY3V0aW9uTWV0aG9kW1wiUEFUQ0hcIl0gPSBcIlBBVENIXCI7XG4gICAgRXhlY3V0aW9uTWV0aG9kW1wiREVMRVRFXCJdID0gXCJERUxFVEVcIjtcbiAgICBFeGVjdXRpb25NZXRob2RbXCJPUFRJT05TXCJdID0gXCJPUFRJT05TXCI7XG59KShFeGVjdXRpb25NZXRob2QgfHwgKEV4ZWN1dGlvbk1ldGhvZCA9IHt9KSk7XG5cbnZhciBJbWFnZUdyYXZpdHk7XG4oZnVuY3Rpb24gKEltYWdlR3Jhdml0eSkge1xuICAgIEltYWdlR3Jhdml0eVtcIkNlbnRlclwiXSA9IFwiY2VudGVyXCI7XG4gICAgSW1hZ2VHcmF2aXR5W1wiVG9wbGVmdFwiXSA9IFwidG9wLWxlZnRcIjtcbiAgICBJbWFnZUdyYXZpdHlbXCJUb3BcIl0gPSBcInRvcFwiO1xuICAgIEltYWdlR3Jhdml0eVtcIlRvcHJpZ2h0XCJdID0gXCJ0b3AtcmlnaHRcIjtcbiAgICBJbWFnZUdyYXZpdHlbXCJMZWZ0XCJdID0gXCJsZWZ0XCI7XG4gICAgSW1hZ2VHcmF2aXR5W1wiUmlnaHRcIl0gPSBcInJpZ2h0XCI7XG4gICAgSW1hZ2VHcmF2aXR5W1wiQm90dG9tbGVmdFwiXSA9IFwiYm90dG9tLWxlZnRcIjtcbiAgICBJbWFnZUdyYXZpdHlbXCJCb3R0b21cIl0gPSBcImJvdHRvbVwiO1xuICAgIEltYWdlR3Jhdml0eVtcIkJvdHRvbXJpZ2h0XCJdID0gXCJib3R0b20tcmlnaHRcIjtcbn0pKEltYWdlR3Jhdml0eSB8fCAoSW1hZ2VHcmF2aXR5ID0ge30pKTtcblxudmFyIEltYWdlRm9ybWF0O1xuKGZ1bmN0aW9uIChJbWFnZUZvcm1hdCkge1xuICAgIEltYWdlRm9ybWF0W1wiSnBnXCJdID0gXCJqcGdcIjtcbiAgICBJbWFnZUZvcm1hdFtcIkpwZWdcIl0gPSBcImpwZWdcIjtcbiAgICBJbWFnZUZvcm1hdFtcIlBuZ1wiXSA9IFwicG5nXCI7XG4gICAgSW1hZ2VGb3JtYXRbXCJXZWJwXCJdID0gXCJ3ZWJwXCI7XG4gICAgSW1hZ2VGb3JtYXRbXCJIZWljXCJdID0gXCJoZWljXCI7XG4gICAgSW1hZ2VGb3JtYXRbXCJBdmlmXCJdID0gXCJhdmlmXCI7XG4gICAgSW1hZ2VGb3JtYXRbXCJHaWZcIl0gPSBcImdpZlwiO1xufSkoSW1hZ2VGb3JtYXQgfHwgKEltYWdlRm9ybWF0ID0ge30pKTtcblxuZXhwb3J0IHsgQWNjb3VudCwgQXBwd3JpdGVFeGNlcHRpb24sIEF1dGhlbnRpY2F0aW9uRmFjdG9yLCBBdXRoZW50aWNhdG9yVHlwZSwgQXZhdGFycywgQnJvd3NlciwgQ2xpZW50LCBDcmVkaXRDYXJkLCBEYXRhYmFzZXMsIEV4ZWN1dGlvbk1ldGhvZCwgRmxhZywgRnVuY3Rpb25zLCBHcmFwaHFsLCBJRCwgSW1hZ2VGb3JtYXQsIEltYWdlR3Jhdml0eSwgTG9jYWxlLCBNZXNzYWdpbmcsIE9BdXRoUHJvdmlkZXIsIFBlcm1pc3Npb24sIFF1ZXJ5LCBSb2xlLCBTdG9yYWdlLCBUZWFtcyB9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9c2RrLmpzLm1hcFxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/appwrite/dist/esm/sdk.js\n");

/***/ })

};
;