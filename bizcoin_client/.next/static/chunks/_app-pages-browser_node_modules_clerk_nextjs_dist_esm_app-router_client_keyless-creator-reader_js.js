"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_clerk_nextjs_dist_esm_app-router_client_keyless-creator-reader_js"],{

/***/ "(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/app-router/client/keyless-creator-reader.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/@clerk/nextjs/dist/esm/app-router/client/keyless-creator-reader.js ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   KeylessCreatorOrReader: () => (/* binding */ KeylessCreatorOrReader)\n/* harmony export */ });\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _keyless_actions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../keyless-actions */ \"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js\");\n\n\n\n\nconst KeylessCreatorOrReader = (props) => {\n  var _a;\n  const { children } = props;\n  const segments = (0,next_navigation__WEBPACK_IMPORTED_MODULE_0__.useSelectedLayoutSegments)();\n  const isNotFoundRoute = ((_a = segments[0]) == null ? void 0 : _a.startsWith(\"/_not-found\")) || false;\n  const [state, fetchKeys] = react__WEBPACK_IMPORTED_MODULE_1___default().useActionState(_keyless_actions__WEBPACK_IMPORTED_MODULE_2__.createOrReadKeylessAction, null);\n  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(() => {\n    if (isNotFoundRoute) {\n      return;\n    }\n    react__WEBPACK_IMPORTED_MODULE_1___default().startTransition(() => {\n      fetchKeys();\n    });\n  }, [isNotFoundRoute]);\n  if (!react__WEBPACK_IMPORTED_MODULE_1___default().isValidElement(children)) {\n    return children;\n  }\n  return react__WEBPACK_IMPORTED_MODULE_1___default().cloneElement(children, {\n    key: state == null ? void 0 : state.publishableKey,\n    publishableKey: state == null ? void 0 : state.publishableKey,\n    __internal_keyless_claimKeylessApplicationUrl: state == null ? void 0 : state.claimUrl,\n    __internal_keyless_copyInstanceKeysUrl: state == null ? void 0 : state.apiKeysUrl,\n    __internal_bypassMissingPublishableKey: true\n  });\n};\n\n//# sourceMappingURL=keyless-creator-reader.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/app-router/client/keyless-creator-reader.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js":
/*!***************************************************************************!*\
  !*** ./node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createOrReadKeylessAction: () => (/* binding */ createOrReadKeylessAction),\n/* harmony export */   deleteKeylessAction: () => (/* binding */ deleteKeylessAction),\n/* harmony export */   syncKeylessConfigAction: () => (/* binding */ syncKeylessConfigAction)\n/* harmony export */ });\n/* harmony import */ var private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! private-next-rsc-action-client-wrapper */ \"(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js\");\n/* harmony import */ var private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_action_entry_do_not_use__ {\"7f60a263ae0b229e4b03c804bb0c99ddda96d2f0c1\":\"syncKeylessConfigAction\",\"7f7754a9187cbbce14269254d2fcab22c8c0e3a0a4\":\"deleteKeylessAction\",\"7fd7b8c15edccfd9f0d2fc0583123153191cdae9a3\":\"createOrReadKeylessAction\"} */ \nvar createOrReadKeylessAction = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"7fd7b8c15edccfd9f0d2fc0583123153191cdae9a3\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"createOrReadKeylessAction\");\nvar deleteKeylessAction = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"7f7754a9187cbbce14269254d2fcab22c8c0e3a0a4\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"deleteKeylessAction\");\nvar syncKeylessConfigAction = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"7f60a263ae0b229e4b03c804bb0c99ddda96d2f0c1\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"syncKeylessConfigAction\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js\n"));

/***/ })

}]);