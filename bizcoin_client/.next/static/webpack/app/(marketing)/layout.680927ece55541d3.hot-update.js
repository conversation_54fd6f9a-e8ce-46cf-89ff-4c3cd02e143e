"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(marketing)/layout",{

/***/ "(app-pages-browser)/./components/AppwriteEmailVerificationModal.tsx":
/*!*******************************************************!*\
  !*** ./components/AppwriteEmailVerificationModal.tsx ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AppwriteEmailVerificationModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _contexts_appwrite_auth_context__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/contexts/appwrite-auth-context */ \"(app-pages-browser)/./contexts/appwrite-auth-context.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction AppwriteEmailVerificationModal(param) {\n    let { isOpen, onClose, onSuccess } = param;\n    _s();\n    const { login, register, loginWithEmailCode, verifyEmailCode, loading } = (0,_contexts_appwrite_auth_context__WEBPACK_IMPORTED_MODULE_7__.useAuth)();\n    const [selectedTab, setSelectedTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"signin\");\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [name, setName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [otp, setOtp] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        \"\",\n        \"\",\n        \"\",\n        \"\",\n        \"\",\n        \"\"\n    ]);\n    const [isOtpSent, setIsOtpSent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [emailCodeData, setEmailCodeData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const handleInputChange = (index, value)=>{\n        if (value.length <= 1) {\n            const newOtp = [\n                ...otp\n            ];\n            newOtp[index] = value;\n            setOtp(newOtp);\n            // Auto-focus next input\n            if (value && index < 5) {\n                const nextInput = document.getElementById(\"otp-\".concat(index + 1));\n                nextInput === null || nextInput === void 0 ? void 0 : nextInput.focus();\n            }\n        }\n    };\n    const handleKeyDown = (index, e)=>{\n        if (e.key === \"Backspace\" && !otp[index] && index > 0) {\n            const prevInput = document.getElementById(\"otp-\".concat(index - 1));\n            prevInput === null || prevInput === void 0 ? void 0 : prevInput.focus();\n        }\n    };\n    const handleSendOtp = async (e)=>{\n        e.preventDefault();\n        setError(\"\");\n        try {\n            if (selectedTab === \"signup\") {\n                // For signup, create account with password\n                await register(email, password, name);\n                sonner__WEBPACK_IMPORTED_MODULE_8__.toast.success(\"Account created successfully! Please check your email for verification.\");\n                onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess();\n                onClose();\n            } else {\n                // For signin, try password first, then email code if needed\n                try {\n                    await login(email, password);\n                    sonner__WEBPACK_IMPORTED_MODULE_8__.toast.success(\"Logged in successfully!\");\n                    onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess();\n                    onClose();\n                } catch (loginError) {\n                    // If password login fails, try email code\n                    console.log(\"Password login failed, trying email code...\");\n                    const tokenData = await loginWithEmailCode(email);\n                    setEmailCodeData(tokenData);\n                    setIsOtpSent(true);\n                    sonner__WEBPACK_IMPORTED_MODULE_8__.toast.success(\"Verification code sent to your email!\");\n                }\n            }\n        } catch (err) {\n            console.error(\"Error:\", err);\n            setError(err.message || \"An error occurred. Please try again.\");\n        }\n    };\n    const handleVerifyOtp = async (e)=>{\n        e.preventDefault();\n        setError(\"\");\n        if (!emailCodeData) {\n            setError(\"No verification data available. Please try again.\");\n            return;\n        }\n        const otpString = otp.join(\"\");\n        try {\n            await verifyEmailCode(emailCodeData.userId, otpString);\n            sonner__WEBPACK_IMPORTED_MODULE_8__.toast.success(\"Logged in successfully!\");\n            onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess();\n            onClose();\n        } catch (err) {\n            console.error(\"Verification error:\", err);\n            setError(err.message || \"Invalid verification code. Please try again.\");\n        }\n    };\n    const resetForm = ()=>{\n        setEmail(\"\");\n        setPassword(\"\");\n        setName(\"\");\n        setOtp([\n            \"\",\n            \"\",\n            \"\",\n            \"\",\n            \"\",\n            \"\"\n        ]);\n        setIsOtpSent(false);\n        setError(\"\");\n        setEmailCodeData(null);\n    };\n    const handleTabChange = (value)=>{\n        setSelectedTab(value);\n        resetForm();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.Dialog, {\n        open: isOpen,\n        onOpenChange: onClose,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogContent, {\n            className: \"sm:max-w-md\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogHeader, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogTitle, {\n                            children: \"Welcome to Bizcoin\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/AppwriteEmailVerificationModal.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogDescription, {\n                            children: isOtpSent ? \"Enter the verification code sent to your email\" : \"Sign in to your account or create a new one\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/AppwriteEmailVerificationModal.tsx\",\n                            lineNumber: 136,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/AppwriteEmailVerificationModal.tsx\",\n                    lineNumber: 134,\n                    columnNumber: 9\n                }, this),\n                !isOtpSent ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.Tabs, {\n                    value: selectedTab,\n                    onValueChange: handleTabChange,\n                    className: \"w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsList, {\n                            className: \"grid w-full grid-cols-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                    value: \"signin\",\n                                    children: \"Sign In\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/AppwriteEmailVerificationModal.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                    value: \"signup\",\n                                    children: \"Sign Up\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/AppwriteEmailVerificationModal.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/AppwriteEmailVerificationModal.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsContent, {\n                            value: \"signin\",\n                            className: \"space-y-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSendOtp,\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                htmlFor: \"email\",\n                                                children: \"Email\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/AppwriteEmailVerificationModal.tsx\",\n                                                lineNumber: 153,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                id: \"email\",\n                                                type: \"email\",\n                                                placeholder: \"Enter your email\",\n                                                value: email,\n                                                onChange: (e)=>setEmail(e.target.value),\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/AppwriteEmailVerificationModal.tsx\",\n                                                lineNumber: 154,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/AppwriteEmailVerificationModal.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                htmlFor: \"password\",\n                                                children: \"Password\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/AppwriteEmailVerificationModal.tsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                id: \"password\",\n                                                type: \"password\",\n                                                placeholder: \"Enter your password\",\n                                                value: password,\n                                                onChange: (e)=>setPassword(e.target.value),\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/AppwriteEmailVerificationModal.tsx\",\n                                                lineNumber: 165,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/AppwriteEmailVerificationModal.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 17\n                                    }, this),\n                                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-red-600\",\n                                        children: error\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/AppwriteEmailVerificationModal.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 27\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        type: \"submit\",\n                                        className: \"w-full\",\n                                        disabled: loading,\n                                        children: loading ? \"Signing In...\" : \"Sign In\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/AppwriteEmailVerificationModal.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/AppwriteEmailVerificationModal.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/AppwriteEmailVerificationModal.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsContent, {\n                            value: \"signup\",\n                            className: \"space-y-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSendOtp,\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                htmlFor: \"name\",\n                                                children: \"Full Name\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/AppwriteEmailVerificationModal.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                id: \"name\",\n                                                type: \"text\",\n                                                placeholder: \"Enter your full name\",\n                                                value: name,\n                                                onChange: (e)=>setName(e.target.value),\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/AppwriteEmailVerificationModal.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/AppwriteEmailVerificationModal.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                htmlFor: \"signup-email\",\n                                                children: \"Email\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/AppwriteEmailVerificationModal.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                id: \"signup-email\",\n                                                type: \"email\",\n                                                placeholder: \"Enter your email\",\n                                                value: email,\n                                                onChange: (e)=>setEmail(e.target.value),\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/AppwriteEmailVerificationModal.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/AppwriteEmailVerificationModal.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                htmlFor: \"signup-password\",\n                                                children: \"Password\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/AppwriteEmailVerificationModal.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                id: \"signup-password\",\n                                                type: \"password\",\n                                                placeholder: \"Create a password\",\n                                                value: password,\n                                                onChange: (e)=>setPassword(e.target.value),\n                                                required: true,\n                                                minLength: 8\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/AppwriteEmailVerificationModal.tsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/AppwriteEmailVerificationModal.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 17\n                                    }, this),\n                                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-red-600\",\n                                        children: error\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/AppwriteEmailVerificationModal.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 27\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        type: \"submit\",\n                                        className: \"w-full\",\n                                        disabled: loading,\n                                        children: loading ? \"Creating Account...\" : \"Create Account\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/AppwriteEmailVerificationModal.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/AppwriteEmailVerificationModal.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/AppwriteEmailVerificationModal.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/AppwriteEmailVerificationModal.tsx\",\n                    lineNumber: 144,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleVerifyOtp,\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                    children: \"Enter Verification Code\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/AppwriteEmailVerificationModal.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center space-x-2\",\n                                    children: otp.map((digit, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                            id: \"otp-\".concat(index),\n                                            type: \"text\",\n                                            inputMode: \"numeric\",\n                                            maxLength: 1,\n                                            value: digit,\n                                            onChange: (e)=>handleInputChange(index, e.target.value),\n                                            onKeyDown: (e)=>handleKeyDown(index, e),\n                                            className: \"w-12 h-12 text-center text-lg font-semibold\"\n                                        }, index, false, {\n                                            fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/AppwriteEmailVerificationModal.tsx\",\n                                            lineNumber: 230,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/AppwriteEmailVerificationModal.tsx\",\n                                    lineNumber: 228,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/AppwriteEmailVerificationModal.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 13\n                        }, this),\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-red-600\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/AppwriteEmailVerificationModal.tsx\",\n                            lineNumber: 244,\n                            columnNumber: 23\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    type: \"button\",\n                                    variant: \"outline\",\n                                    onClick: ()=>setIsOtpSent(false),\n                                    className: \"flex-1\",\n                                    children: \"Back\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/AppwriteEmailVerificationModal.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    type: \"submit\",\n                                    className: \"flex-1\",\n                                    disabled: loading,\n                                    children: loading ? \"Verifying...\" : \"Verify\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/AppwriteEmailVerificationModal.tsx\",\n                                    lineNumber: 254,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/AppwriteEmailVerificationModal.tsx\",\n                            lineNumber: 245,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/AppwriteEmailVerificationModal.tsx\",\n                    lineNumber: 225,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/AppwriteEmailVerificationModal.tsx\",\n            lineNumber: 133,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/AppwriteEmailVerificationModal.tsx\",\n        lineNumber: 132,\n        columnNumber: 5\n    }, this);\n}\n_s(AppwriteEmailVerificationModal, \"Td5B1/uewrCn8zxgylHKALewARs=\", false, function() {\n    return [\n        _contexts_appwrite_auth_context__WEBPACK_IMPORTED_MODULE_7__.useAuth\n    ];\n});\n_c = AppwriteEmailVerificationModal;\nvar _c;\n$RefreshReg$(_c, \"AppwriteEmailVerificationModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/AppwriteEmailVerificationModal.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/navigation.tsx":
/*!***********************************!*\
  !*** ./components/navigation.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Navigation: () => (/* binding */ Navigation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_theme_switcher__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/theme-switcher */ \"(app-pages-browser)/./components/theme-switcher.tsx\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _contexts_appwrite_auth_context__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/contexts/appwrite-auth-context */ \"(app-pages-browser)/./contexts/appwrite-auth-context.tsx\");\n/* harmony import */ var _AppwriteEmailVerificationModal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./AppwriteEmailVerificationModal */ \"(app-pages-browser)/./components/AppwriteEmailVerificationModal.tsx\");\n/* __next_internal_client_entry_do_not_use__ Navigation auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction Navigation() {\n    _s();\n    const [isEmailVerificationOpen, setIsEmailVerificationOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { user, logout } = (0,_contexts_appwrite_auth_context__WEBPACK_IMPORTED_MODULE_6__.useAuth)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \" backdrop-blur-sm sticky top-0 z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between h-16\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/\",\n                        className: \"flex items-center space-x-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                            whileHover: {\n                                scale: 1.05\n                            },\n                            whileTap: {\n                                scale: 0.95\n                            },\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    src: \"/logo.png\",\n                                    alt: \"DexTrip Logo\",\n                                    width: 30,\n                                    height: 30\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/navigation.tsx\",\n                                    lineNumber: 27,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-lg sm:text-xl font-bold text-primary\",\n                                    children: \"DEXTRIP\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/navigation.tsx\",\n                                    lineNumber: 33,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/navigation.tsx\",\n                            lineNumber: 22,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/navigation.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_theme_switcher__WEBPACK_IMPORTED_MODULE_4__.ThemeSwitcher, {}, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/navigation.tsx\",\n                                lineNumber: 42,\n                                columnNumber: 13\n                            }, this),\n                            !user ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: ()=>setIsEmailVerificationOpen(true),\n                                        className: \"bg-primary-gradient hover:opacity-90 text-white border-0 hover:shadow-xl transition-all duration-300\",\n                                        children: \"Join Waitlist\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/navigation.tsx\",\n                                        lineNumber: 45,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AppwriteEmailVerificationModal__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        isOpen: isEmailVerificationOpen,\n                                        onClose: ()=>setIsEmailVerificationOpen(false)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/navigation.tsx\",\n                                        lineNumber: 51,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/navigation.tsx\",\n                                lineNumber: 44,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-muted-foreground\",\n                                        children: [\n                                            \"Welcome, \",\n                                            user.name || user.email\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/navigation.tsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: logout,\n                                        className: \"bg-primary-gradient hover:opacity-90 text-white border-0 hover:shadow-xl transition-all duration-300\",\n                                        children: \"Exit DexTrip\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/navigation.tsx\",\n                                        lineNumber: 61,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/navigation.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/navigation.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/navigation.tsx\",\n                lineNumber: 19,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/navigation.tsx\",\n            lineNumber: 18,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/navigation.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n_s(Navigation, \"HU+8HqCAYs4IK79S3F57Yo78gf4=\", false, function() {\n    return [\n        _contexts_appwrite_auth_context__WEBPACK_IMPORTED_MODULE_6__.useAuth\n    ];\n});\n_c = Navigation;\nvar _c;\n$RefreshReg$(_c, \"Navigation\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2NvbXBvbmVudHMvbmF2aWdhdGlvbi50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFaUM7QUFDSjtBQUNVO0FBQ1M7QUFDWTtBQUM3QjtBQUM0QjtBQUNtQjtBQUV2RSxTQUFTUTs7SUFDZCxNQUFNLENBQUNDLHlCQUF5QkMsMkJBQTJCLEdBQUdWLCtDQUFRQSxDQUFDO0lBQ3ZFLE1BQU0sRUFBRVcsSUFBSSxFQUFFQyxNQUFNLEVBQUUsR0FBR04sd0VBQU9BO0lBRWhDLHFCQUNFLDhEQUFDTztRQUFJQyxXQUFVO2tCQUNiLDRFQUFDQztZQUFJRCxXQUFVO3NCQUNiLDRFQUFDQztnQkFBSUQsV0FBVTs7a0NBRWIsOERBQUNiLGtEQUFJQTt3QkFBQ2UsTUFBSzt3QkFBSUYsV0FBVTtrQ0FDdkIsNEVBQUNaLGlEQUFNQSxDQUFDYSxHQUFHOzRCQUNURSxZQUFZO2dDQUFFQyxPQUFPOzRCQUFLOzRCQUMxQkMsVUFBVTtnQ0FBRUQsT0FBTzs0QkFBSzs0QkFDeEJKLFdBQVU7OzhDQUVWLDhEQUFDVCxrREFBS0E7b0NBQ0plLEtBQUk7b0NBQ0pDLEtBQUk7b0NBQ0pDLE9BQU87b0NBQ1BDLFFBQVE7Ozs7Ozs4Q0FFViw4REFBQ0M7b0NBQUtWLFdBQVU7OENBQTRDOzs7Ozs7Ozs7Ozs7Ozs7OztrQ0FPaEUsOERBQUNDO3dCQUFJRCxXQUFVOzswQ0FFYiw4REFBQ1YscUVBQWFBOzs7Ozs0QkFDYixDQUFDTyxxQkFDQSw4REFBQ0k7Z0NBQUlELFdBQVU7O2tEQUNiLDhEQUFDWCx5REFBTUE7d0NBQ0xzQixTQUFTLElBQU1mLDJCQUEyQjt3Q0FDMUNJLFdBQVU7a0RBQ1g7Ozs7OztrREFHRCw4REFBQ1AsdUVBQThCQTt3Q0FDN0JtQixRQUFRakI7d0NBQ1JrQixTQUFTLElBQU1qQiwyQkFBMkI7Ozs7Ozs7Ozs7O3FEQUk5Qyw4REFBQ0s7Z0NBQUlELFdBQVU7O2tEQUNiLDhEQUFDVTt3Q0FBS1YsV0FBVTs7NENBQWdDOzRDQUNwQ0gsS0FBS2lCLElBQUksSUFBSWpCLEtBQUtrQixLQUFLOzs7Ozs7O2tEQUVuQyw4REFBQzFCLHlEQUFNQTt3Q0FDTHNCLFNBQVNiO3dDQUNURSxXQUFVO2tEQUNYOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBVWpCO0dBOURnQk47O1FBRVdGLG9FQUFPQTs7O0tBRmxCRSIsInNvdXJjZXMiOlsiL1VzZXJzL3NoYXdhei9EZXZlbG9wZXIvYml6Y29pbi9iaXpjb2luX2NsaWVudC9jb21wb25lbnRzL25hdmlnYXRpb24udHN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSB9IGZyb20gXCJyZWFjdFwiO1xuaW1wb3J0IExpbmsgZnJvbSBcIm5leHQvbGlua1wiO1xuaW1wb3J0IHsgbW90aW9uIH0gZnJvbSBcImZyYW1lci1tb3Rpb25cIjtcbmltcG9ydCB7IEJ1dHRvbiB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvYnV0dG9uXCI7XG5pbXBvcnQgeyBUaGVtZVN3aXRjaGVyIH0gZnJvbSBcIkAvY29tcG9uZW50cy90aGVtZS1zd2l0Y2hlclwiO1xuaW1wb3J0IEltYWdlIGZyb20gXCJuZXh0L2ltYWdlXCI7XG5pbXBvcnQgeyB1c2VBdXRoIH0gZnJvbSBcIkAvY29udGV4dHMvYXBwd3JpdGUtYXV0aC1jb250ZXh0XCI7XG5pbXBvcnQgQXBwd3JpdGVFbWFpbFZlcmlmaWNhdGlvbk1vZGFsIGZyb20gXCIuL0FwcHdyaXRlRW1haWxWZXJpZmljYXRpb25Nb2RhbFwiO1xuXG5leHBvcnQgZnVuY3Rpb24gTmF2aWdhdGlvbigpIHtcbiAgY29uc3QgW2lzRW1haWxWZXJpZmljYXRpb25PcGVuLCBzZXRJc0VtYWlsVmVyaWZpY2F0aW9uT3Blbl0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IHsgdXNlciwgbG9nb3V0IH0gPSB1c2VBdXRoKCk7XG5cbiAgcmV0dXJuIChcbiAgICA8bmF2IGNsYXNzTmFtZT1cIiBiYWNrZHJvcC1ibHVyLXNtIHN0aWNreSB0b3AtMCB6LTUwXCI+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbnRhaW5lciBteC1hdXRvIHB4LTZcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gaC0xNlwiPlxuICAgICAgICAgIHsvKiBMb2dvICovfVxuICAgICAgICAgIDxMaW5rIGhyZWY9XCIvXCIgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgICAgICB3aGlsZUhvdmVyPXt7IHNjYWxlOiAxLjA1IH19XG4gICAgICAgICAgICAgIHdoaWxlVGFwPXt7IHNjYWxlOiAwLjk1IH19XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxJbWFnZVxuICAgICAgICAgICAgICAgIHNyYz1cIi9sb2dvLnBuZ1wiXG4gICAgICAgICAgICAgICAgYWx0PVwiRGV4VHJpcCBMb2dvXCJcbiAgICAgICAgICAgICAgICB3aWR0aD17MzB9XG4gICAgICAgICAgICAgICAgaGVpZ2h0PXszMH1cbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1sZyBzbTp0ZXh0LXhsIGZvbnQtYm9sZCB0ZXh0LXByaW1hcnlcIj5cbiAgICAgICAgICAgICAgICBERVhUUklQXG4gICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgIDwvbW90aW9uLmRpdj5cbiAgICAgICAgICA8L0xpbms+XG5cbiAgICAgICAgICB7LyogRGVza3RvcCBOYXZpZ2F0aW9uICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC02XCI+XG4gICAgICAgICAgICB7LyogPE5ldHdvcmtTdGF0dXMgLz4gKi99XG4gICAgICAgICAgICA8VGhlbWVTd2l0Y2hlciAvPlxuICAgICAgICAgICAgeyF1c2VyID8gKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC00XCI+XG4gICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0SXNFbWFpbFZlcmlmaWNhdGlvbk9wZW4odHJ1ZSl9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1wcmltYXJ5LWdyYWRpZW50IGhvdmVyOm9wYWNpdHktOTAgdGV4dC13aGl0ZSBib3JkZXItMCBob3ZlcjpzaGFkb3cteGwgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICBKb2luIFdhaXRsaXN0XG4gICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgPEFwcHdyaXRlRW1haWxWZXJpZmljYXRpb25Nb2RhbFxuICAgICAgICAgICAgICAgICAgaXNPcGVuPXtpc0VtYWlsVmVyaWZpY2F0aW9uT3Blbn1cbiAgICAgICAgICAgICAgICAgIG9uQ2xvc2U9eygpID0+IHNldElzRW1haWxWZXJpZmljYXRpb25PcGVuKGZhbHNlKX1cbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTRcIj5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPlxuICAgICAgICAgICAgICAgICAgV2VsY29tZSwge3VzZXIubmFtZSB8fCB1c2VyLmVtYWlsfVxuICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXtsb2dvdXR9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1wcmltYXJ5LWdyYWRpZW50IGhvdmVyOm9wYWNpdHktOTAgdGV4dC13aGl0ZSBib3JkZXItMCBob3ZlcjpzaGFkb3cteGwgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICBFeGl0IERleFRyaXBcbiAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvbmF2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwiTGluayIsIm1vdGlvbiIsIkJ1dHRvbiIsIlRoZW1lU3dpdGNoZXIiLCJJbWFnZSIsInVzZUF1dGgiLCJBcHB3cml0ZUVtYWlsVmVyaWZpY2F0aW9uTW9kYWwiLCJOYXZpZ2F0aW9uIiwiaXNFbWFpbFZlcmlmaWNhdGlvbk9wZW4iLCJzZXRJc0VtYWlsVmVyaWZpY2F0aW9uT3BlbiIsInVzZXIiLCJsb2dvdXQiLCJuYXYiLCJjbGFzc05hbWUiLCJkaXYiLCJocmVmIiwid2hpbGVIb3ZlciIsInNjYWxlIiwid2hpbGVUYXAiLCJzcmMiLCJhbHQiLCJ3aWR0aCIsImhlaWdodCIsInNwYW4iLCJvbkNsaWNrIiwiaXNPcGVuIiwib25DbG9zZSIsIm5hbWUiLCJlbWFpbCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/navigation.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/ui/dialog.tsx":
/*!**********************************!*\
  !*** ./components/ui/dialog.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Dialog: () => (/* binding */ Dialog),\n/* harmony export */   DialogClose: () => (/* binding */ DialogClose),\n/* harmony export */   DialogContent: () => (/* binding */ DialogContent),\n/* harmony export */   DialogDescription: () => (/* binding */ DialogDescription),\n/* harmony export */   DialogFooter: () => (/* binding */ DialogFooter),\n/* harmony export */   DialogHeader: () => (/* binding */ DialogHeader),\n/* harmony export */   DialogOverlay: () => (/* binding */ DialogOverlay),\n/* harmony export */   DialogPortal: () => (/* binding */ DialogPortal),\n/* harmony export */   DialogTitle: () => (/* binding */ DialogTitle),\n/* harmony export */   DialogTrigger: () => (/* binding */ DialogTrigger)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-dialog */ \"(app-pages-browser)/./node_modules/@radix-ui/react-dialog/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_XIcon_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=XIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Dialog,DialogClose,DialogContent,DialogDescription,DialogFooter,DialogHeader,DialogOverlay,DialogPortal,DialogTitle,DialogTrigger auto */ \n\n\n\n\nfunction Dialog(param) {\n    let { ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        \"data-slot\": \"dialog\",\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/ui/dialog.tsx\",\n        lineNumber: 12,\n        columnNumber: 10\n    }, this);\n}\n_c = Dialog;\nfunction DialogTrigger(param) {\n    let { ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Trigger, {\n        \"data-slot\": \"dialog-trigger\",\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/ui/dialog.tsx\",\n        lineNumber: 18,\n        columnNumber: 10\n    }, this);\n}\n_c1 = DialogTrigger;\nfunction DialogPortal(param) {\n    let { ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Portal, {\n        \"data-slot\": \"dialog-portal\",\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/ui/dialog.tsx\",\n        lineNumber: 24,\n        columnNumber: 10\n    }, this);\n}\n_c2 = DialogPortal;\nfunction DialogClose(param) {\n    let { ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Close, {\n        \"data-slot\": \"dialog-close\",\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/ui/dialog.tsx\",\n        lineNumber: 30,\n        columnNumber: 10\n    }, this);\n}\n_c3 = DialogClose;\nfunction DialogOverlay(param) {\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Overlay, {\n        \"data-slot\": \"dialog-overlay\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/ui/dialog.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n_c4 = DialogOverlay;\nfunction DialogContent(param) {\n    let { className, children, showCloseButton = true, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogPortal, {\n        \"data-slot\": \"dialog-portal\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogOverlay, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/ui/dialog.tsx\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Content, {\n                \"data-slot\": \"dialog-content\",\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\", className),\n                ...props,\n                children: [\n                    children,\n                    showCloseButton && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Close, {\n                        \"data-slot\": \"dialog-close\",\n                        className: \"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_XIcon_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/ui/dialog.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"sr-only\",\n                                children: \"Close\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/ui/dialog.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/ui/dialog.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/ui/dialog.tsx\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/ui/dialog.tsx\",\n        lineNumber: 58,\n        columnNumber: 5\n    }, this);\n}\n_c5 = DialogContent;\nfunction DialogHeader(param) {\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"dialog-header\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col gap-2 text-center sm:text-left\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/ui/dialog.tsx\",\n        lineNumber: 85,\n        columnNumber: 5\n    }, this);\n}\n_c6 = DialogHeader;\nfunction DialogFooter(param) {\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"dialog-footer\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/ui/dialog.tsx\",\n        lineNumber: 95,\n        columnNumber: 5\n    }, this);\n}\n_c7 = DialogFooter;\nfunction DialogTitle(param) {\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Title, {\n        \"data-slot\": \"dialog-title\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-lg leading-none font-semibold\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/ui/dialog.tsx\",\n        lineNumber: 111,\n        columnNumber: 5\n    }, this);\n}\n_c8 = DialogTitle;\nfunction DialogDescription(param) {\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Description, {\n        \"data-slot\": \"dialog-description\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-muted-foreground text-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/ui/dialog.tsx\",\n        lineNumber: 124,\n        columnNumber: 5\n    }, this);\n}\n_c9 = DialogDescription;\n\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9;\n$RefreshReg$(_c, \"Dialog\");\n$RefreshReg$(_c1, \"DialogTrigger\");\n$RefreshReg$(_c2, \"DialogPortal\");\n$RefreshReg$(_c3, \"DialogClose\");\n$RefreshReg$(_c4, \"DialogOverlay\");\n$RefreshReg$(_c5, \"DialogContent\");\n$RefreshReg$(_c6, \"DialogHeader\");\n$RefreshReg$(_c7, \"DialogFooter\");\n$RefreshReg$(_c8, \"DialogTitle\");\n$RefreshReg$(_c9, \"DialogDescription\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/ui/dialog.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/ui/input.tsx":
/*!*********************************!*\
  !*** ./components/ui/input.tsx ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./lib/utils.ts\");\n\n\n\nconst Input = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c = (param, ref)=>{\n    let { className, type, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/ui/input.tsx\",\n        lineNumber: 9,\n        columnNumber: 7\n    }, undefined);\n});\n_c1 = Input;\nInput.displayName = \"Input\";\n\nvar _c, _c1;\n$RefreshReg$(_c, \"Input$React.forwardRef\");\n$RefreshReg$(_c1, \"Input\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2NvbXBvbmVudHMvdWkvaW5wdXQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUErQjtBQUNFO0FBSWpDLE1BQU1FLHNCQUFRRiw2Q0FBZ0IsTUFDNUIsUUFBZ0NJO1FBQS9CLEVBQUVDLFNBQVMsRUFBRUMsSUFBSSxFQUFFLEdBQUdDLE9BQU87SUFDNUIscUJBQ0UsOERBQUNDO1FBQ0NGLE1BQU1BO1FBQ05ELFdBQVdKLDhDQUFFQSxDQUNYLGdXQUNBSTtRQUVGRCxLQUFLQTtRQUNKLEdBQUdHLEtBQUs7Ozs7OztBQUdmOztBQUVGTCxNQUFNTyxXQUFXLEdBQUc7QUFFSCIsInNvdXJjZXMiOlsiL1VzZXJzL3NoYXdhei9EZXZlbG9wZXIvYml6Y29pbi9iaXpjb2luX2NsaWVudC9jb21wb25lbnRzL3VpL2lucHV0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIjtcbmltcG9ydCB7IGNuIH0gZnJvbSBcIkAvbGliL3V0aWxzXCI7XG5cbmV4cG9ydCB0eXBlIElucHV0UHJvcHMgPSBSZWFjdC5JbnB1dEhUTUxBdHRyaWJ1dGVzPEhUTUxJbnB1dEVsZW1lbnQ+O1xuXG5jb25zdCBJbnB1dCA9IFJlYWN0LmZvcndhcmRSZWY8SFRNTElucHV0RWxlbWVudCwgSW5wdXRQcm9wcz4oXG4gICh7IGNsYXNzTmFtZSwgdHlwZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxpbnB1dFxuICAgICAgICB0eXBlPXt0eXBlfVxuICAgICAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgICAgIFwiZmxleCBoLTEwIHctZnVsbCByb3VuZGVkLW1kIGJvcmRlciBib3JkZXItaW5wdXQgYmctYmFja2dyb3VuZCBweC0zIHB5LTIgdGV4dC1zbSByaW5nLW9mZnNldC1iYWNrZ3JvdW5kIGZpbGU6Ym9yZGVyLTAgZmlsZTpiZy10cmFuc3BhcmVudCBmaWxlOnRleHQtc20gZmlsZTpmb250LW1lZGl1bSBwbGFjZWhvbGRlcjp0ZXh0LW11dGVkLWZvcmVncm91bmQgZm9jdXMtdmlzaWJsZTpvdXRsaW5lLW5vbmUgZm9jdXMtdmlzaWJsZTpyaW5nLTIgZm9jdXMtdmlzaWJsZTpyaW5nLXJpbmcgZm9jdXMtdmlzaWJsZTpyaW5nLW9mZnNldC0yIGRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZCBkaXNhYmxlZDpvcGFjaXR5LTUwXCIsXG4gICAgICAgICAgY2xhc3NOYW1lLFxuICAgICAgICApfVxuICAgICAgICByZWY9e3JlZn1cbiAgICAgICAgey4uLnByb3BzfVxuICAgICAgLz5cbiAgICApO1xuICB9LFxuKTtcbklucHV0LmRpc3BsYXlOYW1lID0gXCJJbnB1dFwiO1xuXG5leHBvcnQgeyBJbnB1dCB9O1xuIl0sIm5hbWVzIjpbIlJlYWN0IiwiY24iLCJJbnB1dCIsImZvcndhcmRSZWYiLCJyZWYiLCJjbGFzc05hbWUiLCJ0eXBlIiwicHJvcHMiLCJpbnB1dCIsImRpc3BsYXlOYW1lIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/ui/input.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/ui/label.tsx":
/*!*********************************!*\
  !*** ./components/ui/label.tsx ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Label: () => (/* binding */ Label)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_label__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-label */ \"(app-pages-browser)/./node_modules/@radix-ui/react-label/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Label auto */ \n\n\n\nfunction Label(param) {\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_label__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        \"data-slot\": \"label\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/ui/label.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n_c = Label;\n\nvar _c;\n$RefreshReg$(_c, \"Label\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2NvbXBvbmVudHMvdWkvbGFiZWwudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFFOEI7QUFDeUI7QUFFdkI7QUFFaEMsU0FBU0csTUFBTSxLQUdvQztRQUhwQyxFQUNiQyxTQUFTLEVBQ1QsR0FBR0MsT0FDOEMsR0FIcEM7SUFJYixxQkFDRSw4REFBQ0osdURBQW1CO1FBQ2xCTSxhQUFVO1FBQ1ZILFdBQVdGLDhDQUFFQSxDQUNYLHVOQUNBRTtRQUVELEdBQUdDLEtBQUs7Ozs7OztBQUdmO0tBZFNGO0FBZ0JPIiwic291cmNlcyI6WyIvVXNlcnMvc2hhd2F6L0RldmVsb3Blci9iaXpjb2luL2JpemNvaW5fY2xpZW50L2NvbXBvbmVudHMvdWkvbGFiZWwudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiXG5cbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiXG5pbXBvcnQgKiBhcyBMYWJlbFByaW1pdGl2ZSBmcm9tIFwiQHJhZGl4LXVpL3JlYWN0LWxhYmVsXCJcblxuaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIlxuXG5mdW5jdGlvbiBMYWJlbCh7XG4gIGNsYXNzTmFtZSxcbiAgLi4ucHJvcHNcbn06IFJlYWN0LkNvbXBvbmVudFByb3BzPHR5cGVvZiBMYWJlbFByaW1pdGl2ZS5Sb290Pikge1xuICByZXR1cm4gKFxuICAgIDxMYWJlbFByaW1pdGl2ZS5Sb290XG4gICAgICBkYXRhLXNsb3Q9XCJsYWJlbFwiXG4gICAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgICBcImZsZXggaXRlbXMtY2VudGVyIGdhcC0yIHRleHQtc20gbGVhZGluZy1ub25lIGZvbnQtbWVkaXVtIHNlbGVjdC1ub25lIGdyb3VwLWRhdGEtW2Rpc2FibGVkPXRydWVdOnBvaW50ZXItZXZlbnRzLW5vbmUgZ3JvdXAtZGF0YS1bZGlzYWJsZWQ9dHJ1ZV06b3BhY2l0eS01MCBwZWVyLWRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZCBwZWVyLWRpc2FibGVkOm9wYWNpdHktNTBcIixcbiAgICAgICAgY2xhc3NOYW1lXG4gICAgICApfVxuICAgICAgey4uLnByb3BzfVxuICAgIC8+XG4gIClcbn1cblxuZXhwb3J0IHsgTGFiZWwgfVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiTGFiZWxQcmltaXRpdmUiLCJjbiIsIkxhYmVsIiwiY2xhc3NOYW1lIiwicHJvcHMiLCJSb290IiwiZGF0YS1zbG90Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/ui/label.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/ui/tabs.tsx":
/*!********************************!*\
  !*** ./components/ui/tabs.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Tabs: () => (/* binding */ Tabs),\n/* harmony export */   TabsContent: () => (/* binding */ TabsContent),\n/* harmony export */   TabsList: () => (/* binding */ TabsList),\n/* harmony export */   TabsTrigger: () => (/* binding */ TabsTrigger)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_tabs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-tabs */ \"(app-pages-browser)/./node_modules/@radix-ui/react-tabs/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Tabs,TabsList,TabsTrigger,TabsContent auto */ \n\n\n\nfunction Tabs(param) {\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_tabs__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        \"data-slot\": \"tabs\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col gap-2\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/ui/tabs.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n_c = Tabs;\nfunction TabsList(param) {\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_tabs__WEBPACK_IMPORTED_MODULE_3__.List, {\n        \"data-slot\": \"tabs-list\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/ui/tabs.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n_c1 = TabsList;\nfunction TabsTrigger(param) {\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_tabs__WEBPACK_IMPORTED_MODULE_3__.Trigger, {\n        \"data-slot\": \"tabs-trigger\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/ui/tabs.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, this);\n}\n_c2 = TabsTrigger;\nfunction TabsContent(param) {\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_tabs__WEBPACK_IMPORTED_MODULE_3__.Content, {\n        \"data-slot\": \"tabs-content\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex-1 outline-none\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/ui/tabs.tsx\",\n        lineNumber: 58,\n        columnNumber: 5\n    }, this);\n}\n_c3 = TabsContent;\n\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"Tabs\");\n$RefreshReg$(_c1, \"TabsList\");\n$RefreshReg$(_c2, \"TabsTrigger\");\n$RefreshReg$(_c3, \"TabsContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/ui/tabs.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@radix-ui/react-dialog/dist/index.mjs":
/*!************************************************************!*\
  !*** ./node_modules/@radix-ui/react-dialog/dist/index.mjs ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Close: () => (/* binding */ Close),\n/* harmony export */   Content: () => (/* binding */ Content),\n/* harmony export */   Description: () => (/* binding */ Description),\n/* harmony export */   Dialog: () => (/* binding */ Dialog),\n/* harmony export */   DialogClose: () => (/* binding */ DialogClose),\n/* harmony export */   DialogContent: () => (/* binding */ DialogContent),\n/* harmony export */   DialogDescription: () => (/* binding */ DialogDescription),\n/* harmony export */   DialogOverlay: () => (/* binding */ DialogOverlay),\n/* harmony export */   DialogPortal: () => (/* binding */ DialogPortal),\n/* harmony export */   DialogTitle: () => (/* binding */ DialogTitle),\n/* harmony export */   DialogTrigger: () => (/* binding */ DialogTrigger),\n/* harmony export */   Overlay: () => (/* binding */ Overlay),\n/* harmony export */   Portal: () => (/* binding */ Portal),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   Title: () => (/* binding */ Title),\n/* harmony export */   Trigger: () => (/* binding */ Trigger),\n/* harmony export */   WarningProvider: () => (/* binding */ WarningProvider),\n/* harmony export */   createDialogScope: () => (/* binding */ createDialogScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/primitive */ \"(app-pages-browser)/./node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(app-pages-browser)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(app-pages-browser)/./node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-id */ \"(app-pages-browser)/./node_modules/@radix-ui/react-id/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(app-pages-browser)/./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @radix-ui/react-dismissable-layer */ \"(app-pages-browser)/./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_focus_scope__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @radix-ui/react-focus-scope */ \"(app-pages-browser)/./node_modules/@radix-ui/react-focus-scope/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-portal */ \"(app-pages-browser)/./node_modules/@radix-ui/react-portal/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-presence */ \"(app-pages-browser)/./node_modules/@radix-ui/react-presence/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(app-pages-browser)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_focus_guards__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @radix-ui/react-focus-guards */ \"(app-pages-browser)/./node_modules/@radix-ui/react-focus-guards/dist/index.mjs\");\n/* harmony import */ var react_remove_scroll__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react-remove-scroll */ \"(app-pages-browser)/./node_modules/react-remove-scroll/dist/es2015/Combination.js\");\n/* harmony import */ var aria_hidden__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! aria-hidden */ \"(app-pages-browser)/./node_modules/aria-hidden/dist/es2015/index.js\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(app-pages-browser)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Close,Content,Description,Dialog,DialogClose,DialogContent,DialogDescription,DialogOverlay,DialogPortal,DialogTitle,DialogTrigger,Overlay,Portal,Root,Title,Trigger,WarningProvider,createDialogScope auto */ var _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$(), _s4 = $RefreshSig$(), _s5 = $RefreshSig$(), _s6 = $RefreshSig$(), _s7 = $RefreshSig$(), _s8 = $RefreshSig$(), _s9 = $RefreshSig$(), _s10 = $RefreshSig$(), _s11 = $RefreshSig$(), _s12 = $RefreshSig$(), _s13 = $RefreshSig$();\n// src/dialog.tsx\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar DIALOG_NAME = \"Dialog\";\nvar [createDialogContext, createDialogScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(DIALOG_NAME);\nvar [DialogProvider, useDialogContext] = createDialogContext(DIALOG_NAME);\nvar Dialog = (props)=>{\n    _s();\n    const { __scopeDialog, children, open: openProp, defaultOpen, onOpenChange, modal = true } = props;\n    const triggerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const contentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const [open, setOpen] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_3__.useControllableState)({\n        prop: openProp,\n        defaultProp: defaultOpen !== null && defaultOpen !== void 0 ? defaultOpen : false,\n        onChange: onOpenChange,\n        caller: DIALOG_NAME\n    });\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DialogProvider, {\n        scope: __scopeDialog,\n        triggerRef,\n        contentRef,\n        contentId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__.useId)(),\n        titleId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__.useId)(),\n        descriptionId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__.useId)(),\n        open,\n        onOpenChange: setOpen,\n        onOpenToggle: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n            \"Dialog.useCallback\": ()=>setOpen({\n                    \"Dialog.useCallback\": (prevOpen)=>!prevOpen\n                }[\"Dialog.useCallback\"])\n        }[\"Dialog.useCallback\"], [\n            setOpen\n        ]),\n        modal,\n        children\n    });\n};\n_s(Dialog, \"8SlwgZd3PQY24vTpJma1ROJR1/M=\", false, function() {\n    return [\n        _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_3__.useControllableState,\n        _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__.useId,\n        _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__.useId,\n        _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__.useId\n    ];\n});\n_c = Dialog;\nDialog.displayName = DIALOG_NAME;\nvar TRIGGER_NAME = \"DialogTrigger\";\nvar DialogTrigger = /*#__PURE__*/ _s1(react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(_c1 = _s1((props, forwardedRef)=>{\n    _s1();\n    const { __scopeDialog, ...triggerProps } = props;\n    const context = useDialogContext(TRIGGER_NAME, __scopeDialog);\n    const composedTriggerRef = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs)(forwardedRef, context.triggerRef);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_6__.Primitive.button, {\n        type: \"button\",\n        \"aria-haspopup\": \"dialog\",\n        \"aria-expanded\": context.open,\n        \"aria-controls\": context.contentId,\n        \"data-state\": getState(context.open),\n        ...triggerProps,\n        ref: composedTriggerRef,\n        onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onClick, context.onOpenToggle)\n    });\n}, \"bYhw/KL0iUSfvGN4c0UsGZCd7iM=\", false, function() {\n    return [\n        useDialogContext,\n        _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs\n    ];\n})), \"bYhw/KL0iUSfvGN4c0UsGZCd7iM=\", false, function() {\n    return [\n        useDialogContext,\n        _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs\n    ];\n});\n_c2 = DialogTrigger;\nDialogTrigger.displayName = TRIGGER_NAME;\nvar PORTAL_NAME = \"DialogPortal\";\nvar [PortalProvider, usePortalContext] = createDialogContext(PORTAL_NAME, {\n    forceMount: void 0\n});\nvar DialogPortal = (props)=>{\n    _s2();\n    const { __scopeDialog, forceMount, children, container } = props;\n    const context = useDialogContext(PORTAL_NAME, __scopeDialog);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PortalProvider, {\n        scope: __scopeDialog,\n        forceMount,\n        children: react__WEBPACK_IMPORTED_MODULE_0__.Children.map(children, (child)=>/* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_8__.Presence, {\n                present: forceMount || context.open,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_9__.Portal, {\n                    asChild: true,\n                    container,\n                    children: child\n                })\n            }))\n    });\n};\n_s2(DialogPortal, \"gb1df/SMEHYUh29U87o5E06m2nU=\", false, function() {\n    return [\n        useDialogContext\n    ];\n});\n_c3 = DialogPortal;\nDialogPortal.displayName = PORTAL_NAME;\nvar OVERLAY_NAME = \"DialogOverlay\";\nvar DialogOverlay = /*#__PURE__*/ _s3(react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(_c4 = _s3((props, forwardedRef)=>{\n    _s3();\n    const portalContext = usePortalContext(OVERLAY_NAME, props.__scopeDialog);\n    const { forceMount = portalContext.forceMount, ...overlayProps } = props;\n    const context = useDialogContext(OVERLAY_NAME, props.__scopeDialog);\n    return context.modal ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_8__.Presence, {\n        present: forceMount || context.open,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DialogOverlayImpl, {\n            ...overlayProps,\n            ref: forwardedRef\n        })\n    }) : null;\n}, \"AXliLo+uLr/KDo6FX78QKyFnPa0=\", false, function() {\n    return [\n        usePortalContext,\n        useDialogContext\n    ];\n})), \"AXliLo+uLr/KDo6FX78QKyFnPa0=\", false, function() {\n    return [\n        usePortalContext,\n        useDialogContext\n    ];\n});\n_c5 = DialogOverlay;\nDialogOverlay.displayName = OVERLAY_NAME;\nvar Slot = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_10__.createSlot)(\"DialogOverlay.RemoveScroll\");\nvar DialogOverlayImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(_s4((props, forwardedRef)=>{\n    _s4();\n    const { __scopeDialog, ...overlayProps } = props;\n    const context = useDialogContext(OVERLAY_NAME, __scopeDialog);\n    return(// Make sure `Content` is scrollable even when it doesn't live inside `RemoveScroll`\n    // ie. when `Overlay` and `Content` are siblings\n    /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(react_remove_scroll__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n        as: Slot,\n        allowPinchZoom: true,\n        shards: [\n            context.contentRef\n        ],\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_6__.Primitive.div, {\n            \"data-state\": getState(context.open),\n            ...overlayProps,\n            ref: forwardedRef,\n            style: {\n                pointerEvents: \"auto\",\n                ...overlayProps.style\n            }\n        })\n    }));\n}, \"gb1df/SMEHYUh29U87o5E06m2nU=\", false, function() {\n    return [\n        useDialogContext\n    ];\n}));\n_c6 = DialogOverlayImpl;\nvar CONTENT_NAME = \"DialogContent\";\nvar DialogContent = /*#__PURE__*/ _s5(react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(_c7 = _s5((props, forwardedRef)=>{\n    _s5();\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopeDialog);\n    const { forceMount = portalContext.forceMount, ...contentProps } = props;\n    const context = useDialogContext(CONTENT_NAME, props.__scopeDialog);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_8__.Presence, {\n        present: forceMount || context.open,\n        children: context.modal ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DialogContentModal, {\n            ...contentProps,\n            ref: forwardedRef\n        }) : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DialogContentNonModal, {\n            ...contentProps,\n            ref: forwardedRef\n        })\n    });\n}, \"AXliLo+uLr/KDo6FX78QKyFnPa0=\", false, function() {\n    return [\n        usePortalContext,\n        useDialogContext\n    ];\n})), \"AXliLo+uLr/KDo6FX78QKyFnPa0=\", false, function() {\n    return [\n        usePortalContext,\n        useDialogContext\n    ];\n});\n_c8 = DialogContent;\nDialogContent.displayName = CONTENT_NAME;\nvar DialogContentModal = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(_s6((props, forwardedRef)=>{\n    _s6();\n    const context = useDialogContext(CONTENT_NAME, props.__scopeDialog);\n    const contentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs)(forwardedRef, context.contentRef, contentRef);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"DialogContentModal.useEffect\": ()=>{\n            const content = contentRef.current;\n            if (content) return (0,aria_hidden__WEBPACK_IMPORTED_MODULE_12__.hideOthers)(content);\n        }\n    }[\"DialogContentModal.useEffect\"], []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DialogContentImpl, {\n        ...props,\n        ref: composedRefs,\n        trapFocus: context.open,\n        disableOutsidePointerEvents: true,\n        onCloseAutoFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onCloseAutoFocus, (event)=>{\n            var _context_triggerRef_current;\n            event.preventDefault();\n            (_context_triggerRef_current = context.triggerRef.current) === null || _context_triggerRef_current === void 0 ? void 0 : _context_triggerRef_current.focus();\n        }),\n        onPointerDownOutside: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerDownOutside, (event)=>{\n            const originalEvent = event.detail.originalEvent;\n            const ctrlLeftClick = originalEvent.button === 0 && originalEvent.ctrlKey === true;\n            const isRightClick = originalEvent.button === 2 || ctrlLeftClick;\n            if (isRightClick) event.preventDefault();\n        }),\n        onFocusOutside: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onFocusOutside, (event)=>event.preventDefault())\n    });\n}, \"z0QlyWdXD1MaBi1+3AtBr2MuboI=\", false, function() {\n    return [\n        useDialogContext,\n        _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs\n    ];\n}));\n_c9 = DialogContentModal;\nvar DialogContentNonModal = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(_s7((props, forwardedRef)=>{\n    _s7();\n    const context = useDialogContext(CONTENT_NAME, props.__scopeDialog);\n    const hasInteractedOutsideRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const hasPointerDownOutsideRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DialogContentImpl, {\n        ...props,\n        ref: forwardedRef,\n        trapFocus: false,\n        disableOutsidePointerEvents: false,\n        onCloseAutoFocus: (event)=>{\n            var _props_onCloseAutoFocus;\n            (_props_onCloseAutoFocus = props.onCloseAutoFocus) === null || _props_onCloseAutoFocus === void 0 ? void 0 : _props_onCloseAutoFocus.call(props, event);\n            if (!event.defaultPrevented) {\n                var _context_triggerRef_current;\n                if (!hasInteractedOutsideRef.current) (_context_triggerRef_current = context.triggerRef.current) === null || _context_triggerRef_current === void 0 ? void 0 : _context_triggerRef_current.focus();\n                event.preventDefault();\n            }\n            hasInteractedOutsideRef.current = false;\n            hasPointerDownOutsideRef.current = false;\n        },\n        onInteractOutside: (event)=>{\n            var _props_onInteractOutside, _context_triggerRef_current;\n            (_props_onInteractOutside = props.onInteractOutside) === null || _props_onInteractOutside === void 0 ? void 0 : _props_onInteractOutside.call(props, event);\n            if (!event.defaultPrevented) {\n                hasInteractedOutsideRef.current = true;\n                if (event.detail.originalEvent.type === \"pointerdown\") {\n                    hasPointerDownOutsideRef.current = true;\n                }\n            }\n            const target = event.target;\n            const targetIsTrigger = (_context_triggerRef_current = context.triggerRef.current) === null || _context_triggerRef_current === void 0 ? void 0 : _context_triggerRef_current.contains(target);\n            if (targetIsTrigger) event.preventDefault();\n            if (event.detail.originalEvent.type === \"focusin\" && hasPointerDownOutsideRef.current) {\n                event.preventDefault();\n            }\n        }\n    });\n}, \"YrVQPDdDfWR20TF4ZFjbVSADLNA=\", false, function() {\n    return [\n        useDialogContext\n    ];\n}));\n_c10 = DialogContentNonModal;\nvar DialogContentImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(_s8((props, forwardedRef)=>{\n    _s8();\n    const { __scopeDialog, trapFocus, onOpenAutoFocus, onCloseAutoFocus, ...contentProps } = props;\n    const context = useDialogContext(CONTENT_NAME, __scopeDialog);\n    const contentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs)(forwardedRef, contentRef);\n    (0,_radix_ui_react_focus_guards__WEBPACK_IMPORTED_MODULE_13__.useFocusGuards)();\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n        children: [\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_focus_scope__WEBPACK_IMPORTED_MODULE_14__.FocusScope, {\n                asChild: true,\n                loop: true,\n                trapped: trapFocus,\n                onMountAutoFocus: onOpenAutoFocus,\n                onUnmountAutoFocus: onCloseAutoFocus,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_15__.DismissableLayer, {\n                    role: \"dialog\",\n                    id: context.contentId,\n                    \"aria-describedby\": context.descriptionId,\n                    \"aria-labelledby\": context.titleId,\n                    \"data-state\": getState(context.open),\n                    ...contentProps,\n                    ref: composedRefs,\n                    onDismiss: ()=>context.onOpenChange(false)\n                })\n            }),\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n                children: [\n                    /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TitleWarning, {\n                        titleId: context.titleId\n                    }),\n                    /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DescriptionWarning, {\n                        contentRef,\n                        descriptionId: context.descriptionId\n                    })\n                ]\n            })\n        ]\n    });\n}, \"wjdue5beIiufJExexORWgo6pOh0=\", false, function() {\n    return [\n        useDialogContext,\n        _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs,\n        _radix_ui_react_focus_guards__WEBPACK_IMPORTED_MODULE_13__.useFocusGuards\n    ];\n}));\n_c11 = DialogContentImpl;\nvar TITLE_NAME = \"DialogTitle\";\nvar DialogTitle = /*#__PURE__*/ _s9(react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(_c12 = _s9((props, forwardedRef)=>{\n    _s9();\n    const { __scopeDialog, ...titleProps } = props;\n    const context = useDialogContext(TITLE_NAME, __scopeDialog);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_6__.Primitive.h2, {\n        id: context.titleId,\n        ...titleProps,\n        ref: forwardedRef\n    });\n}, \"gb1df/SMEHYUh29U87o5E06m2nU=\", false, function() {\n    return [\n        useDialogContext\n    ];\n})), \"gb1df/SMEHYUh29U87o5E06m2nU=\", false, function() {\n    return [\n        useDialogContext\n    ];\n});\n_c13 = DialogTitle;\nDialogTitle.displayName = TITLE_NAME;\nvar DESCRIPTION_NAME = \"DialogDescription\";\nvar DialogDescription = /*#__PURE__*/ _s10(react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(_c14 = _s10((props, forwardedRef)=>{\n    _s10();\n    const { __scopeDialog, ...descriptionProps } = props;\n    const context = useDialogContext(DESCRIPTION_NAME, __scopeDialog);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_6__.Primitive.p, {\n        id: context.descriptionId,\n        ...descriptionProps,\n        ref: forwardedRef\n    });\n}, \"gb1df/SMEHYUh29U87o5E06m2nU=\", false, function() {\n    return [\n        useDialogContext\n    ];\n})), \"gb1df/SMEHYUh29U87o5E06m2nU=\", false, function() {\n    return [\n        useDialogContext\n    ];\n});\n_c15 = DialogDescription;\nDialogDescription.displayName = DESCRIPTION_NAME;\nvar CLOSE_NAME = \"DialogClose\";\nvar DialogClose = /*#__PURE__*/ _s11(react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(_c16 = _s11((props, forwardedRef)=>{\n    _s11();\n    const { __scopeDialog, ...closeProps } = props;\n    const context = useDialogContext(CLOSE_NAME, __scopeDialog);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_6__.Primitive.button, {\n        type: \"button\",\n        ...closeProps,\n        ref: forwardedRef,\n        onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onClick, ()=>context.onOpenChange(false))\n    });\n}, \"gb1df/SMEHYUh29U87o5E06m2nU=\", false, function() {\n    return [\n        useDialogContext\n    ];\n})), \"gb1df/SMEHYUh29U87o5E06m2nU=\", false, function() {\n    return [\n        useDialogContext\n    ];\n});\n_c17 = DialogClose;\nDialogClose.displayName = CLOSE_NAME;\nfunction getState(open) {\n    return open ? \"open\" : \"closed\";\n}\nvar TITLE_WARNING_NAME = \"DialogTitleWarning\";\nvar [WarningProvider, useWarningContext] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContext)(TITLE_WARNING_NAME, {\n    contentName: CONTENT_NAME,\n    titleName: TITLE_NAME,\n    docsSlug: \"dialog\"\n});\nvar TitleWarning = (param)=>{\n    let { titleId } = param;\n    _s12();\n    const titleWarningContext = useWarningContext(TITLE_WARNING_NAME);\n    const MESSAGE = \"`\".concat(titleWarningContext.contentName, \"` requires a `\").concat(titleWarningContext.titleName, \"` for the component to be accessible for screen reader users.\\n\\nIf you want to hide the `\").concat(titleWarningContext.titleName, \"`, you can wrap it with our VisuallyHidden component.\\n\\nFor more information, see https://radix-ui.com/primitives/docs/components/\").concat(titleWarningContext.docsSlug);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"TitleWarning.useEffect\": ()=>{\n            if (titleId) {\n                const hasTitle = document.getElementById(titleId);\n                if (!hasTitle) console.error(MESSAGE);\n            }\n        }\n    }[\"TitleWarning.useEffect\"], [\n        MESSAGE,\n        titleId\n    ]);\n    return null;\n};\n_s12(TitleWarning, \"GA0m2oeX5XXEaAUGtZZQs5ML670=\", false, function() {\n    return [\n        useWarningContext\n    ];\n});\n_c18 = TitleWarning;\nvar DESCRIPTION_WARNING_NAME = \"DialogDescriptionWarning\";\nvar DescriptionWarning = (param)=>{\n    let { contentRef, descriptionId } = param;\n    _s13();\n    const descriptionWarningContext = useWarningContext(DESCRIPTION_WARNING_NAME);\n    const MESSAGE = \"Warning: Missing `Description` or `aria-describedby={undefined}` for {\".concat(descriptionWarningContext.contentName, \"}.\");\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"DescriptionWarning.useEffect\": ()=>{\n            var _contentRef_current;\n            const describedById = (_contentRef_current = contentRef.current) === null || _contentRef_current === void 0 ? void 0 : _contentRef_current.getAttribute(\"aria-describedby\");\n            if (descriptionId && describedById) {\n                const hasDescription = document.getElementById(descriptionId);\n                if (!hasDescription) console.warn(MESSAGE);\n            }\n        }\n    }[\"DescriptionWarning.useEffect\"], [\n        MESSAGE,\n        contentRef,\n        descriptionId\n    ]);\n    return null;\n};\n_s13(DescriptionWarning, \"udowy/X+0YeBLGtDTT18KC58FH0=\", false, function() {\n    return [\n        useWarningContext\n    ];\n});\n_c19 = DescriptionWarning;\nvar Root = Dialog;\nvar Trigger = DialogTrigger;\nvar Portal = DialogPortal;\nvar Overlay = DialogOverlay;\nvar Content = DialogContent;\nvar Title = DialogTitle;\nvar Description = DialogDescription;\nvar Close = DialogClose;\n //# sourceMappingURL=index.mjs.map\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18, _c19;\n$RefreshReg$(_c, \"Dialog\");\n$RefreshReg$(_c1, \"DialogTrigger$React.forwardRef\");\n$RefreshReg$(_c2, \"DialogTrigger\");\n$RefreshReg$(_c3, \"DialogPortal\");\n$RefreshReg$(_c4, \"DialogOverlay$React.forwardRef\");\n$RefreshReg$(_c5, \"DialogOverlay\");\n$RefreshReg$(_c6, \"DialogOverlayImpl\");\n$RefreshReg$(_c7, \"DialogContent$React.forwardRef\");\n$RefreshReg$(_c8, \"DialogContent\");\n$RefreshReg$(_c9, \"DialogContentModal\");\n$RefreshReg$(_c10, \"DialogContentNonModal\");\n$RefreshReg$(_c11, \"DialogContentImpl\");\n$RefreshReg$(_c12, \"DialogTitle$React.forwardRef\");\n$RefreshReg$(_c13, \"DialogTitle\");\n$RefreshReg$(_c14, \"DialogDescription$React.forwardRef\");\n$RefreshReg$(_c15, \"DialogDescription\");\n$RefreshReg$(_c16, \"DialogClose$React.forwardRef\");\n$RefreshReg$(_c17, \"DialogClose\");\n$RefreshReg$(_c18, \"TitleWarning\");\n$RefreshReg$(_c19, \"DescriptionWarning\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@radix-ui/react-dialog/dist/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@radix-ui/react-label/dist/index.mjs":
/*!***********************************************************!*\
  !*** ./node_modules/@radix-ui/react-label/dist/index.mjs ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Label: () => (/* binding */ Label),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(app-pages-browser)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Label,Root auto */ // src/label.tsx\n\n\n\nvar NAME = \"Label\";\nvar Label = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(_c = (props, forwardedRef)=>{\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__.Primitive.label, {\n        ...props,\n        ref: forwardedRef,\n        onMouseDown: (event)=>{\n            var _props_onMouseDown;\n            const target = event.target;\n            if (target.closest(\"button, input, select, textarea\")) return;\n            (_props_onMouseDown = props.onMouseDown) === null || _props_onMouseDown === void 0 ? void 0 : _props_onMouseDown.call(props, event);\n            if (!event.defaultPrevented && event.detail > 1) event.preventDefault();\n        }\n    });\n});\n_c1 = Label;\nLabel.displayName = NAME;\nvar Root = Label;\n //# sourceMappingURL=index.mjs.map\nvar _c, _c1;\n$RefreshReg$(_c, \"Label$React.forwardRef\");\n$RefreshReg$(_c1, \"Label\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@radix-ui/react-label/dist/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@radix-ui/react-tabs/dist/index.mjs":
/*!**********************************************************!*\
  !*** ./node_modules/@radix-ui/react-tabs/dist/index.mjs ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Content: () => (/* binding */ Content),\n/* harmony export */   List: () => (/* binding */ List),\n/* harmony export */   Root: () => (/* binding */ Root2),\n/* harmony export */   Tabs: () => (/* binding */ Tabs),\n/* harmony export */   TabsContent: () => (/* binding */ TabsContent),\n/* harmony export */   TabsList: () => (/* binding */ TabsList),\n/* harmony export */   TabsTrigger: () => (/* binding */ TabsTrigger),\n/* harmony export */   Trigger: () => (/* binding */ Trigger),\n/* harmony export */   createTabsScope: () => (/* binding */ createTabsScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/primitive */ \"(app-pages-browser)/./node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(app-pages-browser)/./node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_roving_focus__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-roving-focus */ \"(app-pages-browser)/./node_modules/@radix-ui/react-roving-focus/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-presence */ \"(app-pages-browser)/./node_modules/@radix-ui/react-presence/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(app-pages-browser)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-direction */ \"(app-pages-browser)/./node_modules/@radix-ui/react-direction/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(app-pages-browser)/./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-id */ \"(app-pages-browser)/./node_modules/@radix-ui/react-id/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Content,List,Root,Tabs,TabsContent,TabsList,TabsTrigger,Trigger,createTabsScope auto */ var _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$();\n// src/tabs.tsx\n\n\n\n\n\n\n\n\n\n\n\nvar TABS_NAME = \"Tabs\";\nvar [createTabsContext, createTabsScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(TABS_NAME, [\n    _radix_ui_react_roving_focus__WEBPACK_IMPORTED_MODULE_3__.createRovingFocusGroupScope\n]);\nvar useRovingFocusGroupScope = (0,_radix_ui_react_roving_focus__WEBPACK_IMPORTED_MODULE_3__.createRovingFocusGroupScope)();\nvar [TabsProvider, useTabsContext] = createTabsContext(TABS_NAME);\nvar Tabs = /*#__PURE__*/ _s(react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(_c = _s((props, forwardedRef)=>{\n    _s();\n    const { __scopeTabs, value: valueProp, onValueChange, defaultValue, orientation = \"horizontal\", dir, activationMode = \"automatic\", ...tabsProps } = props;\n    const direction = (0,_radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_4__.useDirection)(dir);\n    const [value, setValue] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_5__.useControllableState)({\n        prop: valueProp,\n        onChange: onValueChange,\n        defaultProp: defaultValue !== null && defaultValue !== void 0 ? defaultValue : \"\",\n        caller: TABS_NAME\n    });\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TabsProvider, {\n        scope: __scopeTabs,\n        baseId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_6__.useId)(),\n        value,\n        onValueChange: setValue,\n        orientation,\n        dir: direction,\n        activationMode,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.div, {\n            dir: direction,\n            \"data-orientation\": orientation,\n            ...tabsProps,\n            ref: forwardedRef\n        })\n    });\n}, \"nid3mV2mzccqbzWgysIQbRheKGE=\", false, function() {\n    return [\n        _radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_4__.useDirection,\n        _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_5__.useControllableState,\n        _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_6__.useId\n    ];\n})), \"nid3mV2mzccqbzWgysIQbRheKGE=\", false, function() {\n    return [\n        _radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_4__.useDirection,\n        _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_5__.useControllableState,\n        _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_6__.useId\n    ];\n});\n_c1 = Tabs;\nTabs.displayName = TABS_NAME;\nvar TAB_LIST_NAME = \"TabsList\";\nvar TabsList = /*#__PURE__*/ _s1(react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(_c2 = _s1((props, forwardedRef)=>{\n    _s1();\n    const { __scopeTabs, loop = true, ...listProps } = props;\n    const context = useTabsContext(TAB_LIST_NAME, __scopeTabs);\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeTabs);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_roving_focus__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        asChild: true,\n        ...rovingFocusGroupScope,\n        orientation: context.orientation,\n        dir: context.dir,\n        loop,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.div, {\n            role: \"tablist\",\n            \"aria-orientation\": context.orientation,\n            ...listProps,\n            ref: forwardedRef\n        })\n    });\n}, \"cfHyu4or5otFl074mR2f59Tyt4o=\", false, function() {\n    return [\n        useTabsContext,\n        useRovingFocusGroupScope\n    ];\n})), \"cfHyu4or5otFl074mR2f59Tyt4o=\", false, function() {\n    return [\n        useTabsContext,\n        useRovingFocusGroupScope\n    ];\n});\n_c3 = TabsList;\nTabsList.displayName = TAB_LIST_NAME;\nvar TRIGGER_NAME = \"TabsTrigger\";\nvar TabsTrigger = /*#__PURE__*/ _s2(react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(_c4 = _s2((props, forwardedRef)=>{\n    _s2();\n    const { __scopeTabs, value, disabled = false, ...triggerProps } = props;\n    const context = useTabsContext(TRIGGER_NAME, __scopeTabs);\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeTabs);\n    const triggerId = makeTriggerId(context.baseId, value);\n    const contentId = makeContentId(context.baseId, value);\n    const isSelected = value === context.value;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_roving_focus__WEBPACK_IMPORTED_MODULE_3__.Item, {\n        asChild: true,\n        ...rovingFocusGroupScope,\n        focusable: !disabled,\n        active: isSelected,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.button, {\n            type: \"button\",\n            role: \"tab\",\n            \"aria-selected\": isSelected,\n            \"aria-controls\": contentId,\n            \"data-state\": isSelected ? \"active\" : \"inactive\",\n            \"data-disabled\": disabled ? \"\" : void 0,\n            disabled,\n            id: triggerId,\n            ...triggerProps,\n            ref: forwardedRef,\n            onMouseDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onMouseDown, (event)=>{\n                if (!disabled && event.button === 0 && event.ctrlKey === false) {\n                    context.onValueChange(value);\n                } else {\n                    event.preventDefault();\n                }\n            }),\n            onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onKeyDown, (event)=>{\n                if ([\n                    \" \",\n                    \"Enter\"\n                ].includes(event.key)) context.onValueChange(value);\n            }),\n            onFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onFocus, ()=>{\n                const isAutomaticActivation = context.activationMode !== \"manual\";\n                if (!isSelected && !disabled && isAutomaticActivation) {\n                    context.onValueChange(value);\n                }\n            })\n        })\n    });\n}, \"cfHyu4or5otFl074mR2f59Tyt4o=\", false, function() {\n    return [\n        useTabsContext,\n        useRovingFocusGroupScope\n    ];\n})), \"cfHyu4or5otFl074mR2f59Tyt4o=\", false, function() {\n    return [\n        useTabsContext,\n        useRovingFocusGroupScope\n    ];\n});\n_c5 = TabsTrigger;\nTabsTrigger.displayName = TRIGGER_NAME;\nvar CONTENT_NAME = \"TabsContent\";\nvar TabsContent = /*#__PURE__*/ _s3(react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(_c6 = _s3((props, forwardedRef)=>{\n    _s3();\n    const { __scopeTabs, value, forceMount, children, ...contentProps } = props;\n    const context = useTabsContext(CONTENT_NAME, __scopeTabs);\n    const triggerId = makeTriggerId(context.baseId, value);\n    const contentId = makeContentId(context.baseId, value);\n    const isSelected = value === context.value;\n    const isMountAnimationPreventedRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(isSelected);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"TabsContent.useEffect\": ()=>{\n            const rAF = requestAnimationFrame({\n                \"TabsContent.useEffect.rAF\": ()=>isMountAnimationPreventedRef.current = false\n            }[\"TabsContent.useEffect.rAF\"]);\n            return ({\n                \"TabsContent.useEffect\": ()=>cancelAnimationFrame(rAF)\n            })[\"TabsContent.useEffect\"];\n        }\n    }[\"TabsContent.useEffect\"], []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_9__.Presence, {\n        present: forceMount || isSelected,\n        children: (param)=>{\n            let { present } = param;\n            return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.div, {\n                \"data-state\": isSelected ? \"active\" : \"inactive\",\n                \"data-orientation\": context.orientation,\n                role: \"tabpanel\",\n                \"aria-labelledby\": triggerId,\n                hidden: !present,\n                id: contentId,\n                tabIndex: 0,\n                ...contentProps,\n                ref: forwardedRef,\n                style: {\n                    ...props.style,\n                    animationDuration: isMountAnimationPreventedRef.current ? \"0s\" : void 0\n                },\n                children: present && children\n            });\n        }\n    });\n}, \"5pMsgA+tqKL6NmsG701SW7bTmuc=\", false, function() {\n    return [\n        useTabsContext\n    ];\n})), \"5pMsgA+tqKL6NmsG701SW7bTmuc=\", false, function() {\n    return [\n        useTabsContext\n    ];\n});\n_c7 = TabsContent;\nTabsContent.displayName = CONTENT_NAME;\nfunction makeTriggerId(baseId, value) {\n    return \"\".concat(baseId, \"-trigger-\").concat(value);\n}\nfunction makeContentId(baseId, value) {\n    return \"\".concat(baseId, \"-content-\").concat(value);\n}\nvar Root2 = Tabs;\nvar List = TabsList;\nvar Trigger = TabsTrigger;\nvar Content = TabsContent;\n //# sourceMappingURL=index.mjs.map\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7;\n$RefreshReg$(_c, \"Tabs$React.forwardRef\");\n$RefreshReg$(_c1, \"Tabs\");\n$RefreshReg$(_c2, \"TabsList$React.forwardRef\");\n$RefreshReg$(_c3, \"TabsList\");\n$RefreshReg$(_c4, \"TabsTrigger$React.forwardRef\");\n$RefreshReg$(_c5, \"TabsTrigger\");\n$RefreshReg$(_c6, \"TabsContent$React.forwardRef\");\n$RefreshReg$(_c7, \"TabsContent\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@radix-ui/react-tabs/dist/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js":
/*!*******************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/x.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ X)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M18 6 6 18\",\n            key: \"1bl5f8\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m6 6 12 12\",\n            key: \"d8bk6v\"\n        }\n    ]\n];\nconst X = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"x\", __iconNode);\n //# sourceMappingURL=x.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMveC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFHTyxNQUFNLFVBQXVCO0lBQ2xDO1FBQUMsTUFBUTtRQUFBO1lBQUUsR0FBRyxDQUFjO1lBQUEsS0FBSztRQUFBLENBQVU7S0FBQTtJQUMzQztRQUFDLENBQVE7UUFBQSxDQUFFO1lBQUEsRUFBRyxhQUFjO1lBQUEsSUFBSztRQUFVO0tBQUE7Q0FDN0M7QUFhTSxRQUFJLGtFQUFpQixNQUFLLENBQVUiLCJzb3VyY2VzIjpbIi9Vc2Vycy9zaGF3YXovc3JjL2ljb25zL3gudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGNyZWF0ZUx1Y2lkZUljb24gZnJvbSAnLi4vY3JlYXRlTHVjaWRlSWNvbic7XG5pbXBvcnQgeyBJY29uTm9kZSB9IGZyb20gJy4uL3R5cGVzJztcblxuZXhwb3J0IGNvbnN0IF9faWNvbk5vZGU6IEljb25Ob2RlID0gW1xuICBbJ3BhdGgnLCB7IGQ6ICdNMTggNiA2IDE4Jywga2V5OiAnMWJsNWY4JyB9XSxcbiAgWydwYXRoJywgeyBkOiAnbTYgNiAxMiAxMicsIGtleTogJ2Q4Yms2dicgfV0sXG5dO1xuXG4vKipcbiAqIEBjb21wb25lbnQgQG5hbWUgWFxuICogQGRlc2NyaXB0aW9uIEx1Y2lkZSBTVkcgaWNvbiBjb21wb25lbnQsIHJlbmRlcnMgU1ZHIEVsZW1lbnQgd2l0aCBjaGlsZHJlbi5cbiAqXG4gKiBAcHJldmlldyAhW2ltZ10oZGF0YTppbWFnZS9zdmcreG1sO2Jhc2U2NCxQSE4yWnlBZ2VHMXNibk05SW1oMGRIQTZMeTkzZDNjdWR6TXViM0puTHpJd01EQXZjM1puSWdvZ0lIZHBaSFJvUFNJeU5DSUtJQ0JvWldsbmFIUTlJakkwSWdvZ0lIWnBaWGRDYjNnOUlqQWdNQ0F5TkNBeU5DSUtJQ0JtYVd4c1BTSnViMjVsSWdvZ0lITjBjbTlyWlQwaUl6QXdNQ0lnYzNSNWJHVTlJbUpoWTJ0bmNtOTFibVF0WTI5c2IzSTZJQ05tWm1ZN0lHSnZjbVJsY2kxeVlXUnBkWE02SURKd2VDSUtJQ0J6ZEhKdmEyVXRkMmxrZEdnOUlqSWlDaUFnYzNSeWIydGxMV3hwYm1WallYQTlJbkp2ZFc1a0lnb2dJSE4wY205clpTMXNhVzVsYW05cGJqMGljbTkxYm1RaUNqNEtJQ0E4Y0dGMGFDQmtQU0pOTVRnZ05pQTJJREU0SWlBdlBnb2dJRHh3WVhSb0lHUTlJbTAySURZZ01USWdNVElpSUM4K0Nqd3ZjM1puUGdvPSkgLSBodHRwczovL2x1Y2lkZS5kZXYvaWNvbnMveFxuICogQHNlZSBodHRwczovL2x1Y2lkZS5kZXYvZ3VpZGUvcGFja2FnZXMvbHVjaWRlLXJlYWN0IC0gRG9jdW1lbnRhdGlvblxuICpcbiAqIEBwYXJhbSB7T2JqZWN0fSBwcm9wcyAtIEx1Y2lkZSBpY29ucyBwcm9wcyBhbmQgYW55IHZhbGlkIFNWRyBhdHRyaWJ1dGVcbiAqIEByZXR1cm5zIHtKU1guRWxlbWVudH0gSlNYIEVsZW1lbnRcbiAqXG4gKi9cbmNvbnN0IFggPSBjcmVhdGVMdWNpZGVJY29uKCd4JywgX19pY29uTm9kZSk7XG5cbmV4cG9ydCBkZWZhdWx0IFg7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/sonner/dist/index.mjs":
/*!********************************************!*\
  !*** ./node_modules/sonner/dist/index.mjs ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toaster: () => (/* binding */ Toaster),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   useSonner: () => (/* binding */ useSonner)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/index.js\");\n/* __next_internal_client_entry_do_not_use__ Toaster,toast,useSonner auto */ var _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$();\nfunction __insertCSS(code) {\n    if (!code || typeof document == 'undefined') return;\n    let head = document.head || document.getElementsByTagName('head')[0];\n    let style = document.createElement('style');\n    style.type = 'text/css';\n    head.appendChild(style);\n    style.styleSheet ? style.styleSheet.cssText = code : style.appendChild(document.createTextNode(code));\n}\n\n\nconst getAsset = (type)=>{\n    switch(type){\n        case 'success':\n            return SuccessIcon;\n        case 'info':\n            return InfoIcon;\n        case 'warning':\n            return WarningIcon;\n        case 'error':\n            return ErrorIcon;\n        default:\n            return null;\n    }\n};\nconst bars = Array(12).fill(0);\nconst Loader = (param)=>{\n    let { visible, className } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        className: [\n            'sonner-loading-wrapper',\n            className\n        ].filter(Boolean).join(' '),\n        \"data-visible\": visible\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        className: \"sonner-spinner\"\n    }, bars.map((_, i)=>/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n            className: \"sonner-loading-bar\",\n            key: \"spinner-bar-\".concat(i)\n        }))));\n};\n_c = Loader;\nconst SuccessIcon = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 20 20\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z\",\n    clipRule: \"evenodd\"\n}));\nconst WarningIcon = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z\",\n    clipRule: \"evenodd\"\n}));\nconst InfoIcon = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 20 20\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z\",\n    clipRule: \"evenodd\"\n}));\nconst ErrorIcon = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 20 20\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z\",\n    clipRule: \"evenodd\"\n}));\nconst CloseIcon = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    width: \"12\",\n    height: \"12\",\n    viewBox: \"0 0 24 24\",\n    fill: \"none\",\n    stroke: \"currentColor\",\n    strokeWidth: \"1.5\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"line\", {\n    x1: \"18\",\n    y1: \"6\",\n    x2: \"6\",\n    y2: \"18\"\n}), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"line\", {\n    x1: \"6\",\n    y1: \"6\",\n    x2: \"18\",\n    y2: \"18\"\n}));\nconst useIsDocumentHidden = ()=>{\n    _s();\n    const [isDocumentHidden, setIsDocumentHidden] = react__WEBPACK_IMPORTED_MODULE_0__.useState(document.hidden);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useIsDocumentHidden.useEffect\": ()=>{\n            const callback = {\n                \"useIsDocumentHidden.useEffect.callback\": ()=>{\n                    setIsDocumentHidden(document.hidden);\n                }\n            }[\"useIsDocumentHidden.useEffect.callback\"];\n            document.addEventListener('visibilitychange', callback);\n            return ({\n                \"useIsDocumentHidden.useEffect\": ()=>window.removeEventListener('visibilitychange', callback)\n            })[\"useIsDocumentHidden.useEffect\"];\n        }\n    }[\"useIsDocumentHidden.useEffect\"], []);\n    return isDocumentHidden;\n};\n_s(useIsDocumentHidden, \"RJwWklAunJjdVVAElZ/SoraKxVU=\");\nlet toastsCounter = 1;\nclass Observer {\n    constructor(){\n        // We use arrow functions to maintain the correct `this` reference\n        this.subscribe = (subscriber)=>{\n            this.subscribers.push(subscriber);\n            return ()=>{\n                const index = this.subscribers.indexOf(subscriber);\n                this.subscribers.splice(index, 1);\n            };\n        };\n        this.publish = (data)=>{\n            this.subscribers.forEach((subscriber)=>subscriber(data));\n        };\n        this.addToast = (data)=>{\n            this.publish(data);\n            this.toasts = [\n                ...this.toasts,\n                data\n            ];\n        };\n        this.create = (data)=>{\n            var _data_id;\n            const { message, ...rest } = data;\n            const id = typeof (data == null ? void 0 : data.id) === 'number' || ((_data_id = data.id) == null ? void 0 : _data_id.length) > 0 ? data.id : toastsCounter++;\n            const alreadyExists = this.toasts.find((toast)=>{\n                return toast.id === id;\n            });\n            const dismissible = data.dismissible === undefined ? true : data.dismissible;\n            if (this.dismissedToasts.has(id)) {\n                this.dismissedToasts.delete(id);\n            }\n            if (alreadyExists) {\n                this.toasts = this.toasts.map((toast)=>{\n                    if (toast.id === id) {\n                        this.publish({\n                            ...toast,\n                            ...data,\n                            id,\n                            title: message\n                        });\n                        return {\n                            ...toast,\n                            ...data,\n                            id,\n                            dismissible,\n                            title: message\n                        };\n                    }\n                    return toast;\n                });\n            } else {\n                this.addToast({\n                    title: message,\n                    ...rest,\n                    dismissible,\n                    id\n                });\n            }\n            return id;\n        };\n        this.dismiss = (id)=>{\n            if (id) {\n                this.dismissedToasts.add(id);\n                requestAnimationFrame(()=>this.subscribers.forEach((subscriber)=>subscriber({\n                            id,\n                            dismiss: true\n                        })));\n            } else {\n                this.toasts.forEach((toast)=>{\n                    this.subscribers.forEach((subscriber)=>subscriber({\n                            id: toast.id,\n                            dismiss: true\n                        }));\n                });\n            }\n            return id;\n        };\n        this.message = (message, data)=>{\n            return this.create({\n                ...data,\n                message\n            });\n        };\n        this.error = (message, data)=>{\n            return this.create({\n                ...data,\n                message,\n                type: 'error'\n            });\n        };\n        this.success = (message, data)=>{\n            return this.create({\n                ...data,\n                type: 'success',\n                message\n            });\n        };\n        this.info = (message, data)=>{\n            return this.create({\n                ...data,\n                type: 'info',\n                message\n            });\n        };\n        this.warning = (message, data)=>{\n            return this.create({\n                ...data,\n                type: 'warning',\n                message\n            });\n        };\n        this.loading = (message, data)=>{\n            return this.create({\n                ...data,\n                type: 'loading',\n                message\n            });\n        };\n        this.promise = (promise, data)=>{\n            if (!data) {\n                // Nothing to show\n                return;\n            }\n            let id = undefined;\n            if (data.loading !== undefined) {\n                id = this.create({\n                    ...data,\n                    promise,\n                    type: 'loading',\n                    message: data.loading,\n                    description: typeof data.description !== 'function' ? data.description : undefined\n                });\n            }\n            const p = Promise.resolve(promise instanceof Function ? promise() : promise);\n            let shouldDismiss = id !== undefined;\n            let result;\n            const originalPromise = p.then(async (response)=>{\n                result = [\n                    'resolve',\n                    response\n                ];\n                const isReactElementResponse = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(response);\n                if (isReactElementResponse) {\n                    shouldDismiss = false;\n                    this.create({\n                        id,\n                        type: 'default',\n                        message: response\n                    });\n                } else if (isHttpResponse(response) && !response.ok) {\n                    shouldDismiss = false;\n                    const promiseData = typeof data.error === 'function' ? await data.error(\"HTTP error! status: \".concat(response.status)) : data.error;\n                    const description = typeof data.description === 'function' ? await data.description(\"HTTP error! status: \".concat(response.status)) : data.description;\n                    const isExtendedResult = typeof promiseData === 'object' && !/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(promiseData);\n                    const toastSettings = isExtendedResult ? promiseData : {\n                        message: promiseData\n                    };\n                    this.create({\n                        id,\n                        type: 'error',\n                        description,\n                        ...toastSettings\n                    });\n                } else if (response instanceof Error) {\n                    shouldDismiss = false;\n                    const promiseData = typeof data.error === 'function' ? await data.error(response) : data.error;\n                    const description = typeof data.description === 'function' ? await data.description(response) : data.description;\n                    const isExtendedResult = typeof promiseData === 'object' && !/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(promiseData);\n                    const toastSettings = isExtendedResult ? promiseData : {\n                        message: promiseData\n                    };\n                    this.create({\n                        id,\n                        type: 'error',\n                        description,\n                        ...toastSettings\n                    });\n                } else if (data.success !== undefined) {\n                    shouldDismiss = false;\n                    const promiseData = typeof data.success === 'function' ? await data.success(response) : data.success;\n                    const description = typeof data.description === 'function' ? await data.description(response) : data.description;\n                    const isExtendedResult = typeof promiseData === 'object' && !/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(promiseData);\n                    const toastSettings = isExtendedResult ? promiseData : {\n                        message: promiseData\n                    };\n                    this.create({\n                        id,\n                        type: 'success',\n                        description,\n                        ...toastSettings\n                    });\n                }\n            }).catch(async (error)=>{\n                result = [\n                    'reject',\n                    error\n                ];\n                if (data.error !== undefined) {\n                    shouldDismiss = false;\n                    const promiseData = typeof data.error === 'function' ? await data.error(error) : data.error;\n                    const description = typeof data.description === 'function' ? await data.description(error) : data.description;\n                    const isExtendedResult = typeof promiseData === 'object' && !/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(promiseData);\n                    const toastSettings = isExtendedResult ? promiseData : {\n                        message: promiseData\n                    };\n                    this.create({\n                        id,\n                        type: 'error',\n                        description,\n                        ...toastSettings\n                    });\n                }\n            }).finally(()=>{\n                if (shouldDismiss) {\n                    // Toast is still in load state (and will be indefinitely — dismiss it)\n                    this.dismiss(id);\n                    id = undefined;\n                }\n                data.finally == null ? void 0 : data.finally.call(data);\n            });\n            const unwrap = ()=>new Promise((resolve, reject)=>originalPromise.then(()=>result[0] === 'reject' ? reject(result[1]) : resolve(result[1])).catch(reject));\n            if (typeof id !== 'string' && typeof id !== 'number') {\n                // cannot Object.assign on undefined\n                return {\n                    unwrap\n                };\n            } else {\n                return Object.assign(id, {\n                    unwrap\n                });\n            }\n        };\n        this.custom = (jsx, data)=>{\n            const id = (data == null ? void 0 : data.id) || toastsCounter++;\n            this.create({\n                jsx: jsx(id),\n                id,\n                ...data\n            });\n            return id;\n        };\n        this.getActiveToasts = ()=>{\n            return this.toasts.filter((toast)=>!this.dismissedToasts.has(toast.id));\n        };\n        this.subscribers = [];\n        this.toasts = [];\n        this.dismissedToasts = new Set();\n    }\n}\nconst ToastState = new Observer();\n// bind this to the toast function\nconst toastFunction = (message, data)=>{\n    const id = (data == null ? void 0 : data.id) || toastsCounter++;\n    ToastState.addToast({\n        title: message,\n        ...data,\n        id\n    });\n    return id;\n};\nconst isHttpResponse = (data)=>{\n    return data && typeof data === 'object' && 'ok' in data && typeof data.ok === 'boolean' && 'status' in data && typeof data.status === 'number';\n};\nconst basicToast = toastFunction;\nconst getHistory = ()=>ToastState.toasts;\nconst getToasts = ()=>ToastState.getActiveToasts();\n// We use `Object.assign` to maintain the correct types as we would lose them otherwise\nconst toast = Object.assign(basicToast, {\n    success: ToastState.success,\n    info: ToastState.info,\n    warning: ToastState.warning,\n    error: ToastState.error,\n    custom: ToastState.custom,\n    message: ToastState.message,\n    promise: ToastState.promise,\n    dismiss: ToastState.dismiss,\n    loading: ToastState.loading\n}, {\n    getHistory,\n    getToasts\n});\n__insertCSS(\"[data-sonner-toaster][dir=ltr],html[dir=ltr]{--toast-icon-margin-start:-3px;--toast-icon-margin-end:4px;--toast-svg-margin-start:-1px;--toast-svg-margin-end:0px;--toast-button-margin-start:auto;--toast-button-margin-end:0;--toast-close-button-start:0;--toast-close-button-end:unset;--toast-close-button-transform:translate(-35%, -35%)}[data-sonner-toaster][dir=rtl],html[dir=rtl]{--toast-icon-margin-start:4px;--toast-icon-margin-end:-3px;--toast-svg-margin-start:0px;--toast-svg-margin-end:-1px;--toast-button-margin-start:0;--toast-button-margin-end:auto;--toast-close-button-start:unset;--toast-close-button-end:0;--toast-close-button-transform:translate(35%, -35%)}[data-sonner-toaster]{position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1:hsl(0, 0%, 99%);--gray2:hsl(0, 0%, 97.3%);--gray3:hsl(0, 0%, 95.1%);--gray4:hsl(0, 0%, 93%);--gray5:hsl(0, 0%, 90.9%);--gray6:hsl(0, 0%, 88.7%);--gray7:hsl(0, 0%, 85.8%);--gray8:hsl(0, 0%, 78%);--gray9:hsl(0, 0%, 56.1%);--gray10:hsl(0, 0%, 52.3%);--gray11:hsl(0, 0%, 43.5%);--gray12:hsl(0, 0%, 9%);--border-radius:8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:0;z-index:999999999;transition:transform .4s ease}@media (hover:none) and (pointer:coarse){[data-sonner-toaster][data-lifted=true]{transform:none}}[data-sonner-toaster][data-x-position=right]{right:var(--offset-right)}[data-sonner-toaster][data-x-position=left]{left:var(--offset-left)}[data-sonner-toaster][data-x-position=center]{left:50%;transform:translateX(-50%)}[data-sonner-toaster][data-y-position=top]{top:var(--offset-top)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--offset-bottom)}[data-sonner-toast]{--y:translateY(100%);--lift-amount:calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:0;overflow-wrap:anywhere}[data-sonner-toast][data-styled=true]{padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px rgba(0,0,0,.1);width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}[data-sonner-toast]:focus-visible{box-shadow:0 4px 12px rgba(0,0,0,.1),0 0 0 2px rgba(0,0,0,.2)}[data-sonner-toast][data-y-position=top]{top:0;--y:translateY(-100%);--lift:1;--lift-amount:calc(1 * var(--gap))}[data-sonner-toast][data-y-position=bottom]{bottom:0;--y:translateY(100%);--lift:-1;--lift-amount:calc(var(--lift) * var(--gap))}[data-sonner-toast][data-styled=true] [data-description]{font-weight:400;line-height:1.4;color:#3f3f3f}[data-rich-colors=true][data-sonner-toast][data-styled=true] [data-description]{color:inherit}[data-sonner-toaster][data-sonner-theme=dark] [data-description]{color:#e8e8e8}[data-sonner-toast][data-styled=true] [data-title]{font-weight:500;line-height:1.5;color:inherit}[data-sonner-toast][data-styled=true] [data-icon]{display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}[data-sonner-toast][data-promise=true] [data-icon]>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}[data-sonner-toast][data-styled=true] [data-icon]>*{flex-shrink:0}[data-sonner-toast][data-styled=true] [data-icon] svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}[data-sonner-toast][data-styled=true] [data-content]{display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;font-weight:500;cursor:pointer;outline:0;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}[data-sonner-toast][data-styled=true] [data-button]:focus-visible{box-shadow:0 0 0 2px rgba(0,0,0,.4)}[data-sonner-toast][data-styled=true] [data-button]:first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}[data-sonner-toast][data-styled=true] [data-cancel]{color:var(--normal-text);background:rgba(0,0,0,.08)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast][data-styled=true] [data-cancel]{background:rgba(255,255,255,.3)}[data-sonner-toast][data-styled=true] [data-close-button]{position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;color:var(--gray12);background:var(--normal-bg);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}[data-sonner-toast][data-styled=true] [data-close-button]:focus-visible{box-shadow:0 4px 12px rgba(0,0,0,.1),0 0 0 2px rgba(0,0,0,.2)}[data-sonner-toast][data-styled=true] [data-disabled=true]{cursor:not-allowed}[data-sonner-toast][data-styled=true]:hover [data-close-button]:hover{background:var(--gray2);border-color:var(--gray5)}[data-sonner-toast][data-swiping=true]::before{content:'';position:absolute;left:-100%;right:-100%;height:100%;z-index:-1}[data-sonner-toast][data-y-position=top][data-swiping=true]::before{bottom:50%;transform:scaleY(3) translateY(50%)}[data-sonner-toast][data-y-position=bottom][data-swiping=true]::before{top:50%;transform:scaleY(3) translateY(-50%)}[data-sonner-toast][data-swiping=false][data-removed=true]::before{content:'';position:absolute;inset:0;transform:scaleY(2)}[data-sonner-toast][data-expanded=true]::after{content:'';position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}[data-sonner-toast][data-mounted=true]{--y:translateY(0);opacity:1}[data-sonner-toast][data-expanded=false][data-front=false]{--scale:var(--toasts-before) * 0.05 + 1;--y:translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}[data-sonner-toast]>*{transition:opacity .4s}[data-sonner-toast][data-x-position=right]{right:0}[data-sonner-toast][data-x-position=left]{left:0}[data-sonner-toast][data-expanded=false][data-front=false][data-styled=true]>*{opacity:0}[data-sonner-toast][data-visible=false]{opacity:0;pointer-events:none}[data-sonner-toast][data-mounted=true][data-expanded=true]{--y:translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}[data-sonner-toast][data-removed=true][data-front=true][data-swipe-out=false]{--y:translateY(calc(var(--lift) * -100%));opacity:0}[data-sonner-toast][data-removed=true][data-front=false][data-swipe-out=false][data-expanded=true]{--y:translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}[data-sonner-toast][data-removed=true][data-front=false][data-swipe-out=false][data-expanded=false]{--y:translateY(40%);opacity:0;transition:transform .5s,opacity .2s}[data-sonner-toast][data-removed=true][data-front=false]::before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount-y,0)) translateX(var(--swipe-amount-x,0));transition:none}[data-sonner-toast][data-swiped=true]{user-select:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation-duration:.2s;animation-timing-function:ease-out;animation-fill-mode:forwards}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=left]{animation-name:swipe-out-left}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=right]{animation-name:swipe-out-right}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=up]{animation-name:swipe-out-up}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=down]{animation-name:swipe-out-down}@keyframes swipe-out-left{from{transform:var(--y) translateX(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translateX(calc(var(--swipe-amount-x) - 100%));opacity:0}}@keyframes swipe-out-right{from{transform:var(--y) translateX(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translateX(calc(var(--swipe-amount-x) + 100%));opacity:0}}@keyframes swipe-out-up{from{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) - 100%));opacity:0}}@keyframes swipe-out-down{from{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) + 100%));opacity:0}}@media (max-width:600px){[data-sonner-toaster]{position:fixed;right:var(--mobile-offset-right);left:var(--mobile-offset-left);width:100%}[data-sonner-toaster][dir=rtl]{left:calc(var(--mobile-offset-left) * -1)}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset-left) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset-left)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--mobile-offset-bottom)}[data-sonner-toaster][data-y-position=top]{top:var(--mobile-offset-top)}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset-left);right:var(--mobile-offset-right);transform:none}}[data-sonner-toaster][data-sonner-theme=light]{--normal-bg:#fff;--normal-border:var(--gray4);--normal-text:var(--gray12);--success-bg:hsl(143, 85%, 96%);--success-border:hsl(145, 92%, 87%);--success-text:hsl(140, 100%, 27%);--info-bg:hsl(208, 100%, 97%);--info-border:hsl(221, 91%, 93%);--info-text:hsl(210, 92%, 45%);--warning-bg:hsl(49, 100%, 97%);--warning-border:hsl(49, 91%, 84%);--warning-text:hsl(31, 92%, 45%);--error-bg:hsl(359, 100%, 97%);--error-border:hsl(359, 100%, 94%);--error-text:hsl(360, 100%, 45%)}[data-sonner-toaster][data-sonner-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg:#000;--normal-border:hsl(0, 0%, 20%);--normal-text:var(--gray1)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg:#fff;--normal-border:var(--gray3);--normal-text:var(--gray12)}[data-sonner-toaster][data-sonner-theme=dark]{--normal-bg:#000;--normal-bg-hover:hsl(0, 0%, 12%);--normal-border:hsl(0, 0%, 20%);--normal-border-hover:hsl(0, 0%, 25%);--normal-text:var(--gray1);--success-bg:hsl(150, 100%, 6%);--success-border:hsl(147, 100%, 12%);--success-text:hsl(150, 86%, 65%);--info-bg:hsl(215, 100%, 6%);--info-border:hsl(223, 43%, 17%);--info-text:hsl(216, 87%, 65%);--warning-bg:hsl(64, 100%, 6%);--warning-border:hsl(60, 100%, 9%);--warning-text:hsl(46, 87%, 65%);--error-bg:hsl(358, 76%, 10%);--error-border:hsl(357, 89%, 16%);--error-text:hsl(358, 100%, 81%)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast] [data-close-button]{background:var(--normal-bg);border-color:var(--normal-border);color:var(--normal-text)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast] [data-close-button]:hover{background:var(--normal-bg-hover);border-color:var(--normal-border-hover)}[data-rich-colors=true][data-sonner-toast][data-type=success]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size:16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:first-child{animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}100%{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}100%{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}100%{opacity:.15}}@media (prefers-reduced-motion){.sonner-loading-bar,[data-sonner-toast],[data-sonner-toast]>*{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}\");\nfunction isAction(action) {\n    return action.label !== undefined;\n}\n// Visible toasts amount\nconst VISIBLE_TOASTS_AMOUNT = 3;\n// Viewport padding\nconst VIEWPORT_OFFSET = '24px';\n// Mobile viewport padding\nconst MOBILE_VIEWPORT_OFFSET = '16px';\n// Default lifetime of a toasts (in ms)\nconst TOAST_LIFETIME = 4000;\n// Default toast width\nconst TOAST_WIDTH = 356;\n// Default gap between toasts\nconst GAP = 14;\n// Threshold to dismiss a toast\nconst SWIPE_THRESHOLD = 45;\n// Equal to exit animation duration\nconst TIME_BEFORE_UNMOUNT = 200;\nfunction cn() {\n    for(var _len = arguments.length, classes = new Array(_len), _key = 0; _key < _len; _key++){\n        classes[_key] = arguments[_key];\n    }\n    return classes.filter(Boolean).join(' ');\n}\nfunction getDefaultSwipeDirections(position) {\n    const [y, x] = position.split('-');\n    const directions = [];\n    if (y) {\n        directions.push(y);\n    }\n    if (x) {\n        directions.push(x);\n    }\n    return directions;\n}\nconst Toast = (props)=>{\n    _s1();\n    var _toast_classNames, _toast_classNames1, _toast_classNames2, _toast_classNames3, _toast_classNames4, _toast_classNames5, _toast_classNames6, _toast_classNames7, _toast_classNames8;\n    const { invert: ToasterInvert, toast, unstyled, interacting, setHeights, visibleToasts, heights, index, toasts, expanded, removeToast, defaultRichColors, closeButton: closeButtonFromToaster, style, cancelButtonStyle, actionButtonStyle, className = '', descriptionClassName = '', duration: durationFromToaster, position, gap, expandByDefault, classNames, icons, closeButtonAriaLabel = 'Close toast' } = props;\n    const [swipeDirection, setSwipeDirection] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [swipeOutDirection, setSwipeOutDirection] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [mounted, setMounted] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [removed, setRemoved] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [swiping, setSwiping] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [swipeOut, setSwipeOut] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [isSwiped, setIsSwiped] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [offsetBeforeRemove, setOffsetBeforeRemove] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const [initialHeight, setInitialHeight] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const remainingTime = react__WEBPACK_IMPORTED_MODULE_0__.useRef(toast.duration || durationFromToaster || TOAST_LIFETIME);\n    const dragStartTime = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const toastRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const isFront = index === 0;\n    const isVisible = index + 1 <= visibleToasts;\n    const toastType = toast.type;\n    const dismissible = toast.dismissible !== false;\n    const toastClassname = toast.className || '';\n    const toastDescriptionClassname = toast.descriptionClassName || '';\n    // Height index is used to calculate the offset as it gets updated before the toast array, which means we can calculate the new layout faster.\n    const heightIndex = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"Toast.useMemo[heightIndex]\": ()=>heights.findIndex({\n                \"Toast.useMemo[heightIndex]\": (height)=>height.toastId === toast.id\n            }[\"Toast.useMemo[heightIndex]\"]) || 0\n    }[\"Toast.useMemo[heightIndex]\"], [\n        heights,\n        toast.id\n    ]);\n    const closeButton = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"Toast.useMemo[closeButton]\": ()=>{\n            var _toast_closeButton;\n            return (_toast_closeButton = toast.closeButton) != null ? _toast_closeButton : closeButtonFromToaster;\n        }\n    }[\"Toast.useMemo[closeButton]\"], [\n        toast.closeButton,\n        closeButtonFromToaster\n    ]);\n    const duration = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"Toast.useMemo[duration]\": ()=>toast.duration || durationFromToaster || TOAST_LIFETIME\n    }[\"Toast.useMemo[duration]\"], [\n        toast.duration,\n        durationFromToaster\n    ]);\n    const closeTimerStartTimeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const offset = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const lastCloseTimerStartTimeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const pointerStartRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const [y, x] = position.split('-');\n    const toastsHeightBefore = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"Toast.useMemo[toastsHeightBefore]\": ()=>{\n            return heights.reduce({\n                \"Toast.useMemo[toastsHeightBefore]\": (prev, curr, reducerIndex)=>{\n                    // Calculate offset up until current toast\n                    if (reducerIndex >= heightIndex) {\n                        return prev;\n                    }\n                    return prev + curr.height;\n                }\n            }[\"Toast.useMemo[toastsHeightBefore]\"], 0);\n        }\n    }[\"Toast.useMemo[toastsHeightBefore]\"], [\n        heights,\n        heightIndex\n    ]);\n    const isDocumentHidden = useIsDocumentHidden();\n    const invert = toast.invert || ToasterInvert;\n    const disabled = toastType === 'loading';\n    offset.current = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"Toast.useMemo\": ()=>heightIndex * gap + toastsHeightBefore\n    }[\"Toast.useMemo\"], [\n        heightIndex,\n        toastsHeightBefore\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toast.useEffect\": ()=>{\n            remainingTime.current = duration;\n        }\n    }[\"Toast.useEffect\"], [\n        duration\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toast.useEffect\": ()=>{\n            // Trigger enter animation without using CSS animation\n            setMounted(true);\n        }\n    }[\"Toast.useEffect\"], []);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toast.useEffect\": ()=>{\n            const toastNode = toastRef.current;\n            if (toastNode) {\n                const height = toastNode.getBoundingClientRect().height;\n                // Add toast height to heights array after the toast is mounted\n                setInitialHeight(height);\n                setHeights({\n                    \"Toast.useEffect\": (h)=>[\n                            {\n                                toastId: toast.id,\n                                height,\n                                position: toast.position\n                            },\n                            ...h\n                        ]\n                }[\"Toast.useEffect\"]);\n                return ({\n                    \"Toast.useEffect\": ()=>setHeights({\n                            \"Toast.useEffect\": (h)=>h.filter({\n                                    \"Toast.useEffect\": (height)=>height.toastId !== toast.id\n                                }[\"Toast.useEffect\"])\n                        }[\"Toast.useEffect\"])\n                })[\"Toast.useEffect\"];\n            }\n        }\n    }[\"Toast.useEffect\"], [\n        setHeights,\n        toast.id\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect({\n        \"Toast.useLayoutEffect\": ()=>{\n            // Keep height up to date with the content in case it updates\n            if (!mounted) return;\n            const toastNode = toastRef.current;\n            const originalHeight = toastNode.style.height;\n            toastNode.style.height = 'auto';\n            const newHeight = toastNode.getBoundingClientRect().height;\n            toastNode.style.height = originalHeight;\n            setInitialHeight(newHeight);\n            setHeights({\n                \"Toast.useLayoutEffect\": (heights)=>{\n                    const alreadyExists = heights.find({\n                        \"Toast.useLayoutEffect.alreadyExists\": (height)=>height.toastId === toast.id\n                    }[\"Toast.useLayoutEffect.alreadyExists\"]);\n                    if (!alreadyExists) {\n                        return [\n                            {\n                                toastId: toast.id,\n                                height: newHeight,\n                                position: toast.position\n                            },\n                            ...heights\n                        ];\n                    } else {\n                        return heights.map({\n                            \"Toast.useLayoutEffect\": (height)=>height.toastId === toast.id ? {\n                                    ...height,\n                                    height: newHeight\n                                } : height\n                        }[\"Toast.useLayoutEffect\"]);\n                    }\n                }\n            }[\"Toast.useLayoutEffect\"]);\n        }\n    }[\"Toast.useLayoutEffect\"], [\n        mounted,\n        toast.title,\n        toast.description,\n        setHeights,\n        toast.id,\n        toast.jsx,\n        toast.action,\n        toast.cancel\n    ]);\n    const deleteToast = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"Toast.useCallback[deleteToast]\": ()=>{\n            // Save the offset for the exit swipe animation\n            setRemoved(true);\n            setOffsetBeforeRemove(offset.current);\n            setHeights({\n                \"Toast.useCallback[deleteToast]\": (h)=>h.filter({\n                        \"Toast.useCallback[deleteToast]\": (height)=>height.toastId !== toast.id\n                    }[\"Toast.useCallback[deleteToast]\"])\n            }[\"Toast.useCallback[deleteToast]\"]);\n            setTimeout({\n                \"Toast.useCallback[deleteToast]\": ()=>{\n                    removeToast(toast);\n                }\n            }[\"Toast.useCallback[deleteToast]\"], TIME_BEFORE_UNMOUNT);\n        }\n    }[\"Toast.useCallback[deleteToast]\"], [\n        toast,\n        removeToast,\n        setHeights,\n        offset\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toast.useEffect\": ()=>{\n            if (toast.promise && toastType === 'loading' || toast.duration === Infinity || toast.type === 'loading') return;\n            let timeoutId;\n            // Pause the timer on each hover\n            const pauseTimer = {\n                \"Toast.useEffect.pauseTimer\": ()=>{\n                    if (lastCloseTimerStartTimeRef.current < closeTimerStartTimeRef.current) {\n                        // Get the elapsed time since the timer started\n                        const elapsedTime = new Date().getTime() - closeTimerStartTimeRef.current;\n                        remainingTime.current = remainingTime.current - elapsedTime;\n                    }\n                    lastCloseTimerStartTimeRef.current = new Date().getTime();\n                }\n            }[\"Toast.useEffect.pauseTimer\"];\n            const startTimer = {\n                \"Toast.useEffect.startTimer\": ()=>{\n                    // setTimeout(, Infinity) behaves as if the delay is 0.\n                    // As a result, the toast would be closed immediately, giving the appearance that it was never rendered.\n                    // See: https://github.com/denysdovhan/wtfjs?tab=readme-ov-file#an-infinite-timeout\n                    if (remainingTime.current === Infinity) return;\n                    closeTimerStartTimeRef.current = new Date().getTime();\n                    // Let the toast know it has started\n                    timeoutId = setTimeout({\n                        \"Toast.useEffect.startTimer\": ()=>{\n                            toast.onAutoClose == null ? void 0 : toast.onAutoClose.call(toast, toast);\n                            deleteToast();\n                        }\n                    }[\"Toast.useEffect.startTimer\"], remainingTime.current);\n                }\n            }[\"Toast.useEffect.startTimer\"];\n            if (expanded || interacting || isDocumentHidden) {\n                pauseTimer();\n            } else {\n                startTimer();\n            }\n            return ({\n                \"Toast.useEffect\": ()=>clearTimeout(timeoutId)\n            })[\"Toast.useEffect\"];\n        }\n    }[\"Toast.useEffect\"], [\n        expanded,\n        interacting,\n        toast,\n        toastType,\n        isDocumentHidden,\n        deleteToast\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toast.useEffect\": ()=>{\n            if (toast.delete) {\n                deleteToast();\n                toast.onDismiss == null ? void 0 : toast.onDismiss.call(toast, toast);\n            }\n        }\n    }[\"Toast.useEffect\"], [\n        deleteToast,\n        toast.delete\n    ]);\n    function getLoadingIcon() {\n        var _toast_classNames;\n        if (icons == null ? void 0 : icons.loading) {\n            var _toast_classNames1;\n            return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n                className: cn(classNames == null ? void 0 : classNames.loader, toast == null ? void 0 : (_toast_classNames1 = toast.classNames) == null ? void 0 : _toast_classNames1.loader, 'sonner-loader'),\n                \"data-visible\": toastType === 'loading'\n            }, icons.loading);\n        }\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(Loader, {\n            className: cn(classNames == null ? void 0 : classNames.loader, toast == null ? void 0 : (_toast_classNames = toast.classNames) == null ? void 0 : _toast_classNames.loader),\n            visible: toastType === 'loading'\n        });\n    }\n    const icon = toast.icon || (icons == null ? void 0 : icons[toastType]) || getAsset(toastType);\n    var _toast_richColors, _icons_close;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"li\", {\n        tabIndex: 0,\n        ref: toastRef,\n        className: cn(className, toastClassname, classNames == null ? void 0 : classNames.toast, toast == null ? void 0 : (_toast_classNames = toast.classNames) == null ? void 0 : _toast_classNames.toast, classNames == null ? void 0 : classNames.default, classNames == null ? void 0 : classNames[toastType], toast == null ? void 0 : (_toast_classNames1 = toast.classNames) == null ? void 0 : _toast_classNames1[toastType]),\n        \"data-sonner-toast\": \"\",\n        \"data-rich-colors\": (_toast_richColors = toast.richColors) != null ? _toast_richColors : defaultRichColors,\n        \"data-styled\": !Boolean(toast.jsx || toast.unstyled || unstyled),\n        \"data-mounted\": mounted,\n        \"data-promise\": Boolean(toast.promise),\n        \"data-swiped\": isSwiped,\n        \"data-removed\": removed,\n        \"data-visible\": isVisible,\n        \"data-y-position\": y,\n        \"data-x-position\": x,\n        \"data-index\": index,\n        \"data-front\": isFront,\n        \"data-swiping\": swiping,\n        \"data-dismissible\": dismissible,\n        \"data-type\": toastType,\n        \"data-invert\": invert,\n        \"data-swipe-out\": swipeOut,\n        \"data-swipe-direction\": swipeOutDirection,\n        \"data-expanded\": Boolean(expanded || expandByDefault && mounted),\n        \"data-testid\": toast.testId,\n        style: {\n            '--index': index,\n            '--toasts-before': index,\n            '--z-index': toasts.length - index,\n            '--offset': \"\".concat(removed ? offsetBeforeRemove : offset.current, \"px\"),\n            '--initial-height': expandByDefault ? 'auto' : \"\".concat(initialHeight, \"px\"),\n            ...style,\n            ...toast.style\n        },\n        onDragEnd: ()=>{\n            setSwiping(false);\n            setSwipeDirection(null);\n            pointerStartRef.current = null;\n        },\n        onPointerDown: (event)=>{\n            if (event.button === 2) return; // Return early on right click\n            if (disabled || !dismissible) return;\n            dragStartTime.current = new Date();\n            setOffsetBeforeRemove(offset.current);\n            // Ensure we maintain correct pointer capture even when going outside of the toast (e.g. when swiping)\n            event.target.setPointerCapture(event.pointerId);\n            if (event.target.tagName === 'BUTTON') return;\n            setSwiping(true);\n            pointerStartRef.current = {\n                x: event.clientX,\n                y: event.clientY\n            };\n        },\n        onPointerUp: ()=>{\n            var _toastRef_current, _toastRef_current1, _dragStartTime_current;\n            if (swipeOut || !dismissible) return;\n            pointerStartRef.current = null;\n            const swipeAmountX = Number(((_toastRef_current = toastRef.current) == null ? void 0 : _toastRef_current.style.getPropertyValue('--swipe-amount-x').replace('px', '')) || 0);\n            const swipeAmountY = Number(((_toastRef_current1 = toastRef.current) == null ? void 0 : _toastRef_current1.style.getPropertyValue('--swipe-amount-y').replace('px', '')) || 0);\n            const timeTaken = new Date().getTime() - ((_dragStartTime_current = dragStartTime.current) == null ? void 0 : _dragStartTime_current.getTime());\n            const swipeAmount = swipeDirection === 'x' ? swipeAmountX : swipeAmountY;\n            const velocity = Math.abs(swipeAmount) / timeTaken;\n            if (Math.abs(swipeAmount) >= SWIPE_THRESHOLD || velocity > 0.11) {\n                setOffsetBeforeRemove(offset.current);\n                toast.onDismiss == null ? void 0 : toast.onDismiss.call(toast, toast);\n                if (swipeDirection === 'x') {\n                    setSwipeOutDirection(swipeAmountX > 0 ? 'right' : 'left');\n                } else {\n                    setSwipeOutDirection(swipeAmountY > 0 ? 'down' : 'up');\n                }\n                deleteToast();\n                setSwipeOut(true);\n                return;\n            } else {\n                var _toastRef_current2, _toastRef_current3;\n                (_toastRef_current2 = toastRef.current) == null ? void 0 : _toastRef_current2.style.setProperty('--swipe-amount-x', \"0px\");\n                (_toastRef_current3 = toastRef.current) == null ? void 0 : _toastRef_current3.style.setProperty('--swipe-amount-y', \"0px\");\n            }\n            setIsSwiped(false);\n            setSwiping(false);\n            setSwipeDirection(null);\n        },\n        onPointerMove: (event)=>{\n            var _window_getSelection, _toastRef_current, _toastRef_current1;\n            if (!pointerStartRef.current || !dismissible) return;\n            const isHighlighted = ((_window_getSelection = window.getSelection()) == null ? void 0 : _window_getSelection.toString().length) > 0;\n            if (isHighlighted) return;\n            const yDelta = event.clientY - pointerStartRef.current.y;\n            const xDelta = event.clientX - pointerStartRef.current.x;\n            var _props_swipeDirections;\n            const swipeDirections = (_props_swipeDirections = props.swipeDirections) != null ? _props_swipeDirections : getDefaultSwipeDirections(position);\n            // Determine swipe direction if not already locked\n            if (!swipeDirection && (Math.abs(xDelta) > 1 || Math.abs(yDelta) > 1)) {\n                setSwipeDirection(Math.abs(xDelta) > Math.abs(yDelta) ? 'x' : 'y');\n            }\n            let swipeAmount = {\n                x: 0,\n                y: 0\n            };\n            const getDampening = (delta)=>{\n                const factor = Math.abs(delta) / 20;\n                return 1 / (1.5 + factor);\n            };\n            // Only apply swipe in the locked direction\n            if (swipeDirection === 'y') {\n                // Handle vertical swipes\n                if (swipeDirections.includes('top') || swipeDirections.includes('bottom')) {\n                    if (swipeDirections.includes('top') && yDelta < 0 || swipeDirections.includes('bottom') && yDelta > 0) {\n                        swipeAmount.y = yDelta;\n                    } else {\n                        // Smoothly transition to dampened movement\n                        const dampenedDelta = yDelta * getDampening(yDelta);\n                        // Ensure we don't jump when transitioning to dampened movement\n                        swipeAmount.y = Math.abs(dampenedDelta) < Math.abs(yDelta) ? dampenedDelta : yDelta;\n                    }\n                }\n            } else if (swipeDirection === 'x') {\n                // Handle horizontal swipes\n                if (swipeDirections.includes('left') || swipeDirections.includes('right')) {\n                    if (swipeDirections.includes('left') && xDelta < 0 || swipeDirections.includes('right') && xDelta > 0) {\n                        swipeAmount.x = xDelta;\n                    } else {\n                        // Smoothly transition to dampened movement\n                        const dampenedDelta = xDelta * getDampening(xDelta);\n                        // Ensure we don't jump when transitioning to dampened movement\n                        swipeAmount.x = Math.abs(dampenedDelta) < Math.abs(xDelta) ? dampenedDelta : xDelta;\n                    }\n                }\n            }\n            if (Math.abs(swipeAmount.x) > 0 || Math.abs(swipeAmount.y) > 0) {\n                setIsSwiped(true);\n            }\n            (_toastRef_current = toastRef.current) == null ? void 0 : _toastRef_current.style.setProperty('--swipe-amount-x', \"\".concat(swipeAmount.x, \"px\"));\n            (_toastRef_current1 = toastRef.current) == null ? void 0 : _toastRef_current1.style.setProperty('--swipe-amount-y', \"\".concat(swipeAmount.y, \"px\"));\n        }\n    }, closeButton && !toast.jsx && toastType !== 'loading' ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"button\", {\n        \"aria-label\": closeButtonAriaLabel,\n        \"data-disabled\": disabled,\n        \"data-close-button\": true,\n        onClick: disabled || !dismissible ? ()=>{} : ()=>{\n            deleteToast();\n            toast.onDismiss == null ? void 0 : toast.onDismiss.call(toast, toast);\n        },\n        className: cn(classNames == null ? void 0 : classNames.closeButton, toast == null ? void 0 : (_toast_classNames2 = toast.classNames) == null ? void 0 : _toast_classNames2.closeButton)\n    }, (_icons_close = icons == null ? void 0 : icons.close) != null ? _icons_close : CloseIcon) : null, (toastType || toast.icon || toast.promise) && toast.icon !== null && ((icons == null ? void 0 : icons[toastType]) !== null || toast.icon) ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        \"data-icon\": \"\",\n        className: cn(classNames == null ? void 0 : classNames.icon, toast == null ? void 0 : (_toast_classNames3 = toast.classNames) == null ? void 0 : _toast_classNames3.icon)\n    }, toast.promise || toast.type === 'loading' && !toast.icon ? toast.icon || getLoadingIcon() : null, toast.type !== 'loading' ? icon : null) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        \"data-content\": \"\",\n        className: cn(classNames == null ? void 0 : classNames.content, toast == null ? void 0 : (_toast_classNames4 = toast.classNames) == null ? void 0 : _toast_classNames4.content)\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        \"data-title\": \"\",\n        className: cn(classNames == null ? void 0 : classNames.title, toast == null ? void 0 : (_toast_classNames5 = toast.classNames) == null ? void 0 : _toast_classNames5.title)\n    }, toast.jsx ? toast.jsx : typeof toast.title === 'function' ? toast.title() : toast.title), toast.description ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        \"data-description\": \"\",\n        className: cn(descriptionClassName, toastDescriptionClassname, classNames == null ? void 0 : classNames.description, toast == null ? void 0 : (_toast_classNames6 = toast.classNames) == null ? void 0 : _toast_classNames6.description)\n    }, typeof toast.description === 'function' ? toast.description() : toast.description) : null), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(toast.cancel) ? toast.cancel : toast.cancel && isAction(toast.cancel) ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"button\", {\n        \"data-button\": true,\n        \"data-cancel\": true,\n        style: toast.cancelButtonStyle || cancelButtonStyle,\n        onClick: (event)=>{\n            // We need to check twice because typescript\n            if (!isAction(toast.cancel)) return;\n            if (!dismissible) return;\n            toast.cancel.onClick == null ? void 0 : toast.cancel.onClick.call(toast.cancel, event);\n            deleteToast();\n        },\n        className: cn(classNames == null ? void 0 : classNames.cancelButton, toast == null ? void 0 : (_toast_classNames7 = toast.classNames) == null ? void 0 : _toast_classNames7.cancelButton)\n    }, toast.cancel.label) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(toast.action) ? toast.action : toast.action && isAction(toast.action) ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"button\", {\n        \"data-button\": true,\n        \"data-action\": true,\n        style: toast.actionButtonStyle || actionButtonStyle,\n        onClick: (event)=>{\n            // We need to check twice because typescript\n            if (!isAction(toast.action)) return;\n            toast.action.onClick == null ? void 0 : toast.action.onClick.call(toast.action, event);\n            if (event.defaultPrevented) return;\n            deleteToast();\n        },\n        className: cn(classNames == null ? void 0 : classNames.actionButton, toast == null ? void 0 : (_toast_classNames8 = toast.classNames) == null ? void 0 : _toast_classNames8.actionButton)\n    }, toast.action.label) : null);\n};\n_s1(Toast, \"Hs2RwklMUctKsF2fEbXUzesmn3w=\", false, function() {\n    return [\n        useIsDocumentHidden\n    ];\n});\n_c1 = Toast;\nfunction getDocumentDirection() {\n    if (typeof window === 'undefined') return 'ltr';\n    if (typeof document === 'undefined') return 'ltr'; // For Fresh purpose\n    const dirAttribute = document.documentElement.getAttribute('dir');\n    if (dirAttribute === 'auto' || !dirAttribute) {\n        return window.getComputedStyle(document.documentElement).direction;\n    }\n    return dirAttribute;\n}\nfunction assignOffset(defaultOffset, mobileOffset) {\n    const styles = {};\n    [\n        defaultOffset,\n        mobileOffset\n    ].forEach((offset, index)=>{\n        const isMobile = index === 1;\n        const prefix = isMobile ? '--mobile-offset' : '--offset';\n        const defaultValue = isMobile ? MOBILE_VIEWPORT_OFFSET : VIEWPORT_OFFSET;\n        function assignAll(offset) {\n            [\n                'top',\n                'right',\n                'bottom',\n                'left'\n            ].forEach((key)=>{\n                styles[\"\".concat(prefix, \"-\").concat(key)] = typeof offset === 'number' ? \"\".concat(offset, \"px\") : offset;\n            });\n        }\n        if (typeof offset === 'number' || typeof offset === 'string') {\n            assignAll(offset);\n        } else if (typeof offset === 'object') {\n            [\n                'top',\n                'right',\n                'bottom',\n                'left'\n            ].forEach((key)=>{\n                if (offset[key] === undefined) {\n                    styles[\"\".concat(prefix, \"-\").concat(key)] = defaultValue;\n                } else {\n                    styles[\"\".concat(prefix, \"-\").concat(key)] = typeof offset[key] === 'number' ? \"\".concat(offset[key], \"px\") : offset[key];\n                }\n            });\n        } else {\n            assignAll(defaultValue);\n        }\n    });\n    return styles;\n}\nfunction useSonner() {\n    _s2();\n    const [activeToasts, setActiveToasts] = react__WEBPACK_IMPORTED_MODULE_0__.useState([]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useSonner.useEffect\": ()=>{\n            return ToastState.subscribe({\n                \"useSonner.useEffect\": (toast)=>{\n                    if (toast.dismiss) {\n                        setTimeout({\n                            \"useSonner.useEffect\": ()=>{\n                                react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync({\n                                    \"useSonner.useEffect\": ()=>{\n                                        setActiveToasts({\n                                            \"useSonner.useEffect\": (toasts)=>toasts.filter({\n                                                    \"useSonner.useEffect\": (t)=>t.id !== toast.id\n                                                }[\"useSonner.useEffect\"])\n                                        }[\"useSonner.useEffect\"]);\n                                    }\n                                }[\"useSonner.useEffect\"]);\n                            }\n                        }[\"useSonner.useEffect\"]);\n                        return;\n                    }\n                    // Prevent batching, temp solution.\n                    setTimeout({\n                        \"useSonner.useEffect\": ()=>{\n                            react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync({\n                                \"useSonner.useEffect\": ()=>{\n                                    setActiveToasts({\n                                        \"useSonner.useEffect\": (toasts)=>{\n                                            const indexOfExistingToast = toasts.findIndex({\n                                                \"useSonner.useEffect.indexOfExistingToast\": (t)=>t.id === toast.id\n                                            }[\"useSonner.useEffect.indexOfExistingToast\"]);\n                                            // Update the toast if it already exists\n                                            if (indexOfExistingToast !== -1) {\n                                                return [\n                                                    ...toasts.slice(0, indexOfExistingToast),\n                                                    {\n                                                        ...toasts[indexOfExistingToast],\n                                                        ...toast\n                                                    },\n                                                    ...toasts.slice(indexOfExistingToast + 1)\n                                                ];\n                                            }\n                                            return [\n                                                toast,\n                                                ...toasts\n                                            ];\n                                        }\n                                    }[\"useSonner.useEffect\"]);\n                                }\n                            }[\"useSonner.useEffect\"]);\n                        }\n                    }[\"useSonner.useEffect\"]);\n                }\n            }[\"useSonner.useEffect\"]);\n        }\n    }[\"useSonner.useEffect\"], []);\n    return {\n        toasts: activeToasts\n    };\n}\n_s2(useSonner, \"wvKkrpl8d9UBJsfUcWYgFEOa7SA=\");\nconst Toaster = /*#__PURE__*/ _s3(react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(_c2 = _s3(function Toaster(props, ref) {\n    _s3();\n    const { id, invert, position = 'bottom-right', hotkey = [\n        'altKey',\n        'KeyT'\n    ], expand, closeButton, className, offset, mobileOffset, theme = 'light', richColors, duration, style, visibleToasts = VISIBLE_TOASTS_AMOUNT, toastOptions, dir = getDocumentDirection(), gap = GAP, icons, containerAriaLabel = 'Notifications' } = props;\n    const [toasts, setToasts] = react__WEBPACK_IMPORTED_MODULE_0__.useState([]);\n    const filteredToasts = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"Toaster.Toaster.useMemo[filteredToasts]\": ()=>{\n            if (id) {\n                return toasts.filter({\n                    \"Toaster.Toaster.useMemo[filteredToasts]\": (toast)=>toast.toasterId === id\n                }[\"Toaster.Toaster.useMemo[filteredToasts]\"]);\n            }\n            return toasts.filter({\n                \"Toaster.Toaster.useMemo[filteredToasts]\": (toast)=>!toast.toasterId\n            }[\"Toaster.Toaster.useMemo[filteredToasts]\"]);\n        }\n    }[\"Toaster.Toaster.useMemo[filteredToasts]\"], [\n        toasts,\n        id\n    ]);\n    const possiblePositions = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"Toaster.Toaster.useMemo[possiblePositions]\": ()=>{\n            return Array.from(new Set([\n                position\n            ].concat(filteredToasts.filter({\n                \"Toaster.Toaster.useMemo[possiblePositions]\": (toast)=>toast.position\n            }[\"Toaster.Toaster.useMemo[possiblePositions]\"]).map({\n                \"Toaster.Toaster.useMemo[possiblePositions]\": (toast)=>toast.position\n            }[\"Toaster.Toaster.useMemo[possiblePositions]\"]))));\n        }\n    }[\"Toaster.Toaster.useMemo[possiblePositions]\"], [\n        filteredToasts,\n        position\n    ]);\n    const [heights, setHeights] = react__WEBPACK_IMPORTED_MODULE_0__.useState([]);\n    const [expanded, setExpanded] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [interacting, setInteracting] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [actualTheme, setActualTheme] = react__WEBPACK_IMPORTED_MODULE_0__.useState(theme !== 'system' ? theme : typeof window !== 'undefined' ? window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light' : 'light');\n    const listRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const hotkeyLabel = hotkey.join('+').replace(/Key/g, '').replace(/Digit/g, '');\n    const lastFocusedElementRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const isFocusWithinRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const removeToast = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"Toaster.Toaster.useCallback[removeToast]\": (toastToRemove)=>{\n            setToasts({\n                \"Toaster.Toaster.useCallback[removeToast]\": (toasts)=>{\n                    var _toasts_find;\n                    if (!((_toasts_find = toasts.find({\n                        \"Toaster.Toaster.useCallback[removeToast]\": (toast)=>toast.id === toastToRemove.id\n                    }[\"Toaster.Toaster.useCallback[removeToast]\"])) == null ? void 0 : _toasts_find.delete)) {\n                        ToastState.dismiss(toastToRemove.id);\n                    }\n                    return toasts.filter({\n                        \"Toaster.Toaster.useCallback[removeToast]\": (param)=>{\n                            let { id } = param;\n                            return id !== toastToRemove.id;\n                        }\n                    }[\"Toaster.Toaster.useCallback[removeToast]\"]);\n                }\n            }[\"Toaster.Toaster.useCallback[removeToast]\"]);\n        }\n    }[\"Toaster.Toaster.useCallback[removeToast]\"], []);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toaster.Toaster.useEffect\": ()=>{\n            return ToastState.subscribe({\n                \"Toaster.Toaster.useEffect\": (toast)=>{\n                    if (toast.dismiss) {\n                        // Prevent batching of other state updates\n                        requestAnimationFrame({\n                            \"Toaster.Toaster.useEffect\": ()=>{\n                                setToasts({\n                                    \"Toaster.Toaster.useEffect\": (toasts)=>toasts.map({\n                                            \"Toaster.Toaster.useEffect\": (t)=>t.id === toast.id ? {\n                                                    ...t,\n                                                    delete: true\n                                                } : t\n                                        }[\"Toaster.Toaster.useEffect\"])\n                                }[\"Toaster.Toaster.useEffect\"]);\n                            }\n                        }[\"Toaster.Toaster.useEffect\"]);\n                        return;\n                    }\n                    // Prevent batching, temp solution.\n                    setTimeout({\n                        \"Toaster.Toaster.useEffect\": ()=>{\n                            react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync({\n                                \"Toaster.Toaster.useEffect\": ()=>{\n                                    setToasts({\n                                        \"Toaster.Toaster.useEffect\": (toasts)=>{\n                                            const indexOfExistingToast = toasts.findIndex({\n                                                \"Toaster.Toaster.useEffect.indexOfExistingToast\": (t)=>t.id === toast.id\n                                            }[\"Toaster.Toaster.useEffect.indexOfExistingToast\"]);\n                                            // Update the toast if it already exists\n                                            if (indexOfExistingToast !== -1) {\n                                                return [\n                                                    ...toasts.slice(0, indexOfExistingToast),\n                                                    {\n                                                        ...toasts[indexOfExistingToast],\n                                                        ...toast\n                                                    },\n                                                    ...toasts.slice(indexOfExistingToast + 1)\n                                                ];\n                                            }\n                                            return [\n                                                toast,\n                                                ...toasts\n                                            ];\n                                        }\n                                    }[\"Toaster.Toaster.useEffect\"]);\n                                }\n                            }[\"Toaster.Toaster.useEffect\"]);\n                        }\n                    }[\"Toaster.Toaster.useEffect\"]);\n                }\n            }[\"Toaster.Toaster.useEffect\"]);\n        }\n    }[\"Toaster.Toaster.useEffect\"], [\n        toasts\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toaster.Toaster.useEffect\": ()=>{\n            if (theme !== 'system') {\n                setActualTheme(theme);\n                return;\n            }\n            if (theme === 'system') {\n                // check if current preference is dark\n                if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {\n                    // it's currently dark\n                    setActualTheme('dark');\n                } else {\n                    // it's not dark\n                    setActualTheme('light');\n                }\n            }\n            if (typeof window === 'undefined') return;\n            const darkMediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n            try {\n                // Chrome & Firefox\n                darkMediaQuery.addEventListener('change', {\n                    \"Toaster.Toaster.useEffect\": (param)=>{\n                        let { matches } = param;\n                        if (matches) {\n                            setActualTheme('dark');\n                        } else {\n                            setActualTheme('light');\n                        }\n                    }\n                }[\"Toaster.Toaster.useEffect\"]);\n            } catch (error) {\n                // Safari < 14\n                darkMediaQuery.addListener({\n                    \"Toaster.Toaster.useEffect\": (param)=>{\n                        let { matches } = param;\n                        try {\n                            if (matches) {\n                                setActualTheme('dark');\n                            } else {\n                                setActualTheme('light');\n                            }\n                        } catch (e) {\n                            console.error(e);\n                        }\n                    }\n                }[\"Toaster.Toaster.useEffect\"]);\n            }\n        }\n    }[\"Toaster.Toaster.useEffect\"], [\n        theme\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toaster.Toaster.useEffect\": ()=>{\n            // Ensure expanded is always false when no toasts are present / only one left\n            if (toasts.length <= 1) {\n                setExpanded(false);\n            }\n        }\n    }[\"Toaster.Toaster.useEffect\"], [\n        toasts\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toaster.Toaster.useEffect\": ()=>{\n            const handleKeyDown = {\n                \"Toaster.Toaster.useEffect.handleKeyDown\": (event)=>{\n                    var _listRef_current;\n                    const isHotkeyPressed = hotkey.every({\n                        \"Toaster.Toaster.useEffect.handleKeyDown.isHotkeyPressed\": (key)=>event[key] || event.code === key\n                    }[\"Toaster.Toaster.useEffect.handleKeyDown.isHotkeyPressed\"]);\n                    if (isHotkeyPressed) {\n                        var _listRef_current1;\n                        setExpanded(true);\n                        (_listRef_current1 = listRef.current) == null ? void 0 : _listRef_current1.focus();\n                    }\n                    if (event.code === 'Escape' && (document.activeElement === listRef.current || ((_listRef_current = listRef.current) == null ? void 0 : _listRef_current.contains(document.activeElement)))) {\n                        setExpanded(false);\n                    }\n                }\n            }[\"Toaster.Toaster.useEffect.handleKeyDown\"];\n            document.addEventListener('keydown', handleKeyDown);\n            return ({\n                \"Toaster.Toaster.useEffect\": ()=>document.removeEventListener('keydown', handleKeyDown)\n            })[\"Toaster.Toaster.useEffect\"];\n        }\n    }[\"Toaster.Toaster.useEffect\"], [\n        hotkey\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toaster.Toaster.useEffect\": ()=>{\n            if (listRef.current) {\n                return ({\n                    \"Toaster.Toaster.useEffect\": ()=>{\n                        if (lastFocusedElementRef.current) {\n                            lastFocusedElementRef.current.focus({\n                                preventScroll: true\n                            });\n                            lastFocusedElementRef.current = null;\n                            isFocusWithinRef.current = false;\n                        }\n                    }\n                })[\"Toaster.Toaster.useEffect\"];\n            }\n        }\n    }[\"Toaster.Toaster.useEffect\"], [\n        listRef.current\n    ]);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"section\", {\n        ref: ref,\n        \"aria-label\": \"\".concat(containerAriaLabel, \" \").concat(hotkeyLabel),\n        tabIndex: -1,\n        \"aria-live\": \"polite\",\n        \"aria-relevant\": \"additions text\",\n        \"aria-atomic\": \"false\",\n        suppressHydrationWarning: true\n    }, possiblePositions.map((position, index)=>{\n        var _heights_;\n        const [y, x] = position.split('-');\n        if (!filteredToasts.length) return null;\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"ol\", {\n            key: position,\n            dir: dir === 'auto' ? getDocumentDirection() : dir,\n            tabIndex: -1,\n            ref: listRef,\n            className: className,\n            \"data-sonner-toaster\": true,\n            \"data-sonner-theme\": actualTheme,\n            \"data-y-position\": y,\n            \"data-x-position\": x,\n            style: {\n                '--front-toast-height': \"\".concat(((_heights_ = heights[0]) == null ? void 0 : _heights_.height) || 0, \"px\"),\n                '--width': \"\".concat(TOAST_WIDTH, \"px\"),\n                '--gap': \"\".concat(gap, \"px\"),\n                ...style,\n                ...assignOffset(offset, mobileOffset)\n            },\n            onBlur: (event)=>{\n                if (isFocusWithinRef.current && !event.currentTarget.contains(event.relatedTarget)) {\n                    isFocusWithinRef.current = false;\n                    if (lastFocusedElementRef.current) {\n                        lastFocusedElementRef.current.focus({\n                            preventScroll: true\n                        });\n                        lastFocusedElementRef.current = null;\n                    }\n                }\n            },\n            onFocus: (event)=>{\n                const isNotDismissible = event.target instanceof HTMLElement && event.target.dataset.dismissible === 'false';\n                if (isNotDismissible) return;\n                if (!isFocusWithinRef.current) {\n                    isFocusWithinRef.current = true;\n                    lastFocusedElementRef.current = event.relatedTarget;\n                }\n            },\n            onMouseEnter: ()=>setExpanded(true),\n            onMouseMove: ()=>setExpanded(true),\n            onMouseLeave: ()=>{\n                // Avoid setting expanded to false when interacting with a toast, e.g. swiping\n                if (!interacting) {\n                    setExpanded(false);\n                }\n            },\n            onDragEnd: ()=>setExpanded(false),\n            onPointerDown: (event)=>{\n                const isNotDismissible = event.target instanceof HTMLElement && event.target.dataset.dismissible === 'false';\n                if (isNotDismissible) return;\n                setInteracting(true);\n            },\n            onPointerUp: ()=>setInteracting(false)\n        }, filteredToasts.filter((toast)=>!toast.position && index === 0 || toast.position === position).map((toast, index)=>{\n            var _toastOptions_duration, _toastOptions_closeButton;\n            return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(Toast, {\n                key: toast.id,\n                icons: icons,\n                index: index,\n                toast: toast,\n                defaultRichColors: richColors,\n                duration: (_toastOptions_duration = toastOptions == null ? void 0 : toastOptions.duration) != null ? _toastOptions_duration : duration,\n                className: toastOptions == null ? void 0 : toastOptions.className,\n                descriptionClassName: toastOptions == null ? void 0 : toastOptions.descriptionClassName,\n                invert: invert,\n                visibleToasts: visibleToasts,\n                closeButton: (_toastOptions_closeButton = toastOptions == null ? void 0 : toastOptions.closeButton) != null ? _toastOptions_closeButton : closeButton,\n                interacting: interacting,\n                position: position,\n                style: toastOptions == null ? void 0 : toastOptions.style,\n                unstyled: toastOptions == null ? void 0 : toastOptions.unstyled,\n                classNames: toastOptions == null ? void 0 : toastOptions.classNames,\n                cancelButtonStyle: toastOptions == null ? void 0 : toastOptions.cancelButtonStyle,\n                actionButtonStyle: toastOptions == null ? void 0 : toastOptions.actionButtonStyle,\n                closeButtonAriaLabel: toastOptions == null ? void 0 : toastOptions.closeButtonAriaLabel,\n                removeToast: removeToast,\n                toasts: filteredToasts.filter((t)=>t.position == toast.position),\n                heights: heights.filter((h)=>h.position == toast.position),\n                setHeights: setHeights,\n                expandByDefault: expand,\n                gap: gap,\n                expanded: expanded,\n                swipeDirections: props.swipeDirections\n            });\n        }));\n    }));\n}, \"ShJc+0hlP7kpJg+bSV2b9eE3FXo=\")), \"ShJc+0hlP7kpJg+bSV2b9eE3FXo=\");\n_c3 = Toaster;\n\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"Loader\");\n$RefreshReg$(_c1, \"Toast\");\n$RefreshReg$(_c2, \"Toaster$React.forwardRef\");\n$RefreshReg$(_c3, \"Toaster\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/sonner/dist/index.mjs\n"));

/***/ })

});