"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(marketing)/layout",{

/***/ "(app-pages-browser)/./components/navigation.tsx":
/*!***********************************!*\
  !*** ./components/navigation.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Navigation: () => (/* binding */ Navigation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_theme_switcher__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/theme-switcher */ \"(app-pages-browser)/./components/theme-switcher.tsx\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _contexts_appwrite_auth_context__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/contexts/appwrite-auth-context */ \"(app-pages-browser)/./contexts/appwrite-auth-context.tsx\");\n/* __next_internal_client_entry_do_not_use__ Navigation auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction Navigation() {\n    _s();\n    const [isEmailVerificationOpen, setIsEmailVerificationOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { user, logout } = (0,_contexts_appwrite_auth_context__WEBPACK_IMPORTED_MODULE_6__.useAuth)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \" backdrop-blur-sm sticky top-0 z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between h-16\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/\",\n                        className: \"flex items-center space-x-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                            whileHover: {\n                                scale: 1.05\n                            },\n                            whileTap: {\n                                scale: 0.95\n                            },\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    src: \"/logo.png\",\n                                    alt: \"DexTrip Logo\",\n                                    width: 30,\n                                    height: 30\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/navigation.tsx\",\n                                    lineNumber: 27,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-lg sm:text-xl font-bold text-primary\",\n                                    children: \"DEXTRIP\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/navigation.tsx\",\n                                    lineNumber: 33,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/navigation.tsx\",\n                            lineNumber: 22,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/navigation.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_theme_switcher__WEBPACK_IMPORTED_MODULE_4__.ThemeSwitcher, {}, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/navigation.tsx\",\n                                lineNumber: 42,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SignedOut, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            onClick: ()=>setIsEmailVerificationOpen(true),\n                                            className: \"bg-primary-gradient hover:opacity-90 text-white border-0 hover:shadow-xl transition-all duration-300\",\n                                            children: \"Join Waitlist\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/navigation.tsx\",\n                                            lineNumber: 45,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EmailVerificationModal, {\n                                            isOpen: isEmailVerificationOpen,\n                                            onClose: ()=>setIsEmailVerificationOpen(false)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/navigation.tsx\",\n                                            lineNumber: 51,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/navigation.tsx\",\n                                    lineNumber: 44,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/navigation.tsx\",\n                                lineNumber: 43,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SignedIn, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SignOutButton, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        className: \"bg-primary-gradient hover:opacity-90 text-white border-0 hover:shadow-xl transition-all duration-300\",\n                                        children: \"Exit DexTrip\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/navigation.tsx\",\n                                        lineNumber: 59,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/navigation.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/navigation.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/navigation.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/navigation.tsx\",\n                lineNumber: 19,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/navigation.tsx\",\n            lineNumber: 18,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/navigation.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n_s(Navigation, \"HU+8HqCAYs4IK79S3F57Yo78gf4=\", false, function() {\n    return [\n        _contexts_appwrite_auth_context__WEBPACK_IMPORTED_MODULE_6__.useAuth\n    ];\n});\n_c = Navigation;\nvar _c;\n$RefreshReg$(_c, \"Navigation\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/navigation.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./contexts/appwrite-auth-context.tsx":
/*!********************************************!*\
  !*** ./contexts/appwrite-auth-context.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_appwrite__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/appwrite */ \"(app-pages-browser)/./lib/appwrite.ts\");\n/* __next_internal_client_entry_do_not_use__ useAuth,AuthProvider auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst useAuth = ()=>{\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nconst AuthProvider = (param)=>{\n    let { children } = param;\n    _s1();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userProfile, setUserProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Check if user is logged in on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            checkAuth();\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const checkAuth = async ()=>{\n        try {\n            setLoading(true);\n            const currentUser = await _lib_appwrite__WEBPACK_IMPORTED_MODULE_2__.authService.getCurrentUser();\n            if (currentUser) {\n                setUser(currentUser);\n                // Try to get user profile\n                const profile = await _lib_appwrite__WEBPACK_IMPORTED_MODULE_2__.databaseService.getUserProfile(currentUser.$id);\n                setUserProfile(profile);\n            }\n        } catch (error) {\n            console.error('Auth check failed:', error);\n            setUser(null);\n            setUserProfile(null);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const login = async (email, password)=>{\n        try {\n            setLoading(true);\n            await _lib_appwrite__WEBPACK_IMPORTED_MODULE_2__.authService.login(email, password);\n            await checkAuth();\n        } catch (error) {\n            setLoading(false);\n            throw error;\n        }\n    };\n    const loginWithEmailCode = async (email)=>{\n        try {\n            const token = await _lib_appwrite__WEBPACK_IMPORTED_MODULE_2__.authService.loginWithEmailCode(email);\n            return {\n                userId: token.userId,\n                secret: token.secret\n            };\n        } catch (error) {\n            throw error;\n        }\n    };\n    const verifyEmailCode = async (userId, secret)=>{\n        try {\n            setLoading(true);\n            await _lib_appwrite__WEBPACK_IMPORTED_MODULE_2__.authService.verifyEmailCode(userId, secret);\n            await checkAuth();\n        } catch (error) {\n            setLoading(false);\n            throw error;\n        }\n    };\n    const register = async (email, password, name)=>{\n        try {\n            setLoading(true);\n            const newUser = await _lib_appwrite__WEBPACK_IMPORTED_MODULE_2__.authService.createAccount(email, password, name);\n            // Create user profile in database\n            try {\n                await _lib_appwrite__WEBPACK_IMPORTED_MODULE_2__.databaseService.createUserProfile(newUser.$id, {\n                    email,\n                    name,\n                    createdAt: new Date().toISOString()\n                });\n            } catch (dbError) {\n                console.error('Failed to create user profile:', dbError);\n            // Continue even if profile creation fails\n            }\n            await checkAuth();\n        } catch (error) {\n            setLoading(false);\n            throw error;\n        }\n    };\n    const logout = async ()=>{\n        try {\n            setLoading(true);\n            await _lib_appwrite__WEBPACK_IMPORTED_MODULE_2__.authService.logout();\n            setUser(null);\n            setUserProfile(null);\n        } catch (error) {\n            console.error('Logout failed:', error);\n            // Clear state even if logout fails\n            setUser(null);\n            setUserProfile(null);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const sendVerificationEmail = async ()=>{\n        try {\n            await _lib_appwrite__WEBPACK_IMPORTED_MODULE_2__.authService.sendVerificationEmail();\n        } catch (error) {\n            throw error;\n        }\n    };\n    const resetPassword = async (email)=>{\n        try {\n            await _lib_appwrite__WEBPACK_IMPORTED_MODULE_2__.authService.resetPassword(email);\n        } catch (error) {\n            throw error;\n        }\n    };\n    const updatePassword = async (userId, secret, password)=>{\n        try {\n            await _lib_appwrite__WEBPACK_IMPORTED_MODULE_2__.authService.updatePassword(userId, secret, password);\n        } catch (error) {\n            throw error;\n        }\n    };\n    const value = {\n        user,\n        userProfile,\n        loading,\n        login,\n        loginWithEmailCode,\n        verifyEmailCode,\n        register,\n        logout,\n        sendVerificationEmail,\n        resetPassword,\n        updatePassword\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/contexts/appwrite-auth-context.tsx\",\n        lineNumber: 177,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(AuthProvider, \"WycvliXlKHiFjN+6Nj63IB/wNNM=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./contexts/appwrite-auth-context.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/appwrite.ts":
/*!*************************!*\
  !*** ./lib/appwrite.ts ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DATABASE_ID: () => (/* binding */ DATABASE_ID),\n/* harmony export */   USERS_COLLECTION_ID: () => (/* binding */ USERS_COLLECTION_ID),\n/* harmony export */   account: () => (/* binding */ account),\n/* harmony export */   authService: () => (/* binding */ authService),\n/* harmony export */   client: () => (/* binding */ client),\n/* harmony export */   databaseService: () => (/* binding */ databaseService),\n/* harmony export */   databases: () => (/* binding */ databases)\n/* harmony export */ });\n/* harmony import */ var appwrite__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! appwrite */ \"(app-pages-browser)/./node_modules/appwrite/dist/esm/sdk.js\");\n\n// Initialize Appwrite client\nconst client = new appwrite__WEBPACK_IMPORTED_MODULE_0__.Client().setEndpoint(\"http://31.97.229.201/v1\").setProject(\"689a0d8a001fa8d95134\");\n// Initialize services\nconst account = new appwrite__WEBPACK_IMPORTED_MODULE_0__.Account(client);\nconst databases = new appwrite__WEBPACK_IMPORTED_MODULE_0__.Databases(client);\n// Database and collection IDs (you'll need to create these in Appwrite console)\nconst DATABASE_ID = 'bizcoin_db';\nconst USERS_COLLECTION_ID = 'users';\n// Auth functions\nconst authService = {\n    // Create account with email and password\n    async createAccount (email, password, name) {\n        try {\n            const userAccount = await account.create(appwrite__WEBPACK_IMPORTED_MODULE_0__.ID.unique(), email, password, name);\n            // Send verification email\n            await this.sendVerificationEmail();\n            return userAccount;\n        } catch (error) {\n            console.error('Error creating account:', error);\n            throw error;\n        }\n    },\n    // Login with email and password\n    async login (email, password) {\n        try {\n            return await account.createEmailPasswordSession(email, password);\n        } catch (error) {\n            console.error('Error logging in:', error);\n            throw error;\n        }\n    },\n    // Login with email verification code\n    async loginWithEmailCode (email) {\n        try {\n            // Create email session (sends verification code)\n            const token = await account.createEmailToken(appwrite__WEBPACK_IMPORTED_MODULE_0__.ID.unique(), email);\n            return token;\n        } catch (error) {\n            console.error('Error sending email code:', error);\n            throw error;\n        }\n    },\n    // Verify email code and create session\n    async verifyEmailCode (userId, secret) {\n        try {\n            return await account.createSession(userId, secret);\n        } catch (error) {\n            console.error('Error verifying email code:', error);\n            throw error;\n        }\n    },\n    // Send verification email\n    async sendVerificationEmail () {\n        try {\n            return await account.createVerification(\"\".concat(window.location.origin, \"/verify-email\"));\n        } catch (error) {\n            console.error('Error sending verification email:', error);\n            throw error;\n        }\n    },\n    // Verify email with URL\n    async verifyEmail (userId, secret) {\n        try {\n            return await account.updateVerification(userId, secret);\n        } catch (error) {\n            console.error('Error verifying email:', error);\n            throw error;\n        }\n    },\n    // Get current user\n    async getCurrentUser () {\n        try {\n            return await account.get();\n        } catch (error) {\n            console.error('Error getting current user:', error);\n            return null;\n        }\n    },\n    // Logout\n    async logout () {\n        try {\n            return await account.deleteSession('current');\n        } catch (error) {\n            console.error('Error logging out:', error);\n            throw error;\n        }\n    },\n    // Logout from all sessions\n    async logoutAll () {\n        try {\n            return await account.deleteSessions();\n        } catch (error) {\n            console.error('Error logging out from all sessions:', error);\n            throw error;\n        }\n    },\n    // Reset password\n    async resetPassword (email) {\n        try {\n            return await account.createRecovery(email, \"\".concat(window.location.origin, \"/reset-password\"));\n        } catch (error) {\n            console.error('Error sending password reset:', error);\n            throw error;\n        }\n    },\n    // Update password with recovery\n    async updatePassword (userId, secret, password) {\n        try {\n            return await account.updateRecovery(userId, secret, password);\n        } catch (error) {\n            console.error('Error updating password:', error);\n            throw error;\n        }\n    }\n};\n// Database functions\nconst databaseService = {\n    // Create user profile\n    async createUserProfile (userId, data) {\n        try {\n            return await databases.createDocument(DATABASE_ID, USERS_COLLECTION_ID, userId, data);\n        } catch (error) {\n            console.error('Error creating user profile:', error);\n            throw error;\n        }\n    },\n    // Get user profile\n    async getUserProfile (userId) {\n        try {\n            return await databases.getDocument(DATABASE_ID, USERS_COLLECTION_ID, userId);\n        } catch (error) {\n            console.error('Error getting user profile:', error);\n            return null;\n        }\n    },\n    // Update user profile\n    async updateUserProfile (userId, data) {\n        try {\n            return await databases.updateDocument(DATABASE_ID, USERS_COLLECTION_ID, userId, data);\n        } catch (error) {\n            console.error('Error updating user profile:', error);\n            throw error;\n        }\n    }\n};\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/appwrite.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/appwrite/dist/esm/sdk.js":
/*!***********************************************!*\
  !*** ./node_modules/appwrite/dist/esm/sdk.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Account: () => (/* binding */ Account),\n/* harmony export */   AppwriteException: () => (/* binding */ AppwriteException),\n/* harmony export */   AuthenticationFactor: () => (/* binding */ AuthenticationFactor),\n/* harmony export */   AuthenticatorType: () => (/* binding */ AuthenticatorType),\n/* harmony export */   Avatars: () => (/* binding */ Avatars),\n/* harmony export */   Browser: () => (/* binding */ Browser),\n/* harmony export */   Client: () => (/* binding */ Client),\n/* harmony export */   CreditCard: () => (/* binding */ CreditCard),\n/* harmony export */   Databases: () => (/* binding */ Databases),\n/* harmony export */   ExecutionMethod: () => (/* binding */ ExecutionMethod),\n/* harmony export */   Flag: () => (/* binding */ Flag),\n/* harmony export */   Functions: () => (/* binding */ Functions),\n/* harmony export */   Graphql: () => (/* binding */ Graphql),\n/* harmony export */   ID: () => (/* binding */ ID),\n/* harmony export */   ImageFormat: () => (/* binding */ ImageFormat),\n/* harmony export */   ImageGravity: () => (/* binding */ ImageGravity),\n/* harmony export */   Locale: () => (/* binding */ Locale),\n/* harmony export */   Messaging: () => (/* binding */ Messaging),\n/* harmony export */   OAuthProvider: () => (/* binding */ OAuthProvider),\n/* harmony export */   Permission: () => (/* binding */ Permission),\n/* harmony export */   Query: () => (/* binding */ Query),\n/* harmony export */   Role: () => (/* binding */ Role),\n/* harmony export */   Storage: () => (/* binding */ Storage),\n/* harmony export */   Teams: () => (/* binding */ Teams)\n/* harmony export */ });\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n\r\nfunction __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nfunction __classPrivateFieldGet(receiver, state, kind, f) {\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\r\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\r\n}\n\n/**\n * Helper class to generate query strings.\n */\nclass Query {\n    /**\n     * Constructor for Query class.\n     *\n     * @param {string} method\n     * @param {AttributesTypes} attribute\n     * @param {QueryTypes} values\n     */\n    constructor(method, attribute, values) {\n        this.method = method;\n        this.attribute = attribute;\n        if (values !== undefined) {\n            if (Array.isArray(values)) {\n                this.values = values;\n            }\n            else {\n                this.values = [values];\n            }\n        }\n    }\n    /**\n     * Convert the query object to a JSON string.\n     *\n     * @returns {string}\n     */\n    toString() {\n        return JSON.stringify({\n            method: this.method,\n            attribute: this.attribute,\n            values: this.values,\n        });\n    }\n}\n/**\n * Filter resources where attribute is equal to value.\n *\n * @param {string} attribute\n * @param {QueryTypes} value\n * @returns {string}\n */\nQuery.equal = (attribute, value) => new Query(\"equal\", attribute, value).toString();\n/**\n * Filter resources where attribute is not equal to value.\n *\n * @param {string} attribute\n * @param {QueryTypes} value\n * @returns {string}\n */\nQuery.notEqual = (attribute, value) => new Query(\"notEqual\", attribute, value).toString();\n/**\n * Filter resources where attribute is less than value.\n *\n * @param {string} attribute\n * @param {QueryTypes} value\n * @returns {string}\n */\nQuery.lessThan = (attribute, value) => new Query(\"lessThan\", attribute, value).toString();\n/**\n * Filter resources where attribute is less than or equal to value.\n *\n * @param {string} attribute\n * @param {QueryTypes} value\n * @returns {string}\n */\nQuery.lessThanEqual = (attribute, value) => new Query(\"lessThanEqual\", attribute, value).toString();\n/**\n * Filter resources where attribute is greater than value.\n *\n * @param {string} attribute\n * @param {QueryTypes} value\n * @returns {string}\n */\nQuery.greaterThan = (attribute, value) => new Query(\"greaterThan\", attribute, value).toString();\n/**\n * Filter resources where attribute is greater than or equal to value.\n *\n * @param {string} attribute\n * @param {QueryTypes} value\n * @returns {string}\n */\nQuery.greaterThanEqual = (attribute, value) => new Query(\"greaterThanEqual\", attribute, value).toString();\n/**\n * Filter resources where attribute is null.\n *\n * @param {string} attribute\n * @returns {string}\n */\nQuery.isNull = (attribute) => new Query(\"isNull\", attribute).toString();\n/**\n * Filter resources where attribute is not null.\n *\n * @param {string} attribute\n * @returns {string}\n */\nQuery.isNotNull = (attribute) => new Query(\"isNotNull\", attribute).toString();\n/**\n * Filter resources where attribute is between start and end (inclusive).\n *\n * @param {string} attribute\n * @param {string | number} start\n * @param {string | number} end\n * @returns {string}\n */\nQuery.between = (attribute, start, end) => new Query(\"between\", attribute, [start, end]).toString();\n/**\n * Filter resources where attribute starts with value.\n *\n * @param {string} attribute\n * @param {string} value\n * @returns {string}\n */\nQuery.startsWith = (attribute, value) => new Query(\"startsWith\", attribute, value).toString();\n/**\n * Filter resources where attribute ends with value.\n *\n * @param {string} attribute\n * @param {string} value\n * @returns {string}\n */\nQuery.endsWith = (attribute, value) => new Query(\"endsWith\", attribute, value).toString();\n/**\n * Specify which attributes should be returned by the API call.\n *\n * @param {string[]} attributes\n * @returns {string}\n */\nQuery.select = (attributes) => new Query(\"select\", undefined, attributes).toString();\n/**\n * Filter resources by searching attribute for value.\n * A fulltext index on attribute is required for this query to work.\n *\n * @param {string} attribute\n * @param {string} value\n * @returns {string}\n */\nQuery.search = (attribute, value) => new Query(\"search\", attribute, value).toString();\n/**\n * Sort results by attribute descending.\n *\n * @param {string} attribute\n * @returns {string}\n */\nQuery.orderDesc = (attribute) => new Query(\"orderDesc\", attribute).toString();\n/**\n * Sort results by attribute ascending.\n *\n * @param {string} attribute\n * @returns {string}\n */\nQuery.orderAsc = (attribute) => new Query(\"orderAsc\", attribute).toString();\n/**\n * Return results after documentId.\n *\n * @param {string} documentId\n * @returns {string}\n */\nQuery.cursorAfter = (documentId) => new Query(\"cursorAfter\", undefined, documentId).toString();\n/**\n * Return results before documentId.\n *\n * @param {string} documentId\n * @returns {string}\n */\nQuery.cursorBefore = (documentId) => new Query(\"cursorBefore\", undefined, documentId).toString();\n/**\n * Return only limit results.\n *\n * @param {number} limit\n * @returns {string}\n */\nQuery.limit = (limit) => new Query(\"limit\", undefined, limit).toString();\n/**\n * Filter resources by skipping the first offset results.\n *\n * @param {number} offset\n * @returns {string}\n */\nQuery.offset = (offset) => new Query(\"offset\", undefined, offset).toString();\n/**\n * Filter resources where attribute contains the specified value.\n *\n * @param {string} attribute\n * @param {string | string[]} value\n * @returns {string}\n */\nQuery.contains = (attribute, value) => new Query(\"contains\", attribute, value).toString();\n/**\n * Combine multiple queries using logical OR operator.\n *\n * @param {string[]} queries\n * @returns {string}\n */\nQuery.or = (queries) => new Query(\"or\", undefined, queries.map((query) => JSON.parse(query))).toString();\n/**\n * Combine multiple queries using logical AND operator.\n *\n * @param {string[]} queries\n * @returns {string}\n */\nQuery.and = (queries) => new Query(\"and\", undefined, queries.map((query) => JSON.parse(query))).toString();\n\n/**\n * Exception thrown by the  package\n */\nclass AppwriteException extends Error {\n    /**\n     * Initializes a Appwrite Exception.\n     *\n     * @param {string} message - The error message.\n     * @param {number} code - The error code. Default is 0.\n     * @param {string} type - The error type. Default is an empty string.\n     * @param {string} response - The response string. Default is an empty string.\n     */\n    constructor(message, code = 0, type = '', response = '') {\n        super(message);\n        this.name = 'AppwriteException';\n        this.message = message;\n        this.code = code;\n        this.type = type;\n        this.response = response;\n    }\n}\n/**\n * Client that handles requests to Appwrite\n */\nclass Client {\n    constructor() {\n        /**\n         * Holds configuration such as project.\n         */\n        this.config = {\n            endpoint: 'https://cloud.appwrite.io/v1',\n            endpointRealtime: '',\n            project: '',\n            jwt: '',\n            locale: '',\n            session: '',\n            devkey: '',\n        };\n        /**\n         * Custom headers for API requests.\n         */\n        this.headers = {\n            'x-sdk-name': 'Web',\n            'x-sdk-platform': 'client',\n            'x-sdk-language': 'web',\n            'x-sdk-version': '18.2.0',\n            'X-Appwrite-Response-Format': '1.7.0',\n        };\n        this.realtime = {\n            socket: undefined,\n            timeout: undefined,\n            heartbeat: undefined,\n            url: '',\n            channels: new Set(),\n            subscriptions: new Map(),\n            subscriptionsCounter: 0,\n            reconnect: true,\n            reconnectAttempts: 0,\n            lastMessage: undefined,\n            connect: () => {\n                clearTimeout(this.realtime.timeout);\n                this.realtime.timeout = window === null || window === void 0 ? void 0 : window.setTimeout(() => {\n                    this.realtime.createSocket();\n                }, 50);\n            },\n            getTimeout: () => {\n                switch (true) {\n                    case this.realtime.reconnectAttempts < 5:\n                        return 1000;\n                    case this.realtime.reconnectAttempts < 15:\n                        return 5000;\n                    case this.realtime.reconnectAttempts < 100:\n                        return 10000;\n                    default:\n                        return 60000;\n                }\n            },\n            createHeartbeat: () => {\n                if (this.realtime.heartbeat) {\n                    clearTimeout(this.realtime.heartbeat);\n                }\n                this.realtime.heartbeat = window === null || window === void 0 ? void 0 : window.setInterval(() => {\n                    var _a;\n                    (_a = this.realtime.socket) === null || _a === void 0 ? void 0 : _a.send(JSON.stringify({\n                        type: 'ping'\n                    }));\n                }, 20000);\n            },\n            createSocket: () => {\n                var _a, _b, _c;\n                if (this.realtime.channels.size < 1) {\n                    this.realtime.reconnect = false;\n                    (_a = this.realtime.socket) === null || _a === void 0 ? void 0 : _a.close();\n                    return;\n                }\n                const channels = new URLSearchParams();\n                channels.set('project', this.config.project);\n                this.realtime.channels.forEach(channel => {\n                    channels.append('channels[]', channel);\n                });\n                const url = this.config.endpointRealtime + '/realtime?' + channels.toString();\n                if (url !== this.realtime.url || // Check if URL is present\n                    !this.realtime.socket || // Check if WebSocket has not been created\n                    ((_b = this.realtime.socket) === null || _b === void 0 ? void 0 : _b.readyState) > WebSocket.OPEN // Check if WebSocket is CLOSING (3) or CLOSED (4)\n                ) {\n                    if (this.realtime.socket &&\n                        ((_c = this.realtime.socket) === null || _c === void 0 ? void 0 : _c.readyState) < WebSocket.CLOSING // Close WebSocket if it is CONNECTING (0) or OPEN (1)\n                    ) {\n                        this.realtime.reconnect = false;\n                        this.realtime.socket.close();\n                    }\n                    this.realtime.url = url;\n                    this.realtime.socket = new WebSocket(url);\n                    this.realtime.socket.addEventListener('message', this.realtime.onMessage);\n                    this.realtime.socket.addEventListener('open', _event => {\n                        this.realtime.reconnectAttempts = 0;\n                        this.realtime.createHeartbeat();\n                    });\n                    this.realtime.socket.addEventListener('close', event => {\n                        var _a, _b, _c;\n                        if (!this.realtime.reconnect ||\n                            (((_b = (_a = this.realtime) === null || _a === void 0 ? void 0 : _a.lastMessage) === null || _b === void 0 ? void 0 : _b.type) === 'error' && // Check if last message was of type error\n                                ((_c = this.realtime) === null || _c === void 0 ? void 0 : _c.lastMessage.data).code === 1008 // Check for policy violation 1008\n                            )) {\n                            this.realtime.reconnect = true;\n                            return;\n                        }\n                        const timeout = this.realtime.getTimeout();\n                        console.error(`Realtime got disconnected. Reconnect will be attempted in ${timeout / 1000} seconds.`, event.reason);\n                        setTimeout(() => {\n                            this.realtime.reconnectAttempts++;\n                            this.realtime.createSocket();\n                        }, timeout);\n                    });\n                }\n            },\n            onMessage: (event) => {\n                var _a, _b;\n                try {\n                    const message = JSON.parse(event.data);\n                    this.realtime.lastMessage = message;\n                    switch (message.type) {\n                        case 'connected':\n                            const cookie = JSON.parse((_a = window.localStorage.getItem('cookieFallback')) !== null && _a !== void 0 ? _a : '{}');\n                            const session = cookie === null || cookie === void 0 ? void 0 : cookie[`a_session_${this.config.project}`];\n                            const messageData = message.data;\n                            if (session && !messageData.user) {\n                                (_b = this.realtime.socket) === null || _b === void 0 ? void 0 : _b.send(JSON.stringify({\n                                    type: 'authentication',\n                                    data: {\n                                        session\n                                    }\n                                }));\n                            }\n                            break;\n                        case 'event':\n                            let data = message.data;\n                            if (data === null || data === void 0 ? void 0 : data.channels) {\n                                const isSubscribed = data.channels.some(channel => this.realtime.channels.has(channel));\n                                if (!isSubscribed)\n                                    return;\n                                this.realtime.subscriptions.forEach(subscription => {\n                                    if (data.channels.some(channel => subscription.channels.includes(channel))) {\n                                        setTimeout(() => subscription.callback(data));\n                                    }\n                                });\n                            }\n                            break;\n                        case 'pong':\n                            break; // Handle pong response if needed\n                        case 'error':\n                            throw message.data;\n                        default:\n                            break;\n                    }\n                }\n                catch (e) {\n                    console.error(e);\n                }\n            },\n            cleanUp: channels => {\n                this.realtime.channels.forEach(channel => {\n                    if (channels.includes(channel)) {\n                        let found = Array.from(this.realtime.subscriptions).some(([_key, subscription]) => {\n                            return subscription.channels.includes(channel);\n                        });\n                        if (!found) {\n                            this.realtime.channels.delete(channel);\n                        }\n                    }\n                });\n            }\n        };\n    }\n    /**\n     * Set Endpoint\n     *\n     * Your project endpoint\n     *\n     * @param {string} endpoint\n     *\n     * @returns {this}\n     */\n    setEndpoint(endpoint) {\n        if (!endpoint.startsWith('http://') && !endpoint.startsWith('https://')) {\n            throw new AppwriteException('Invalid endpoint URL: ' + endpoint);\n        }\n        this.config.endpoint = endpoint;\n        this.config.endpointRealtime = endpoint.replace('https://', 'wss://').replace('http://', 'ws://');\n        return this;\n    }\n    /**\n     * Set Realtime Endpoint\n     *\n     * @param {string} endpointRealtime\n     *\n     * @returns {this}\n     */\n    setEndpointRealtime(endpointRealtime) {\n        if (!endpointRealtime.startsWith('ws://') && !endpointRealtime.startsWith('wss://')) {\n            throw new AppwriteException('Invalid realtime endpoint URL: ' + endpointRealtime);\n        }\n        this.config.endpointRealtime = endpointRealtime;\n        return this;\n    }\n    /**\n     * Set Project\n     *\n     * Your project ID\n     *\n     * @param value string\n     *\n     * @return {this}\n     */\n    setProject(value) {\n        this.headers['X-Appwrite-Project'] = value;\n        this.config.project = value;\n        return this;\n    }\n    /**\n     * Set JWT\n     *\n     * Your secret JSON Web Token\n     *\n     * @param value string\n     *\n     * @return {this}\n     */\n    setJWT(value) {\n        this.headers['X-Appwrite-JWT'] = value;\n        this.config.jwt = value;\n        return this;\n    }\n    /**\n     * Set Locale\n     *\n     * @param value string\n     *\n     * @return {this}\n     */\n    setLocale(value) {\n        this.headers['X-Appwrite-Locale'] = value;\n        this.config.locale = value;\n        return this;\n    }\n    /**\n     * Set Session\n     *\n     * The user session to authenticate with\n     *\n     * @param value string\n     *\n     * @return {this}\n     */\n    setSession(value) {\n        this.headers['X-Appwrite-Session'] = value;\n        this.config.session = value;\n        return this;\n    }\n    /**\n     * Set DevKey\n     *\n     * Your secret dev API key\n     *\n     * @param value string\n     *\n     * @return {this}\n     */\n    setDevKey(value) {\n        this.headers['X-Appwrite-Dev-Key'] = value;\n        this.config.devkey = value;\n        return this;\n    }\n    /**\n     * Subscribes to Appwrite events and passes you the payload in realtime.\n     *\n     * @param {string|string[]} channels\n     * Channel to subscribe - pass a single channel as a string or multiple with an array of strings.\n     *\n     * Possible channels are:\n     * - account\n     * - collections\n     * - collections.[ID]\n     * - collections.[ID].documents\n     * - documents\n     * - documents.[ID]\n     * - files\n     * - files.[ID]\n     * - executions\n     * - executions.[ID]\n     * - functions.[ID]\n     * - teams\n     * - teams.[ID]\n     * - memberships\n     * - memberships.[ID]\n     * @param {(payload: RealtimeMessage) => void} callback Is called on every realtime update.\n     * @returns {() => void} Unsubscribes from events.\n     */\n    subscribe(channels, callback) {\n        let channelArray = typeof channels === 'string' ? [channels] : channels;\n        channelArray.forEach(channel => this.realtime.channels.add(channel));\n        const counter = this.realtime.subscriptionsCounter++;\n        this.realtime.subscriptions.set(counter, {\n            channels: channelArray,\n            callback\n        });\n        this.realtime.connect();\n        return () => {\n            this.realtime.subscriptions.delete(counter);\n            this.realtime.cleanUp(channelArray);\n            this.realtime.connect();\n        };\n    }\n    prepareRequest(method, url, headers = {}, params = {}) {\n        method = method.toUpperCase();\n        headers = Object.assign({}, this.headers, headers);\n        if (typeof window !== 'undefined' && window.localStorage) {\n            const cookieFallback = window.localStorage.getItem('cookieFallback');\n            if (cookieFallback) {\n                headers['X-Fallback-Cookies'] = cookieFallback;\n            }\n        }\n        let options = {\n            method,\n            headers,\n        };\n        if (headers['X-Appwrite-Dev-Key'] === undefined) {\n            options.credentials = 'include';\n        }\n        if (method === 'GET') {\n            for (const [key, value] of Object.entries(Client.flatten(params))) {\n                url.searchParams.append(key, value);\n            }\n        }\n        else {\n            switch (headers['content-type']) {\n                case 'application/json':\n                    options.body = JSON.stringify(params);\n                    break;\n                case 'multipart/form-data':\n                    const formData = new FormData();\n                    for (const [key, value] of Object.entries(params)) {\n                        if (value instanceof File) {\n                            formData.append(key, value, value.name);\n                        }\n                        else if (Array.isArray(value)) {\n                            for (const nestedValue of value) {\n                                formData.append(`${key}[]`, nestedValue);\n                            }\n                        }\n                        else {\n                            formData.append(key, value);\n                        }\n                    }\n                    options.body = formData;\n                    delete headers['content-type'];\n                    break;\n            }\n        }\n        return { uri: url.toString(), options };\n    }\n    chunkedUpload(method, url, headers = {}, originalPayload = {}, onProgress) {\n        var _a;\n        return __awaiter(this, void 0, void 0, function* () {\n            const [fileParam, file] = (_a = Object.entries(originalPayload).find(([_, value]) => value instanceof File)) !== null && _a !== void 0 ? _a : [];\n            if (!file || !fileParam) {\n                throw new Error('File not found in payload');\n            }\n            if (file.size <= Client.CHUNK_SIZE) {\n                return yield this.call(method, url, headers, originalPayload);\n            }\n            let start = 0;\n            let response = null;\n            while (start < file.size) {\n                let end = start + Client.CHUNK_SIZE; // Prepare end for the next chunk\n                if (end >= file.size) {\n                    end = file.size; // Adjust for the last chunk to include the last byte\n                }\n                headers['content-range'] = `bytes ${start}-${end - 1}/${file.size}`;\n                const chunk = file.slice(start, end);\n                let payload = Object.assign({}, originalPayload);\n                payload[fileParam] = new File([chunk], file.name);\n                response = yield this.call(method, url, headers, payload);\n                if (onProgress && typeof onProgress === 'function') {\n                    onProgress({\n                        $id: response.$id,\n                        progress: Math.round((end / file.size) * 100),\n                        sizeUploaded: end,\n                        chunksTotal: Math.ceil(file.size / Client.CHUNK_SIZE),\n                        chunksUploaded: Math.ceil(end / Client.CHUNK_SIZE)\n                    });\n                }\n                if (response && response.$id) {\n                    headers['x-appwrite-id'] = response.$id;\n                }\n                start = end;\n            }\n            return response;\n        });\n    }\n    ping() {\n        return __awaiter(this, void 0, void 0, function* () {\n            return this.call('GET', new URL(this.config.endpoint + '/ping'));\n        });\n    }\n    call(method, url, headers = {}, params = {}, responseType = 'json') {\n        var _a, _b;\n        return __awaiter(this, void 0, void 0, function* () {\n            const { uri, options } = this.prepareRequest(method, url, headers, params);\n            let data = null;\n            const response = yield fetch(uri, options);\n            // type opaque: No-CORS, different-origin response (CORS-issue)\n            if (response.type === 'opaque') {\n                throw new AppwriteException(`Invalid Origin. Register your new client (${window.location.host}) as a new Web platform on your project console dashboard`, 403, \"forbidden\", \"\");\n            }\n            const warnings = response.headers.get('x-appwrite-warning');\n            if (warnings) {\n                warnings.split(';').forEach((warning) => console.warn('Warning: ' + warning));\n            }\n            if ((_a = response.headers.get('content-type')) === null || _a === void 0 ? void 0 : _a.includes('application/json')) {\n                data = yield response.json();\n            }\n            else if (responseType === 'arrayBuffer') {\n                data = yield response.arrayBuffer();\n            }\n            else {\n                data = {\n                    message: yield response.text()\n                };\n            }\n            if (400 <= response.status) {\n                let responseText = '';\n                if (((_b = response.headers.get('content-type')) === null || _b === void 0 ? void 0 : _b.includes('application/json')) || responseType === 'arrayBuffer') {\n                    responseText = JSON.stringify(data);\n                }\n                else {\n                    responseText = data === null || data === void 0 ? void 0 : data.message;\n                }\n                throw new AppwriteException(data === null || data === void 0 ? void 0 : data.message, response.status, data === null || data === void 0 ? void 0 : data.type, responseText);\n            }\n            const cookieFallback = response.headers.get('X-Fallback-Cookies');\n            if (typeof window !== 'undefined' && window.localStorage && cookieFallback) {\n                window.console.warn('Appwrite is using localStorage for session management. Increase your security by adding a custom domain as your API endpoint.');\n                window.localStorage.setItem('cookieFallback', cookieFallback);\n            }\n            return data;\n        });\n    }\n    static flatten(data, prefix = '') {\n        let output = {};\n        for (const [key, value] of Object.entries(data)) {\n            let finalKey = prefix ? prefix + '[' + key + ']' : key;\n            if (Array.isArray(value)) {\n                output = Object.assign(Object.assign({}, output), Client.flatten(value, finalKey));\n            }\n            else {\n                output[finalKey] = value;\n            }\n        }\n        return output;\n    }\n}\nClient.CHUNK_SIZE = 1024 * 1024 * 5;\n\nclass Service {\n    constructor(client) {\n        this.client = client;\n    }\n    static flatten(data, prefix = '') {\n        let output = {};\n        for (const [key, value] of Object.entries(data)) {\n            let finalKey = prefix ? prefix + '[' + key + ']' : key;\n            if (Array.isArray(value)) {\n                output = Object.assign(Object.assign({}, output), Service.flatten(value, finalKey));\n            }\n            else {\n                output[finalKey] = value;\n            }\n        }\n        return output;\n    }\n}\n/**\n * The size for chunked uploads in bytes.\n */\nService.CHUNK_SIZE = 5 * 1024 * 1024; // 5MB\n\nclass Account {\n    constructor(client) {\n        this.client = client;\n    }\n    /**\n     * Get the currently logged in user.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.User<Preferences>>}\n     */\n    get() {\n        const apiPath = '/account';\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n    /**\n     * Use this endpoint to allow a new user to register a new account in your project. After the user registration completes successfully, you can use the [/account/verfication](https://appwrite.io/docs/references/cloud/client-web/account#createVerification) route to start verifying the user email address. To allow the new user to login to their new account, you need to create a new [account session](https://appwrite.io/docs/references/cloud/client-web/account#createEmailSession).\n     *\n     * @param {string} userId\n     * @param {string} email\n     * @param {string} password\n     * @param {string} name\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.User<Preferences>>}\n     */\n    create(userId, email, password, name) {\n        if (typeof userId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"userId\"');\n        }\n        if (typeof email === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"email\"');\n        }\n        if (typeof password === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"password\"');\n        }\n        const apiPath = '/account';\n        const payload = {};\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n        if (typeof email !== 'undefined') {\n            payload['email'] = email;\n        }\n        if (typeof password !== 'undefined') {\n            payload['password'] = password;\n        }\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('post', uri, apiHeaders, payload);\n    }\n    /**\n     * Update currently logged in user account email address. After changing user address, the user confirmation status will get reset. A new confirmation email is not sent automatically however you can use the send confirmation email endpoint again to send the confirmation email. For security measures, user password is required to complete this request.\n     * This endpoint can also be used to convert an anonymous account to a normal one, by passing an email address and a new password.\n     *\n     *\n     * @param {string} email\n     * @param {string} password\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.User<Preferences>>}\n     */\n    updateEmail(email, password) {\n        if (typeof email === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"email\"');\n        }\n        if (typeof password === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"password\"');\n        }\n        const apiPath = '/account/email';\n        const payload = {};\n        if (typeof email !== 'undefined') {\n            payload['email'] = email;\n        }\n        if (typeof password !== 'undefined') {\n            payload['password'] = password;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('patch', uri, apiHeaders, payload);\n    }\n    /**\n     * Get the list of identities for the currently logged in user.\n     *\n     * @param {string[]} queries\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.IdentityList>}\n     */\n    listIdentities(queries) {\n        const apiPath = '/account/identities';\n        const payload = {};\n        if (typeof queries !== 'undefined') {\n            payload['queries'] = queries;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n    /**\n     * Delete an identity by its unique ID.\n     *\n     * @param {string} identityId\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    deleteIdentity(identityId) {\n        if (typeof identityId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"identityId\"');\n        }\n        const apiPath = '/account/identities/{identityId}'.replace('{identityId}', identityId);\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('delete', uri, apiHeaders, payload);\n    }\n    /**\n     * Use this endpoint to create a JSON Web Token. You can use the resulting JWT to authenticate on behalf of the current user when working with the Appwrite server-side API and SDKs. The JWT secret is valid for 15 minutes from its creation and will be invalid if the user will logout in that time frame.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Jwt>}\n     */\n    createJWT() {\n        const apiPath = '/account/jwts';\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('post', uri, apiHeaders, payload);\n    }\n    /**\n     * Get the list of latest security activity logs for the currently logged in user. Each log returns user IP address, location and date and time of log.\n     *\n     * @param {string[]} queries\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.LogList>}\n     */\n    listLogs(queries) {\n        const apiPath = '/account/logs';\n        const payload = {};\n        if (typeof queries !== 'undefined') {\n            payload['queries'] = queries;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n    /**\n     * Enable or disable MFA on an account.\n     *\n     * @param {boolean} mfa\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.User<Preferences>>}\n     */\n    updateMFA(mfa) {\n        if (typeof mfa === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"mfa\"');\n        }\n        const apiPath = '/account/mfa';\n        const payload = {};\n        if (typeof mfa !== 'undefined') {\n            payload['mfa'] = mfa;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('patch', uri, apiHeaders, payload);\n    }\n    /**\n     * Add an authenticator app to be used as an MFA factor. Verify the authenticator using the [verify authenticator](/docs/references/cloud/client-web/account#updateMfaAuthenticator) method.\n     *\n     * @param {AuthenticatorType} type\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.MfaType>}\n     */\n    createMfaAuthenticator(type) {\n        if (typeof type === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"type\"');\n        }\n        const apiPath = '/account/mfa/authenticators/{type}'.replace('{type}', type);\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('post', uri, apiHeaders, payload);\n    }\n    /**\n     * Verify an authenticator app after adding it using the [add authenticator](/docs/references/cloud/client-web/account#createMfaAuthenticator) method.\n     *\n     * @param {AuthenticatorType} type\n     * @param {string} otp\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.User<Preferences>>}\n     */\n    updateMfaAuthenticator(type, otp) {\n        if (typeof type === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"type\"');\n        }\n        if (typeof otp === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"otp\"');\n        }\n        const apiPath = '/account/mfa/authenticators/{type}'.replace('{type}', type);\n        const payload = {};\n        if (typeof otp !== 'undefined') {\n            payload['otp'] = otp;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('put', uri, apiHeaders, payload);\n    }\n    /**\n     * Delete an authenticator for a user by ID.\n     *\n     * @param {AuthenticatorType} type\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    deleteMfaAuthenticator(type) {\n        if (typeof type === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"type\"');\n        }\n        const apiPath = '/account/mfa/authenticators/{type}'.replace('{type}', type);\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('delete', uri, apiHeaders, payload);\n    }\n    /**\n     * Begin the process of MFA verification after sign-in. Finish the flow with [updateMfaChallenge](/docs/references/cloud/client-web/account#updateMfaChallenge) method.\n     *\n     * @param {AuthenticationFactor} factor\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.MfaChallenge>}\n     */\n    createMfaChallenge(factor) {\n        if (typeof factor === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"factor\"');\n        }\n        const apiPath = '/account/mfa/challenge';\n        const payload = {};\n        if (typeof factor !== 'undefined') {\n            payload['factor'] = factor;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('post', uri, apiHeaders, payload);\n    }\n    /**\n     * Complete the MFA challenge by providing the one-time password. Finish the process of MFA verification by providing the one-time password. To begin the flow, use [createMfaChallenge](/docs/references/cloud/client-web/account#createMfaChallenge) method.\n     *\n     * @param {string} challengeId\n     * @param {string} otp\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Session>}\n     */\n    updateMfaChallenge(challengeId, otp) {\n        if (typeof challengeId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"challengeId\"');\n        }\n        if (typeof otp === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"otp\"');\n        }\n        const apiPath = '/account/mfa/challenge';\n        const payload = {};\n        if (typeof challengeId !== 'undefined') {\n            payload['challengeId'] = challengeId;\n        }\n        if (typeof otp !== 'undefined') {\n            payload['otp'] = otp;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('put', uri, apiHeaders, payload);\n    }\n    /**\n     * List the factors available on the account to be used as a MFA challange.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.MfaFactors>}\n     */\n    listMfaFactors() {\n        const apiPath = '/account/mfa/factors';\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n    /**\n     * Get recovery codes that can be used as backup for MFA flow. Before getting codes, they must be generated using [createMfaRecoveryCodes](/docs/references/cloud/client-web/account#createMfaRecoveryCodes) method. An OTP challenge is required to read recovery codes.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.MfaRecoveryCodes>}\n     */\n    getMfaRecoveryCodes() {\n        const apiPath = '/account/mfa/recovery-codes';\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n    /**\n     * Generate recovery codes as backup for MFA flow. It&#039;s recommended to generate and show then immediately after user successfully adds their authehticator. Recovery codes can be used as a MFA verification type in [createMfaChallenge](/docs/references/cloud/client-web/account#createMfaChallenge) method.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.MfaRecoveryCodes>}\n     */\n    createMfaRecoveryCodes() {\n        const apiPath = '/account/mfa/recovery-codes';\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('post', uri, apiHeaders, payload);\n    }\n    /**\n     * Regenerate recovery codes that can be used as backup for MFA flow. Before regenerating codes, they must be first generated using [createMfaRecoveryCodes](/docs/references/cloud/client-web/account#createMfaRecoveryCodes) method. An OTP challenge is required to regenreate recovery codes.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.MfaRecoveryCodes>}\n     */\n    updateMfaRecoveryCodes() {\n        const apiPath = '/account/mfa/recovery-codes';\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('patch', uri, apiHeaders, payload);\n    }\n    /**\n     * Update currently logged in user account name.\n     *\n     * @param {string} name\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.User<Preferences>>}\n     */\n    updateName(name) {\n        if (typeof name === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"name\"');\n        }\n        const apiPath = '/account/name';\n        const payload = {};\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('patch', uri, apiHeaders, payload);\n    }\n    /**\n     * Update currently logged in user password. For validation, user is required to pass in the new password, and the old password. For users created with OAuth, Team Invites and Magic URL, oldPassword is optional.\n     *\n     * @param {string} password\n     * @param {string} oldPassword\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.User<Preferences>>}\n     */\n    updatePassword(password, oldPassword) {\n        if (typeof password === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"password\"');\n        }\n        const apiPath = '/account/password';\n        const payload = {};\n        if (typeof password !== 'undefined') {\n            payload['password'] = password;\n        }\n        if (typeof oldPassword !== 'undefined') {\n            payload['oldPassword'] = oldPassword;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('patch', uri, apiHeaders, payload);\n    }\n    /**\n     * Update the currently logged in user&#039;s phone number. After updating the phone number, the phone verification status will be reset. A confirmation SMS is not sent automatically, however you can use the [POST /account/verification/phone](https://appwrite.io/docs/references/cloud/client-web/account#createPhoneVerification) endpoint to send a confirmation SMS.\n     *\n     * @param {string} phone\n     * @param {string} password\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.User<Preferences>>}\n     */\n    updatePhone(phone, password) {\n        if (typeof phone === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"phone\"');\n        }\n        if (typeof password === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"password\"');\n        }\n        const apiPath = '/account/phone';\n        const payload = {};\n        if (typeof phone !== 'undefined') {\n            payload['phone'] = phone;\n        }\n        if (typeof password !== 'undefined') {\n            payload['password'] = password;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('patch', uri, apiHeaders, payload);\n    }\n    /**\n     * Get the preferences as a key-value object for the currently logged in user.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Preferences>}\n     */\n    getPrefs() {\n        const apiPath = '/account/prefs';\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n    /**\n     * Update currently logged in user account preferences. The object you pass is stored as is, and replaces any previous value. The maximum allowed prefs size is 64kB and throws error if exceeded.\n     *\n     * @param {Partial<Preferences>} prefs\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.User<Preferences>>}\n     */\n    updatePrefs(prefs) {\n        if (typeof prefs === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"prefs\"');\n        }\n        const apiPath = '/account/prefs';\n        const payload = {};\n        if (typeof prefs !== 'undefined') {\n            payload['prefs'] = prefs;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('patch', uri, apiHeaders, payload);\n    }\n    /**\n     * Sends the user an email with a temporary secret key for password reset. When the user clicks the confirmation link he is redirected back to your app password reset URL with the secret key and email address values attached to the URL query string. Use the query string params to submit a request to the [PUT /account/recovery](https://appwrite.io/docs/references/cloud/client-web/account#updateRecovery) endpoint to complete the process. The verification link sent to the user&#039;s email address is valid for 1 hour.\n     *\n     * @param {string} email\n     * @param {string} url\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Token>}\n     */\n    createRecovery(email, url) {\n        if (typeof email === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"email\"');\n        }\n        if (typeof url === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"url\"');\n        }\n        const apiPath = '/account/recovery';\n        const payload = {};\n        if (typeof email !== 'undefined') {\n            payload['email'] = email;\n        }\n        if (typeof url !== 'undefined') {\n            payload['url'] = url;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('post', uri, apiHeaders, payload);\n    }\n    /**\n     * Use this endpoint to complete the user account password reset. Both the **userId** and **secret** arguments will be passed as query parameters to the redirect URL you have provided when sending your request to the [POST /account/recovery](https://appwrite.io/docs/references/cloud/client-web/account#createRecovery) endpoint.\n     *\n     * Please note that in order to avoid a [Redirect Attack](https://github.com/OWASP/CheatSheetSeries/blob/master/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.md) the only valid redirect URLs are the ones from domains you have set when adding your platforms in the console interface.\n     *\n     * @param {string} userId\n     * @param {string} secret\n     * @param {string} password\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Token>}\n     */\n    updateRecovery(userId, secret, password) {\n        if (typeof userId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"userId\"');\n        }\n        if (typeof secret === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"secret\"');\n        }\n        if (typeof password === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"password\"');\n        }\n        const apiPath = '/account/recovery';\n        const payload = {};\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n        if (typeof secret !== 'undefined') {\n            payload['secret'] = secret;\n        }\n        if (typeof password !== 'undefined') {\n            payload['password'] = password;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('put', uri, apiHeaders, payload);\n    }\n    /**\n     * Get the list of active sessions across different devices for the currently logged in user.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.SessionList>}\n     */\n    listSessions() {\n        const apiPath = '/account/sessions';\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n    /**\n     * Delete all sessions from the user account and remove any sessions cookies from the end client.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    deleteSessions() {\n        const apiPath = '/account/sessions';\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('delete', uri, apiHeaders, payload);\n    }\n    /**\n     * Use this endpoint to allow a new user to register an anonymous account in your project. This route will also create a new session for the user. To allow the new user to convert an anonymous account to a normal account, you need to update its [email and password](https://appwrite.io/docs/references/cloud/client-web/account#updateEmail) or create an [OAuth2 session](https://appwrite.io/docs/references/cloud/client-web/account#CreateOAuth2Session).\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Session>}\n     */\n    createAnonymousSession() {\n        const apiPath = '/account/sessions/anonymous';\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('post', uri, apiHeaders, payload);\n    }\n    /**\n     * Allow the user to login into their account by providing a valid email and password combination. This route will create a new session for the user.\n     *\n     * A user is limited to 10 active sessions at a time by default. [Learn more about session limits](https://appwrite.io/docs/authentication-security#limits).\n     *\n     * @param {string} email\n     * @param {string} password\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Session>}\n     */\n    createEmailPasswordSession(email, password) {\n        if (typeof email === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"email\"');\n        }\n        if (typeof password === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"password\"');\n        }\n        const apiPath = '/account/sessions/email';\n        const payload = {};\n        if (typeof email !== 'undefined') {\n            payload['email'] = email;\n        }\n        if (typeof password !== 'undefined') {\n            payload['password'] = password;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('post', uri, apiHeaders, payload);\n    }\n    /**\n     * Use this endpoint to create a session from token. Provide the **userId** and **secret** parameters from the successful response of authentication flows initiated by token creation. For example, magic URL and phone login.\n     *\n     * @param {string} userId\n     * @param {string} secret\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Session>}\n     */\n    updateMagicURLSession(userId, secret) {\n        if (typeof userId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"userId\"');\n        }\n        if (typeof secret === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"secret\"');\n        }\n        const apiPath = '/account/sessions/magic-url';\n        const payload = {};\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n        if (typeof secret !== 'undefined') {\n            payload['secret'] = secret;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('put', uri, apiHeaders, payload);\n    }\n    /**\n     * Allow the user to login to their account using the OAuth2 provider of their choice. Each OAuth2 provider should be enabled from the Appwrite console first. Use the success and failure arguments to provide a redirect URL&#039;s back to your app when login is completed.\n     *\n     * If there is already an active session, the new session will be attached to the logged-in account. If there are no active sessions, the server will attempt to look for a user with the same email address as the email received from the OAuth2 provider and attach the new session to the existing user. If no matching user is found - the server will create a new user.\n     *\n     * A user is limited to 10 active sessions at a time by default. [Learn more about session limits](https://appwrite.io/docs/authentication-security#limits).\n     *\n     *\n     * @param {OAuthProvider} provider\n     * @param {string} success\n     * @param {string} failure\n     * @param {string[]} scopes\n     * @throws {AppwriteException}\n     * @returns {void | string}\n     */\n    createOAuth2Session(provider, success, failure, scopes) {\n        if (typeof provider === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"provider\"');\n        }\n        const apiPath = '/account/sessions/oauth2/{provider}'.replace('{provider}', provider);\n        const payload = {};\n        if (typeof success !== 'undefined') {\n            payload['success'] = success;\n        }\n        if (typeof failure !== 'undefined') {\n            payload['failure'] = failure;\n        }\n        if (typeof scopes !== 'undefined') {\n            payload['scopes'] = scopes;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        payload['project'] = this.client.config.project;\n        for (const [key, value] of Object.entries(Service.flatten(payload))) {\n            uri.searchParams.append(key, value);\n        }\n        if (typeof window !== 'undefined' && (window === null || window === void 0 ? void 0 : window.location)) {\n            window.location.href = uri.toString();\n            return;\n        }\n        else {\n            return uri.toString();\n        }\n    }\n    /**\n     * Use this endpoint to create a session from token. Provide the **userId** and **secret** parameters from the successful response of authentication flows initiated by token creation. For example, magic URL and phone login.\n     *\n     * @param {string} userId\n     * @param {string} secret\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Session>}\n     */\n    updatePhoneSession(userId, secret) {\n        if (typeof userId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"userId\"');\n        }\n        if (typeof secret === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"secret\"');\n        }\n        const apiPath = '/account/sessions/phone';\n        const payload = {};\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n        if (typeof secret !== 'undefined') {\n            payload['secret'] = secret;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('put', uri, apiHeaders, payload);\n    }\n    /**\n     * Use this endpoint to create a session from token. Provide the **userId** and **secret** parameters from the successful response of authentication flows initiated by token creation. For example, magic URL and phone login.\n     *\n     * @param {string} userId\n     * @param {string} secret\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Session>}\n     */\n    createSession(userId, secret) {\n        if (typeof userId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"userId\"');\n        }\n        if (typeof secret === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"secret\"');\n        }\n        const apiPath = '/account/sessions/token';\n        const payload = {};\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n        if (typeof secret !== 'undefined') {\n            payload['secret'] = secret;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('post', uri, apiHeaders, payload);\n    }\n    /**\n     * Use this endpoint to get a logged in user&#039;s session using a Session ID. Inputting &#039;current&#039; will return the current session being used.\n     *\n     * @param {string} sessionId\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Session>}\n     */\n    getSession(sessionId) {\n        if (typeof sessionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"sessionId\"');\n        }\n        const apiPath = '/account/sessions/{sessionId}'.replace('{sessionId}', sessionId);\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n    /**\n     * Use this endpoint to extend a session&#039;s length. Extending a session is useful when session expiry is short. If the session was created using an OAuth provider, this endpoint refreshes the access token from the provider.\n     *\n     * @param {string} sessionId\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Session>}\n     */\n    updateSession(sessionId) {\n        if (typeof sessionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"sessionId\"');\n        }\n        const apiPath = '/account/sessions/{sessionId}'.replace('{sessionId}', sessionId);\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('patch', uri, apiHeaders, payload);\n    }\n    /**\n     * Logout the user. Use &#039;current&#039; as the session ID to logout on this device, use a session ID to logout on another device. If you&#039;re looking to logout the user on all devices, use [Delete Sessions](https://appwrite.io/docs/references/cloud/client-web/account#deleteSessions) instead.\n     *\n     * @param {string} sessionId\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    deleteSession(sessionId) {\n        if (typeof sessionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"sessionId\"');\n        }\n        const apiPath = '/account/sessions/{sessionId}'.replace('{sessionId}', sessionId);\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('delete', uri, apiHeaders, payload);\n    }\n    /**\n     * Block the currently logged in user account. Behind the scene, the user record is not deleted but permanently blocked from any access. To completely delete a user, use the Users API instead.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.User<Preferences>>}\n     */\n    updateStatus() {\n        const apiPath = '/account/status';\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('patch', uri, apiHeaders, payload);\n    }\n    /**\n     * Use this endpoint to register a device for push notifications. Provide a target ID (custom or generated using ID.unique()), a device identifier (usually a device token), and optionally specify which provider should send notifications to this target. The target is automatically linked to the current session and includes device information like brand and model.\n     *\n     * @param {string} targetId\n     * @param {string} identifier\n     * @param {string} providerId\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Target>}\n     */\n    createPushTarget(targetId, identifier, providerId) {\n        if (typeof targetId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"targetId\"');\n        }\n        if (typeof identifier === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"identifier\"');\n        }\n        const apiPath = '/account/targets/push';\n        const payload = {};\n        if (typeof targetId !== 'undefined') {\n            payload['targetId'] = targetId;\n        }\n        if (typeof identifier !== 'undefined') {\n            payload['identifier'] = identifier;\n        }\n        if (typeof providerId !== 'undefined') {\n            payload['providerId'] = providerId;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('post', uri, apiHeaders, payload);\n    }\n    /**\n     * Update the currently logged in user&#039;s push notification target. You can modify the target&#039;s identifier (device token) and provider ID (token, email, phone etc.). The target must exist and belong to the current user. If you change the provider ID, notifications will be sent through the new messaging provider instead.\n     *\n     * @param {string} targetId\n     * @param {string} identifier\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Target>}\n     */\n    updatePushTarget(targetId, identifier) {\n        if (typeof targetId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"targetId\"');\n        }\n        if (typeof identifier === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"identifier\"');\n        }\n        const apiPath = '/account/targets/{targetId}/push'.replace('{targetId}', targetId);\n        const payload = {};\n        if (typeof identifier !== 'undefined') {\n            payload['identifier'] = identifier;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('put', uri, apiHeaders, payload);\n    }\n    /**\n     * Delete a push notification target for the currently logged in user. After deletion, the device will no longer receive push notifications. The target must exist and belong to the current user.\n     *\n     * @param {string} targetId\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    deletePushTarget(targetId) {\n        if (typeof targetId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"targetId\"');\n        }\n        const apiPath = '/account/targets/{targetId}/push'.replace('{targetId}', targetId);\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('delete', uri, apiHeaders, payload);\n    }\n    /**\n     * Sends the user an email with a secret key for creating a session. If the provided user ID has not be registered, a new user will be created. Use the returned user ID and secret and submit a request to the [POST /v1/account/sessions/token](https://appwrite.io/docs/references/cloud/client-web/account#createSession) endpoint to complete the login process. The secret sent to the user&#039;s email is valid for 15 minutes.\n     *\n     * A user is limited to 10 active sessions at a time by default. [Learn more about session limits](https://appwrite.io/docs/authentication-security#limits).\n     *\n     * @param {string} userId\n     * @param {string} email\n     * @param {boolean} phrase\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Token>}\n     */\n    createEmailToken(userId, email, phrase) {\n        if (typeof userId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"userId\"');\n        }\n        if (typeof email === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"email\"');\n        }\n        const apiPath = '/account/tokens/email';\n        const payload = {};\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n        if (typeof email !== 'undefined') {\n            payload['email'] = email;\n        }\n        if (typeof phrase !== 'undefined') {\n            payload['phrase'] = phrase;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('post', uri, apiHeaders, payload);\n    }\n    /**\n     * Sends the user an email with a secret key for creating a session. If the provided user ID has not been registered, a new user will be created. When the user clicks the link in the email, the user is redirected back to the URL you provided with the secret key and userId values attached to the URL query string. Use the query string parameters to submit a request to the [POST /v1/account/sessions/token](https://appwrite.io/docs/references/cloud/client-web/account#createSession) endpoint to complete the login process. The link sent to the user&#039;s email address is valid for 1 hour.\n     *\n     * A user is limited to 10 active sessions at a time by default. [Learn more about session limits](https://appwrite.io/docs/authentication-security#limits).\n     *\n     *\n     * @param {string} userId\n     * @param {string} email\n     * @param {string} url\n     * @param {boolean} phrase\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Token>}\n     */\n    createMagicURLToken(userId, email, url, phrase) {\n        if (typeof userId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"userId\"');\n        }\n        if (typeof email === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"email\"');\n        }\n        const apiPath = '/account/tokens/magic-url';\n        const payload = {};\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n        if (typeof email !== 'undefined') {\n            payload['email'] = email;\n        }\n        if (typeof url !== 'undefined') {\n            payload['url'] = url;\n        }\n        if (typeof phrase !== 'undefined') {\n            payload['phrase'] = phrase;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('post', uri, apiHeaders, payload);\n    }\n    /**\n     * Allow the user to login to their account using the OAuth2 provider of their choice. Each OAuth2 provider should be enabled from the Appwrite console first. Use the success and failure arguments to provide a redirect URL&#039;s back to your app when login is completed.\n     *\n     * If authentication succeeds, `userId` and `secret` of a token will be appended to the success URL as query parameters. These can be used to create a new session using the [Create session](https://appwrite.io/docs/references/cloud/client-web/account#createSession) endpoint.\n     *\n     * A user is limited to 10 active sessions at a time by default. [Learn more about session limits](https://appwrite.io/docs/authentication-security#limits).\n     *\n     * @param {OAuthProvider} provider\n     * @param {string} success\n     * @param {string} failure\n     * @param {string[]} scopes\n     * @throws {AppwriteException}\n     * @returns {void | string}\n     */\n    createOAuth2Token(provider, success, failure, scopes) {\n        if (typeof provider === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"provider\"');\n        }\n        const apiPath = '/account/tokens/oauth2/{provider}'.replace('{provider}', provider);\n        const payload = {};\n        if (typeof success !== 'undefined') {\n            payload['success'] = success;\n        }\n        if (typeof failure !== 'undefined') {\n            payload['failure'] = failure;\n        }\n        if (typeof scopes !== 'undefined') {\n            payload['scopes'] = scopes;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        payload['project'] = this.client.config.project;\n        for (const [key, value] of Object.entries(Service.flatten(payload))) {\n            uri.searchParams.append(key, value);\n        }\n        if (typeof window !== 'undefined' && (window === null || window === void 0 ? void 0 : window.location)) {\n            window.location.href = uri.toString();\n            return;\n        }\n        else {\n            return uri.toString();\n        }\n    }\n    /**\n     * Sends the user an SMS with a secret key for creating a session. If the provided user ID has not be registered, a new user will be created. Use the returned user ID and secret and submit a request to the [POST /v1/account/sessions/token](https://appwrite.io/docs/references/cloud/client-web/account#createSession) endpoint to complete the login process. The secret sent to the user&#039;s phone is valid for 15 minutes.\n     *\n     * A user is limited to 10 active sessions at a time by default. [Learn more about session limits](https://appwrite.io/docs/authentication-security#limits).\n     *\n     * @param {string} userId\n     * @param {string} phone\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Token>}\n     */\n    createPhoneToken(userId, phone) {\n        if (typeof userId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"userId\"');\n        }\n        if (typeof phone === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"phone\"');\n        }\n        const apiPath = '/account/tokens/phone';\n        const payload = {};\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n        if (typeof phone !== 'undefined') {\n            payload['phone'] = phone;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('post', uri, apiHeaders, payload);\n    }\n    /**\n     * Use this endpoint to send a verification message to your user email address to confirm they are the valid owners of that address. Both the **userId** and **secret** arguments will be passed as query parameters to the URL you have provided to be attached to the verification email. The provided URL should redirect the user back to your app and allow you to complete the verification process by verifying both the **userId** and **secret** parameters. Learn more about how to [complete the verification process](https://appwrite.io/docs/references/cloud/client-web/account#updateVerification). The verification link sent to the user&#039;s email address is valid for 7 days.\n     *\n     * Please note that in order to avoid a [Redirect Attack](https://github.com/OWASP/CheatSheetSeries/blob/master/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.md), the only valid redirect URLs are the ones from domains you have set when adding your platforms in the console interface.\n     *\n     *\n     * @param {string} url\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Token>}\n     */\n    createVerification(url) {\n        if (typeof url === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"url\"');\n        }\n        const apiPath = '/account/verification';\n        const payload = {};\n        if (typeof url !== 'undefined') {\n            payload['url'] = url;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('post', uri, apiHeaders, payload);\n    }\n    /**\n     * Use this endpoint to complete the user email verification process. Use both the **userId** and **secret** parameters that were attached to your app URL to verify the user email ownership. If confirmed this route will return a 200 status code.\n     *\n     * @param {string} userId\n     * @param {string} secret\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Token>}\n     */\n    updateVerification(userId, secret) {\n        if (typeof userId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"userId\"');\n        }\n        if (typeof secret === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"secret\"');\n        }\n        const apiPath = '/account/verification';\n        const payload = {};\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n        if (typeof secret !== 'undefined') {\n            payload['secret'] = secret;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('put', uri, apiHeaders, payload);\n    }\n    /**\n     * Use this endpoint to send a verification SMS to the currently logged in user. This endpoint is meant for use after updating a user&#039;s phone number using the [accountUpdatePhone](https://appwrite.io/docs/references/cloud/client-web/account#updatePhone) endpoint. Learn more about how to [complete the verification process](https://appwrite.io/docs/references/cloud/client-web/account#updatePhoneVerification). The verification code sent to the user&#039;s phone number is valid for 15 minutes.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Token>}\n     */\n    createPhoneVerification() {\n        const apiPath = '/account/verification/phone';\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('post', uri, apiHeaders, payload);\n    }\n    /**\n     * Use this endpoint to complete the user phone verification process. Use the **userId** and **secret** that were sent to your user&#039;s phone number to verify the user email ownership. If confirmed this route will return a 200 status code.\n     *\n     * @param {string} userId\n     * @param {string} secret\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Token>}\n     */\n    updatePhoneVerification(userId, secret) {\n        if (typeof userId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"userId\"');\n        }\n        if (typeof secret === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"secret\"');\n        }\n        const apiPath = '/account/verification/phone';\n        const payload = {};\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n        if (typeof secret !== 'undefined') {\n            payload['secret'] = secret;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('put', uri, apiHeaders, payload);\n    }\n}\n\nclass Avatars {\n    constructor(client) {\n        this.client = client;\n    }\n    /**\n     * You can use this endpoint to show different browser icons to your users. The code argument receives the browser code as it appears in your user [GET /account/sessions](https://appwrite.io/docs/references/cloud/client-web/account#getSessions) endpoint. Use width, height and quality arguments to change the output settings.\n     *\n     * When one dimension is specified and the other is 0, the image is scaled with preserved aspect ratio. If both dimensions are 0, the API provides an image at source quality. If dimensions are not specified, the default size of image returned is 100x100px.\n     *\n     * @param {Browser} code\n     * @param {number} width\n     * @param {number} height\n     * @param {number} quality\n     * @throws {AppwriteException}\n     * @returns {string}\n     */\n    getBrowser(code, width, height, quality) {\n        if (typeof code === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"code\"');\n        }\n        const apiPath = '/avatars/browsers/{code}'.replace('{code}', code);\n        const payload = {};\n        if (typeof width !== 'undefined') {\n            payload['width'] = width;\n        }\n        if (typeof height !== 'undefined') {\n            payload['height'] = height;\n        }\n        if (typeof quality !== 'undefined') {\n            payload['quality'] = quality;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        payload['project'] = this.client.config.project;\n        for (const [key, value] of Object.entries(Service.flatten(payload))) {\n            uri.searchParams.append(key, value);\n        }\n        return uri.toString();\n    }\n    /**\n     * The credit card endpoint will return you the icon of the credit card provider you need. Use width, height and quality arguments to change the output settings.\n     *\n     * When one dimension is specified and the other is 0, the image is scaled with preserved aspect ratio. If both dimensions are 0, the API provides an image at source quality. If dimensions are not specified, the default size of image returned is 100x100px.\n     *\n     *\n     * @param {CreditCard} code\n     * @param {number} width\n     * @param {number} height\n     * @param {number} quality\n     * @throws {AppwriteException}\n     * @returns {string}\n     */\n    getCreditCard(code, width, height, quality) {\n        if (typeof code === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"code\"');\n        }\n        const apiPath = '/avatars/credit-cards/{code}'.replace('{code}', code);\n        const payload = {};\n        if (typeof width !== 'undefined') {\n            payload['width'] = width;\n        }\n        if (typeof height !== 'undefined') {\n            payload['height'] = height;\n        }\n        if (typeof quality !== 'undefined') {\n            payload['quality'] = quality;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        payload['project'] = this.client.config.project;\n        for (const [key, value] of Object.entries(Service.flatten(payload))) {\n            uri.searchParams.append(key, value);\n        }\n        return uri.toString();\n    }\n    /**\n     * Use this endpoint to fetch the favorite icon (AKA favicon) of any remote website URL.\n     *\n     * This endpoint does not follow HTTP redirects.\n     *\n     * @param {string} url\n     * @throws {AppwriteException}\n     * @returns {string}\n     */\n    getFavicon(url) {\n        if (typeof url === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"url\"');\n        }\n        const apiPath = '/avatars/favicon';\n        const payload = {};\n        if (typeof url !== 'undefined') {\n            payload['url'] = url;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        payload['project'] = this.client.config.project;\n        for (const [key, value] of Object.entries(Service.flatten(payload))) {\n            uri.searchParams.append(key, value);\n        }\n        return uri.toString();\n    }\n    /**\n     * You can use this endpoint to show different country flags icons to your users. The code argument receives the 2 letter country code. Use width, height and quality arguments to change the output settings. Country codes follow the [ISO 3166-1](https://en.wikipedia.org/wiki/ISO_3166-1) standard.\n     *\n     * When one dimension is specified and the other is 0, the image is scaled with preserved aspect ratio. If both dimensions are 0, the API provides an image at source quality. If dimensions are not specified, the default size of image returned is 100x100px.\n     *\n     *\n     * @param {Flag} code\n     * @param {number} width\n     * @param {number} height\n     * @param {number} quality\n     * @throws {AppwriteException}\n     * @returns {string}\n     */\n    getFlag(code, width, height, quality) {\n        if (typeof code === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"code\"');\n        }\n        const apiPath = '/avatars/flags/{code}'.replace('{code}', code);\n        const payload = {};\n        if (typeof width !== 'undefined') {\n            payload['width'] = width;\n        }\n        if (typeof height !== 'undefined') {\n            payload['height'] = height;\n        }\n        if (typeof quality !== 'undefined') {\n            payload['quality'] = quality;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        payload['project'] = this.client.config.project;\n        for (const [key, value] of Object.entries(Service.flatten(payload))) {\n            uri.searchParams.append(key, value);\n        }\n        return uri.toString();\n    }\n    /**\n     * Use this endpoint to fetch a remote image URL and crop it to any image size you want. This endpoint is very useful if you need to crop and display remote images in your app or in case you want to make sure a 3rd party image is properly served using a TLS protocol.\n     *\n     * When one dimension is specified and the other is 0, the image is scaled with preserved aspect ratio. If both dimensions are 0, the API provides an image at source quality. If dimensions are not specified, the default size of image returned is 400x400px.\n     *\n     * This endpoint does not follow HTTP redirects.\n     *\n     * @param {string} url\n     * @param {number} width\n     * @param {number} height\n     * @throws {AppwriteException}\n     * @returns {string}\n     */\n    getImage(url, width, height) {\n        if (typeof url === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"url\"');\n        }\n        const apiPath = '/avatars/image';\n        const payload = {};\n        if (typeof url !== 'undefined') {\n            payload['url'] = url;\n        }\n        if (typeof width !== 'undefined') {\n            payload['width'] = width;\n        }\n        if (typeof height !== 'undefined') {\n            payload['height'] = height;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        payload['project'] = this.client.config.project;\n        for (const [key, value] of Object.entries(Service.flatten(payload))) {\n            uri.searchParams.append(key, value);\n        }\n        return uri.toString();\n    }\n    /**\n     * Use this endpoint to show your user initials avatar icon on your website or app. By default, this route will try to print your logged-in user name or email initials. You can also overwrite the user name if you pass the &#039;name&#039; parameter. If no name is given and no user is logged, an empty avatar will be returned.\n     *\n     * You can use the color and background params to change the avatar colors. By default, a random theme will be selected. The random theme will persist for the user&#039;s initials when reloading the same theme will always return for the same initials.\n     *\n     * When one dimension is specified and the other is 0, the image is scaled with preserved aspect ratio. If both dimensions are 0, the API provides an image at source quality. If dimensions are not specified, the default size of image returned is 100x100px.\n     *\n     *\n     * @param {string} name\n     * @param {number} width\n     * @param {number} height\n     * @param {string} background\n     * @throws {AppwriteException}\n     * @returns {string}\n     */\n    getInitials(name, width, height, background) {\n        const apiPath = '/avatars/initials';\n        const payload = {};\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n        if (typeof width !== 'undefined') {\n            payload['width'] = width;\n        }\n        if (typeof height !== 'undefined') {\n            payload['height'] = height;\n        }\n        if (typeof background !== 'undefined') {\n            payload['background'] = background;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        payload['project'] = this.client.config.project;\n        for (const [key, value] of Object.entries(Service.flatten(payload))) {\n            uri.searchParams.append(key, value);\n        }\n        return uri.toString();\n    }\n    /**\n     * Converts a given plain text to a QR code image. You can use the query parameters to change the size and style of the resulting image.\n     *\n     *\n     * @param {string} text\n     * @param {number} size\n     * @param {number} margin\n     * @param {boolean} download\n     * @throws {AppwriteException}\n     * @returns {string}\n     */\n    getQR(text, size, margin, download) {\n        if (typeof text === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"text\"');\n        }\n        const apiPath = '/avatars/qr';\n        const payload = {};\n        if (typeof text !== 'undefined') {\n            payload['text'] = text;\n        }\n        if (typeof size !== 'undefined') {\n            payload['size'] = size;\n        }\n        if (typeof margin !== 'undefined') {\n            payload['margin'] = margin;\n        }\n        if (typeof download !== 'undefined') {\n            payload['download'] = download;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        payload['project'] = this.client.config.project;\n        for (const [key, value] of Object.entries(Service.flatten(payload))) {\n            uri.searchParams.append(key, value);\n        }\n        return uri.toString();\n    }\n}\n\nclass Databases {\n    constructor(client) {\n        this.client = client;\n    }\n    /**\n     * Get a list of all the user&#039;s documents in a given collection. You can use the query params to filter your results.\n     *\n     * @param {string} databaseId\n     * @param {string} collectionId\n     * @param {string[]} queries\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.DocumentList<Document>>}\n     */\n    listDocuments(databaseId, collectionId, queries) {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n        if (typeof collectionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"collectionId\"');\n        }\n        const apiPath = '/databases/{databaseId}/collections/{collectionId}/documents'.replace('{databaseId}', databaseId).replace('{collectionId}', collectionId);\n        const payload = {};\n        if (typeof queries !== 'undefined') {\n            payload['queries'] = queries;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n    /**\n     * Create a new Document. Before using this route, you should create a new collection resource using either a [server integration](https://appwrite.io/docs/server/databases#databasesCreateCollection) API or directly from your database console.\n     *\n     * @param {string} databaseId\n     * @param {string} collectionId\n     * @param {string} documentId\n     * @param {Document extends Models.DefaultDocument ? Models.DataWithoutDocumentKeys : Omit<Document, keyof Models.Document>} data\n     * @param {string[]} permissions\n     * @throws {AppwriteException}\n     * @returns {Promise<Document>}\n     */\n    createDocument(databaseId, collectionId, documentId, data, permissions) {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n        if (typeof collectionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"collectionId\"');\n        }\n        if (typeof documentId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"documentId\"');\n        }\n        if (typeof data === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"data\"');\n        }\n        const apiPath = '/databases/{databaseId}/collections/{collectionId}/documents'.replace('{databaseId}', databaseId).replace('{collectionId}', collectionId);\n        const payload = {};\n        if (typeof documentId !== 'undefined') {\n            payload['documentId'] = documentId;\n        }\n        if (typeof data !== 'undefined') {\n            payload['data'] = data;\n        }\n        if (typeof permissions !== 'undefined') {\n            payload['permissions'] = permissions;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('post', uri, apiHeaders, payload);\n    }\n    /**\n     * Get a document by its unique ID. This endpoint response returns a JSON object with the document data.\n     *\n     * @param {string} databaseId\n     * @param {string} collectionId\n     * @param {string} documentId\n     * @param {string[]} queries\n     * @throws {AppwriteException}\n     * @returns {Promise<Document>}\n     */\n    getDocument(databaseId, collectionId, documentId, queries) {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n        if (typeof collectionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"collectionId\"');\n        }\n        if (typeof documentId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"documentId\"');\n        }\n        const apiPath = '/databases/{databaseId}/collections/{collectionId}/documents/{documentId}'.replace('{databaseId}', databaseId).replace('{collectionId}', collectionId).replace('{documentId}', documentId);\n        const payload = {};\n        if (typeof queries !== 'undefined') {\n            payload['queries'] = queries;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n    /**\n     * **WARNING: Experimental Feature** - This endpoint is experimental and not yet officially supported. It may be subject to breaking changes or removal in future versions.\n     *\n     * Create or update a Document. Before using this route, you should create a new collection resource using either a [server integration](https://appwrite.io/docs/server/databases#databasesCreateCollection) API or directly from your database console.\n     *\n     * @param {string} databaseId\n     * @param {string} collectionId\n     * @param {string} documentId\n     * @param {object} data\n     * @param {string[]} permissions\n     * @throws {AppwriteException}\n     * @returns {Promise<Document>}\n     */\n    upsertDocument(databaseId, collectionId, documentId, data, permissions) {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n        if (typeof collectionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"collectionId\"');\n        }\n        if (typeof documentId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"documentId\"');\n        }\n        if (typeof data === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"data\"');\n        }\n        const apiPath = '/databases/{databaseId}/collections/{collectionId}/documents/{documentId}'.replace('{databaseId}', databaseId).replace('{collectionId}', collectionId).replace('{documentId}', documentId);\n        const payload = {};\n        if (typeof data !== 'undefined') {\n            payload['data'] = data;\n        }\n        if (typeof permissions !== 'undefined') {\n            payload['permissions'] = permissions;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('put', uri, apiHeaders, payload);\n    }\n    /**\n     * Update a document by its unique ID. Using the patch method you can pass only specific fields that will get updated.\n     *\n     * @param {string} databaseId\n     * @param {string} collectionId\n     * @param {string} documentId\n     * @param {Partial<Document extends Models.DefaultDocument ? Models.DataWithoutDocumentKeys : Omit<Document, keyof Models.Document>>} data\n     * @param {string[]} permissions\n     * @throws {AppwriteException}\n     * @returns {Promise<Document>}\n     */\n    updateDocument(databaseId, collectionId, documentId, data, permissions) {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n        if (typeof collectionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"collectionId\"');\n        }\n        if (typeof documentId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"documentId\"');\n        }\n        const apiPath = '/databases/{databaseId}/collections/{collectionId}/documents/{documentId}'.replace('{databaseId}', databaseId).replace('{collectionId}', collectionId).replace('{documentId}', documentId);\n        const payload = {};\n        if (typeof data !== 'undefined') {\n            payload['data'] = data;\n        }\n        if (typeof permissions !== 'undefined') {\n            payload['permissions'] = permissions;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('patch', uri, apiHeaders, payload);\n    }\n    /**\n     * Delete a document by its unique ID.\n     *\n     * @param {string} databaseId\n     * @param {string} collectionId\n     * @param {string} documentId\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    deleteDocument(databaseId, collectionId, documentId) {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n        if (typeof collectionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"collectionId\"');\n        }\n        if (typeof documentId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"documentId\"');\n        }\n        const apiPath = '/databases/{databaseId}/collections/{collectionId}/documents/{documentId}'.replace('{databaseId}', databaseId).replace('{collectionId}', collectionId).replace('{documentId}', documentId);\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('delete', uri, apiHeaders, payload);\n    }\n    /**\n     * Decrement a specific attribute of a document by a given value.\n     *\n     * @param {string} databaseId\n     * @param {string} collectionId\n     * @param {string} documentId\n     * @param {string} attribute\n     * @param {number} value\n     * @param {number} min\n     * @throws {AppwriteException}\n     * @returns {Promise<Document>}\n     */\n    decrementDocumentAttribute(databaseId, collectionId, documentId, attribute, value, min) {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n        if (typeof collectionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"collectionId\"');\n        }\n        if (typeof documentId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"documentId\"');\n        }\n        if (typeof attribute === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"attribute\"');\n        }\n        const apiPath = '/databases/{databaseId}/collections/{collectionId}/documents/{documentId}/{attribute}/decrement'.replace('{databaseId}', databaseId).replace('{collectionId}', collectionId).replace('{documentId}', documentId).replace('{attribute}', attribute);\n        const payload = {};\n        if (typeof value !== 'undefined') {\n            payload['value'] = value;\n        }\n        if (typeof min !== 'undefined') {\n            payload['min'] = min;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('patch', uri, apiHeaders, payload);\n    }\n    /**\n     * Increment a specific attribute of a document by a given value.\n     *\n     * @param {string} databaseId\n     * @param {string} collectionId\n     * @param {string} documentId\n     * @param {string} attribute\n     * @param {number} value\n     * @param {number} max\n     * @throws {AppwriteException}\n     * @returns {Promise<Document>}\n     */\n    incrementDocumentAttribute(databaseId, collectionId, documentId, attribute, value, max) {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n        if (typeof collectionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"collectionId\"');\n        }\n        if (typeof documentId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"documentId\"');\n        }\n        if (typeof attribute === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"attribute\"');\n        }\n        const apiPath = '/databases/{databaseId}/collections/{collectionId}/documents/{documentId}/{attribute}/increment'.replace('{databaseId}', databaseId).replace('{collectionId}', collectionId).replace('{documentId}', documentId).replace('{attribute}', attribute);\n        const payload = {};\n        if (typeof value !== 'undefined') {\n            payload['value'] = value;\n        }\n        if (typeof max !== 'undefined') {\n            payload['max'] = max;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('patch', uri, apiHeaders, payload);\n    }\n}\n\nclass Functions {\n    constructor(client) {\n        this.client = client;\n    }\n    /**\n     * Get a list of all the current user function execution logs. You can use the query params to filter your results.\n     *\n     * @param {string} functionId\n     * @param {string[]} queries\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.ExecutionList>}\n     */\n    listExecutions(functionId, queries) {\n        if (typeof functionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"functionId\"');\n        }\n        const apiPath = '/functions/{functionId}/executions'.replace('{functionId}', functionId);\n        const payload = {};\n        if (typeof queries !== 'undefined') {\n            payload['queries'] = queries;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n    /**\n     * Trigger a function execution. The returned object will return you the current execution status. You can ping the `Get Execution` endpoint to get updates on the current execution status. Once this endpoint is called, your function execution process will start asynchronously.\n     *\n     * @param {string} functionId\n     * @param {string} body\n     * @param {boolean} async\n     * @param {string} xpath\n     * @param {ExecutionMethod} method\n     * @param {object} headers\n     * @param {string} scheduledAt\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Execution>}\n     */\n    createExecution(functionId, body, async, xpath, method, headers, scheduledAt) {\n        if (typeof functionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"functionId\"');\n        }\n        const apiPath = '/functions/{functionId}/executions'.replace('{functionId}', functionId);\n        const payload = {};\n        if (typeof body !== 'undefined') {\n            payload['body'] = body;\n        }\n        if (typeof async !== 'undefined') {\n            payload['async'] = async;\n        }\n        if (typeof xpath !== 'undefined') {\n            payload['path'] = xpath;\n        }\n        if (typeof method !== 'undefined') {\n            payload['method'] = method;\n        }\n        if (typeof headers !== 'undefined') {\n            payload['headers'] = headers;\n        }\n        if (typeof scheduledAt !== 'undefined') {\n            payload['scheduledAt'] = scheduledAt;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('post', uri, apiHeaders, payload);\n    }\n    /**\n     * Get a function execution log by its unique ID.\n     *\n     * @param {string} functionId\n     * @param {string} executionId\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Execution>}\n     */\n    getExecution(functionId, executionId) {\n        if (typeof functionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"functionId\"');\n        }\n        if (typeof executionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"executionId\"');\n        }\n        const apiPath = '/functions/{functionId}/executions/{executionId}'.replace('{functionId}', functionId).replace('{executionId}', executionId);\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n}\n\nclass Graphql {\n    constructor(client) {\n        this.client = client;\n    }\n    /**\n     * Execute a GraphQL mutation.\n     *\n     * @param {object} query\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    query(query) {\n        if (typeof query === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"query\"');\n        }\n        const apiPath = '/graphql';\n        const payload = {};\n        if (typeof query !== 'undefined') {\n            payload['query'] = query;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'x-sdk-graphql': 'true',\n            'content-type': 'application/json',\n        };\n        return this.client.call('post', uri, apiHeaders, payload);\n    }\n    /**\n     * Execute a GraphQL mutation.\n     *\n     * @param {object} query\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    mutation(query) {\n        if (typeof query === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"query\"');\n        }\n        const apiPath = '/graphql/mutation';\n        const payload = {};\n        if (typeof query !== 'undefined') {\n            payload['query'] = query;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'x-sdk-graphql': 'true',\n            'content-type': 'application/json',\n        };\n        return this.client.call('post', uri, apiHeaders, payload);\n    }\n}\n\nclass Locale {\n    constructor(client) {\n        this.client = client;\n    }\n    /**\n     * Get the current user location based on IP. Returns an object with user country code, country name, continent name, continent code, ip address and suggested currency. You can use the locale header to get the data in a supported language.\n     *\n     * ([IP Geolocation by DB-IP](https://db-ip.com))\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Locale>}\n     */\n    get() {\n        const apiPath = '/locale';\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n    /**\n     * List of all locale codes in [ISO 639-1](https://en.wikipedia.org/wiki/List_of_ISO_639-1_codes).\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.LocaleCodeList>}\n     */\n    listCodes() {\n        const apiPath = '/locale/codes';\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n    /**\n     * List of all continents. You can use the locale header to get the data in a supported language.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.ContinentList>}\n     */\n    listContinents() {\n        const apiPath = '/locale/continents';\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n    /**\n     * List of all countries. You can use the locale header to get the data in a supported language.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.CountryList>}\n     */\n    listCountries() {\n        const apiPath = '/locale/countries';\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n    /**\n     * List of all countries that are currently members of the EU. You can use the locale header to get the data in a supported language.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.CountryList>}\n     */\n    listCountriesEU() {\n        const apiPath = '/locale/countries/eu';\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n    /**\n     * List of all countries phone codes. You can use the locale header to get the data in a supported language.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.PhoneList>}\n     */\n    listCountriesPhones() {\n        const apiPath = '/locale/countries/phones';\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n    /**\n     * List of all currencies, including currency symbol, name, plural, and decimal digits for all major and minor currencies. You can use the locale header to get the data in a supported language.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.CurrencyList>}\n     */\n    listCurrencies() {\n        const apiPath = '/locale/currencies';\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n    /**\n     * List of all languages classified by ISO 639-1 including 2-letter code, name in English, and name in the respective language.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.LanguageList>}\n     */\n    listLanguages() {\n        const apiPath = '/locale/languages';\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n}\n\nclass Messaging {\n    constructor(client) {\n        this.client = client;\n    }\n    /**\n     * Create a new subscriber.\n     *\n     * @param {string} topicId\n     * @param {string} subscriberId\n     * @param {string} targetId\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Subscriber>}\n     */\n    createSubscriber(topicId, subscriberId, targetId) {\n        if (typeof topicId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"topicId\"');\n        }\n        if (typeof subscriberId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"subscriberId\"');\n        }\n        if (typeof targetId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"targetId\"');\n        }\n        const apiPath = '/messaging/topics/{topicId}/subscribers'.replace('{topicId}', topicId);\n        const payload = {};\n        if (typeof subscriberId !== 'undefined') {\n            payload['subscriberId'] = subscriberId;\n        }\n        if (typeof targetId !== 'undefined') {\n            payload['targetId'] = targetId;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('post', uri, apiHeaders, payload);\n    }\n    /**\n     * Delete a subscriber by its unique ID.\n     *\n     * @param {string} topicId\n     * @param {string} subscriberId\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    deleteSubscriber(topicId, subscriberId) {\n        if (typeof topicId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"topicId\"');\n        }\n        if (typeof subscriberId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"subscriberId\"');\n        }\n        const apiPath = '/messaging/topics/{topicId}/subscribers/{subscriberId}'.replace('{topicId}', topicId).replace('{subscriberId}', subscriberId);\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('delete', uri, apiHeaders, payload);\n    }\n}\n\nclass Storage {\n    constructor(client) {\n        this.client = client;\n    }\n    /**\n     * Get a list of all the user files. You can use the query params to filter your results.\n     *\n     * @param {string} bucketId\n     * @param {string[]} queries\n     * @param {string} search\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.FileList>}\n     */\n    listFiles(bucketId, queries, search) {\n        if (typeof bucketId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"bucketId\"');\n        }\n        const apiPath = '/storage/buckets/{bucketId}/files'.replace('{bucketId}', bucketId);\n        const payload = {};\n        if (typeof queries !== 'undefined') {\n            payload['queries'] = queries;\n        }\n        if (typeof search !== 'undefined') {\n            payload['search'] = search;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n    /**\n     * Create a new file. Before using this route, you should create a new bucket resource using either a [server integration](https://appwrite.io/docs/server/storage#storageCreateBucket) API or directly from your Appwrite console.\n     *\n     * Larger files should be uploaded using multiple requests with the [content-range](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Content-Range) header to send a partial request with a maximum supported chunk of `5MB`. The `content-range` header values should always be in bytes.\n     *\n     * When the first request is sent, the server will return the **File** object, and the subsequent part request must include the file&#039;s **id** in `x-appwrite-id` header to allow the server to know that the partial upload is for the existing file and not for a new one.\n     *\n     * If you&#039;re creating a new file using one of the Appwrite SDKs, all the chunking logic will be managed by the SDK internally.\n     *\n     *\n     * @param {string} bucketId\n     * @param {string} fileId\n     * @param {File} file\n     * @param {string[]} permissions\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.File>}\n     */\n    createFile(bucketId, fileId, file, permissions, onProgress = (progress) => { }) {\n        if (typeof bucketId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"bucketId\"');\n        }\n        if (typeof fileId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"fileId\"');\n        }\n        if (typeof file === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"file\"');\n        }\n        const apiPath = '/storage/buckets/{bucketId}/files'.replace('{bucketId}', bucketId);\n        const payload = {};\n        if (typeof fileId !== 'undefined') {\n            payload['fileId'] = fileId;\n        }\n        if (typeof file !== 'undefined') {\n            payload['file'] = file;\n        }\n        if (typeof permissions !== 'undefined') {\n            payload['permissions'] = permissions;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'multipart/form-data',\n        };\n        return this.client.chunkedUpload('post', uri, apiHeaders, payload, onProgress);\n    }\n    /**\n     * Get a file by its unique ID. This endpoint response returns a JSON object with the file metadata.\n     *\n     * @param {string} bucketId\n     * @param {string} fileId\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.File>}\n     */\n    getFile(bucketId, fileId) {\n        if (typeof bucketId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"bucketId\"');\n        }\n        if (typeof fileId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"fileId\"');\n        }\n        const apiPath = '/storage/buckets/{bucketId}/files/{fileId}'.replace('{bucketId}', bucketId).replace('{fileId}', fileId);\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n    /**\n     * Update a file by its unique ID. Only users with write permissions have access to update this resource.\n     *\n     * @param {string} bucketId\n     * @param {string} fileId\n     * @param {string} name\n     * @param {string[]} permissions\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.File>}\n     */\n    updateFile(bucketId, fileId, name, permissions) {\n        if (typeof bucketId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"bucketId\"');\n        }\n        if (typeof fileId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"fileId\"');\n        }\n        const apiPath = '/storage/buckets/{bucketId}/files/{fileId}'.replace('{bucketId}', bucketId).replace('{fileId}', fileId);\n        const payload = {};\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n        if (typeof permissions !== 'undefined') {\n            payload['permissions'] = permissions;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('put', uri, apiHeaders, payload);\n    }\n    /**\n     * Delete a file by its unique ID. Only users with write permissions have access to delete this resource.\n     *\n     * @param {string} bucketId\n     * @param {string} fileId\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    deleteFile(bucketId, fileId) {\n        if (typeof bucketId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"bucketId\"');\n        }\n        if (typeof fileId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"fileId\"');\n        }\n        const apiPath = '/storage/buckets/{bucketId}/files/{fileId}'.replace('{bucketId}', bucketId).replace('{fileId}', fileId);\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('delete', uri, apiHeaders, payload);\n    }\n    /**\n     * Get a file content by its unique ID. The endpoint response return with a &#039;Content-Disposition: attachment&#039; header that tells the browser to start downloading the file to user downloads directory.\n     *\n     * @param {string} bucketId\n     * @param {string} fileId\n     * @param {string} token\n     * @throws {AppwriteException}\n     * @returns {string}\n     */\n    getFileDownload(bucketId, fileId, token) {\n        if (typeof bucketId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"bucketId\"');\n        }\n        if (typeof fileId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"fileId\"');\n        }\n        const apiPath = '/storage/buckets/{bucketId}/files/{fileId}/download'.replace('{bucketId}', bucketId).replace('{fileId}', fileId);\n        const payload = {};\n        if (typeof token !== 'undefined') {\n            payload['token'] = token;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        payload['project'] = this.client.config.project;\n        for (const [key, value] of Object.entries(Service.flatten(payload))) {\n            uri.searchParams.append(key, value);\n        }\n        return uri.toString();\n    }\n    /**\n     * Get a file preview image. Currently, this method supports preview for image files (jpg, png, and gif), other supported formats, like pdf, docs, slides, and spreadsheets, will return the file icon image. You can also pass query string arguments for cutting and resizing your preview image. Preview is supported only for image files smaller than 10MB.\n     *\n     * @param {string} bucketId\n     * @param {string} fileId\n     * @param {number} width\n     * @param {number} height\n     * @param {ImageGravity} gravity\n     * @param {number} quality\n     * @param {number} borderWidth\n     * @param {string} borderColor\n     * @param {number} borderRadius\n     * @param {number} opacity\n     * @param {number} rotation\n     * @param {string} background\n     * @param {ImageFormat} output\n     * @param {string} token\n     * @throws {AppwriteException}\n     * @returns {string}\n     */\n    getFilePreview(bucketId, fileId, width, height, gravity, quality, borderWidth, borderColor, borderRadius, opacity, rotation, background, output, token) {\n        if (typeof bucketId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"bucketId\"');\n        }\n        if (typeof fileId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"fileId\"');\n        }\n        const apiPath = '/storage/buckets/{bucketId}/files/{fileId}/preview'.replace('{bucketId}', bucketId).replace('{fileId}', fileId);\n        const payload = {};\n        if (typeof width !== 'undefined') {\n            payload['width'] = width;\n        }\n        if (typeof height !== 'undefined') {\n            payload['height'] = height;\n        }\n        if (typeof gravity !== 'undefined') {\n            payload['gravity'] = gravity;\n        }\n        if (typeof quality !== 'undefined') {\n            payload['quality'] = quality;\n        }\n        if (typeof borderWidth !== 'undefined') {\n            payload['borderWidth'] = borderWidth;\n        }\n        if (typeof borderColor !== 'undefined') {\n            payload['borderColor'] = borderColor;\n        }\n        if (typeof borderRadius !== 'undefined') {\n            payload['borderRadius'] = borderRadius;\n        }\n        if (typeof opacity !== 'undefined') {\n            payload['opacity'] = opacity;\n        }\n        if (typeof rotation !== 'undefined') {\n            payload['rotation'] = rotation;\n        }\n        if (typeof background !== 'undefined') {\n            payload['background'] = background;\n        }\n        if (typeof output !== 'undefined') {\n            payload['output'] = output;\n        }\n        if (typeof token !== 'undefined') {\n            payload['token'] = token;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        payload['project'] = this.client.config.project;\n        for (const [key, value] of Object.entries(Service.flatten(payload))) {\n            uri.searchParams.append(key, value);\n        }\n        return uri.toString();\n    }\n    /**\n     * Get a file content by its unique ID. This endpoint is similar to the download method but returns with no  &#039;Content-Disposition: attachment&#039; header.\n     *\n     * @param {string} bucketId\n     * @param {string} fileId\n     * @param {string} token\n     * @throws {AppwriteException}\n     * @returns {string}\n     */\n    getFileView(bucketId, fileId, token) {\n        if (typeof bucketId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"bucketId\"');\n        }\n        if (typeof fileId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"fileId\"');\n        }\n        const apiPath = '/storage/buckets/{bucketId}/files/{fileId}/view'.replace('{bucketId}', bucketId).replace('{fileId}', fileId);\n        const payload = {};\n        if (typeof token !== 'undefined') {\n            payload['token'] = token;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        payload['project'] = this.client.config.project;\n        for (const [key, value] of Object.entries(Service.flatten(payload))) {\n            uri.searchParams.append(key, value);\n        }\n        return uri.toString();\n    }\n}\n\nclass Teams {\n    constructor(client) {\n        this.client = client;\n    }\n    /**\n     * Get a list of all the teams in which the current user is a member. You can use the parameters to filter your results.\n     *\n     * @param {string[]} queries\n     * @param {string} search\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.TeamList<Preferences>>}\n     */\n    list(queries, search) {\n        const apiPath = '/teams';\n        const payload = {};\n        if (typeof queries !== 'undefined') {\n            payload['queries'] = queries;\n        }\n        if (typeof search !== 'undefined') {\n            payload['search'] = search;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n    /**\n     * Create a new team. The user who creates the team will automatically be assigned as the owner of the team. Only the users with the owner role can invite new members, add new owners and delete or update the team.\n     *\n     * @param {string} teamId\n     * @param {string} name\n     * @param {string[]} roles\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Team<Preferences>>}\n     */\n    create(teamId, name, roles) {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n        if (typeof name === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"name\"');\n        }\n        const apiPath = '/teams';\n        const payload = {};\n        if (typeof teamId !== 'undefined') {\n            payload['teamId'] = teamId;\n        }\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n        if (typeof roles !== 'undefined') {\n            payload['roles'] = roles;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('post', uri, apiHeaders, payload);\n    }\n    /**\n     * Get a team by its ID. All team members have read access for this resource.\n     *\n     * @param {string} teamId\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Team<Preferences>>}\n     */\n    get(teamId) {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n        const apiPath = '/teams/{teamId}'.replace('{teamId}', teamId);\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n    /**\n     * Update the team&#039;s name by its unique ID.\n     *\n     * @param {string} teamId\n     * @param {string} name\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Team<Preferences>>}\n     */\n    updateName(teamId, name) {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n        if (typeof name === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"name\"');\n        }\n        const apiPath = '/teams/{teamId}'.replace('{teamId}', teamId);\n        const payload = {};\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('put', uri, apiHeaders, payload);\n    }\n    /**\n     * Delete a team using its ID. Only team members with the owner role can delete the team.\n     *\n     * @param {string} teamId\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    delete(teamId) {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n        const apiPath = '/teams/{teamId}'.replace('{teamId}', teamId);\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('delete', uri, apiHeaders, payload);\n    }\n    /**\n     * Use this endpoint to list a team&#039;s members using the team&#039;s ID. All team members have read access to this endpoint. Hide sensitive attributes from the response by toggling membership privacy in the Console.\n     *\n     * @param {string} teamId\n     * @param {string[]} queries\n     * @param {string} search\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.MembershipList>}\n     */\n    listMemberships(teamId, queries, search) {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n        const apiPath = '/teams/{teamId}/memberships'.replace('{teamId}', teamId);\n        const payload = {};\n        if (typeof queries !== 'undefined') {\n            payload['queries'] = queries;\n        }\n        if (typeof search !== 'undefined') {\n            payload['search'] = search;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n    /**\n     * Invite a new member to join your team. Provide an ID for existing users, or invite unregistered users using an email or phone number. If initiated from a Client SDK, Appwrite will send an email or sms with a link to join the team to the invited user, and an account will be created for them if one doesn&#039;t exist. If initiated from a Server SDK, the new member will be added automatically to the team.\n     *\n     * You only need to provide one of a user ID, email, or phone number. Appwrite will prioritize accepting the user ID &gt; email &gt; phone number if you provide more than one of these parameters.\n     *\n     * Use the `url` parameter to redirect the user from the invitation email to your app. After the user is redirected, use the [Update Team Membership Status](https://appwrite.io/docs/references/cloud/client-web/teams#updateMembershipStatus) endpoint to allow the user to accept the invitation to the team.\n     *\n     * Please note that to avoid a [Redirect Attack](https://github.com/OWASP/CheatSheetSeries/blob/master/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.md) Appwrite will accept the only redirect URLs under the domains you have added as a platform on the Appwrite Console.\n     *\n     *\n     * @param {string} teamId\n     * @param {string[]} roles\n     * @param {string} email\n     * @param {string} userId\n     * @param {string} phone\n     * @param {string} url\n     * @param {string} name\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Membership>}\n     */\n    createMembership(teamId, roles, email, userId, phone, url, name) {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n        if (typeof roles === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"roles\"');\n        }\n        const apiPath = '/teams/{teamId}/memberships'.replace('{teamId}', teamId);\n        const payload = {};\n        if (typeof email !== 'undefined') {\n            payload['email'] = email;\n        }\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n        if (typeof phone !== 'undefined') {\n            payload['phone'] = phone;\n        }\n        if (typeof roles !== 'undefined') {\n            payload['roles'] = roles;\n        }\n        if (typeof url !== 'undefined') {\n            payload['url'] = url;\n        }\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('post', uri, apiHeaders, payload);\n    }\n    /**\n     * Get a team member by the membership unique id. All team members have read access for this resource. Hide sensitive attributes from the response by toggling membership privacy in the Console.\n     *\n     * @param {string} teamId\n     * @param {string} membershipId\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Membership>}\n     */\n    getMembership(teamId, membershipId) {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n        if (typeof membershipId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"membershipId\"');\n        }\n        const apiPath = '/teams/{teamId}/memberships/{membershipId}'.replace('{teamId}', teamId).replace('{membershipId}', membershipId);\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n    /**\n     * Modify the roles of a team member. Only team members with the owner role have access to this endpoint. Learn more about [roles and permissions](https://appwrite.io/docs/permissions).\n     *\n     *\n     * @param {string} teamId\n     * @param {string} membershipId\n     * @param {string[]} roles\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Membership>}\n     */\n    updateMembership(teamId, membershipId, roles) {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n        if (typeof membershipId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"membershipId\"');\n        }\n        if (typeof roles === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"roles\"');\n        }\n        const apiPath = '/teams/{teamId}/memberships/{membershipId}'.replace('{teamId}', teamId).replace('{membershipId}', membershipId);\n        const payload = {};\n        if (typeof roles !== 'undefined') {\n            payload['roles'] = roles;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('patch', uri, apiHeaders, payload);\n    }\n    /**\n     * This endpoint allows a user to leave a team or for a team owner to delete the membership of any other team member. You can also use this endpoint to delete a user membership even if it is not accepted.\n     *\n     * @param {string} teamId\n     * @param {string} membershipId\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    deleteMembership(teamId, membershipId) {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n        if (typeof membershipId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"membershipId\"');\n        }\n        const apiPath = '/teams/{teamId}/memberships/{membershipId}'.replace('{teamId}', teamId).replace('{membershipId}', membershipId);\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('delete', uri, apiHeaders, payload);\n    }\n    /**\n     * Use this endpoint to allow a user to accept an invitation to join a team after being redirected back to your app from the invitation email received by the user.\n     *\n     * If the request is successful, a session for the user is automatically created.\n     *\n     *\n     * @param {string} teamId\n     * @param {string} membershipId\n     * @param {string} userId\n     * @param {string} secret\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Membership>}\n     */\n    updateMembershipStatus(teamId, membershipId, userId, secret) {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n        if (typeof membershipId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"membershipId\"');\n        }\n        if (typeof userId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"userId\"');\n        }\n        if (typeof secret === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"secret\"');\n        }\n        const apiPath = '/teams/{teamId}/memberships/{membershipId}/status'.replace('{teamId}', teamId).replace('{membershipId}', membershipId);\n        const payload = {};\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n        if (typeof secret !== 'undefined') {\n            payload['secret'] = secret;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('patch', uri, apiHeaders, payload);\n    }\n    /**\n     * Get the team&#039;s shared preferences by its unique ID. If a preference doesn&#039;t need to be shared by all team members, prefer storing them in [user preferences](https://appwrite.io/docs/references/cloud/client-web/account#getPrefs).\n     *\n     * @param {string} teamId\n     * @throws {AppwriteException}\n     * @returns {Promise<Preferences>}\n     */\n    getPrefs(teamId) {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n        const apiPath = '/teams/{teamId}/prefs'.replace('{teamId}', teamId);\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n    /**\n     * Update the team&#039;s preferences by its unique ID. The object you pass is stored as is and replaces any previous value. The maximum allowed prefs size is 64kB and throws an error if exceeded.\n     *\n     * @param {string} teamId\n     * @param {object} prefs\n     * @throws {AppwriteException}\n     * @returns {Promise<Preferences>}\n     */\n    updatePrefs(teamId, prefs) {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n        if (typeof prefs === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"prefs\"');\n        }\n        const apiPath = '/teams/{teamId}/prefs'.replace('{teamId}', teamId);\n        const payload = {};\n        if (typeof prefs !== 'undefined') {\n            payload['prefs'] = prefs;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('put', uri, apiHeaders, payload);\n    }\n}\n\n/**\n * Helper class to generate permission strings for resources.\n */\nclass Permission {\n}\n/**\n * Generate read permission string for the provided role.\n *\n * @param {string} role\n * @returns {string}\n */\nPermission.read = (role) => {\n    return `read(\"${role}\")`;\n};\n/**\n * Generate write permission string for the provided role.\n *\n * This is an alias of update, delete, and possibly create.\n * Don't use write in combination with update, delete, or create.\n *\n * @param {string} role\n * @returns {string}\n */\nPermission.write = (role) => {\n    return `write(\"${role}\")`;\n};\n/**\n * Generate create permission string for the provided role.\n *\n * @param {string} role\n * @returns {string}\n */\nPermission.create = (role) => {\n    return `create(\"${role}\")`;\n};\n/**\n * Generate update permission string for the provided role.\n *\n * @param {string} role\n * @returns {string}\n */\nPermission.update = (role) => {\n    return `update(\"${role}\")`;\n};\n/**\n * Generate delete permission string for the provided role.\n *\n * @param {string} role\n * @returns {string}\n */\nPermission.delete = (role) => {\n    return `delete(\"${role}\")`;\n};\n\n/**\n * Helper class to generate role strings for `Permission`.\n */\nclass Role {\n    /**\n     * Grants access to anyone.\n     *\n     * This includes authenticated and unauthenticated users.\n     *\n     * @returns {string}\n     */\n    static any() {\n        return 'any';\n    }\n    /**\n     * Grants access to a specific user by user ID.\n     *\n     * You can optionally pass verified or unverified for\n     * `status` to target specific types of users.\n     *\n     * @param {string} id\n     * @param {string} status\n     * @returns {string}\n     */\n    static user(id, status = '') {\n        if (status === '') {\n            return `user:${id}`;\n        }\n        return `user:${id}/${status}`;\n    }\n    /**\n     * Grants access to any authenticated or anonymous user.\n     *\n     * You can optionally pass verified or unverified for\n     * `status` to target specific types of users.\n     *\n     * @param {string} status\n     * @returns {string}\n     */\n    static users(status = '') {\n        if (status === '') {\n            return 'users';\n        }\n        return `users/${status}`;\n    }\n    /**\n     * Grants access to any guest user without a session.\n     *\n     * Authenticated users don't have access to this role.\n     *\n     * @returns {string}\n     */\n    static guests() {\n        return 'guests';\n    }\n    /**\n     * Grants access to a team by team ID.\n     *\n     * You can optionally pass a role for `role` to target\n     * team members with the specified role.\n     *\n     * @param {string} id\n     * @param {string} role\n     * @returns {string}\n     */\n    static team(id, role = '') {\n        if (role === '') {\n            return `team:${id}`;\n        }\n        return `team:${id}/${role}`;\n    }\n    /**\n     * Grants access to a specific member of a team.\n     *\n     * When the member is removed from the team, they will\n     * no longer have access.\n     *\n     * @param {string} id\n     * @returns {string}\n     */\n    static member(id) {\n        return `member:${id}`;\n    }\n    /**\n     * Grants access to a user with the specified label.\n     *\n     * @param {string} name\n     * @returns  {string}\n     */\n    static label(name) {\n        return `label:${name}`;\n    }\n}\n\nvar _a, _ID_hexTimestamp;\n/**\n * Helper class to generate ID strings for resources.\n */\nclass ID {\n    /**\n     * Uses the provided ID as the ID for the resource.\n     *\n     * @param {string} id\n     * @returns {string}\n     */\n    static custom(id) {\n        return id;\n    }\n    /**\n     * Have Appwrite generate a unique ID for you.\n     *\n     * @param {number} padding. Default is 7.\n     * @returns {string}\n     */\n    static unique(padding = 7) {\n        // Generate a unique ID with padding to have a longer ID\n        const baseId = __classPrivateFieldGet(ID, _a, \"m\", _ID_hexTimestamp).call(ID);\n        let randomPadding = '';\n        for (let i = 0; i < padding; i++) {\n            const randomHexDigit = Math.floor(Math.random() * 16).toString(16);\n            randomPadding += randomHexDigit;\n        }\n        return baseId + randomPadding;\n    }\n}\n_a = ID, _ID_hexTimestamp = function _ID_hexTimestamp() {\n    const now = new Date();\n    const sec = Math.floor(now.getTime() / 1000);\n    const msec = now.getMilliseconds();\n    // Convert to hexadecimal\n    const hexTimestamp = sec.toString(16) + msec.toString(16).padStart(5, '0');\n    return hexTimestamp;\n};\n\nvar AuthenticatorType;\n(function (AuthenticatorType) {\n    AuthenticatorType[\"Totp\"] = \"totp\";\n})(AuthenticatorType || (AuthenticatorType = {}));\n\nvar AuthenticationFactor;\n(function (AuthenticationFactor) {\n    AuthenticationFactor[\"Email\"] = \"email\";\n    AuthenticationFactor[\"Phone\"] = \"phone\";\n    AuthenticationFactor[\"Totp\"] = \"totp\";\n    AuthenticationFactor[\"Recoverycode\"] = \"recoverycode\";\n})(AuthenticationFactor || (AuthenticationFactor = {}));\n\nvar OAuthProvider;\n(function (OAuthProvider) {\n    OAuthProvider[\"Amazon\"] = \"amazon\";\n    OAuthProvider[\"Apple\"] = \"apple\";\n    OAuthProvider[\"Auth0\"] = \"auth0\";\n    OAuthProvider[\"Authentik\"] = \"authentik\";\n    OAuthProvider[\"Autodesk\"] = \"autodesk\";\n    OAuthProvider[\"Bitbucket\"] = \"bitbucket\";\n    OAuthProvider[\"Bitly\"] = \"bitly\";\n    OAuthProvider[\"Box\"] = \"box\";\n    OAuthProvider[\"Dailymotion\"] = \"dailymotion\";\n    OAuthProvider[\"Discord\"] = \"discord\";\n    OAuthProvider[\"Disqus\"] = \"disqus\";\n    OAuthProvider[\"Dropbox\"] = \"dropbox\";\n    OAuthProvider[\"Etsy\"] = \"etsy\";\n    OAuthProvider[\"Facebook\"] = \"facebook\";\n    OAuthProvider[\"Figma\"] = \"figma\";\n    OAuthProvider[\"Github\"] = \"github\";\n    OAuthProvider[\"Gitlab\"] = \"gitlab\";\n    OAuthProvider[\"Google\"] = \"google\";\n    OAuthProvider[\"Linkedin\"] = \"linkedin\";\n    OAuthProvider[\"Microsoft\"] = \"microsoft\";\n    OAuthProvider[\"Notion\"] = \"notion\";\n    OAuthProvider[\"Oidc\"] = \"oidc\";\n    OAuthProvider[\"Okta\"] = \"okta\";\n    OAuthProvider[\"Paypal\"] = \"paypal\";\n    OAuthProvider[\"PaypalSandbox\"] = \"paypalSandbox\";\n    OAuthProvider[\"Podio\"] = \"podio\";\n    OAuthProvider[\"Salesforce\"] = \"salesforce\";\n    OAuthProvider[\"Slack\"] = \"slack\";\n    OAuthProvider[\"Spotify\"] = \"spotify\";\n    OAuthProvider[\"Stripe\"] = \"stripe\";\n    OAuthProvider[\"Tradeshift\"] = \"tradeshift\";\n    OAuthProvider[\"TradeshiftBox\"] = \"tradeshiftBox\";\n    OAuthProvider[\"Twitch\"] = \"twitch\";\n    OAuthProvider[\"Wordpress\"] = \"wordpress\";\n    OAuthProvider[\"Yahoo\"] = \"yahoo\";\n    OAuthProvider[\"Yammer\"] = \"yammer\";\n    OAuthProvider[\"Yandex\"] = \"yandex\";\n    OAuthProvider[\"Zoho\"] = \"zoho\";\n    OAuthProvider[\"Zoom\"] = \"zoom\";\n    OAuthProvider[\"Mock\"] = \"mock\";\n})(OAuthProvider || (OAuthProvider = {}));\n\nvar Browser;\n(function (Browser) {\n    Browser[\"AvantBrowser\"] = \"aa\";\n    Browser[\"AndroidWebViewBeta\"] = \"an\";\n    Browser[\"GoogleChrome\"] = \"ch\";\n    Browser[\"GoogleChromeIOS\"] = \"ci\";\n    Browser[\"GoogleChromeMobile\"] = \"cm\";\n    Browser[\"Chromium\"] = \"cr\";\n    Browser[\"MozillaFirefox\"] = \"ff\";\n    Browser[\"Safari\"] = \"sf\";\n    Browser[\"MobileSafari\"] = \"mf\";\n    Browser[\"MicrosoftEdge\"] = \"ps\";\n    Browser[\"MicrosoftEdgeIOS\"] = \"oi\";\n    Browser[\"OperaMini\"] = \"om\";\n    Browser[\"Opera\"] = \"op\";\n    Browser[\"OperaNext\"] = \"on\";\n})(Browser || (Browser = {}));\n\nvar CreditCard;\n(function (CreditCard) {\n    CreditCard[\"AmericanExpress\"] = \"amex\";\n    CreditCard[\"Argencard\"] = \"argencard\";\n    CreditCard[\"Cabal\"] = \"cabal\";\n    CreditCard[\"Cencosud\"] = \"cencosud\";\n    CreditCard[\"DinersClub\"] = \"diners\";\n    CreditCard[\"Discover\"] = \"discover\";\n    CreditCard[\"Elo\"] = \"elo\";\n    CreditCard[\"Hipercard\"] = \"hipercard\";\n    CreditCard[\"JCB\"] = \"jcb\";\n    CreditCard[\"Mastercard\"] = \"mastercard\";\n    CreditCard[\"Naranja\"] = \"naranja\";\n    CreditCard[\"TarjetaShopping\"] = \"targeta-shopping\";\n    CreditCard[\"UnionChinaPay\"] = \"union-china-pay\";\n    CreditCard[\"Visa\"] = \"visa\";\n    CreditCard[\"MIR\"] = \"mir\";\n    CreditCard[\"Maestro\"] = \"maestro\";\n    CreditCard[\"Rupay\"] = \"rupay\";\n})(CreditCard || (CreditCard = {}));\n\nvar Flag;\n(function (Flag) {\n    Flag[\"Afghanistan\"] = \"af\";\n    Flag[\"Angola\"] = \"ao\";\n    Flag[\"Albania\"] = \"al\";\n    Flag[\"Andorra\"] = \"ad\";\n    Flag[\"UnitedArabEmirates\"] = \"ae\";\n    Flag[\"Argentina\"] = \"ar\";\n    Flag[\"Armenia\"] = \"am\";\n    Flag[\"AntiguaAndBarbuda\"] = \"ag\";\n    Flag[\"Australia\"] = \"au\";\n    Flag[\"Austria\"] = \"at\";\n    Flag[\"Azerbaijan\"] = \"az\";\n    Flag[\"Burundi\"] = \"bi\";\n    Flag[\"Belgium\"] = \"be\";\n    Flag[\"Benin\"] = \"bj\";\n    Flag[\"BurkinaFaso\"] = \"bf\";\n    Flag[\"Bangladesh\"] = \"bd\";\n    Flag[\"Bulgaria\"] = \"bg\";\n    Flag[\"Bahrain\"] = \"bh\";\n    Flag[\"Bahamas\"] = \"bs\";\n    Flag[\"BosniaAndHerzegovina\"] = \"ba\";\n    Flag[\"Belarus\"] = \"by\";\n    Flag[\"Belize\"] = \"bz\";\n    Flag[\"Bolivia\"] = \"bo\";\n    Flag[\"Brazil\"] = \"br\";\n    Flag[\"Barbados\"] = \"bb\";\n    Flag[\"BruneiDarussalam\"] = \"bn\";\n    Flag[\"Bhutan\"] = \"bt\";\n    Flag[\"Botswana\"] = \"bw\";\n    Flag[\"CentralAfricanRepublic\"] = \"cf\";\n    Flag[\"Canada\"] = \"ca\";\n    Flag[\"Switzerland\"] = \"ch\";\n    Flag[\"Chile\"] = \"cl\";\n    Flag[\"China\"] = \"cn\";\n    Flag[\"CoteDIvoire\"] = \"ci\";\n    Flag[\"Cameroon\"] = \"cm\";\n    Flag[\"DemocraticRepublicOfTheCongo\"] = \"cd\";\n    Flag[\"RepublicOfTheCongo\"] = \"cg\";\n    Flag[\"Colombia\"] = \"co\";\n    Flag[\"Comoros\"] = \"km\";\n    Flag[\"CapeVerde\"] = \"cv\";\n    Flag[\"CostaRica\"] = \"cr\";\n    Flag[\"Cuba\"] = \"cu\";\n    Flag[\"Cyprus\"] = \"cy\";\n    Flag[\"CzechRepublic\"] = \"cz\";\n    Flag[\"Germany\"] = \"de\";\n    Flag[\"Djibouti\"] = \"dj\";\n    Flag[\"Dominica\"] = \"dm\";\n    Flag[\"Denmark\"] = \"dk\";\n    Flag[\"DominicanRepublic\"] = \"do\";\n    Flag[\"Algeria\"] = \"dz\";\n    Flag[\"Ecuador\"] = \"ec\";\n    Flag[\"Egypt\"] = \"eg\";\n    Flag[\"Eritrea\"] = \"er\";\n    Flag[\"Spain\"] = \"es\";\n    Flag[\"Estonia\"] = \"ee\";\n    Flag[\"Ethiopia\"] = \"et\";\n    Flag[\"Finland\"] = \"fi\";\n    Flag[\"Fiji\"] = \"fj\";\n    Flag[\"France\"] = \"fr\";\n    Flag[\"MicronesiaFederatedStatesOf\"] = \"fm\";\n    Flag[\"Gabon\"] = \"ga\";\n    Flag[\"UnitedKingdom\"] = \"gb\";\n    Flag[\"Georgia\"] = \"ge\";\n    Flag[\"Ghana\"] = \"gh\";\n    Flag[\"Guinea\"] = \"gn\";\n    Flag[\"Gambia\"] = \"gm\";\n    Flag[\"GuineaBissau\"] = \"gw\";\n    Flag[\"EquatorialGuinea\"] = \"gq\";\n    Flag[\"Greece\"] = \"gr\";\n    Flag[\"Grenada\"] = \"gd\";\n    Flag[\"Guatemala\"] = \"gt\";\n    Flag[\"Guyana\"] = \"gy\";\n    Flag[\"Honduras\"] = \"hn\";\n    Flag[\"Croatia\"] = \"hr\";\n    Flag[\"Haiti\"] = \"ht\";\n    Flag[\"Hungary\"] = \"hu\";\n    Flag[\"Indonesia\"] = \"id\";\n    Flag[\"India\"] = \"in\";\n    Flag[\"Ireland\"] = \"ie\";\n    Flag[\"IranIslamicRepublicOf\"] = \"ir\";\n    Flag[\"Iraq\"] = \"iq\";\n    Flag[\"Iceland\"] = \"is\";\n    Flag[\"Israel\"] = \"il\";\n    Flag[\"Italy\"] = \"it\";\n    Flag[\"Jamaica\"] = \"jm\";\n    Flag[\"Jordan\"] = \"jo\";\n    Flag[\"Japan\"] = \"jp\";\n    Flag[\"Kazakhstan\"] = \"kz\";\n    Flag[\"Kenya\"] = \"ke\";\n    Flag[\"Kyrgyzstan\"] = \"kg\";\n    Flag[\"Cambodia\"] = \"kh\";\n    Flag[\"Kiribati\"] = \"ki\";\n    Flag[\"SaintKittsAndNevis\"] = \"kn\";\n    Flag[\"SouthKorea\"] = \"kr\";\n    Flag[\"Kuwait\"] = \"kw\";\n    Flag[\"LaoPeopleSDemocraticRepublic\"] = \"la\";\n    Flag[\"Lebanon\"] = \"lb\";\n    Flag[\"Liberia\"] = \"lr\";\n    Flag[\"Libya\"] = \"ly\";\n    Flag[\"SaintLucia\"] = \"lc\";\n    Flag[\"Liechtenstein\"] = \"li\";\n    Flag[\"SriLanka\"] = \"lk\";\n    Flag[\"Lesotho\"] = \"ls\";\n    Flag[\"Lithuania\"] = \"lt\";\n    Flag[\"Luxembourg\"] = \"lu\";\n    Flag[\"Latvia\"] = \"lv\";\n    Flag[\"Morocco\"] = \"ma\";\n    Flag[\"Monaco\"] = \"mc\";\n    Flag[\"Moldova\"] = \"md\";\n    Flag[\"Madagascar\"] = \"mg\";\n    Flag[\"Maldives\"] = \"mv\";\n    Flag[\"Mexico\"] = \"mx\";\n    Flag[\"MarshallIslands\"] = \"mh\";\n    Flag[\"NorthMacedonia\"] = \"mk\";\n    Flag[\"Mali\"] = \"ml\";\n    Flag[\"Malta\"] = \"mt\";\n    Flag[\"Myanmar\"] = \"mm\";\n    Flag[\"Montenegro\"] = \"me\";\n    Flag[\"Mongolia\"] = \"mn\";\n    Flag[\"Mozambique\"] = \"mz\";\n    Flag[\"Mauritania\"] = \"mr\";\n    Flag[\"Mauritius\"] = \"mu\";\n    Flag[\"Malawi\"] = \"mw\";\n    Flag[\"Malaysia\"] = \"my\";\n    Flag[\"Namibia\"] = \"na\";\n    Flag[\"Niger\"] = \"ne\";\n    Flag[\"Nigeria\"] = \"ng\";\n    Flag[\"Nicaragua\"] = \"ni\";\n    Flag[\"Netherlands\"] = \"nl\";\n    Flag[\"Norway\"] = \"no\";\n    Flag[\"Nepal\"] = \"np\";\n    Flag[\"Nauru\"] = \"nr\";\n    Flag[\"NewZealand\"] = \"nz\";\n    Flag[\"Oman\"] = \"om\";\n    Flag[\"Pakistan\"] = \"pk\";\n    Flag[\"Panama\"] = \"pa\";\n    Flag[\"Peru\"] = \"pe\";\n    Flag[\"Philippines\"] = \"ph\";\n    Flag[\"Palau\"] = \"pw\";\n    Flag[\"PapuaNewGuinea\"] = \"pg\";\n    Flag[\"Poland\"] = \"pl\";\n    Flag[\"FrenchPolynesia\"] = \"pf\";\n    Flag[\"NorthKorea\"] = \"kp\";\n    Flag[\"Portugal\"] = \"pt\";\n    Flag[\"Paraguay\"] = \"py\";\n    Flag[\"Qatar\"] = \"qa\";\n    Flag[\"Romania\"] = \"ro\";\n    Flag[\"Russia\"] = \"ru\";\n    Flag[\"Rwanda\"] = \"rw\";\n    Flag[\"SaudiArabia\"] = \"sa\";\n    Flag[\"Sudan\"] = \"sd\";\n    Flag[\"Senegal\"] = \"sn\";\n    Flag[\"Singapore\"] = \"sg\";\n    Flag[\"SolomonIslands\"] = \"sb\";\n    Flag[\"SierraLeone\"] = \"sl\";\n    Flag[\"ElSalvador\"] = \"sv\";\n    Flag[\"SanMarino\"] = \"sm\";\n    Flag[\"Somalia\"] = \"so\";\n    Flag[\"Serbia\"] = \"rs\";\n    Flag[\"SouthSudan\"] = \"ss\";\n    Flag[\"SaoTomeAndPrincipe\"] = \"st\";\n    Flag[\"Suriname\"] = \"sr\";\n    Flag[\"Slovakia\"] = \"sk\";\n    Flag[\"Slovenia\"] = \"si\";\n    Flag[\"Sweden\"] = \"se\";\n    Flag[\"Eswatini\"] = \"sz\";\n    Flag[\"Seychelles\"] = \"sc\";\n    Flag[\"Syria\"] = \"sy\";\n    Flag[\"Chad\"] = \"td\";\n    Flag[\"Togo\"] = \"tg\";\n    Flag[\"Thailand\"] = \"th\";\n    Flag[\"Tajikistan\"] = \"tj\";\n    Flag[\"Turkmenistan\"] = \"tm\";\n    Flag[\"TimorLeste\"] = \"tl\";\n    Flag[\"Tonga\"] = \"to\";\n    Flag[\"TrinidadAndTobago\"] = \"tt\";\n    Flag[\"Tunisia\"] = \"tn\";\n    Flag[\"Turkey\"] = \"tr\";\n    Flag[\"Tuvalu\"] = \"tv\";\n    Flag[\"Tanzania\"] = \"tz\";\n    Flag[\"Uganda\"] = \"ug\";\n    Flag[\"Ukraine\"] = \"ua\";\n    Flag[\"Uruguay\"] = \"uy\";\n    Flag[\"UnitedStates\"] = \"us\";\n    Flag[\"Uzbekistan\"] = \"uz\";\n    Flag[\"VaticanCity\"] = \"va\";\n    Flag[\"SaintVincentAndTheGrenadines\"] = \"vc\";\n    Flag[\"Venezuela\"] = \"ve\";\n    Flag[\"Vietnam\"] = \"vn\";\n    Flag[\"Vanuatu\"] = \"vu\";\n    Flag[\"Samoa\"] = \"ws\";\n    Flag[\"Yemen\"] = \"ye\";\n    Flag[\"SouthAfrica\"] = \"za\";\n    Flag[\"Zambia\"] = \"zm\";\n    Flag[\"Zimbabwe\"] = \"zw\";\n})(Flag || (Flag = {}));\n\nvar ExecutionMethod;\n(function (ExecutionMethod) {\n    ExecutionMethod[\"GET\"] = \"GET\";\n    ExecutionMethod[\"POST\"] = \"POST\";\n    ExecutionMethod[\"PUT\"] = \"PUT\";\n    ExecutionMethod[\"PATCH\"] = \"PATCH\";\n    ExecutionMethod[\"DELETE\"] = \"DELETE\";\n    ExecutionMethod[\"OPTIONS\"] = \"OPTIONS\";\n})(ExecutionMethod || (ExecutionMethod = {}));\n\nvar ImageGravity;\n(function (ImageGravity) {\n    ImageGravity[\"Center\"] = \"center\";\n    ImageGravity[\"Topleft\"] = \"top-left\";\n    ImageGravity[\"Top\"] = \"top\";\n    ImageGravity[\"Topright\"] = \"top-right\";\n    ImageGravity[\"Left\"] = \"left\";\n    ImageGravity[\"Right\"] = \"right\";\n    ImageGravity[\"Bottomleft\"] = \"bottom-left\";\n    ImageGravity[\"Bottom\"] = \"bottom\";\n    ImageGravity[\"Bottomright\"] = \"bottom-right\";\n})(ImageGravity || (ImageGravity = {}));\n\nvar ImageFormat;\n(function (ImageFormat) {\n    ImageFormat[\"Jpg\"] = \"jpg\";\n    ImageFormat[\"Jpeg\"] = \"jpeg\";\n    ImageFormat[\"Png\"] = \"png\";\n    ImageFormat[\"Webp\"] = \"webp\";\n    ImageFormat[\"Heic\"] = \"heic\";\n    ImageFormat[\"Avif\"] = \"avif\";\n    ImageFormat[\"Gif\"] = \"gif\";\n})(ImageFormat || (ImageFormat = {}));\n\n\n//# sourceMappingURL=sdk.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9hcHB3cml0ZS9kaXN0L2VzbS9zZGsuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0QkFBNEIsK0RBQStELGlCQUFpQjtBQUM1RztBQUNBLG9DQUFvQyxNQUFNLCtCQUErQixZQUFZO0FBQ3JGLG1DQUFtQyxNQUFNLG1DQUFtQyxZQUFZO0FBQ3hGLGdDQUFnQztBQUNoQztBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsUUFBUTtBQUN2QixlQUFlLGlCQUFpQjtBQUNoQyxlQUFlLFlBQVk7QUFDM0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFFBQVE7QUFDbkIsV0FBVyxZQUFZO0FBQ3ZCLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CLFdBQVcsWUFBWTtBQUN2QixhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsUUFBUTtBQUNuQixXQUFXLFlBQVk7QUFDdkIsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFFBQVE7QUFDbkIsV0FBVyxZQUFZO0FBQ3ZCLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CLFdBQVcsWUFBWTtBQUN2QixhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsUUFBUTtBQUNuQixXQUFXLFlBQVk7QUFDdkIsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFFBQVE7QUFDbkIsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFFBQVE7QUFDbkIsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFFBQVE7QUFDbkIsV0FBVyxpQkFBaUI7QUFDNUIsV0FBVyxpQkFBaUI7QUFDNUIsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFFBQVE7QUFDbkIsV0FBVyxRQUFRO0FBQ25CLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CLFdBQVcsUUFBUTtBQUNuQixhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsVUFBVTtBQUNyQixhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CLFdBQVcsUUFBUTtBQUNuQixhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsUUFBUTtBQUNuQixhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsUUFBUTtBQUNuQixhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsUUFBUTtBQUNuQixhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsUUFBUTtBQUNuQixhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsUUFBUTtBQUNuQixhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsUUFBUTtBQUNuQixhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsUUFBUTtBQUNuQixXQUFXLG1CQUFtQjtBQUM5QixhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsVUFBVTtBQUNyQixhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsVUFBVTtBQUNyQixhQUFhO0FBQ2I7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQixhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCO0FBQ3JCLGlCQUFpQjtBQUNqQixhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUdBQW1HLGdCQUFnQjtBQUNuSDtBQUNBO0FBQ0E7QUFDQSx5QkFBeUI7QUFDekIscUJBQXFCO0FBQ3JCO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsK0lBQStJO0FBQy9JLGdIQUFnSCxvQkFBb0I7QUFDcEk7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQ0FBaUM7QUFDakM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUNBQWlDO0FBQ2pDO0FBQ0E7QUFDQTtBQUNBLG1DQUFtQztBQUNuQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlCQUF5QjtBQUN6QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsUUFBUTtBQUN2QjtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0I7QUFDaEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0I7QUFDaEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQjtBQUNoQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQjtBQUNoQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQjtBQUNoQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLGlCQUFpQjtBQUNoQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLG9DQUFvQztBQUNuRCxpQkFBaUIsWUFBWTtBQUM3QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNENBQTRDLGFBQWE7QUFDekQ7QUFDQSxrQ0FBa0M7QUFDbEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtREFBbUQsSUFBSTtBQUN2RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0EsMkNBQTJDLHNCQUFzQjtBQUNqRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxREFBcUQ7QUFDckQ7QUFDQSxxQ0FBcUM7QUFDckM7QUFDQSxvREFBb0QsTUFBTSxHQUFHLFFBQVEsR0FBRyxVQUFVO0FBQ2xGO0FBQ0EsOENBQThDO0FBQzlDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQjtBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBLGtDQUFrQyxhQUFhO0FBQy9DO0FBQ0E7QUFDQSxvQkFBb0IsZUFBZTtBQUNuQztBQUNBO0FBQ0E7QUFDQTtBQUNBLHlGQUF5RixxQkFBcUI7QUFDOUc7QUFDQTtBQUNBO0FBQ0EsaUNBQWlDO0FBQ2pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVEQUF1RDtBQUN2RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdURBQXVEO0FBQ3ZEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQ0FBc0M7O0FBRXRDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxVQUFVO0FBQ3pCLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsUUFBUTtBQUN2QixnQkFBZ0I7QUFDaEIsaUJBQWlCLFVBQVU7QUFDM0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhDQUE4QyxXQUFXLFlBQVksV0FBVztBQUNoRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLFVBQVU7QUFDekIsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxTQUFTO0FBQ3hCLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLG1CQUFtQjtBQUNsQyxnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzREFBc0QsS0FBSyxZQUFZLEtBQUs7QUFDNUU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLG1CQUFtQjtBQUNsQyxlQUFlLFFBQVE7QUFDdkIsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0RBQXNELEtBQUssWUFBWSxLQUFLO0FBQzVFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxtQkFBbUI7QUFDbEMsZ0JBQWdCO0FBQ2hCLGlCQUFpQixVQUFVO0FBQzNCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzREFBc0QsS0FBSyxZQUFZLEtBQUs7QUFDNUU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLHNCQUFzQjtBQUNyQyxnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwrREFBK0Q7QUFDL0Q7QUFDQSxnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsUUFBUTtBQUN2QixnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnREFBZ0Q7QUFDaEQ7QUFDQSxlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLHNCQUFzQjtBQUNyQyxnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3ZUFBd2U7QUFDeGU7QUFDQSxlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQjtBQUNoQixpQkFBaUIsVUFBVTtBQUMzQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3T0FBd087QUFDeE87QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxlQUFlO0FBQzlCLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZUFBZSxVQUFVO0FBQ3pCLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1EQUFtRCxTQUFTLFlBQVksU0FBUztBQUNqRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzREFBc0QsOENBQThDLGNBQWM7QUFDbEg7QUFDQSxlQUFlLFFBQVE7QUFDdkIsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNENBQTRDLFVBQVUsWUFBWSxVQUFVO0FBQzVFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtEQUFrRDtBQUNsRDtBQUNBLGVBQWUsUUFBUTtBQUN2QixnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0Q0FBNEMsVUFBVSxZQUFZLFVBQVU7QUFDNUU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtDQUFrQyxjQUFjLHNHQUFzRztBQUN0SjtBQUNBLGVBQWUsUUFBUTtBQUN2QixnQkFBZ0I7QUFDaEIsaUJBQWlCLFVBQVU7QUFDM0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRDQUE0QyxVQUFVLFlBQVksVUFBVTtBQUM1RTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnREFBZ0QsMkRBQTJEO0FBQzNHO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyQ0FBMkMsU0FBUyxpQkFBaUIsU0FBUztBQUM5RTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsUUFBUTtBQUN2QixnQkFBZ0I7QUFDaEIsaUJBQWlCLFVBQVU7QUFDM0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJDQUEyQyxTQUFTLGlCQUFpQixTQUFTO0FBQzlFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyWUFBMlk7QUFDM1k7QUFDQTtBQUNBO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixlQUFlLFNBQVM7QUFDeEIsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOGlCQUE4aUI7QUFDOWlCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZUFBZSxTQUFTO0FBQ3hCLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdPQUF3TztBQUN4TztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxlQUFlO0FBQzlCLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZUFBZSxVQUFVO0FBQ3pCLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlEQUFpRCxTQUFTLFlBQVksU0FBUztBQUMvRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5WUFBeVk7QUFDelk7QUFDQTtBQUNBO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvb0JBQW9vQjtBQUNwb0I7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLFFBQVE7QUFDdkIsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsK0lBQStJLGlVQUFpVTtBQUNoZDtBQUNBLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDZJQUE2STtBQUM3STtBQUNBLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsU0FBUztBQUN4QixlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0Q0FBNEMsS0FBSyxZQUFZLEtBQUs7QUFDbEU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsWUFBWTtBQUMzQixlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnREFBZ0QsS0FBSyxZQUFZLEtBQUs7QUFDdEU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLFFBQVE7QUFDdkIsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxNQUFNO0FBQ3JCLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlDQUF5QyxLQUFLLFlBQVksS0FBSztBQUMvRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdPQUF3TyxXQUFXO0FBQ25QO0FBQ0EsNktBQTZLO0FBQzdLO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZUFBZSxTQUFTO0FBQ3hCLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1Q0FBdUM7QUFDdkM7QUFDQSxlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsVUFBVTtBQUN6QixnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQ0FBcUMsV0FBVyxjQUFjLGFBQWEsc0JBQXNCLFdBQVcseUJBQXlCLGFBQWE7QUFDbEo7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsa0hBQWtIO0FBQ2pJLGVBQWUsVUFBVTtBQUN6QixnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQ0FBcUMsV0FBVyxjQUFjLGFBQWEsc0JBQXNCLFdBQVcseUJBQXlCLGFBQWE7QUFDbEo7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixlQUFlLFVBQVU7QUFDekIsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUNBQXFDLFdBQVcsY0FBYyxhQUFhLFlBQVksV0FBVyxZQUFZLFdBQVcseUJBQXlCLGFBQWEsMkJBQTJCLFdBQVc7QUFDck07QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZUFBZSxVQUFVO0FBQ3pCLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFDQUFxQyxXQUFXLGNBQWMsYUFBYSxZQUFZLFdBQVcsWUFBWSxXQUFXLHlCQUF5QixhQUFhLDJCQUEyQixXQUFXO0FBQ3JNO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZUFBZSwySEFBMkg7QUFDMUksZUFBZSxVQUFVO0FBQ3pCLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFDQUFxQyxXQUFXLGNBQWMsYUFBYSxZQUFZLFdBQVcsWUFBWSxXQUFXLHlCQUF5QixhQUFhLDJCQUEyQixXQUFXO0FBQ3JNO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZ0JBQWdCO0FBQ2hCLGlCQUFpQixVQUFVO0FBQzNCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQ0FBcUMsV0FBVyxjQUFjLGFBQWEsWUFBWSxXQUFXLFlBQVksV0FBVyx5QkFBeUIsYUFBYSwyQkFBMkIsV0FBVztBQUNyTTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFDQUFxQyxXQUFXLGNBQWMsYUFBYSxZQUFZLFdBQVcsRUFBRSxVQUFVLHNCQUFzQixXQUFXLHlCQUF5QixhQUFhLDJCQUEyQixXQUFXLHlCQUF5QixVQUFVO0FBQzlQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUNBQXFDLFdBQVcsY0FBYyxhQUFhLFlBQVksV0FBVyxFQUFFLFVBQVUsc0JBQXNCLFdBQVcseUJBQXlCLGFBQWEsMkJBQTJCLFdBQVcseUJBQXlCLFVBQVU7QUFDOVA7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsUUFBUTtBQUN2QixlQUFlLFVBQVU7QUFDekIsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUNBQXFDLFdBQVcsdUJBQXVCLFdBQVc7QUFDbEY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZUFBZSxTQUFTO0FBQ3hCLGVBQWUsUUFBUTtBQUN2QixlQUFlLGlCQUFpQjtBQUNoQyxlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFDQUFxQyxXQUFXLHVCQUF1QixXQUFXO0FBQ2xGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQ0FBcUMsV0FBVyxhQUFhLFlBQVksWUFBWSxXQUFXLHlCQUF5QixZQUFZO0FBQ3JJO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsUUFBUTtBQUN2QixnQkFBZ0I7QUFDaEIsaUJBQWlCLFVBQVU7QUFDM0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsUUFBUTtBQUN2QixnQkFBZ0I7QUFDaEIsaUJBQWlCLFVBQVU7QUFDM0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNENBQTRDLFFBQVEsd0JBQXdCLFFBQVE7QUFDcEY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGdCQUFnQjtBQUNoQixpQkFBaUIsVUFBVTtBQUMzQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNENBQTRDLFFBQVEsY0FBYyxhQUFhLFlBQVksUUFBUSxzQkFBc0IsYUFBYTtBQUN0STtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsVUFBVTtBQUN6QixlQUFlLFFBQVE7QUFDdkIsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkNBQTJDLFNBQVMsa0JBQWtCLFNBQVM7QUFDL0U7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4SUFBOEk7QUFDOUk7QUFDQSxtQkFBbUI7QUFDbkI7QUFDQTtBQUNBLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZUFBZSxNQUFNO0FBQ3JCLGVBQWUsVUFBVTtBQUN6QixnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0Esa0ZBQWtGO0FBQ2xGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJDQUEyQyxTQUFTLGtCQUFrQixTQUFTO0FBQy9FO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyQ0FBMkMsU0FBUyxRQUFRLE9BQU8sWUFBWSxTQUFTLHVCQUF1QixPQUFPO0FBQ3RIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixlQUFlLFVBQVU7QUFDekIsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkNBQTJDLFNBQVMsUUFBUSxPQUFPLFlBQVksU0FBUyx1QkFBdUIsT0FBTztBQUN0SDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZ0JBQWdCO0FBQ2hCLGlCQUFpQixVQUFVO0FBQzNCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyQ0FBMkMsU0FBUyxRQUFRLE9BQU8sWUFBWSxTQUFTLHVCQUF1QixPQUFPO0FBQ3RIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzRkFBc0Ysc0NBQXNDO0FBQzVIO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkNBQTJDLFNBQVMsUUFBUSxPQUFPLHFCQUFxQixTQUFTLHVCQUF1QixPQUFPO0FBQy9IO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZUFBZSxjQUFjO0FBQzdCLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixlQUFlLGFBQWE7QUFDNUIsZUFBZSxRQUFRO0FBQ3ZCLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJDQUEyQyxTQUFTLFFBQVEsT0FBTyxvQkFBb0IsU0FBUyx1QkFBdUIsT0FBTztBQUM5SDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1SEFBdUgsc0NBQXNDO0FBQzdKO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkNBQTJDLFNBQVMsUUFBUSxPQUFPLGlCQUFpQixTQUFTLHVCQUF1QixPQUFPO0FBQzNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsVUFBVTtBQUN6QixlQUFlLFFBQVE7QUFDdkIsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixlQUFlLFVBQVU7QUFDekIsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsUUFBUTtBQUN2QixnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQ0FBaUMsT0FBTyxZQUFZLE9BQU87QUFDM0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNEJBQTRCO0FBQzVCO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQ0FBaUMsT0FBTyxZQUFZLE9BQU87QUFDM0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLFFBQVE7QUFDdkIsZ0JBQWdCO0FBQ2hCLGlCQUFpQixVQUFVO0FBQzNCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQ0FBaUMsT0FBTyxZQUFZLE9BQU87QUFDM0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDZDQUE2Qyw4QkFBOEI7QUFDM0U7QUFDQSxlQUFlLFFBQVE7QUFDdkIsZUFBZSxVQUFVO0FBQ3pCLGVBQWUsUUFBUTtBQUN2QixnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQ0FBaUMsT0FBTyx3QkFBd0IsT0FBTztBQUN2RTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0VEFBNFQ7QUFDNVQ7QUFDQSw4SEFBOEgsV0FBVztBQUN6STtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLFFBQVE7QUFDdkIsZUFBZSxVQUFVO0FBQ3pCLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUNBQWlDLE9BQU8sd0JBQXdCLE9BQU87QUFDdkU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQyxPQUFPLGNBQWMsYUFBYSxZQUFZLE9BQU8scUJBQXFCLGFBQWE7QUFDeEg7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixlQUFlLFVBQVU7QUFDekIsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUNBQWlDLE9BQU8sY0FBYyxhQUFhLFlBQVksT0FBTyxxQkFBcUIsYUFBYTtBQUN4SDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZ0JBQWdCO0FBQ2hCLGlCQUFpQixVQUFVO0FBQzNCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQ0FBaUMsT0FBTyxjQUFjLGFBQWEsWUFBWSxPQUFPLHFCQUFxQixhQUFhO0FBQ3hIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQyxPQUFPLGNBQWMsYUFBYSxtQkFBbUIsT0FBTyxxQkFBcUIsYUFBYTtBQUMvSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUJBQXlCLGtFQUFrRTtBQUMzRjtBQUNBLGVBQWUsUUFBUTtBQUN2QixnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQ0FBaUMsT0FBTyxrQkFBa0IsT0FBTztBQUNqRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0QkFBNEI7QUFDNUI7QUFDQSxlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQyxPQUFPLGtCQUFrQixPQUFPO0FBQ2pFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsUUFBUTtBQUNuQixhQUFhO0FBQ2I7QUFDQTtBQUNBLG9CQUFvQixLQUFLO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CLGFBQWE7QUFDYjtBQUNBO0FBQ0EscUJBQXFCLEtBQUs7QUFDMUI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFFBQVE7QUFDbkIsYUFBYTtBQUNiO0FBQ0E7QUFDQSxzQkFBc0IsS0FBSztBQUMzQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsUUFBUTtBQUNuQixhQUFhO0FBQ2I7QUFDQTtBQUNBLHNCQUFzQixLQUFLO0FBQzNCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CLGFBQWE7QUFDYjtBQUNBO0FBQ0Esc0JBQXNCLEtBQUs7QUFDM0I7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0EsMkJBQTJCLEdBQUc7QUFDOUI7QUFDQSx1QkFBdUIsR0FBRyxHQUFHLE9BQU87QUFDcEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLFFBQVE7QUFDdkIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IsT0FBTztBQUMvQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQSwyQkFBMkIsR0FBRztBQUM5QjtBQUNBLHVCQUF1QixHQUFHLEdBQUcsS0FBSztBQUNsQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsUUFBUTtBQUN2QixpQkFBaUI7QUFDakI7QUFDQTtBQUNBLHlCQUF5QixHQUFHO0FBQzVCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCLGtCQUFrQjtBQUNsQjtBQUNBO0FBQ0Esd0JBQXdCLEtBQUs7QUFDN0I7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsUUFBUTtBQUN2QixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdCQUF3QixhQUFhO0FBQ3JDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsQ0FBQyw4Q0FBOEM7O0FBRS9DO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsb0RBQW9EOztBQUVyRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLHNDQUFzQzs7QUFFdkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLDBCQUEwQjs7QUFFM0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLGdDQUFnQzs7QUFFakM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsb0JBQW9COztBQUVyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQywwQ0FBMEM7O0FBRTNDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLG9DQUFvQzs7QUFFckM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyxrQ0FBa0M7O0FBRWtQO0FBQ3JSIiwic291cmNlcyI6WyIvVXNlcnMvc2hhd2F6L0RldmVsb3Blci9iaXpjb2luL2JpemNvaW5fY2xpZW50L25vZGVfbW9kdWxlcy9hcHB3cml0ZS9kaXN0L2VzbS9zZGsuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKlxyXG5Db3B5cmlnaHQgKGMpIE1pY3Jvc29mdCBDb3Jwb3JhdGlvbi5cclxuXHJcblBlcm1pc3Npb24gdG8gdXNlLCBjb3B5LCBtb2RpZnksIGFuZC9vciBkaXN0cmlidXRlIHRoaXMgc29mdHdhcmUgZm9yIGFueVxyXG5wdXJwb3NlIHdpdGggb3Igd2l0aG91dCBmZWUgaXMgaGVyZWJ5IGdyYW50ZWQuXHJcblxyXG5USEUgU09GVFdBUkUgSVMgUFJPVklERUQgXCJBUyBJU1wiIEFORCBUSEUgQVVUSE9SIERJU0NMQUlNUyBBTEwgV0FSUkFOVElFUyBXSVRIXHJcblJFR0FSRCBUTyBUSElTIFNPRlRXQVJFIElOQ0xVRElORyBBTEwgSU1QTElFRCBXQVJSQU5USUVTIE9GIE1FUkNIQU5UQUJJTElUWVxyXG5BTkQgRklUTkVTUy4gSU4gTk8gRVZFTlQgU0hBTEwgVEhFIEFVVEhPUiBCRSBMSUFCTEUgRk9SIEFOWSBTUEVDSUFMLCBESVJFQ1QsXHJcbklORElSRUNULCBPUiBDT05TRVFVRU5USUFMIERBTUFHRVMgT1IgQU5ZIERBTUFHRVMgV0hBVFNPRVZFUiBSRVNVTFRJTkcgRlJPTVxyXG5MT1NTIE9GIFVTRSwgREFUQSBPUiBQUk9GSVRTLCBXSEVUSEVSIElOIEFOIEFDVElPTiBPRiBDT05UUkFDVCwgTkVHTElHRU5DRSBPUlxyXG5PVEhFUiBUT1JUSU9VUyBBQ1RJT04sIEFSSVNJTkcgT1VUIE9GIE9SIElOIENPTk5FQ1RJT04gV0lUSCBUSEUgVVNFIE9SXHJcblBFUkZPUk1BTkNFIE9GIFRISVMgU09GVFdBUkUuXHJcbioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqICovXHJcblxyXG5mdW5jdGlvbiBfX2F3YWl0ZXIodGhpc0FyZywgX2FyZ3VtZW50cywgUCwgZ2VuZXJhdG9yKSB7XHJcbiAgICBmdW5jdGlvbiBhZG9wdCh2YWx1ZSkgeyByZXR1cm4gdmFsdWUgaW5zdGFuY2VvZiBQID8gdmFsdWUgOiBuZXcgUChmdW5jdGlvbiAocmVzb2x2ZSkgeyByZXNvbHZlKHZhbHVlKTsgfSk7IH1cclxuICAgIHJldHVybiBuZXcgKFAgfHwgKFAgPSBQcm9taXNlKSkoZnVuY3Rpb24gKHJlc29sdmUsIHJlamVjdCkge1xyXG4gICAgICAgIGZ1bmN0aW9uIGZ1bGZpbGxlZCh2YWx1ZSkgeyB0cnkgeyBzdGVwKGdlbmVyYXRvci5uZXh0KHZhbHVlKSk7IH0gY2F0Y2ggKGUpIHsgcmVqZWN0KGUpOyB9IH1cclxuICAgICAgICBmdW5jdGlvbiByZWplY3RlZCh2YWx1ZSkgeyB0cnkgeyBzdGVwKGdlbmVyYXRvcltcInRocm93XCJdKHZhbHVlKSk7IH0gY2F0Y2ggKGUpIHsgcmVqZWN0KGUpOyB9IH1cclxuICAgICAgICBmdW5jdGlvbiBzdGVwKHJlc3VsdCkgeyByZXN1bHQuZG9uZSA/IHJlc29sdmUocmVzdWx0LnZhbHVlKSA6IGFkb3B0KHJlc3VsdC52YWx1ZSkudGhlbihmdWxmaWxsZWQsIHJlamVjdGVkKTsgfVxyXG4gICAgICAgIHN0ZXAoKGdlbmVyYXRvciA9IGdlbmVyYXRvci5hcHBseSh0aGlzQXJnLCBfYXJndW1lbnRzIHx8IFtdKSkubmV4dCgpKTtcclxuICAgIH0pO1xyXG59XHJcblxyXG5mdW5jdGlvbiBfX2NsYXNzUHJpdmF0ZUZpZWxkR2V0KHJlY2VpdmVyLCBzdGF0ZSwga2luZCwgZikge1xyXG4gICAgaWYgKGtpbmQgPT09IFwiYVwiICYmICFmKSB0aHJvdyBuZXcgVHlwZUVycm9yKFwiUHJpdmF0ZSBhY2Nlc3NvciB3YXMgZGVmaW5lZCB3aXRob3V0IGEgZ2V0dGVyXCIpO1xyXG4gICAgaWYgKHR5cGVvZiBzdGF0ZSA9PT0gXCJmdW5jdGlvblwiID8gcmVjZWl2ZXIgIT09IHN0YXRlIHx8ICFmIDogIXN0YXRlLmhhcyhyZWNlaXZlcikpIHRocm93IG5ldyBUeXBlRXJyb3IoXCJDYW5ub3QgcmVhZCBwcml2YXRlIG1lbWJlciBmcm9tIGFuIG9iamVjdCB3aG9zZSBjbGFzcyBkaWQgbm90IGRlY2xhcmUgaXRcIik7XHJcbiAgICByZXR1cm4ga2luZCA9PT0gXCJtXCIgPyBmIDoga2luZCA9PT0gXCJhXCIgPyBmLmNhbGwocmVjZWl2ZXIpIDogZiA/IGYudmFsdWUgOiBzdGF0ZS5nZXQocmVjZWl2ZXIpO1xyXG59XG5cbi8qKlxuICogSGVscGVyIGNsYXNzIHRvIGdlbmVyYXRlIHF1ZXJ5IHN0cmluZ3MuXG4gKi9cbmNsYXNzIFF1ZXJ5IHtcbiAgICAvKipcbiAgICAgKiBDb25zdHJ1Y3RvciBmb3IgUXVlcnkgY2xhc3MuXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gbWV0aG9kXG4gICAgICogQHBhcmFtIHtBdHRyaWJ1dGVzVHlwZXN9IGF0dHJpYnV0ZVxuICAgICAqIEBwYXJhbSB7UXVlcnlUeXBlc30gdmFsdWVzXG4gICAgICovXG4gICAgY29uc3RydWN0b3IobWV0aG9kLCBhdHRyaWJ1dGUsIHZhbHVlcykge1xuICAgICAgICB0aGlzLm1ldGhvZCA9IG1ldGhvZDtcbiAgICAgICAgdGhpcy5hdHRyaWJ1dGUgPSBhdHRyaWJ1dGU7XG4gICAgICAgIGlmICh2YWx1ZXMgIT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgaWYgKEFycmF5LmlzQXJyYXkodmFsdWVzKSkge1xuICAgICAgICAgICAgICAgIHRoaXMudmFsdWVzID0gdmFsdWVzO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgdGhpcy52YWx1ZXMgPSBbdmFsdWVzXTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH1cbiAgICAvKipcbiAgICAgKiBDb252ZXJ0IHRoZSBxdWVyeSBvYmplY3QgdG8gYSBKU09OIHN0cmluZy5cbiAgICAgKlxuICAgICAqIEByZXR1cm5zIHtzdHJpbmd9XG4gICAgICovXG4gICAgdG9TdHJpbmcoKSB7XG4gICAgICAgIHJldHVybiBKU09OLnN0cmluZ2lmeSh7XG4gICAgICAgICAgICBtZXRob2Q6IHRoaXMubWV0aG9kLFxuICAgICAgICAgICAgYXR0cmlidXRlOiB0aGlzLmF0dHJpYnV0ZSxcbiAgICAgICAgICAgIHZhbHVlczogdGhpcy52YWx1ZXMsXG4gICAgICAgIH0pO1xuICAgIH1cbn1cbi8qKlxuICogRmlsdGVyIHJlc291cmNlcyB3aGVyZSBhdHRyaWJ1dGUgaXMgZXF1YWwgdG8gdmFsdWUuXG4gKlxuICogQHBhcmFtIHtzdHJpbmd9IGF0dHJpYnV0ZVxuICogQHBhcmFtIHtRdWVyeVR5cGVzfSB2YWx1ZVxuICogQHJldHVybnMge3N0cmluZ31cbiAqL1xuUXVlcnkuZXF1YWwgPSAoYXR0cmlidXRlLCB2YWx1ZSkgPT4gbmV3IFF1ZXJ5KFwiZXF1YWxcIiwgYXR0cmlidXRlLCB2YWx1ZSkudG9TdHJpbmcoKTtcbi8qKlxuICogRmlsdGVyIHJlc291cmNlcyB3aGVyZSBhdHRyaWJ1dGUgaXMgbm90IGVxdWFsIHRvIHZhbHVlLlxuICpcbiAqIEBwYXJhbSB7c3RyaW5nfSBhdHRyaWJ1dGVcbiAqIEBwYXJhbSB7UXVlcnlUeXBlc30gdmFsdWVcbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKi9cblF1ZXJ5Lm5vdEVxdWFsID0gKGF0dHJpYnV0ZSwgdmFsdWUpID0+IG5ldyBRdWVyeShcIm5vdEVxdWFsXCIsIGF0dHJpYnV0ZSwgdmFsdWUpLnRvU3RyaW5nKCk7XG4vKipcbiAqIEZpbHRlciByZXNvdXJjZXMgd2hlcmUgYXR0cmlidXRlIGlzIGxlc3MgdGhhbiB2YWx1ZS5cbiAqXG4gKiBAcGFyYW0ge3N0cmluZ30gYXR0cmlidXRlXG4gKiBAcGFyYW0ge1F1ZXJ5VHlwZXN9IHZhbHVlXG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICovXG5RdWVyeS5sZXNzVGhhbiA9IChhdHRyaWJ1dGUsIHZhbHVlKSA9PiBuZXcgUXVlcnkoXCJsZXNzVGhhblwiLCBhdHRyaWJ1dGUsIHZhbHVlKS50b1N0cmluZygpO1xuLyoqXG4gKiBGaWx0ZXIgcmVzb3VyY2VzIHdoZXJlIGF0dHJpYnV0ZSBpcyBsZXNzIHRoYW4gb3IgZXF1YWwgdG8gdmFsdWUuXG4gKlxuICogQHBhcmFtIHtzdHJpbmd9IGF0dHJpYnV0ZVxuICogQHBhcmFtIHtRdWVyeVR5cGVzfSB2YWx1ZVxuICogQHJldHVybnMge3N0cmluZ31cbiAqL1xuUXVlcnkubGVzc1RoYW5FcXVhbCA9IChhdHRyaWJ1dGUsIHZhbHVlKSA9PiBuZXcgUXVlcnkoXCJsZXNzVGhhbkVxdWFsXCIsIGF0dHJpYnV0ZSwgdmFsdWUpLnRvU3RyaW5nKCk7XG4vKipcbiAqIEZpbHRlciByZXNvdXJjZXMgd2hlcmUgYXR0cmlidXRlIGlzIGdyZWF0ZXIgdGhhbiB2YWx1ZS5cbiAqXG4gKiBAcGFyYW0ge3N0cmluZ30gYXR0cmlidXRlXG4gKiBAcGFyYW0ge1F1ZXJ5VHlwZXN9IHZhbHVlXG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICovXG5RdWVyeS5ncmVhdGVyVGhhbiA9IChhdHRyaWJ1dGUsIHZhbHVlKSA9PiBuZXcgUXVlcnkoXCJncmVhdGVyVGhhblwiLCBhdHRyaWJ1dGUsIHZhbHVlKS50b1N0cmluZygpO1xuLyoqXG4gKiBGaWx0ZXIgcmVzb3VyY2VzIHdoZXJlIGF0dHJpYnV0ZSBpcyBncmVhdGVyIHRoYW4gb3IgZXF1YWwgdG8gdmFsdWUuXG4gKlxuICogQHBhcmFtIHtzdHJpbmd9IGF0dHJpYnV0ZVxuICogQHBhcmFtIHtRdWVyeVR5cGVzfSB2YWx1ZVxuICogQHJldHVybnMge3N0cmluZ31cbiAqL1xuUXVlcnkuZ3JlYXRlclRoYW5FcXVhbCA9IChhdHRyaWJ1dGUsIHZhbHVlKSA9PiBuZXcgUXVlcnkoXCJncmVhdGVyVGhhbkVxdWFsXCIsIGF0dHJpYnV0ZSwgdmFsdWUpLnRvU3RyaW5nKCk7XG4vKipcbiAqIEZpbHRlciByZXNvdXJjZXMgd2hlcmUgYXR0cmlidXRlIGlzIG51bGwuXG4gKlxuICogQHBhcmFtIHtzdHJpbmd9IGF0dHJpYnV0ZVxuICogQHJldHVybnMge3N0cmluZ31cbiAqL1xuUXVlcnkuaXNOdWxsID0gKGF0dHJpYnV0ZSkgPT4gbmV3IFF1ZXJ5KFwiaXNOdWxsXCIsIGF0dHJpYnV0ZSkudG9TdHJpbmcoKTtcbi8qKlxuICogRmlsdGVyIHJlc291cmNlcyB3aGVyZSBhdHRyaWJ1dGUgaXMgbm90IG51bGwuXG4gKlxuICogQHBhcmFtIHtzdHJpbmd9IGF0dHJpYnV0ZVxuICogQHJldHVybnMge3N0cmluZ31cbiAqL1xuUXVlcnkuaXNOb3ROdWxsID0gKGF0dHJpYnV0ZSkgPT4gbmV3IFF1ZXJ5KFwiaXNOb3ROdWxsXCIsIGF0dHJpYnV0ZSkudG9TdHJpbmcoKTtcbi8qKlxuICogRmlsdGVyIHJlc291cmNlcyB3aGVyZSBhdHRyaWJ1dGUgaXMgYmV0d2VlbiBzdGFydCBhbmQgZW5kIChpbmNsdXNpdmUpLlxuICpcbiAqIEBwYXJhbSB7c3RyaW5nfSBhdHRyaWJ1dGVcbiAqIEBwYXJhbSB7c3RyaW5nIHwgbnVtYmVyfSBzdGFydFxuICogQHBhcmFtIHtzdHJpbmcgfCBudW1iZXJ9IGVuZFxuICogQHJldHVybnMge3N0cmluZ31cbiAqL1xuUXVlcnkuYmV0d2VlbiA9IChhdHRyaWJ1dGUsIHN0YXJ0LCBlbmQpID0+IG5ldyBRdWVyeShcImJldHdlZW5cIiwgYXR0cmlidXRlLCBbc3RhcnQsIGVuZF0pLnRvU3RyaW5nKCk7XG4vKipcbiAqIEZpbHRlciByZXNvdXJjZXMgd2hlcmUgYXR0cmlidXRlIHN0YXJ0cyB3aXRoIHZhbHVlLlxuICpcbiAqIEBwYXJhbSB7c3RyaW5nfSBhdHRyaWJ1dGVcbiAqIEBwYXJhbSB7c3RyaW5nfSB2YWx1ZVxuICogQHJldHVybnMge3N0cmluZ31cbiAqL1xuUXVlcnkuc3RhcnRzV2l0aCA9IChhdHRyaWJ1dGUsIHZhbHVlKSA9PiBuZXcgUXVlcnkoXCJzdGFydHNXaXRoXCIsIGF0dHJpYnV0ZSwgdmFsdWUpLnRvU3RyaW5nKCk7XG4vKipcbiAqIEZpbHRlciByZXNvdXJjZXMgd2hlcmUgYXR0cmlidXRlIGVuZHMgd2l0aCB2YWx1ZS5cbiAqXG4gKiBAcGFyYW0ge3N0cmluZ30gYXR0cmlidXRlXG4gKiBAcGFyYW0ge3N0cmluZ30gdmFsdWVcbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKi9cblF1ZXJ5LmVuZHNXaXRoID0gKGF0dHJpYnV0ZSwgdmFsdWUpID0+IG5ldyBRdWVyeShcImVuZHNXaXRoXCIsIGF0dHJpYnV0ZSwgdmFsdWUpLnRvU3RyaW5nKCk7XG4vKipcbiAqIFNwZWNpZnkgd2hpY2ggYXR0cmlidXRlcyBzaG91bGQgYmUgcmV0dXJuZWQgYnkgdGhlIEFQSSBjYWxsLlxuICpcbiAqIEBwYXJhbSB7c3RyaW5nW119IGF0dHJpYnV0ZXNcbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKi9cblF1ZXJ5LnNlbGVjdCA9IChhdHRyaWJ1dGVzKSA9PiBuZXcgUXVlcnkoXCJzZWxlY3RcIiwgdW5kZWZpbmVkLCBhdHRyaWJ1dGVzKS50b1N0cmluZygpO1xuLyoqXG4gKiBGaWx0ZXIgcmVzb3VyY2VzIGJ5IHNlYXJjaGluZyBhdHRyaWJ1dGUgZm9yIHZhbHVlLlxuICogQSBmdWxsdGV4dCBpbmRleCBvbiBhdHRyaWJ1dGUgaXMgcmVxdWlyZWQgZm9yIHRoaXMgcXVlcnkgdG8gd29yay5cbiAqXG4gKiBAcGFyYW0ge3N0cmluZ30gYXR0cmlidXRlXG4gKiBAcGFyYW0ge3N0cmluZ30gdmFsdWVcbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKi9cblF1ZXJ5LnNlYXJjaCA9IChhdHRyaWJ1dGUsIHZhbHVlKSA9PiBuZXcgUXVlcnkoXCJzZWFyY2hcIiwgYXR0cmlidXRlLCB2YWx1ZSkudG9TdHJpbmcoKTtcbi8qKlxuICogU29ydCByZXN1bHRzIGJ5IGF0dHJpYnV0ZSBkZXNjZW5kaW5nLlxuICpcbiAqIEBwYXJhbSB7c3RyaW5nfSBhdHRyaWJ1dGVcbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKi9cblF1ZXJ5Lm9yZGVyRGVzYyA9IChhdHRyaWJ1dGUpID0+IG5ldyBRdWVyeShcIm9yZGVyRGVzY1wiLCBhdHRyaWJ1dGUpLnRvU3RyaW5nKCk7XG4vKipcbiAqIFNvcnQgcmVzdWx0cyBieSBhdHRyaWJ1dGUgYXNjZW5kaW5nLlxuICpcbiAqIEBwYXJhbSB7c3RyaW5nfSBhdHRyaWJ1dGVcbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKi9cblF1ZXJ5Lm9yZGVyQXNjID0gKGF0dHJpYnV0ZSkgPT4gbmV3IFF1ZXJ5KFwib3JkZXJBc2NcIiwgYXR0cmlidXRlKS50b1N0cmluZygpO1xuLyoqXG4gKiBSZXR1cm4gcmVzdWx0cyBhZnRlciBkb2N1bWVudElkLlxuICpcbiAqIEBwYXJhbSB7c3RyaW5nfSBkb2N1bWVudElkXG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICovXG5RdWVyeS5jdXJzb3JBZnRlciA9IChkb2N1bWVudElkKSA9PiBuZXcgUXVlcnkoXCJjdXJzb3JBZnRlclwiLCB1bmRlZmluZWQsIGRvY3VtZW50SWQpLnRvU3RyaW5nKCk7XG4vKipcbiAqIFJldHVybiByZXN1bHRzIGJlZm9yZSBkb2N1bWVudElkLlxuICpcbiAqIEBwYXJhbSB7c3RyaW5nfSBkb2N1bWVudElkXG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICovXG5RdWVyeS5jdXJzb3JCZWZvcmUgPSAoZG9jdW1lbnRJZCkgPT4gbmV3IFF1ZXJ5KFwiY3Vyc29yQmVmb3JlXCIsIHVuZGVmaW5lZCwgZG9jdW1lbnRJZCkudG9TdHJpbmcoKTtcbi8qKlxuICogUmV0dXJuIG9ubHkgbGltaXQgcmVzdWx0cy5cbiAqXG4gKiBAcGFyYW0ge251bWJlcn0gbGltaXRcbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKi9cblF1ZXJ5LmxpbWl0ID0gKGxpbWl0KSA9PiBuZXcgUXVlcnkoXCJsaW1pdFwiLCB1bmRlZmluZWQsIGxpbWl0KS50b1N0cmluZygpO1xuLyoqXG4gKiBGaWx0ZXIgcmVzb3VyY2VzIGJ5IHNraXBwaW5nIHRoZSBmaXJzdCBvZmZzZXQgcmVzdWx0cy5cbiAqXG4gKiBAcGFyYW0ge251bWJlcn0gb2Zmc2V0XG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICovXG5RdWVyeS5vZmZzZXQgPSAob2Zmc2V0KSA9PiBuZXcgUXVlcnkoXCJvZmZzZXRcIiwgdW5kZWZpbmVkLCBvZmZzZXQpLnRvU3RyaW5nKCk7XG4vKipcbiAqIEZpbHRlciByZXNvdXJjZXMgd2hlcmUgYXR0cmlidXRlIGNvbnRhaW5zIHRoZSBzcGVjaWZpZWQgdmFsdWUuXG4gKlxuICogQHBhcmFtIHtzdHJpbmd9IGF0dHJpYnV0ZVxuICogQHBhcmFtIHtzdHJpbmcgfCBzdHJpbmdbXX0gdmFsdWVcbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKi9cblF1ZXJ5LmNvbnRhaW5zID0gKGF0dHJpYnV0ZSwgdmFsdWUpID0+IG5ldyBRdWVyeShcImNvbnRhaW5zXCIsIGF0dHJpYnV0ZSwgdmFsdWUpLnRvU3RyaW5nKCk7XG4vKipcbiAqIENvbWJpbmUgbXVsdGlwbGUgcXVlcmllcyB1c2luZyBsb2dpY2FsIE9SIG9wZXJhdG9yLlxuICpcbiAqIEBwYXJhbSB7c3RyaW5nW119IHF1ZXJpZXNcbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKi9cblF1ZXJ5Lm9yID0gKHF1ZXJpZXMpID0+IG5ldyBRdWVyeShcIm9yXCIsIHVuZGVmaW5lZCwgcXVlcmllcy5tYXAoKHF1ZXJ5KSA9PiBKU09OLnBhcnNlKHF1ZXJ5KSkpLnRvU3RyaW5nKCk7XG4vKipcbiAqIENvbWJpbmUgbXVsdGlwbGUgcXVlcmllcyB1c2luZyBsb2dpY2FsIEFORCBvcGVyYXRvci5cbiAqXG4gKiBAcGFyYW0ge3N0cmluZ1tdfSBxdWVyaWVzXG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICovXG5RdWVyeS5hbmQgPSAocXVlcmllcykgPT4gbmV3IFF1ZXJ5KFwiYW5kXCIsIHVuZGVmaW5lZCwgcXVlcmllcy5tYXAoKHF1ZXJ5KSA9PiBKU09OLnBhcnNlKHF1ZXJ5KSkpLnRvU3RyaW5nKCk7XG5cbi8qKlxuICogRXhjZXB0aW9uIHRocm93biBieSB0aGUgIHBhY2thZ2VcbiAqL1xuY2xhc3MgQXBwd3JpdGVFeGNlcHRpb24gZXh0ZW5kcyBFcnJvciB7XG4gICAgLyoqXG4gICAgICogSW5pdGlhbGl6ZXMgYSBBcHB3cml0ZSBFeGNlcHRpb24uXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gbWVzc2FnZSAtIFRoZSBlcnJvciBtZXNzYWdlLlxuICAgICAqIEBwYXJhbSB7bnVtYmVyfSBjb2RlIC0gVGhlIGVycm9yIGNvZGUuIERlZmF1bHQgaXMgMC5cbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gdHlwZSAtIFRoZSBlcnJvciB0eXBlLiBEZWZhdWx0IGlzIGFuIGVtcHR5IHN0cmluZy5cbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gcmVzcG9uc2UgLSBUaGUgcmVzcG9uc2Ugc3RyaW5nLiBEZWZhdWx0IGlzIGFuIGVtcHR5IHN0cmluZy5cbiAgICAgKi9cbiAgICBjb25zdHJ1Y3RvcihtZXNzYWdlLCBjb2RlID0gMCwgdHlwZSA9ICcnLCByZXNwb25zZSA9ICcnKSB7XG4gICAgICAgIHN1cGVyKG1lc3NhZ2UpO1xuICAgICAgICB0aGlzLm5hbWUgPSAnQXBwd3JpdGVFeGNlcHRpb24nO1xuICAgICAgICB0aGlzLm1lc3NhZ2UgPSBtZXNzYWdlO1xuICAgICAgICB0aGlzLmNvZGUgPSBjb2RlO1xuICAgICAgICB0aGlzLnR5cGUgPSB0eXBlO1xuICAgICAgICB0aGlzLnJlc3BvbnNlID0gcmVzcG9uc2U7XG4gICAgfVxufVxuLyoqXG4gKiBDbGllbnQgdGhhdCBoYW5kbGVzIHJlcXVlc3RzIHRvIEFwcHdyaXRlXG4gKi9cbmNsYXNzIENsaWVudCB7XG4gICAgY29uc3RydWN0b3IoKSB7XG4gICAgICAgIC8qKlxuICAgICAgICAgKiBIb2xkcyBjb25maWd1cmF0aW9uIHN1Y2ggYXMgcHJvamVjdC5cbiAgICAgICAgICovXG4gICAgICAgIHRoaXMuY29uZmlnID0ge1xuICAgICAgICAgICAgZW5kcG9pbnQ6ICdodHRwczovL2Nsb3VkLmFwcHdyaXRlLmlvL3YxJyxcbiAgICAgICAgICAgIGVuZHBvaW50UmVhbHRpbWU6ICcnLFxuICAgICAgICAgICAgcHJvamVjdDogJycsXG4gICAgICAgICAgICBqd3Q6ICcnLFxuICAgICAgICAgICAgbG9jYWxlOiAnJyxcbiAgICAgICAgICAgIHNlc3Npb246ICcnLFxuICAgICAgICAgICAgZGV2a2V5OiAnJyxcbiAgICAgICAgfTtcbiAgICAgICAgLyoqXG4gICAgICAgICAqIEN1c3RvbSBoZWFkZXJzIGZvciBBUEkgcmVxdWVzdHMuXG4gICAgICAgICAqL1xuICAgICAgICB0aGlzLmhlYWRlcnMgPSB7XG4gICAgICAgICAgICAneC1zZGstbmFtZSc6ICdXZWInLFxuICAgICAgICAgICAgJ3gtc2RrLXBsYXRmb3JtJzogJ2NsaWVudCcsXG4gICAgICAgICAgICAneC1zZGstbGFuZ3VhZ2UnOiAnd2ViJyxcbiAgICAgICAgICAgICd4LXNkay12ZXJzaW9uJzogJzE4LjIuMCcsXG4gICAgICAgICAgICAnWC1BcHB3cml0ZS1SZXNwb25zZS1Gb3JtYXQnOiAnMS43LjAnLFxuICAgICAgICB9O1xuICAgICAgICB0aGlzLnJlYWx0aW1lID0ge1xuICAgICAgICAgICAgc29ja2V0OiB1bmRlZmluZWQsXG4gICAgICAgICAgICB0aW1lb3V0OiB1bmRlZmluZWQsXG4gICAgICAgICAgICBoZWFydGJlYXQ6IHVuZGVmaW5lZCxcbiAgICAgICAgICAgIHVybDogJycsXG4gICAgICAgICAgICBjaGFubmVsczogbmV3IFNldCgpLFxuICAgICAgICAgICAgc3Vic2NyaXB0aW9uczogbmV3IE1hcCgpLFxuICAgICAgICAgICAgc3Vic2NyaXB0aW9uc0NvdW50ZXI6IDAsXG4gICAgICAgICAgICByZWNvbm5lY3Q6IHRydWUsXG4gICAgICAgICAgICByZWNvbm5lY3RBdHRlbXB0czogMCxcbiAgICAgICAgICAgIGxhc3RNZXNzYWdlOiB1bmRlZmluZWQsXG4gICAgICAgICAgICBjb25uZWN0OiAoKSA9PiB7XG4gICAgICAgICAgICAgICAgY2xlYXJUaW1lb3V0KHRoaXMucmVhbHRpbWUudGltZW91dCk7XG4gICAgICAgICAgICAgICAgdGhpcy5yZWFsdGltZS50aW1lb3V0ID0gd2luZG93ID09PSBudWxsIHx8IHdpbmRvdyA9PT0gdm9pZCAwID8gdm9pZCAwIDogd2luZG93LnNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICB0aGlzLnJlYWx0aW1lLmNyZWF0ZVNvY2tldCgpO1xuICAgICAgICAgICAgICAgIH0sIDUwKTtcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICBnZXRUaW1lb3V0OiAoKSA9PiB7XG4gICAgICAgICAgICAgICAgc3dpdGNoICh0cnVlKSB7XG4gICAgICAgICAgICAgICAgICAgIGNhc2UgdGhpcy5yZWFsdGltZS5yZWNvbm5lY3RBdHRlbXB0cyA8IDU6XG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gMTAwMDtcbiAgICAgICAgICAgICAgICAgICAgY2FzZSB0aGlzLnJlYWx0aW1lLnJlY29ubmVjdEF0dGVtcHRzIDwgMTU6XG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gNTAwMDtcbiAgICAgICAgICAgICAgICAgICAgY2FzZSB0aGlzLnJlYWx0aW1lLnJlY29ubmVjdEF0dGVtcHRzIDwgMTAwOlxuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIDEwMDAwO1xuICAgICAgICAgICAgICAgICAgICBkZWZhdWx0OlxuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIDYwMDAwO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICBjcmVhdGVIZWFydGJlYXQ6ICgpID0+IHtcbiAgICAgICAgICAgICAgICBpZiAodGhpcy5yZWFsdGltZS5oZWFydGJlYXQpIHtcbiAgICAgICAgICAgICAgICAgICAgY2xlYXJUaW1lb3V0KHRoaXMucmVhbHRpbWUuaGVhcnRiZWF0KTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgdGhpcy5yZWFsdGltZS5oZWFydGJlYXQgPSB3aW5kb3cgPT09IG51bGwgfHwgd2luZG93ID09PSB2b2lkIDAgPyB2b2lkIDAgOiB3aW5kb3cuc2V0SW50ZXJ2YWwoKCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICB2YXIgX2E7XG4gICAgICAgICAgICAgICAgICAgIChfYSA9IHRoaXMucmVhbHRpbWUuc29ja2V0KSA9PT0gbnVsbCB8fCBfYSA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2Euc2VuZChKU09OLnN0cmluZ2lmeSh7XG4gICAgICAgICAgICAgICAgICAgICAgICB0eXBlOiAncGluZydcbiAgICAgICAgICAgICAgICAgICAgfSkpO1xuICAgICAgICAgICAgICAgIH0sIDIwMDAwKTtcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICBjcmVhdGVTb2NrZXQ6ICgpID0+IHtcbiAgICAgICAgICAgICAgICB2YXIgX2EsIF9iLCBfYztcbiAgICAgICAgICAgICAgICBpZiAodGhpcy5yZWFsdGltZS5jaGFubmVscy5zaXplIDwgMSkge1xuICAgICAgICAgICAgICAgICAgICB0aGlzLnJlYWx0aW1lLnJlY29ubmVjdCA9IGZhbHNlO1xuICAgICAgICAgICAgICAgICAgICAoX2EgPSB0aGlzLnJlYWx0aW1lLnNvY2tldCkgPT09IG51bGwgfHwgX2EgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9hLmNsb3NlKCk7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgY29uc3QgY2hhbm5lbHMgPSBuZXcgVVJMU2VhcmNoUGFyYW1zKCk7XG4gICAgICAgICAgICAgICAgY2hhbm5lbHMuc2V0KCdwcm9qZWN0JywgdGhpcy5jb25maWcucHJvamVjdCk7XG4gICAgICAgICAgICAgICAgdGhpcy5yZWFsdGltZS5jaGFubmVscy5mb3JFYWNoKGNoYW5uZWwgPT4ge1xuICAgICAgICAgICAgICAgICAgICBjaGFubmVscy5hcHBlbmQoJ2NoYW5uZWxzW10nLCBjaGFubmVsKTtcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICBjb25zdCB1cmwgPSB0aGlzLmNvbmZpZy5lbmRwb2ludFJlYWx0aW1lICsgJy9yZWFsdGltZT8nICsgY2hhbm5lbHMudG9TdHJpbmcoKTtcbiAgICAgICAgICAgICAgICBpZiAodXJsICE9PSB0aGlzLnJlYWx0aW1lLnVybCB8fCAvLyBDaGVjayBpZiBVUkwgaXMgcHJlc2VudFxuICAgICAgICAgICAgICAgICAgICAhdGhpcy5yZWFsdGltZS5zb2NrZXQgfHwgLy8gQ2hlY2sgaWYgV2ViU29ja2V0IGhhcyBub3QgYmVlbiBjcmVhdGVkXG4gICAgICAgICAgICAgICAgICAgICgoX2IgPSB0aGlzLnJlYWx0aW1lLnNvY2tldCkgPT09IG51bGwgfHwgX2IgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9iLnJlYWR5U3RhdGUpID4gV2ViU29ja2V0Lk9QRU4gLy8gQ2hlY2sgaWYgV2ViU29ja2V0IGlzIENMT1NJTkcgKDMpIG9yIENMT1NFRCAoNClcbiAgICAgICAgICAgICAgICApIHtcbiAgICAgICAgICAgICAgICAgICAgaWYgKHRoaXMucmVhbHRpbWUuc29ja2V0ICYmXG4gICAgICAgICAgICAgICAgICAgICAgICAoKF9jID0gdGhpcy5yZWFsdGltZS5zb2NrZXQpID09PSBudWxsIHx8IF9jID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfYy5yZWFkeVN0YXRlKSA8IFdlYlNvY2tldC5DTE9TSU5HIC8vIENsb3NlIFdlYlNvY2tldCBpZiBpdCBpcyBDT05ORUNUSU5HICgwKSBvciBPUEVOICgxKVxuICAgICAgICAgICAgICAgICAgICApIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMucmVhbHRpbWUucmVjb25uZWN0ID0gZmFsc2U7XG4gICAgICAgICAgICAgICAgICAgICAgICB0aGlzLnJlYWx0aW1lLnNvY2tldC5jbG9zZSgpO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIHRoaXMucmVhbHRpbWUudXJsID0gdXJsO1xuICAgICAgICAgICAgICAgICAgICB0aGlzLnJlYWx0aW1lLnNvY2tldCA9IG5ldyBXZWJTb2NrZXQodXJsKTtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5yZWFsdGltZS5zb2NrZXQuYWRkRXZlbnRMaXN0ZW5lcignbWVzc2FnZScsIHRoaXMucmVhbHRpbWUub25NZXNzYWdlKTtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5yZWFsdGltZS5zb2NrZXQuYWRkRXZlbnRMaXN0ZW5lcignb3BlbicsIF9ldmVudCA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICB0aGlzLnJlYWx0aW1lLnJlY29ubmVjdEF0dGVtcHRzID0gMDtcbiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMucmVhbHRpbWUuY3JlYXRlSGVhcnRiZWF0KCk7XG4gICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgICAgICB0aGlzLnJlYWx0aW1lLnNvY2tldC5hZGRFdmVudExpc3RlbmVyKCdjbG9zZScsIGV2ZW50ID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHZhciBfYSwgX2IsIF9jO1xuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKCF0aGlzLnJlYWx0aW1lLnJlY29ubmVjdCB8fFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICgoKF9iID0gKF9hID0gdGhpcy5yZWFsdGltZSkgPT09IG51bGwgfHwgX2EgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9hLmxhc3RNZXNzYWdlKSA9PT0gbnVsbCB8fCBfYiA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2IudHlwZSkgPT09ICdlcnJvcicgJiYgLy8gQ2hlY2sgaWYgbGFzdCBtZXNzYWdlIHdhcyBvZiB0eXBlIGVycm9yXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICgoX2MgPSB0aGlzLnJlYWx0aW1lKSA9PT0gbnVsbCB8fCBfYyA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2MubGFzdE1lc3NhZ2UuZGF0YSkuY29kZSA9PT0gMTAwOCAvLyBDaGVjayBmb3IgcG9saWN5IHZpb2xhdGlvbiAxMDA4XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgKSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMucmVhbHRpbWUucmVjb25uZWN0ID0gdHJ1ZTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCB0aW1lb3V0ID0gdGhpcy5yZWFsdGltZS5nZXRUaW1lb3V0KCk7XG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKGBSZWFsdGltZSBnb3QgZGlzY29ubmVjdGVkLiBSZWNvbm5lY3Qgd2lsbCBiZSBhdHRlbXB0ZWQgaW4gJHt0aW1lb3V0IC8gMTAwMH0gc2Vjb25kcy5gLCBldmVudC5yZWFzb24pO1xuICAgICAgICAgICAgICAgICAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5yZWFsdGltZS5yZWNvbm5lY3RBdHRlbXB0cysrO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMucmVhbHRpbWUuY3JlYXRlU29ja2V0KCk7XG4gICAgICAgICAgICAgICAgICAgICAgICB9LCB0aW1lb3V0KTtcbiAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIG9uTWVzc2FnZTogKGV2ZW50KSA9PiB7XG4gICAgICAgICAgICAgICAgdmFyIF9hLCBfYjtcbiAgICAgICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBtZXNzYWdlID0gSlNPTi5wYXJzZShldmVudC5kYXRhKTtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5yZWFsdGltZS5sYXN0TWVzc2FnZSA9IG1lc3NhZ2U7XG4gICAgICAgICAgICAgICAgICAgIHN3aXRjaCAobWVzc2FnZS50eXBlKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBjYXNlICdjb25uZWN0ZWQnOlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGNvb2tpZSA9IEpTT04ucGFyc2UoKF9hID0gd2luZG93LmxvY2FsU3RvcmFnZS5nZXRJdGVtKCdjb29raWVGYWxsYmFjaycpKSAhPT0gbnVsbCAmJiBfYSAhPT0gdm9pZCAwID8gX2EgOiAne30nKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBzZXNzaW9uID0gY29va2llID09PSBudWxsIHx8IGNvb2tpZSA9PT0gdm9pZCAwID8gdm9pZCAwIDogY29va2llW2BhX3Nlc3Npb25fJHt0aGlzLmNvbmZpZy5wcm9qZWN0fWBdO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IG1lc3NhZ2VEYXRhID0gbWVzc2FnZS5kYXRhO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmIChzZXNzaW9uICYmICFtZXNzYWdlRGF0YS51c2VyKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIChfYiA9IHRoaXMucmVhbHRpbWUuc29ja2V0KSA9PT0gbnVsbCB8fCBfYiA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2Iuc2VuZChKU09OLnN0cmluZ2lmeSh7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlOiAnYXV0aGVudGljYXRpb24nLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGF0YToge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNlc3Npb25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSkpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICAgICAgICAgIGNhc2UgJ2V2ZW50JzpcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBsZXQgZGF0YSA9IG1lc3NhZ2UuZGF0YTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAoZGF0YSA9PT0gbnVsbCB8fCBkYXRhID09PSB2b2lkIDAgPyB2b2lkIDAgOiBkYXRhLmNoYW5uZWxzKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGlzU3Vic2NyaWJlZCA9IGRhdGEuY2hhbm5lbHMuc29tZShjaGFubmVsID0+IHRoaXMucmVhbHRpbWUuY2hhbm5lbHMuaGFzKGNoYW5uZWwpKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKCFpc1N1YnNjcmliZWQpXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMucmVhbHRpbWUuc3Vic2NyaXB0aW9ucy5mb3JFYWNoKHN1YnNjcmlwdGlvbiA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAoZGF0YS5jaGFubmVscy5zb21lKGNoYW5uZWwgPT4gc3Vic2NyaXB0aW9uLmNoYW5uZWxzLmluY2x1ZGVzKGNoYW5uZWwpKSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4gc3Vic2NyaXB0aW9uLmNhbGxiYWNrKGRhdGEpKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgICAgICAgICAgICAgY2FzZSAncG9uZyc6XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgYnJlYWs7IC8vIEhhbmRsZSBwb25nIHJlc3BvbnNlIGlmIG5lZWRlZFxuICAgICAgICAgICAgICAgICAgICAgICAgY2FzZSAnZXJyb3InOlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRocm93IG1lc3NhZ2UuZGF0YTtcbiAgICAgICAgICAgICAgICAgICAgICAgIGRlZmF1bHQ6XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgY2F0Y2ggKGUpIHtcbiAgICAgICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcihlKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAgY2xlYW5VcDogY2hhbm5lbHMgPT4ge1xuICAgICAgICAgICAgICAgIHRoaXMucmVhbHRpbWUuY2hhbm5lbHMuZm9yRWFjaChjaGFubmVsID0+IHtcbiAgICAgICAgICAgICAgICAgICAgaWYgKGNoYW5uZWxzLmluY2x1ZGVzKGNoYW5uZWwpKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBsZXQgZm91bmQgPSBBcnJheS5mcm9tKHRoaXMucmVhbHRpbWUuc3Vic2NyaXB0aW9ucykuc29tZSgoW19rZXksIHN1YnNjcmlwdGlvbl0pID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gc3Vic2NyaXB0aW9uLmNoYW5uZWxzLmluY2x1ZGVzKGNoYW5uZWwpO1xuICAgICAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAoIWZvdW5kKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5yZWFsdGltZS5jaGFubmVscy5kZWxldGUoY2hhbm5lbCk7XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogU2V0IEVuZHBvaW50XG4gICAgICpcbiAgICAgKiBZb3VyIHByb2plY3QgZW5kcG9pbnRcbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBlbmRwb2ludFxuICAgICAqXG4gICAgICogQHJldHVybnMge3RoaXN9XG4gICAgICovXG4gICAgc2V0RW5kcG9pbnQoZW5kcG9pbnQpIHtcbiAgICAgICAgaWYgKCFlbmRwb2ludC5zdGFydHNXaXRoKCdodHRwOi8vJykgJiYgIWVuZHBvaW50LnN0YXJ0c1dpdGgoJ2h0dHBzOi8vJykpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignSW52YWxpZCBlbmRwb2ludCBVUkw6ICcgKyBlbmRwb2ludCk7XG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5jb25maWcuZW5kcG9pbnQgPSBlbmRwb2ludDtcbiAgICAgICAgdGhpcy5jb25maWcuZW5kcG9pbnRSZWFsdGltZSA9IGVuZHBvaW50LnJlcGxhY2UoJ2h0dHBzOi8vJywgJ3dzczovLycpLnJlcGxhY2UoJ2h0dHA6Ly8nLCAnd3M6Ly8nKTtcbiAgICAgICAgcmV0dXJuIHRoaXM7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFNldCBSZWFsdGltZSBFbmRwb2ludFxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGVuZHBvaW50UmVhbHRpbWVcbiAgICAgKlxuICAgICAqIEByZXR1cm5zIHt0aGlzfVxuICAgICAqL1xuICAgIHNldEVuZHBvaW50UmVhbHRpbWUoZW5kcG9pbnRSZWFsdGltZSkge1xuICAgICAgICBpZiAoIWVuZHBvaW50UmVhbHRpbWUuc3RhcnRzV2l0aCgnd3M6Ly8nKSAmJiAhZW5kcG9pbnRSZWFsdGltZS5zdGFydHNXaXRoKCd3c3M6Ly8nKSkge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdJbnZhbGlkIHJlYWx0aW1lIGVuZHBvaW50IFVSTDogJyArIGVuZHBvaW50UmVhbHRpbWUpO1xuICAgICAgICB9XG4gICAgICAgIHRoaXMuY29uZmlnLmVuZHBvaW50UmVhbHRpbWUgPSBlbmRwb2ludFJlYWx0aW1lO1xuICAgICAgICByZXR1cm4gdGhpcztcbiAgICB9XG4gICAgLyoqXG4gICAgICogU2V0IFByb2plY3RcbiAgICAgKlxuICAgICAqIFlvdXIgcHJvamVjdCBJRFxuICAgICAqXG4gICAgICogQHBhcmFtIHZhbHVlIHN0cmluZ1xuICAgICAqXG4gICAgICogQHJldHVybiB7dGhpc31cbiAgICAgKi9cbiAgICBzZXRQcm9qZWN0KHZhbHVlKSB7XG4gICAgICAgIHRoaXMuaGVhZGVyc1snWC1BcHB3cml0ZS1Qcm9qZWN0J10gPSB2YWx1ZTtcbiAgICAgICAgdGhpcy5jb25maWcucHJvamVjdCA9IHZhbHVlO1xuICAgICAgICByZXR1cm4gdGhpcztcbiAgICB9XG4gICAgLyoqXG4gICAgICogU2V0IEpXVFxuICAgICAqXG4gICAgICogWW91ciBzZWNyZXQgSlNPTiBXZWIgVG9rZW5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB2YWx1ZSBzdHJpbmdcbiAgICAgKlxuICAgICAqIEByZXR1cm4ge3RoaXN9XG4gICAgICovXG4gICAgc2V0SldUKHZhbHVlKSB7XG4gICAgICAgIHRoaXMuaGVhZGVyc1snWC1BcHB3cml0ZS1KV1QnXSA9IHZhbHVlO1xuICAgICAgICB0aGlzLmNvbmZpZy5qd3QgPSB2YWx1ZTtcbiAgICAgICAgcmV0dXJuIHRoaXM7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFNldCBMb2NhbGVcbiAgICAgKlxuICAgICAqIEBwYXJhbSB2YWx1ZSBzdHJpbmdcbiAgICAgKlxuICAgICAqIEByZXR1cm4ge3RoaXN9XG4gICAgICovXG4gICAgc2V0TG9jYWxlKHZhbHVlKSB7XG4gICAgICAgIHRoaXMuaGVhZGVyc1snWC1BcHB3cml0ZS1Mb2NhbGUnXSA9IHZhbHVlO1xuICAgICAgICB0aGlzLmNvbmZpZy5sb2NhbGUgPSB2YWx1ZTtcbiAgICAgICAgcmV0dXJuIHRoaXM7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFNldCBTZXNzaW9uXG4gICAgICpcbiAgICAgKiBUaGUgdXNlciBzZXNzaW9uIHRvIGF1dGhlbnRpY2F0ZSB3aXRoXG4gICAgICpcbiAgICAgKiBAcGFyYW0gdmFsdWUgc3RyaW5nXG4gICAgICpcbiAgICAgKiBAcmV0dXJuIHt0aGlzfVxuICAgICAqL1xuICAgIHNldFNlc3Npb24odmFsdWUpIHtcbiAgICAgICAgdGhpcy5oZWFkZXJzWydYLUFwcHdyaXRlLVNlc3Npb24nXSA9IHZhbHVlO1xuICAgICAgICB0aGlzLmNvbmZpZy5zZXNzaW9uID0gdmFsdWU7XG4gICAgICAgIHJldHVybiB0aGlzO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBTZXQgRGV2S2V5XG4gICAgICpcbiAgICAgKiBZb3VyIHNlY3JldCBkZXYgQVBJIGtleVxuICAgICAqXG4gICAgICogQHBhcmFtIHZhbHVlIHN0cmluZ1xuICAgICAqXG4gICAgICogQHJldHVybiB7dGhpc31cbiAgICAgKi9cbiAgICBzZXREZXZLZXkodmFsdWUpIHtcbiAgICAgICAgdGhpcy5oZWFkZXJzWydYLUFwcHdyaXRlLURldi1LZXknXSA9IHZhbHVlO1xuICAgICAgICB0aGlzLmNvbmZpZy5kZXZrZXkgPSB2YWx1ZTtcbiAgICAgICAgcmV0dXJuIHRoaXM7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFN1YnNjcmliZXMgdG8gQXBwd3JpdGUgZXZlbnRzIGFuZCBwYXNzZXMgeW91IHRoZSBwYXlsb2FkIGluIHJlYWx0aW1lLlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmd8c3RyaW5nW119IGNoYW5uZWxzXG4gICAgICogQ2hhbm5lbCB0byBzdWJzY3JpYmUgLSBwYXNzIGEgc2luZ2xlIGNoYW5uZWwgYXMgYSBzdHJpbmcgb3IgbXVsdGlwbGUgd2l0aCBhbiBhcnJheSBvZiBzdHJpbmdzLlxuICAgICAqXG4gICAgICogUG9zc2libGUgY2hhbm5lbHMgYXJlOlxuICAgICAqIC0gYWNjb3VudFxuICAgICAqIC0gY29sbGVjdGlvbnNcbiAgICAgKiAtIGNvbGxlY3Rpb25zLltJRF1cbiAgICAgKiAtIGNvbGxlY3Rpb25zLltJRF0uZG9jdW1lbnRzXG4gICAgICogLSBkb2N1bWVudHNcbiAgICAgKiAtIGRvY3VtZW50cy5bSURdXG4gICAgICogLSBmaWxlc1xuICAgICAqIC0gZmlsZXMuW0lEXVxuICAgICAqIC0gZXhlY3V0aW9uc1xuICAgICAqIC0gZXhlY3V0aW9ucy5bSURdXG4gICAgICogLSBmdW5jdGlvbnMuW0lEXVxuICAgICAqIC0gdGVhbXNcbiAgICAgKiAtIHRlYW1zLltJRF1cbiAgICAgKiAtIG1lbWJlcnNoaXBzXG4gICAgICogLSBtZW1iZXJzaGlwcy5bSURdXG4gICAgICogQHBhcmFtIHsocGF5bG9hZDogUmVhbHRpbWVNZXNzYWdlKSA9PiB2b2lkfSBjYWxsYmFjayBJcyBjYWxsZWQgb24gZXZlcnkgcmVhbHRpbWUgdXBkYXRlLlxuICAgICAqIEByZXR1cm5zIHsoKSA9PiB2b2lkfSBVbnN1YnNjcmliZXMgZnJvbSBldmVudHMuXG4gICAgICovXG4gICAgc3Vic2NyaWJlKGNoYW5uZWxzLCBjYWxsYmFjaykge1xuICAgICAgICBsZXQgY2hhbm5lbEFycmF5ID0gdHlwZW9mIGNoYW5uZWxzID09PSAnc3RyaW5nJyA/IFtjaGFubmVsc10gOiBjaGFubmVscztcbiAgICAgICAgY2hhbm5lbEFycmF5LmZvckVhY2goY2hhbm5lbCA9PiB0aGlzLnJlYWx0aW1lLmNoYW5uZWxzLmFkZChjaGFubmVsKSk7XG4gICAgICAgIGNvbnN0IGNvdW50ZXIgPSB0aGlzLnJlYWx0aW1lLnN1YnNjcmlwdGlvbnNDb3VudGVyKys7XG4gICAgICAgIHRoaXMucmVhbHRpbWUuc3Vic2NyaXB0aW9ucy5zZXQoY291bnRlciwge1xuICAgICAgICAgICAgY2hhbm5lbHM6IGNoYW5uZWxBcnJheSxcbiAgICAgICAgICAgIGNhbGxiYWNrXG4gICAgICAgIH0pO1xuICAgICAgICB0aGlzLnJlYWx0aW1lLmNvbm5lY3QoKTtcbiAgICAgICAgcmV0dXJuICgpID0+IHtcbiAgICAgICAgICAgIHRoaXMucmVhbHRpbWUuc3Vic2NyaXB0aW9ucy5kZWxldGUoY291bnRlcik7XG4gICAgICAgICAgICB0aGlzLnJlYWx0aW1lLmNsZWFuVXAoY2hhbm5lbEFycmF5KTtcbiAgICAgICAgICAgIHRoaXMucmVhbHRpbWUuY29ubmVjdCgpO1xuICAgICAgICB9O1xuICAgIH1cbiAgICBwcmVwYXJlUmVxdWVzdChtZXRob2QsIHVybCwgaGVhZGVycyA9IHt9LCBwYXJhbXMgPSB7fSkge1xuICAgICAgICBtZXRob2QgPSBtZXRob2QudG9VcHBlckNhc2UoKTtcbiAgICAgICAgaGVhZGVycyA9IE9iamVjdC5hc3NpZ24oe30sIHRoaXMuaGVhZGVycywgaGVhZGVycyk7XG4gICAgICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJyAmJiB3aW5kb3cubG9jYWxTdG9yYWdlKSB7XG4gICAgICAgICAgICBjb25zdCBjb29raWVGYWxsYmFjayA9IHdpbmRvdy5sb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnY29va2llRmFsbGJhY2snKTtcbiAgICAgICAgICAgIGlmIChjb29raWVGYWxsYmFjaykge1xuICAgICAgICAgICAgICAgIGhlYWRlcnNbJ1gtRmFsbGJhY2stQ29va2llcyddID0gY29va2llRmFsbGJhY2s7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgbGV0IG9wdGlvbnMgPSB7XG4gICAgICAgICAgICBtZXRob2QsXG4gICAgICAgICAgICBoZWFkZXJzLFxuICAgICAgICB9O1xuICAgICAgICBpZiAoaGVhZGVyc1snWC1BcHB3cml0ZS1EZXYtS2V5J10gPT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgb3B0aW9ucy5jcmVkZW50aWFscyA9ICdpbmNsdWRlJztcbiAgICAgICAgfVxuICAgICAgICBpZiAobWV0aG9kID09PSAnR0VUJykge1xuICAgICAgICAgICAgZm9yIChjb25zdCBba2V5LCB2YWx1ZV0gb2YgT2JqZWN0LmVudHJpZXMoQ2xpZW50LmZsYXR0ZW4ocGFyYW1zKSkpIHtcbiAgICAgICAgICAgICAgICB1cmwuc2VhcmNoUGFyYW1zLmFwcGVuZChrZXksIHZhbHVlKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIHN3aXRjaCAoaGVhZGVyc1snY29udGVudC10eXBlJ10pIHtcbiAgICAgICAgICAgICAgICBjYXNlICdhcHBsaWNhdGlvbi9qc29uJzpcbiAgICAgICAgICAgICAgICAgICAgb3B0aW9ucy5ib2R5ID0gSlNPTi5zdHJpbmdpZnkocGFyYW1zKTtcbiAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgY2FzZSAnbXVsdGlwYXJ0L2Zvcm0tZGF0YSc6XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGZvcm1EYXRhID0gbmV3IEZvcm1EYXRhKCk7XG4gICAgICAgICAgICAgICAgICAgIGZvciAoY29uc3QgW2tleSwgdmFsdWVdIG9mIE9iamVjdC5lbnRyaWVzKHBhcmFtcykpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGlmICh2YWx1ZSBpbnN0YW5jZW9mIEZpbGUpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb3JtRGF0YS5hcHBlbmQoa2V5LCB2YWx1ZSwgdmFsdWUubmFtZSk7XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICBlbHNlIGlmIChBcnJheS5pc0FycmF5KHZhbHVlKSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZvciAoY29uc3QgbmVzdGVkVmFsdWUgb2YgdmFsdWUpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZm9ybURhdGEuYXBwZW5kKGAke2tleX1bXWAsIG5lc3RlZFZhbHVlKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb3JtRGF0YS5hcHBlbmQoa2V5LCB2YWx1ZSk7XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgb3B0aW9ucy5ib2R5ID0gZm9ybURhdGE7XG4gICAgICAgICAgICAgICAgICAgIGRlbGV0ZSBoZWFkZXJzWydjb250ZW50LXR5cGUnXTtcbiAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHsgdXJpOiB1cmwudG9TdHJpbmcoKSwgb3B0aW9ucyB9O1xuICAgIH1cbiAgICBjaHVua2VkVXBsb2FkKG1ldGhvZCwgdXJsLCBoZWFkZXJzID0ge30sIG9yaWdpbmFsUGF5bG9hZCA9IHt9LCBvblByb2dyZXNzKSB7XG4gICAgICAgIHZhciBfYTtcbiAgICAgICAgcmV0dXJuIF9fYXdhaXRlcih0aGlzLCB2b2lkIDAsIHZvaWQgMCwgZnVuY3Rpb24qICgpIHtcbiAgICAgICAgICAgIGNvbnN0IFtmaWxlUGFyYW0sIGZpbGVdID0gKF9hID0gT2JqZWN0LmVudHJpZXMob3JpZ2luYWxQYXlsb2FkKS5maW5kKChbXywgdmFsdWVdKSA9PiB2YWx1ZSBpbnN0YW5jZW9mIEZpbGUpKSAhPT0gbnVsbCAmJiBfYSAhPT0gdm9pZCAwID8gX2EgOiBbXTtcbiAgICAgICAgICAgIGlmICghZmlsZSB8fCAhZmlsZVBhcmFtKSB7XG4gICAgICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdGaWxlIG5vdCBmb3VuZCBpbiBwYXlsb2FkJyk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoZmlsZS5zaXplIDw9IENsaWVudC5DSFVOS19TSVpFKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIHlpZWxkIHRoaXMuY2FsbChtZXRob2QsIHVybCwgaGVhZGVycywgb3JpZ2luYWxQYXlsb2FkKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGxldCBzdGFydCA9IDA7XG4gICAgICAgICAgICBsZXQgcmVzcG9uc2UgPSBudWxsO1xuICAgICAgICAgICAgd2hpbGUgKHN0YXJ0IDwgZmlsZS5zaXplKSB7XG4gICAgICAgICAgICAgICAgbGV0IGVuZCA9IHN0YXJ0ICsgQ2xpZW50LkNIVU5LX1NJWkU7IC8vIFByZXBhcmUgZW5kIGZvciB0aGUgbmV4dCBjaHVua1xuICAgICAgICAgICAgICAgIGlmIChlbmQgPj0gZmlsZS5zaXplKSB7XG4gICAgICAgICAgICAgICAgICAgIGVuZCA9IGZpbGUuc2l6ZTsgLy8gQWRqdXN0IGZvciB0aGUgbGFzdCBjaHVuayB0byBpbmNsdWRlIHRoZSBsYXN0IGJ5dGVcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgaGVhZGVyc1snY29udGVudC1yYW5nZSddID0gYGJ5dGVzICR7c3RhcnR9LSR7ZW5kIC0gMX0vJHtmaWxlLnNpemV9YDtcbiAgICAgICAgICAgICAgICBjb25zdCBjaHVuayA9IGZpbGUuc2xpY2Uoc3RhcnQsIGVuZCk7XG4gICAgICAgICAgICAgICAgbGV0IHBheWxvYWQgPSBPYmplY3QuYXNzaWduKHt9LCBvcmlnaW5hbFBheWxvYWQpO1xuICAgICAgICAgICAgICAgIHBheWxvYWRbZmlsZVBhcmFtXSA9IG5ldyBGaWxlKFtjaHVua10sIGZpbGUubmFtZSk7XG4gICAgICAgICAgICAgICAgcmVzcG9uc2UgPSB5aWVsZCB0aGlzLmNhbGwobWV0aG9kLCB1cmwsIGhlYWRlcnMsIHBheWxvYWQpO1xuICAgICAgICAgICAgICAgIGlmIChvblByb2dyZXNzICYmIHR5cGVvZiBvblByb2dyZXNzID09PSAnZnVuY3Rpb24nKSB7XG4gICAgICAgICAgICAgICAgICAgIG9uUHJvZ3Jlc3Moe1xuICAgICAgICAgICAgICAgICAgICAgICAgJGlkOiByZXNwb25zZS4kaWQsXG4gICAgICAgICAgICAgICAgICAgICAgICBwcm9ncmVzczogTWF0aC5yb3VuZCgoZW5kIC8gZmlsZS5zaXplKSAqIDEwMCksXG4gICAgICAgICAgICAgICAgICAgICAgICBzaXplVXBsb2FkZWQ6IGVuZCxcbiAgICAgICAgICAgICAgICAgICAgICAgIGNodW5rc1RvdGFsOiBNYXRoLmNlaWwoZmlsZS5zaXplIC8gQ2xpZW50LkNIVU5LX1NJWkUpLFxuICAgICAgICAgICAgICAgICAgICAgICAgY2h1bmtzVXBsb2FkZWQ6IE1hdGguY2VpbChlbmQgLyBDbGllbnQuQ0hVTktfU0laRSlcbiAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGlmIChyZXNwb25zZSAmJiByZXNwb25zZS4kaWQpIHtcbiAgICAgICAgICAgICAgICAgICAgaGVhZGVyc1sneC1hcHB3cml0ZS1pZCddID0gcmVzcG9uc2UuJGlkO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBzdGFydCA9IGVuZDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHJldHVybiByZXNwb25zZTtcbiAgICAgICAgfSk7XG4gICAgfVxuICAgIHBpbmcoKSB7XG4gICAgICAgIHJldHVybiBfX2F3YWl0ZXIodGhpcywgdm9pZCAwLCB2b2lkIDAsIGZ1bmN0aW9uKiAoKSB7XG4gICAgICAgICAgICByZXR1cm4gdGhpcy5jYWxsKCdHRVQnLCBuZXcgVVJMKHRoaXMuY29uZmlnLmVuZHBvaW50ICsgJy9waW5nJykpO1xuICAgICAgICB9KTtcbiAgICB9XG4gICAgY2FsbChtZXRob2QsIHVybCwgaGVhZGVycyA9IHt9LCBwYXJhbXMgPSB7fSwgcmVzcG9uc2VUeXBlID0gJ2pzb24nKSB7XG4gICAgICAgIHZhciBfYSwgX2I7XG4gICAgICAgIHJldHVybiBfX2F3YWl0ZXIodGhpcywgdm9pZCAwLCB2b2lkIDAsIGZ1bmN0aW9uKiAoKSB7XG4gICAgICAgICAgICBjb25zdCB7IHVyaSwgb3B0aW9ucyB9ID0gdGhpcy5wcmVwYXJlUmVxdWVzdChtZXRob2QsIHVybCwgaGVhZGVycywgcGFyYW1zKTtcbiAgICAgICAgICAgIGxldCBkYXRhID0gbnVsbDtcbiAgICAgICAgICAgIGNvbnN0IHJlc3BvbnNlID0geWllbGQgZmV0Y2godXJpLCBvcHRpb25zKTtcbiAgICAgICAgICAgIC8vIHR5cGUgb3BhcXVlOiBOby1DT1JTLCBkaWZmZXJlbnQtb3JpZ2luIHJlc3BvbnNlIChDT1JTLWlzc3VlKVxuICAgICAgICAgICAgaWYgKHJlc3BvbnNlLnR5cGUgPT09ICdvcGFxdWUnKSB7XG4gICAgICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKGBJbnZhbGlkIE9yaWdpbi4gUmVnaXN0ZXIgeW91ciBuZXcgY2xpZW50ICgke3dpbmRvdy5sb2NhdGlvbi5ob3N0fSkgYXMgYSBuZXcgV2ViIHBsYXRmb3JtIG9uIHlvdXIgcHJvamVjdCBjb25zb2xlIGRhc2hib2FyZGAsIDQwMywgXCJmb3JiaWRkZW5cIiwgXCJcIik7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjb25zdCB3YXJuaW5ncyA9IHJlc3BvbnNlLmhlYWRlcnMuZ2V0KCd4LWFwcHdyaXRlLXdhcm5pbmcnKTtcbiAgICAgICAgICAgIGlmICh3YXJuaW5ncykge1xuICAgICAgICAgICAgICAgIHdhcm5pbmdzLnNwbGl0KCc7JykuZm9yRWFjaCgod2FybmluZykgPT4gY29uc29sZS53YXJuKCdXYXJuaW5nOiAnICsgd2FybmluZykpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKChfYSA9IHJlc3BvbnNlLmhlYWRlcnMuZ2V0KCdjb250ZW50LXR5cGUnKSkgPT09IG51bGwgfHwgX2EgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9hLmluY2x1ZGVzKCdhcHBsaWNhdGlvbi9qc29uJykpIHtcbiAgICAgICAgICAgICAgICBkYXRhID0geWllbGQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZWxzZSBpZiAocmVzcG9uc2VUeXBlID09PSAnYXJyYXlCdWZmZXInKSB7XG4gICAgICAgICAgICAgICAgZGF0YSA9IHlpZWxkIHJlc3BvbnNlLmFycmF5QnVmZmVyKCk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICBkYXRhID0ge1xuICAgICAgICAgICAgICAgICAgICBtZXNzYWdlOiB5aWVsZCByZXNwb25zZS50ZXh0KClcbiAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKDQwMCA8PSByZXNwb25zZS5zdGF0dXMpIHtcbiAgICAgICAgICAgICAgICBsZXQgcmVzcG9uc2VUZXh0ID0gJyc7XG4gICAgICAgICAgICAgICAgaWYgKCgoX2IgPSByZXNwb25zZS5oZWFkZXJzLmdldCgnY29udGVudC10eXBlJykpID09PSBudWxsIHx8IF9iID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfYi5pbmNsdWRlcygnYXBwbGljYXRpb24vanNvbicpKSB8fCByZXNwb25zZVR5cGUgPT09ICdhcnJheUJ1ZmZlcicpIHtcbiAgICAgICAgICAgICAgICAgICAgcmVzcG9uc2VUZXh0ID0gSlNPTi5zdHJpbmdpZnkoZGF0YSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICByZXNwb25zZVRleHQgPSBkYXRhID09PSBudWxsIHx8IGRhdGEgPT09IHZvaWQgMCA/IHZvaWQgMCA6IGRhdGEubWVzc2FnZTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKGRhdGEgPT09IG51bGwgfHwgZGF0YSA9PT0gdm9pZCAwID8gdm9pZCAwIDogZGF0YS5tZXNzYWdlLCByZXNwb25zZS5zdGF0dXMsIGRhdGEgPT09IG51bGwgfHwgZGF0YSA9PT0gdm9pZCAwID8gdm9pZCAwIDogZGF0YS50eXBlLCByZXNwb25zZVRleHQpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY29uc3QgY29va2llRmFsbGJhY2sgPSByZXNwb25zZS5oZWFkZXJzLmdldCgnWC1GYWxsYmFjay1Db29raWVzJyk7XG4gICAgICAgICAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcgJiYgd2luZG93LmxvY2FsU3RvcmFnZSAmJiBjb29raWVGYWxsYmFjaykge1xuICAgICAgICAgICAgICAgIHdpbmRvdy5jb25zb2xlLndhcm4oJ0FwcHdyaXRlIGlzIHVzaW5nIGxvY2FsU3RvcmFnZSBmb3Igc2Vzc2lvbiBtYW5hZ2VtZW50LiBJbmNyZWFzZSB5b3VyIHNlY3VyaXR5IGJ5IGFkZGluZyBhIGN1c3RvbSBkb21haW4gYXMgeW91ciBBUEkgZW5kcG9pbnQuJyk7XG4gICAgICAgICAgICAgICAgd2luZG93LmxvY2FsU3RvcmFnZS5zZXRJdGVtKCdjb29raWVGYWxsYmFjaycsIGNvb2tpZUZhbGxiYWNrKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHJldHVybiBkYXRhO1xuICAgICAgICB9KTtcbiAgICB9XG4gICAgc3RhdGljIGZsYXR0ZW4oZGF0YSwgcHJlZml4ID0gJycpIHtcbiAgICAgICAgbGV0IG91dHB1dCA9IHt9O1xuICAgICAgICBmb3IgKGNvbnN0IFtrZXksIHZhbHVlXSBvZiBPYmplY3QuZW50cmllcyhkYXRhKSkge1xuICAgICAgICAgICAgbGV0IGZpbmFsS2V5ID0gcHJlZml4ID8gcHJlZml4ICsgJ1snICsga2V5ICsgJ10nIDoga2V5O1xuICAgICAgICAgICAgaWYgKEFycmF5LmlzQXJyYXkodmFsdWUpKSB7XG4gICAgICAgICAgICAgICAgb3V0cHV0ID0gT2JqZWN0LmFzc2lnbihPYmplY3QuYXNzaWduKHt9LCBvdXRwdXQpLCBDbGllbnQuZmxhdHRlbih2YWx1ZSwgZmluYWxLZXkpKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgIG91dHB1dFtmaW5hbEtleV0gPSB2YWx1ZTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gb3V0cHV0O1xuICAgIH1cbn1cbkNsaWVudC5DSFVOS19TSVpFID0gMTAyNCAqIDEwMjQgKiA1O1xuXG5jbGFzcyBTZXJ2aWNlIHtcbiAgICBjb25zdHJ1Y3RvcihjbGllbnQpIHtcbiAgICAgICAgdGhpcy5jbGllbnQgPSBjbGllbnQ7XG4gICAgfVxuICAgIHN0YXRpYyBmbGF0dGVuKGRhdGEsIHByZWZpeCA9ICcnKSB7XG4gICAgICAgIGxldCBvdXRwdXQgPSB7fTtcbiAgICAgICAgZm9yIChjb25zdCBba2V5LCB2YWx1ZV0gb2YgT2JqZWN0LmVudHJpZXMoZGF0YSkpIHtcbiAgICAgICAgICAgIGxldCBmaW5hbEtleSA9IHByZWZpeCA/IHByZWZpeCArICdbJyArIGtleSArICddJyA6IGtleTtcbiAgICAgICAgICAgIGlmIChBcnJheS5pc0FycmF5KHZhbHVlKSkge1xuICAgICAgICAgICAgICAgIG91dHB1dCA9IE9iamVjdC5hc3NpZ24oT2JqZWN0LmFzc2lnbih7fSwgb3V0cHV0KSwgU2VydmljZS5mbGF0dGVuKHZhbHVlLCBmaW5hbEtleSkpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgb3V0cHV0W2ZpbmFsS2V5XSA9IHZhbHVlO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIHJldHVybiBvdXRwdXQ7XG4gICAgfVxufVxuLyoqXG4gKiBUaGUgc2l6ZSBmb3IgY2h1bmtlZCB1cGxvYWRzIGluIGJ5dGVzLlxuICovXG5TZXJ2aWNlLkNIVU5LX1NJWkUgPSA1ICogMTAyNCAqIDEwMjQ7IC8vIDVNQlxuXG5jbGFzcyBBY2NvdW50IHtcbiAgICBjb25zdHJ1Y3RvcihjbGllbnQpIHtcbiAgICAgICAgdGhpcy5jbGllbnQgPSBjbGllbnQ7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIEdldCB0aGUgY3VycmVudGx5IGxvZ2dlZCBpbiB1c2VyLlxuICAgICAqXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8TW9kZWxzLlVzZXI8UHJlZmVyZW5jZXM+Pn1cbiAgICAgKi9cbiAgICBnZXQoKSB7XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2FjY291bnQnO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7fTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ2dldCcsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFVzZSB0aGlzIGVuZHBvaW50IHRvIGFsbG93IGEgbmV3IHVzZXIgdG8gcmVnaXN0ZXIgYSBuZXcgYWNjb3VudCBpbiB5b3VyIHByb2plY3QuIEFmdGVyIHRoZSB1c2VyIHJlZ2lzdHJhdGlvbiBjb21wbGV0ZXMgc3VjY2Vzc2Z1bGx5LCB5b3UgY2FuIHVzZSB0aGUgWy9hY2NvdW50L3ZlcmZpY2F0aW9uXShodHRwczovL2FwcHdyaXRlLmlvL2RvY3MvcmVmZXJlbmNlcy9jbG91ZC9jbGllbnQtd2ViL2FjY291bnQjY3JlYXRlVmVyaWZpY2F0aW9uKSByb3V0ZSB0byBzdGFydCB2ZXJpZnlpbmcgdGhlIHVzZXIgZW1haWwgYWRkcmVzcy4gVG8gYWxsb3cgdGhlIG5ldyB1c2VyIHRvIGxvZ2luIHRvIHRoZWlyIG5ldyBhY2NvdW50LCB5b3UgbmVlZCB0byBjcmVhdGUgYSBuZXcgW2FjY291bnQgc2Vzc2lvbl0oaHR0cHM6Ly9hcHB3cml0ZS5pby9kb2NzL3JlZmVyZW5jZXMvY2xvdWQvY2xpZW50LXdlYi9hY2NvdW50I2NyZWF0ZUVtYWlsU2Vzc2lvbikuXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gdXNlcklkXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGVtYWlsXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IHBhc3N3b3JkXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IG5hbWVcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTxNb2RlbHMuVXNlcjxQcmVmZXJlbmNlcz4+fVxuICAgICAqL1xuICAgIGNyZWF0ZSh1c2VySWQsIGVtYWlsLCBwYXNzd29yZCwgbmFtZSkge1xuICAgICAgICBpZiAodHlwZW9mIHVzZXJJZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwidXNlcklkXCInKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIGVtYWlsID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJlbWFpbFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBwYXNzd29yZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwicGFzc3dvcmRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2FjY291bnQnO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGlmICh0eXBlb2YgdXNlcklkICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsndXNlcklkJ10gPSB1c2VySWQ7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBlbWFpbCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ2VtYWlsJ10gPSBlbWFpbDtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIHBhc3N3b3JkICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsncGFzc3dvcmQnXSA9IHBhc3N3b3JkO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgbmFtZSAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ25hbWUnXSA9IG5hbWU7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgY29uc3QgYXBpSGVhZGVycyA9IHtcbiAgICAgICAgICAgICdjb250ZW50LXR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgIH07XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdwb3N0JywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogVXBkYXRlIGN1cnJlbnRseSBsb2dnZWQgaW4gdXNlciBhY2NvdW50IGVtYWlsIGFkZHJlc3MuIEFmdGVyIGNoYW5naW5nIHVzZXIgYWRkcmVzcywgdGhlIHVzZXIgY29uZmlybWF0aW9uIHN0YXR1cyB3aWxsIGdldCByZXNldC4gQSBuZXcgY29uZmlybWF0aW9uIGVtYWlsIGlzIG5vdCBzZW50IGF1dG9tYXRpY2FsbHkgaG93ZXZlciB5b3UgY2FuIHVzZSB0aGUgc2VuZCBjb25maXJtYXRpb24gZW1haWwgZW5kcG9pbnQgYWdhaW4gdG8gc2VuZCB0aGUgY29uZmlybWF0aW9uIGVtYWlsLiBGb3Igc2VjdXJpdHkgbWVhc3VyZXMsIHVzZXIgcGFzc3dvcmQgaXMgcmVxdWlyZWQgdG8gY29tcGxldGUgdGhpcyByZXF1ZXN0LlxuICAgICAqIFRoaXMgZW5kcG9pbnQgY2FuIGFsc28gYmUgdXNlZCB0byBjb252ZXJ0IGFuIGFub255bW91cyBhY2NvdW50IHRvIGEgbm9ybWFsIG9uZSwgYnkgcGFzc2luZyBhbiBlbWFpbCBhZGRyZXNzIGFuZCBhIG5ldyBwYXNzd29yZC5cbiAgICAgKlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGVtYWlsXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IHBhc3N3b3JkXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8TW9kZWxzLlVzZXI8UHJlZmVyZW5jZXM+Pn1cbiAgICAgKi9cbiAgICB1cGRhdGVFbWFpbChlbWFpbCwgcGFzc3dvcmQpIHtcbiAgICAgICAgaWYgKHR5cGVvZiBlbWFpbCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwiZW1haWxcIicpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgcGFzc3dvcmQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcInBhc3N3b3JkXCInKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy9hY2NvdW50L2VtYWlsJztcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBpZiAodHlwZW9mIGVtYWlsICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsnZW1haWwnXSA9IGVtYWlsO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgcGFzc3dvcmQgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydwYXNzd29yZCddID0gcGFzc3dvcmQ7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgY29uc3QgYXBpSGVhZGVycyA9IHtcbiAgICAgICAgICAgICdjb250ZW50LXR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgIH07XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdwYXRjaCcsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIEdldCB0aGUgbGlzdCBvZiBpZGVudGl0aWVzIGZvciB0aGUgY3VycmVudGx5IGxvZ2dlZCBpbiB1c2VyLlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmdbXX0gcXVlcmllc1xuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPE1vZGVscy5JZGVudGl0eUxpc3Q+fVxuICAgICAqL1xuICAgIGxpc3RJZGVudGl0aWVzKHF1ZXJpZXMpIHtcbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvYWNjb3VudC9pZGVudGl0aWVzJztcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBpZiAodHlwZW9mIHF1ZXJpZXMgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydxdWVyaWVzJ10gPSBxdWVyaWVzO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7fTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ2dldCcsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIERlbGV0ZSBhbiBpZGVudGl0eSBieSBpdHMgdW5pcXVlIElELlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGlkZW50aXR5SWRcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTx7fT59XG4gICAgICovXG4gICAgZGVsZXRlSWRlbnRpdHkoaWRlbnRpdHlJZCkge1xuICAgICAgICBpZiAodHlwZW9mIGlkZW50aXR5SWQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcImlkZW50aXR5SWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2FjY291bnQvaWRlbnRpdGllcy97aWRlbnRpdHlJZH0nLnJlcGxhY2UoJ3tpZGVudGl0eUlkfScsIGlkZW50aXR5SWQpO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7XG4gICAgICAgICAgICAnY29udGVudC10eXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgnZGVsZXRlJywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogVXNlIHRoaXMgZW5kcG9pbnQgdG8gY3JlYXRlIGEgSlNPTiBXZWIgVG9rZW4uIFlvdSBjYW4gdXNlIHRoZSByZXN1bHRpbmcgSldUIHRvIGF1dGhlbnRpY2F0ZSBvbiBiZWhhbGYgb2YgdGhlIGN1cnJlbnQgdXNlciB3aGVuIHdvcmtpbmcgd2l0aCB0aGUgQXBwd3JpdGUgc2VydmVyLXNpZGUgQVBJIGFuZCBTREtzLiBUaGUgSldUIHNlY3JldCBpcyB2YWxpZCBmb3IgMTUgbWludXRlcyBmcm9tIGl0cyBjcmVhdGlvbiBhbmQgd2lsbCBiZSBpbnZhbGlkIGlmIHRoZSB1c2VyIHdpbGwgbG9nb3V0IGluIHRoYXQgdGltZSBmcmFtZS5cbiAgICAgKlxuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPE1vZGVscy5Kd3Q+fVxuICAgICAqL1xuICAgIGNyZWF0ZUpXVCgpIHtcbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvYWNjb3VudC9qd3RzJztcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge1xuICAgICAgICAgICAgJ2NvbnRlbnQtdHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ3Bvc3QnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBHZXQgdGhlIGxpc3Qgb2YgbGF0ZXN0IHNlY3VyaXR5IGFjdGl2aXR5IGxvZ3MgZm9yIHRoZSBjdXJyZW50bHkgbG9nZ2VkIGluIHVzZXIuIEVhY2ggbG9nIHJldHVybnMgdXNlciBJUCBhZGRyZXNzLCBsb2NhdGlvbiBhbmQgZGF0ZSBhbmQgdGltZSBvZiBsb2cuXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge3N0cmluZ1tdfSBxdWVyaWVzXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8TW9kZWxzLkxvZ0xpc3Q+fVxuICAgICAqL1xuICAgIGxpc3RMb2dzKHF1ZXJpZXMpIHtcbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvYWNjb3VudC9sb2dzJztcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBpZiAodHlwZW9mIHF1ZXJpZXMgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydxdWVyaWVzJ10gPSBxdWVyaWVzO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7fTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ2dldCcsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIEVuYWJsZSBvciBkaXNhYmxlIE1GQSBvbiBhbiBhY2NvdW50LlxuICAgICAqXG4gICAgICogQHBhcmFtIHtib29sZWFufSBtZmFcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTxNb2RlbHMuVXNlcjxQcmVmZXJlbmNlcz4+fVxuICAgICAqL1xuICAgIHVwZGF0ZU1GQShtZmEpIHtcbiAgICAgICAgaWYgKHR5cGVvZiBtZmEgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcIm1mYVwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvYWNjb3VudC9tZmEnO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGlmICh0eXBlb2YgbWZhICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsnbWZhJ10gPSBtZmE7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgY29uc3QgYXBpSGVhZGVycyA9IHtcbiAgICAgICAgICAgICdjb250ZW50LXR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgIH07XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdwYXRjaCcsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIEFkZCBhbiBhdXRoZW50aWNhdG9yIGFwcCB0byBiZSB1c2VkIGFzIGFuIE1GQSBmYWN0b3IuIFZlcmlmeSB0aGUgYXV0aGVudGljYXRvciB1c2luZyB0aGUgW3ZlcmlmeSBhdXRoZW50aWNhdG9yXSgvZG9jcy9yZWZlcmVuY2VzL2Nsb3VkL2NsaWVudC13ZWIvYWNjb3VudCN1cGRhdGVNZmFBdXRoZW50aWNhdG9yKSBtZXRob2QuXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge0F1dGhlbnRpY2F0b3JUeXBlfSB0eXBlXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8TW9kZWxzLk1mYVR5cGU+fVxuICAgICAqL1xuICAgIGNyZWF0ZU1mYUF1dGhlbnRpY2F0b3IodHlwZSkge1xuICAgICAgICBpZiAodHlwZW9mIHR5cGUgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcInR5cGVcIicpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2FjY291bnQvbWZhL2F1dGhlbnRpY2F0b3JzL3t0eXBlfScucmVwbGFjZSgne3R5cGV9JywgdHlwZSk7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgY29uc3QgYXBpSGVhZGVycyA9IHtcbiAgICAgICAgICAgICdjb250ZW50LXR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgIH07XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdwb3N0JywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogVmVyaWZ5IGFuIGF1dGhlbnRpY2F0b3IgYXBwIGFmdGVyIGFkZGluZyBpdCB1c2luZyB0aGUgW2FkZCBhdXRoZW50aWNhdG9yXSgvZG9jcy9yZWZlcmVuY2VzL2Nsb3VkL2NsaWVudC13ZWIvYWNjb3VudCNjcmVhdGVNZmFBdXRoZW50aWNhdG9yKSBtZXRob2QuXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge0F1dGhlbnRpY2F0b3JUeXBlfSB0eXBlXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IG90cFxuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPE1vZGVscy5Vc2VyPFByZWZlcmVuY2VzPj59XG4gICAgICovXG4gICAgdXBkYXRlTWZhQXV0aGVudGljYXRvcih0eXBlLCBvdHApIHtcbiAgICAgICAgaWYgKHR5cGVvZiB0eXBlID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJ0eXBlXCInKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIG90cCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwib3RwXCInKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy9hY2NvdW50L21mYS9hdXRoZW50aWNhdG9ycy97dHlwZX0nLnJlcGxhY2UoJ3t0eXBlfScsIHR5cGUpO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGlmICh0eXBlb2Ygb3RwICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsnb3RwJ10gPSBvdHA7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgY29uc3QgYXBpSGVhZGVycyA9IHtcbiAgICAgICAgICAgICdjb250ZW50LXR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgIH07XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdwdXQnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBEZWxldGUgYW4gYXV0aGVudGljYXRvciBmb3IgYSB1c2VyIGJ5IElELlxuICAgICAqXG4gICAgICogQHBhcmFtIHtBdXRoZW50aWNhdG9yVHlwZX0gdHlwZVxuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPHt9Pn1cbiAgICAgKi9cbiAgICBkZWxldGVNZmFBdXRoZW50aWNhdG9yKHR5cGUpIHtcbiAgICAgICAgaWYgKHR5cGVvZiB0eXBlID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJ0eXBlXCInKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy9hY2NvdW50L21mYS9hdXRoZW50aWNhdG9ycy97dHlwZX0nLnJlcGxhY2UoJ3t0eXBlfScsIHR5cGUpO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7XG4gICAgICAgICAgICAnY29udGVudC10eXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgnZGVsZXRlJywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogQmVnaW4gdGhlIHByb2Nlc3Mgb2YgTUZBIHZlcmlmaWNhdGlvbiBhZnRlciBzaWduLWluLiBGaW5pc2ggdGhlIGZsb3cgd2l0aCBbdXBkYXRlTWZhQ2hhbGxlbmdlXSgvZG9jcy9yZWZlcmVuY2VzL2Nsb3VkL2NsaWVudC13ZWIvYWNjb3VudCN1cGRhdGVNZmFDaGFsbGVuZ2UpIG1ldGhvZC5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7QXV0aGVudGljYXRpb25GYWN0b3J9IGZhY3RvclxuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPE1vZGVscy5NZmFDaGFsbGVuZ2U+fVxuICAgICAqL1xuICAgIGNyZWF0ZU1mYUNoYWxsZW5nZShmYWN0b3IpIHtcbiAgICAgICAgaWYgKHR5cGVvZiBmYWN0b3IgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcImZhY3RvclwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvYWNjb3VudC9tZmEvY2hhbGxlbmdlJztcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBpZiAodHlwZW9mIGZhY3RvciAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ2ZhY3RvciddID0gZmFjdG9yO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7XG4gICAgICAgICAgICAnY29udGVudC10eXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgncG9zdCcsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIENvbXBsZXRlIHRoZSBNRkEgY2hhbGxlbmdlIGJ5IHByb3ZpZGluZyB0aGUgb25lLXRpbWUgcGFzc3dvcmQuIEZpbmlzaCB0aGUgcHJvY2VzcyBvZiBNRkEgdmVyaWZpY2F0aW9uIGJ5IHByb3ZpZGluZyB0aGUgb25lLXRpbWUgcGFzc3dvcmQuIFRvIGJlZ2luIHRoZSBmbG93LCB1c2UgW2NyZWF0ZU1mYUNoYWxsZW5nZV0oL2RvY3MvcmVmZXJlbmNlcy9jbG91ZC9jbGllbnQtd2ViL2FjY291bnQjY3JlYXRlTWZhQ2hhbGxlbmdlKSBtZXRob2QuXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gY2hhbGxlbmdlSWRcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gb3RwXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8TW9kZWxzLlNlc3Npb24+fVxuICAgICAqL1xuICAgIHVwZGF0ZU1mYUNoYWxsZW5nZShjaGFsbGVuZ2VJZCwgb3RwKSB7XG4gICAgICAgIGlmICh0eXBlb2YgY2hhbGxlbmdlSWQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcImNoYWxsZW5nZUlkXCInKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIG90cCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwib3RwXCInKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy9hY2NvdW50L21mYS9jaGFsbGVuZ2UnO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGlmICh0eXBlb2YgY2hhbGxlbmdlSWQgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydjaGFsbGVuZ2VJZCddID0gY2hhbGxlbmdlSWQ7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBvdHAgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydvdHAnXSA9IG90cDtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge1xuICAgICAgICAgICAgJ2NvbnRlbnQtdHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ3B1dCcsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIExpc3QgdGhlIGZhY3RvcnMgYXZhaWxhYmxlIG9uIHRoZSBhY2NvdW50IHRvIGJlIHVzZWQgYXMgYSBNRkEgY2hhbGxhbmdlLlxuICAgICAqXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8TW9kZWxzLk1mYUZhY3RvcnM+fVxuICAgICAqL1xuICAgIGxpc3RNZmFGYWN0b3JzKCkge1xuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy9hY2NvdW50L21mYS9mYWN0b3JzJztcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge307XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdnZXQnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBHZXQgcmVjb3ZlcnkgY29kZXMgdGhhdCBjYW4gYmUgdXNlZCBhcyBiYWNrdXAgZm9yIE1GQSBmbG93LiBCZWZvcmUgZ2V0dGluZyBjb2RlcywgdGhleSBtdXN0IGJlIGdlbmVyYXRlZCB1c2luZyBbY3JlYXRlTWZhUmVjb3ZlcnlDb2Rlc10oL2RvY3MvcmVmZXJlbmNlcy9jbG91ZC9jbGllbnQtd2ViL2FjY291bnQjY3JlYXRlTWZhUmVjb3ZlcnlDb2RlcykgbWV0aG9kLiBBbiBPVFAgY2hhbGxlbmdlIGlzIHJlcXVpcmVkIHRvIHJlYWQgcmVjb3ZlcnkgY29kZXMuXG4gICAgICpcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTxNb2RlbHMuTWZhUmVjb3ZlcnlDb2Rlcz59XG4gICAgICovXG4gICAgZ2V0TWZhUmVjb3ZlcnlDb2RlcygpIHtcbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvYWNjb3VudC9tZmEvcmVjb3ZlcnktY29kZXMnO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7fTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ2dldCcsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIEdlbmVyYXRlIHJlY292ZXJ5IGNvZGVzIGFzIGJhY2t1cCBmb3IgTUZBIGZsb3cuIEl0JiMwMzk7cyByZWNvbW1lbmRlZCB0byBnZW5lcmF0ZSBhbmQgc2hvdyB0aGVuIGltbWVkaWF0ZWx5IGFmdGVyIHVzZXIgc3VjY2Vzc2Z1bGx5IGFkZHMgdGhlaXIgYXV0aGVodGljYXRvci4gUmVjb3ZlcnkgY29kZXMgY2FuIGJlIHVzZWQgYXMgYSBNRkEgdmVyaWZpY2F0aW9uIHR5cGUgaW4gW2NyZWF0ZU1mYUNoYWxsZW5nZV0oL2RvY3MvcmVmZXJlbmNlcy9jbG91ZC9jbGllbnQtd2ViL2FjY291bnQjY3JlYXRlTWZhQ2hhbGxlbmdlKSBtZXRob2QuXG4gICAgICpcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTxNb2RlbHMuTWZhUmVjb3ZlcnlDb2Rlcz59XG4gICAgICovXG4gICAgY3JlYXRlTWZhUmVjb3ZlcnlDb2RlcygpIHtcbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvYWNjb3VudC9tZmEvcmVjb3ZlcnktY29kZXMnO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7XG4gICAgICAgICAgICAnY29udGVudC10eXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgncG9zdCcsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFJlZ2VuZXJhdGUgcmVjb3ZlcnkgY29kZXMgdGhhdCBjYW4gYmUgdXNlZCBhcyBiYWNrdXAgZm9yIE1GQSBmbG93LiBCZWZvcmUgcmVnZW5lcmF0aW5nIGNvZGVzLCB0aGV5IG11c3QgYmUgZmlyc3QgZ2VuZXJhdGVkIHVzaW5nIFtjcmVhdGVNZmFSZWNvdmVyeUNvZGVzXSgvZG9jcy9yZWZlcmVuY2VzL2Nsb3VkL2NsaWVudC13ZWIvYWNjb3VudCNjcmVhdGVNZmFSZWNvdmVyeUNvZGVzKSBtZXRob2QuIEFuIE9UUCBjaGFsbGVuZ2UgaXMgcmVxdWlyZWQgdG8gcmVnZW5yZWF0ZSByZWNvdmVyeSBjb2Rlcy5cbiAgICAgKlxuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPE1vZGVscy5NZmFSZWNvdmVyeUNvZGVzPn1cbiAgICAgKi9cbiAgICB1cGRhdGVNZmFSZWNvdmVyeUNvZGVzKCkge1xuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy9hY2NvdW50L21mYS9yZWNvdmVyeS1jb2Rlcyc7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgY29uc3QgYXBpSGVhZGVycyA9IHtcbiAgICAgICAgICAgICdjb250ZW50LXR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgIH07XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdwYXRjaCcsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFVwZGF0ZSBjdXJyZW50bHkgbG9nZ2VkIGluIHVzZXIgYWNjb3VudCBuYW1lLlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IG5hbWVcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTxNb2RlbHMuVXNlcjxQcmVmZXJlbmNlcz4+fVxuICAgICAqL1xuICAgIHVwZGF0ZU5hbWUobmFtZSkge1xuICAgICAgICBpZiAodHlwZW9mIG5hbWUgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcIm5hbWVcIicpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2FjY291bnQvbmFtZSc7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgaWYgKHR5cGVvZiBuYW1lICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsnbmFtZSddID0gbmFtZTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge1xuICAgICAgICAgICAgJ2NvbnRlbnQtdHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ3BhdGNoJywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogVXBkYXRlIGN1cnJlbnRseSBsb2dnZWQgaW4gdXNlciBwYXNzd29yZC4gRm9yIHZhbGlkYXRpb24sIHVzZXIgaXMgcmVxdWlyZWQgdG8gcGFzcyBpbiB0aGUgbmV3IHBhc3N3b3JkLCBhbmQgdGhlIG9sZCBwYXNzd29yZC4gRm9yIHVzZXJzIGNyZWF0ZWQgd2l0aCBPQXV0aCwgVGVhbSBJbnZpdGVzIGFuZCBNYWdpYyBVUkwsIG9sZFBhc3N3b3JkIGlzIG9wdGlvbmFsLlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IHBhc3N3b3JkXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IG9sZFBhc3N3b3JkXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8TW9kZWxzLlVzZXI8UHJlZmVyZW5jZXM+Pn1cbiAgICAgKi9cbiAgICB1cGRhdGVQYXNzd29yZChwYXNzd29yZCwgb2xkUGFzc3dvcmQpIHtcbiAgICAgICAgaWYgKHR5cGVvZiBwYXNzd29yZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwicGFzc3dvcmRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2FjY291bnQvcGFzc3dvcmQnO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGlmICh0eXBlb2YgcGFzc3dvcmQgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydwYXNzd29yZCddID0gcGFzc3dvcmQ7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBvbGRQYXNzd29yZCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ29sZFBhc3N3b3JkJ10gPSBvbGRQYXNzd29yZDtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge1xuICAgICAgICAgICAgJ2NvbnRlbnQtdHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ3BhdGNoJywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogVXBkYXRlIHRoZSBjdXJyZW50bHkgbG9nZ2VkIGluIHVzZXImIzAzOTtzIHBob25lIG51bWJlci4gQWZ0ZXIgdXBkYXRpbmcgdGhlIHBob25lIG51bWJlciwgdGhlIHBob25lIHZlcmlmaWNhdGlvbiBzdGF0dXMgd2lsbCBiZSByZXNldC4gQSBjb25maXJtYXRpb24gU01TIGlzIG5vdCBzZW50IGF1dG9tYXRpY2FsbHksIGhvd2V2ZXIgeW91IGNhbiB1c2UgdGhlIFtQT1NUIC9hY2NvdW50L3ZlcmlmaWNhdGlvbi9waG9uZV0oaHR0cHM6Ly9hcHB3cml0ZS5pby9kb2NzL3JlZmVyZW5jZXMvY2xvdWQvY2xpZW50LXdlYi9hY2NvdW50I2NyZWF0ZVBob25lVmVyaWZpY2F0aW9uKSBlbmRwb2ludCB0byBzZW5kIGEgY29uZmlybWF0aW9uIFNNUy5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBwaG9uZVxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBwYXNzd29yZFxuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPE1vZGVscy5Vc2VyPFByZWZlcmVuY2VzPj59XG4gICAgICovXG4gICAgdXBkYXRlUGhvbmUocGhvbmUsIHBhc3N3b3JkKSB7XG4gICAgICAgIGlmICh0eXBlb2YgcGhvbmUgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcInBob25lXCInKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIHBhc3N3b3JkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJwYXNzd29yZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvYWNjb3VudC9waG9uZSc7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgaWYgKHR5cGVvZiBwaG9uZSAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3Bob25lJ10gPSBwaG9uZTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIHBhc3N3b3JkICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsncGFzc3dvcmQnXSA9IHBhc3N3b3JkO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7XG4gICAgICAgICAgICAnY29udGVudC10eXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgncGF0Y2gnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBHZXQgdGhlIHByZWZlcmVuY2VzIGFzIGEga2V5LXZhbHVlIG9iamVjdCBmb3IgdGhlIGN1cnJlbnRseSBsb2dnZWQgaW4gdXNlci5cbiAgICAgKlxuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPFByZWZlcmVuY2VzPn1cbiAgICAgKi9cbiAgICBnZXRQcmVmcygpIHtcbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvYWNjb3VudC9wcmVmcyc7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgY29uc3QgYXBpSGVhZGVycyA9IHt9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgnZ2V0JywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogVXBkYXRlIGN1cnJlbnRseSBsb2dnZWQgaW4gdXNlciBhY2NvdW50IHByZWZlcmVuY2VzLiBUaGUgb2JqZWN0IHlvdSBwYXNzIGlzIHN0b3JlZCBhcyBpcywgYW5kIHJlcGxhY2VzIGFueSBwcmV2aW91cyB2YWx1ZS4gVGhlIG1heGltdW0gYWxsb3dlZCBwcmVmcyBzaXplIGlzIDY0a0IgYW5kIHRocm93cyBlcnJvciBpZiBleGNlZWRlZC5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7UGFydGlhbDxQcmVmZXJlbmNlcz59IHByZWZzXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8TW9kZWxzLlVzZXI8UHJlZmVyZW5jZXM+Pn1cbiAgICAgKi9cbiAgICB1cGRhdGVQcmVmcyhwcmVmcykge1xuICAgICAgICBpZiAodHlwZW9mIHByZWZzID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJwcmVmc1wiJyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvYWNjb3VudC9wcmVmcyc7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgaWYgKHR5cGVvZiBwcmVmcyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3ByZWZzJ10gPSBwcmVmcztcbiAgICAgICAgfVxuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge1xuICAgICAgICAgICAgJ2NvbnRlbnQtdHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ3BhdGNoJywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogU2VuZHMgdGhlIHVzZXIgYW4gZW1haWwgd2l0aCBhIHRlbXBvcmFyeSBzZWNyZXQga2V5IGZvciBwYXNzd29yZCByZXNldC4gV2hlbiB0aGUgdXNlciBjbGlja3MgdGhlIGNvbmZpcm1hdGlvbiBsaW5rIGhlIGlzIHJlZGlyZWN0ZWQgYmFjayB0byB5b3VyIGFwcCBwYXNzd29yZCByZXNldCBVUkwgd2l0aCB0aGUgc2VjcmV0IGtleSBhbmQgZW1haWwgYWRkcmVzcyB2YWx1ZXMgYXR0YWNoZWQgdG8gdGhlIFVSTCBxdWVyeSBzdHJpbmcuIFVzZSB0aGUgcXVlcnkgc3RyaW5nIHBhcmFtcyB0byBzdWJtaXQgYSByZXF1ZXN0IHRvIHRoZSBbUFVUIC9hY2NvdW50L3JlY292ZXJ5XShodHRwczovL2FwcHdyaXRlLmlvL2RvY3MvcmVmZXJlbmNlcy9jbG91ZC9jbGllbnQtd2ViL2FjY291bnQjdXBkYXRlUmVjb3ZlcnkpIGVuZHBvaW50IHRvIGNvbXBsZXRlIHRoZSBwcm9jZXNzLiBUaGUgdmVyaWZpY2F0aW9uIGxpbmsgc2VudCB0byB0aGUgdXNlciYjMDM5O3MgZW1haWwgYWRkcmVzcyBpcyB2YWxpZCBmb3IgMSBob3VyLlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGVtYWlsXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IHVybFxuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPE1vZGVscy5Ub2tlbj59XG4gICAgICovXG4gICAgY3JlYXRlUmVjb3ZlcnkoZW1haWwsIHVybCkge1xuICAgICAgICBpZiAodHlwZW9mIGVtYWlsID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJlbWFpbFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiB1cmwgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcInVybFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvYWNjb3VudC9yZWNvdmVyeSc7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgaWYgKHR5cGVvZiBlbWFpbCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ2VtYWlsJ10gPSBlbWFpbDtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIHVybCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3VybCddID0gdXJsO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7XG4gICAgICAgICAgICAnY29udGVudC10eXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgncG9zdCcsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFVzZSB0aGlzIGVuZHBvaW50IHRvIGNvbXBsZXRlIHRoZSB1c2VyIGFjY291bnQgcGFzc3dvcmQgcmVzZXQuIEJvdGggdGhlICoqdXNlcklkKiogYW5kICoqc2VjcmV0KiogYXJndW1lbnRzIHdpbGwgYmUgcGFzc2VkIGFzIHF1ZXJ5IHBhcmFtZXRlcnMgdG8gdGhlIHJlZGlyZWN0IFVSTCB5b3UgaGF2ZSBwcm92aWRlZCB3aGVuIHNlbmRpbmcgeW91ciByZXF1ZXN0IHRvIHRoZSBbUE9TVCAvYWNjb3VudC9yZWNvdmVyeV0oaHR0cHM6Ly9hcHB3cml0ZS5pby9kb2NzL3JlZmVyZW5jZXMvY2xvdWQvY2xpZW50LXdlYi9hY2NvdW50I2NyZWF0ZVJlY292ZXJ5KSBlbmRwb2ludC5cbiAgICAgKlxuICAgICAqIFBsZWFzZSBub3RlIHRoYXQgaW4gb3JkZXIgdG8gYXZvaWQgYSBbUmVkaXJlY3QgQXR0YWNrXShodHRwczovL2dpdGh1Yi5jb20vT1dBU1AvQ2hlYXRTaGVldFNlcmllcy9ibG9iL21hc3Rlci9jaGVhdHNoZWV0cy9VbnZhbGlkYXRlZF9SZWRpcmVjdHNfYW5kX0ZvcndhcmRzX0NoZWF0X1NoZWV0Lm1kKSB0aGUgb25seSB2YWxpZCByZWRpcmVjdCBVUkxzIGFyZSB0aGUgb25lcyBmcm9tIGRvbWFpbnMgeW91IGhhdmUgc2V0IHdoZW4gYWRkaW5nIHlvdXIgcGxhdGZvcm1zIGluIHRoZSBjb25zb2xlIGludGVyZmFjZS5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSB1c2VySWRcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gc2VjcmV0XG4gICAgICogQHBhcmFtIHtzdHJpbmd9IHBhc3N3b3JkXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8TW9kZWxzLlRva2VuPn1cbiAgICAgKi9cbiAgICB1cGRhdGVSZWNvdmVyeSh1c2VySWQsIHNlY3JldCwgcGFzc3dvcmQpIHtcbiAgICAgICAgaWYgKHR5cGVvZiB1c2VySWQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcInVzZXJJZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBzZWNyZXQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcInNlY3JldFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBwYXNzd29yZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwicGFzc3dvcmRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2FjY291bnQvcmVjb3ZlcnknO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGlmICh0eXBlb2YgdXNlcklkICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsndXNlcklkJ10gPSB1c2VySWQ7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBzZWNyZXQgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydzZWNyZXQnXSA9IHNlY3JldDtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIHBhc3N3b3JkICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsncGFzc3dvcmQnXSA9IHBhc3N3b3JkO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7XG4gICAgICAgICAgICAnY29udGVudC10eXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgncHV0JywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogR2V0IHRoZSBsaXN0IG9mIGFjdGl2ZSBzZXNzaW9ucyBhY3Jvc3MgZGlmZmVyZW50IGRldmljZXMgZm9yIHRoZSBjdXJyZW50bHkgbG9nZ2VkIGluIHVzZXIuXG4gICAgICpcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTxNb2RlbHMuU2Vzc2lvbkxpc3Q+fVxuICAgICAqL1xuICAgIGxpc3RTZXNzaW9ucygpIHtcbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvYWNjb3VudC9zZXNzaW9ucyc7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgY29uc3QgYXBpSGVhZGVycyA9IHt9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgnZ2V0JywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogRGVsZXRlIGFsbCBzZXNzaW9ucyBmcm9tIHRoZSB1c2VyIGFjY291bnQgYW5kIHJlbW92ZSBhbnkgc2Vzc2lvbnMgY29va2llcyBmcm9tIHRoZSBlbmQgY2xpZW50LlxuICAgICAqXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8e30+fVxuICAgICAqL1xuICAgIGRlbGV0ZVNlc3Npb25zKCkge1xuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy9hY2NvdW50L3Nlc3Npb25zJztcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge1xuICAgICAgICAgICAgJ2NvbnRlbnQtdHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ2RlbGV0ZScsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFVzZSB0aGlzIGVuZHBvaW50IHRvIGFsbG93IGEgbmV3IHVzZXIgdG8gcmVnaXN0ZXIgYW4gYW5vbnltb3VzIGFjY291bnQgaW4geW91ciBwcm9qZWN0LiBUaGlzIHJvdXRlIHdpbGwgYWxzbyBjcmVhdGUgYSBuZXcgc2Vzc2lvbiBmb3IgdGhlIHVzZXIuIFRvIGFsbG93IHRoZSBuZXcgdXNlciB0byBjb252ZXJ0IGFuIGFub255bW91cyBhY2NvdW50IHRvIGEgbm9ybWFsIGFjY291bnQsIHlvdSBuZWVkIHRvIHVwZGF0ZSBpdHMgW2VtYWlsIGFuZCBwYXNzd29yZF0oaHR0cHM6Ly9hcHB3cml0ZS5pby9kb2NzL3JlZmVyZW5jZXMvY2xvdWQvY2xpZW50LXdlYi9hY2NvdW50I3VwZGF0ZUVtYWlsKSBvciBjcmVhdGUgYW4gW09BdXRoMiBzZXNzaW9uXShodHRwczovL2FwcHdyaXRlLmlvL2RvY3MvcmVmZXJlbmNlcy9jbG91ZC9jbGllbnQtd2ViL2FjY291bnQjQ3JlYXRlT0F1dGgyU2Vzc2lvbikuXG4gICAgICpcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTxNb2RlbHMuU2Vzc2lvbj59XG4gICAgICovXG4gICAgY3JlYXRlQW5vbnltb3VzU2Vzc2lvbigpIHtcbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvYWNjb3VudC9zZXNzaW9ucy9hbm9ueW1vdXMnO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7XG4gICAgICAgICAgICAnY29udGVudC10eXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgncG9zdCcsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIEFsbG93IHRoZSB1c2VyIHRvIGxvZ2luIGludG8gdGhlaXIgYWNjb3VudCBieSBwcm92aWRpbmcgYSB2YWxpZCBlbWFpbCBhbmQgcGFzc3dvcmQgY29tYmluYXRpb24uIFRoaXMgcm91dGUgd2lsbCBjcmVhdGUgYSBuZXcgc2Vzc2lvbiBmb3IgdGhlIHVzZXIuXG4gICAgICpcbiAgICAgKiBBIHVzZXIgaXMgbGltaXRlZCB0byAxMCBhY3RpdmUgc2Vzc2lvbnMgYXQgYSB0aW1lIGJ5IGRlZmF1bHQuIFtMZWFybiBtb3JlIGFib3V0IHNlc3Npb24gbGltaXRzXShodHRwczovL2FwcHdyaXRlLmlvL2RvY3MvYXV0aGVudGljYXRpb24tc2VjdXJpdHkjbGltaXRzKS5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBlbWFpbFxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBwYXNzd29yZFxuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPE1vZGVscy5TZXNzaW9uPn1cbiAgICAgKi9cbiAgICBjcmVhdGVFbWFpbFBhc3N3b3JkU2Vzc2lvbihlbWFpbCwgcGFzc3dvcmQpIHtcbiAgICAgICAgaWYgKHR5cGVvZiBlbWFpbCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwiZW1haWxcIicpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgcGFzc3dvcmQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcInBhc3N3b3JkXCInKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy9hY2NvdW50L3Nlc3Npb25zL2VtYWlsJztcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBpZiAodHlwZW9mIGVtYWlsICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsnZW1haWwnXSA9IGVtYWlsO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgcGFzc3dvcmQgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydwYXNzd29yZCddID0gcGFzc3dvcmQ7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgY29uc3QgYXBpSGVhZGVycyA9IHtcbiAgICAgICAgICAgICdjb250ZW50LXR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgIH07XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdwb3N0JywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogVXNlIHRoaXMgZW5kcG9pbnQgdG8gY3JlYXRlIGEgc2Vzc2lvbiBmcm9tIHRva2VuLiBQcm92aWRlIHRoZSAqKnVzZXJJZCoqIGFuZCAqKnNlY3JldCoqIHBhcmFtZXRlcnMgZnJvbSB0aGUgc3VjY2Vzc2Z1bCByZXNwb25zZSBvZiBhdXRoZW50aWNhdGlvbiBmbG93cyBpbml0aWF0ZWQgYnkgdG9rZW4gY3JlYXRpb24uIEZvciBleGFtcGxlLCBtYWdpYyBVUkwgYW5kIHBob25lIGxvZ2luLlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IHVzZXJJZFxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBzZWNyZXRcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTxNb2RlbHMuU2Vzc2lvbj59XG4gICAgICovXG4gICAgdXBkYXRlTWFnaWNVUkxTZXNzaW9uKHVzZXJJZCwgc2VjcmV0KSB7XG4gICAgICAgIGlmICh0eXBlb2YgdXNlcklkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJ1c2VySWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2Ygc2VjcmV0ID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJzZWNyZXRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2FjY291bnQvc2Vzc2lvbnMvbWFnaWMtdXJsJztcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBpZiAodHlwZW9mIHVzZXJJZCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3VzZXJJZCddID0gdXNlcklkO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2Ygc2VjcmV0ICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsnc2VjcmV0J10gPSBzZWNyZXQ7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgY29uc3QgYXBpSGVhZGVycyA9IHtcbiAgICAgICAgICAgICdjb250ZW50LXR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgIH07XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdwdXQnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBBbGxvdyB0aGUgdXNlciB0byBsb2dpbiB0byB0aGVpciBhY2NvdW50IHVzaW5nIHRoZSBPQXV0aDIgcHJvdmlkZXIgb2YgdGhlaXIgY2hvaWNlLiBFYWNoIE9BdXRoMiBwcm92aWRlciBzaG91bGQgYmUgZW5hYmxlZCBmcm9tIHRoZSBBcHB3cml0ZSBjb25zb2xlIGZpcnN0LiBVc2UgdGhlIHN1Y2Nlc3MgYW5kIGZhaWx1cmUgYXJndW1lbnRzIHRvIHByb3ZpZGUgYSByZWRpcmVjdCBVUkwmIzAzOTtzIGJhY2sgdG8geW91ciBhcHAgd2hlbiBsb2dpbiBpcyBjb21wbGV0ZWQuXG4gICAgICpcbiAgICAgKiBJZiB0aGVyZSBpcyBhbHJlYWR5IGFuIGFjdGl2ZSBzZXNzaW9uLCB0aGUgbmV3IHNlc3Npb24gd2lsbCBiZSBhdHRhY2hlZCB0byB0aGUgbG9nZ2VkLWluIGFjY291bnQuIElmIHRoZXJlIGFyZSBubyBhY3RpdmUgc2Vzc2lvbnMsIHRoZSBzZXJ2ZXIgd2lsbCBhdHRlbXB0IHRvIGxvb2sgZm9yIGEgdXNlciB3aXRoIHRoZSBzYW1lIGVtYWlsIGFkZHJlc3MgYXMgdGhlIGVtYWlsIHJlY2VpdmVkIGZyb20gdGhlIE9BdXRoMiBwcm92aWRlciBhbmQgYXR0YWNoIHRoZSBuZXcgc2Vzc2lvbiB0byB0aGUgZXhpc3RpbmcgdXNlci4gSWYgbm8gbWF0Y2hpbmcgdXNlciBpcyBmb3VuZCAtIHRoZSBzZXJ2ZXIgd2lsbCBjcmVhdGUgYSBuZXcgdXNlci5cbiAgICAgKlxuICAgICAqIEEgdXNlciBpcyBsaW1pdGVkIHRvIDEwIGFjdGl2ZSBzZXNzaW9ucyBhdCBhIHRpbWUgYnkgZGVmYXVsdC4gW0xlYXJuIG1vcmUgYWJvdXQgc2Vzc2lvbiBsaW1pdHNdKGh0dHBzOi8vYXBwd3JpdGUuaW8vZG9jcy9hdXRoZW50aWNhdGlvbi1zZWN1cml0eSNsaW1pdHMpLlxuICAgICAqXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge09BdXRoUHJvdmlkZXJ9IHByb3ZpZGVyXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IHN1Y2Nlc3NcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gZmFpbHVyZVxuICAgICAqIEBwYXJhbSB7c3RyaW5nW119IHNjb3Blc1xuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHt2b2lkIHwgc3RyaW5nfVxuICAgICAqL1xuICAgIGNyZWF0ZU9BdXRoMlNlc3Npb24ocHJvdmlkZXIsIHN1Y2Nlc3MsIGZhaWx1cmUsIHNjb3Blcykge1xuICAgICAgICBpZiAodHlwZW9mIHByb3ZpZGVyID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJwcm92aWRlclwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvYWNjb3VudC9zZXNzaW9ucy9vYXV0aDIve3Byb3ZpZGVyfScucmVwbGFjZSgne3Byb3ZpZGVyfScsIHByb3ZpZGVyKTtcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBpZiAodHlwZW9mIHN1Y2Nlc3MgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydzdWNjZXNzJ10gPSBzdWNjZXNzO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgZmFpbHVyZSAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ2ZhaWx1cmUnXSA9IGZhaWx1cmU7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBzY29wZXMgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydzY29wZXMnXSA9IHNjb3BlcztcbiAgICAgICAgfVxuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBwYXlsb2FkWydwcm9qZWN0J10gPSB0aGlzLmNsaWVudC5jb25maWcucHJvamVjdDtcbiAgICAgICAgZm9yIChjb25zdCBba2V5LCB2YWx1ZV0gb2YgT2JqZWN0LmVudHJpZXMoU2VydmljZS5mbGF0dGVuKHBheWxvYWQpKSkge1xuICAgICAgICAgICAgdXJpLnNlYXJjaFBhcmFtcy5hcHBlbmQoa2V5LCB2YWx1ZSk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnICYmICh3aW5kb3cgPT09IG51bGwgfHwgd2luZG93ID09PSB2b2lkIDAgPyB2b2lkIDAgOiB3aW5kb3cubG9jYXRpb24pKSB7XG4gICAgICAgICAgICB3aW5kb3cubG9jYXRpb24uaHJlZiA9IHVyaS50b1N0cmluZygpO1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgcmV0dXJuIHVyaS50b1N0cmluZygpO1xuICAgICAgICB9XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFVzZSB0aGlzIGVuZHBvaW50IHRvIGNyZWF0ZSBhIHNlc3Npb24gZnJvbSB0b2tlbi4gUHJvdmlkZSB0aGUgKip1c2VySWQqKiBhbmQgKipzZWNyZXQqKiBwYXJhbWV0ZXJzIGZyb20gdGhlIHN1Y2Nlc3NmdWwgcmVzcG9uc2Ugb2YgYXV0aGVudGljYXRpb24gZmxvd3MgaW5pdGlhdGVkIGJ5IHRva2VuIGNyZWF0aW9uLiBGb3IgZXhhbXBsZSwgbWFnaWMgVVJMIGFuZCBwaG9uZSBsb2dpbi5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSB1c2VySWRcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gc2VjcmV0XG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8TW9kZWxzLlNlc3Npb24+fVxuICAgICAqL1xuICAgIHVwZGF0ZVBob25lU2Vzc2lvbih1c2VySWQsIHNlY3JldCkge1xuICAgICAgICBpZiAodHlwZW9mIHVzZXJJZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwidXNlcklkXCInKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIHNlY3JldCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwic2VjcmV0XCInKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy9hY2NvdW50L3Nlc3Npb25zL3Bob25lJztcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBpZiAodHlwZW9mIHVzZXJJZCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3VzZXJJZCddID0gdXNlcklkO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2Ygc2VjcmV0ICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsnc2VjcmV0J10gPSBzZWNyZXQ7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgY29uc3QgYXBpSGVhZGVycyA9IHtcbiAgICAgICAgICAgICdjb250ZW50LXR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgIH07XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdwdXQnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBVc2UgdGhpcyBlbmRwb2ludCB0byBjcmVhdGUgYSBzZXNzaW9uIGZyb20gdG9rZW4uIFByb3ZpZGUgdGhlICoqdXNlcklkKiogYW5kICoqc2VjcmV0KiogcGFyYW1ldGVycyBmcm9tIHRoZSBzdWNjZXNzZnVsIHJlc3BvbnNlIG9mIGF1dGhlbnRpY2F0aW9uIGZsb3dzIGluaXRpYXRlZCBieSB0b2tlbiBjcmVhdGlvbi4gRm9yIGV4YW1wbGUsIG1hZ2ljIFVSTCBhbmQgcGhvbmUgbG9naW4uXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gdXNlcklkXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IHNlY3JldFxuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPE1vZGVscy5TZXNzaW9uPn1cbiAgICAgKi9cbiAgICBjcmVhdGVTZXNzaW9uKHVzZXJJZCwgc2VjcmV0KSB7XG4gICAgICAgIGlmICh0eXBlb2YgdXNlcklkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJ1c2VySWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2Ygc2VjcmV0ID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJzZWNyZXRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2FjY291bnQvc2Vzc2lvbnMvdG9rZW4nO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGlmICh0eXBlb2YgdXNlcklkICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsndXNlcklkJ10gPSB1c2VySWQ7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBzZWNyZXQgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydzZWNyZXQnXSA9IHNlY3JldDtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge1xuICAgICAgICAgICAgJ2NvbnRlbnQtdHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ3Bvc3QnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBVc2UgdGhpcyBlbmRwb2ludCB0byBnZXQgYSBsb2dnZWQgaW4gdXNlciYjMDM5O3Mgc2Vzc2lvbiB1c2luZyBhIFNlc3Npb24gSUQuIElucHV0dGluZyAmIzAzOTtjdXJyZW50JiMwMzk7IHdpbGwgcmV0dXJuIHRoZSBjdXJyZW50IHNlc3Npb24gYmVpbmcgdXNlZC5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBzZXNzaW9uSWRcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTxNb2RlbHMuU2Vzc2lvbj59XG4gICAgICovXG4gICAgZ2V0U2Vzc2lvbihzZXNzaW9uSWQpIHtcbiAgICAgICAgaWYgKHR5cGVvZiBzZXNzaW9uSWQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcInNlc3Npb25JZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvYWNjb3VudC9zZXNzaW9ucy97c2Vzc2lvbklkfScucmVwbGFjZSgne3Nlc3Npb25JZH0nLCBzZXNzaW9uSWQpO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7fTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ2dldCcsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFVzZSB0aGlzIGVuZHBvaW50IHRvIGV4dGVuZCBhIHNlc3Npb24mIzAzOTtzIGxlbmd0aC4gRXh0ZW5kaW5nIGEgc2Vzc2lvbiBpcyB1c2VmdWwgd2hlbiBzZXNzaW9uIGV4cGlyeSBpcyBzaG9ydC4gSWYgdGhlIHNlc3Npb24gd2FzIGNyZWF0ZWQgdXNpbmcgYW4gT0F1dGggcHJvdmlkZXIsIHRoaXMgZW5kcG9pbnQgcmVmcmVzaGVzIHRoZSBhY2Nlc3MgdG9rZW4gZnJvbSB0aGUgcHJvdmlkZXIuXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gc2Vzc2lvbklkXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8TW9kZWxzLlNlc3Npb24+fVxuICAgICAqL1xuICAgIHVwZGF0ZVNlc3Npb24oc2Vzc2lvbklkKSB7XG4gICAgICAgIGlmICh0eXBlb2Ygc2Vzc2lvbklkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJzZXNzaW9uSWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2FjY291bnQvc2Vzc2lvbnMve3Nlc3Npb25JZH0nLnJlcGxhY2UoJ3tzZXNzaW9uSWR9Jywgc2Vzc2lvbklkKTtcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge1xuICAgICAgICAgICAgJ2NvbnRlbnQtdHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ3BhdGNoJywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogTG9nb3V0IHRoZSB1c2VyLiBVc2UgJiMwMzk7Y3VycmVudCYjMDM5OyBhcyB0aGUgc2Vzc2lvbiBJRCB0byBsb2dvdXQgb24gdGhpcyBkZXZpY2UsIHVzZSBhIHNlc3Npb24gSUQgdG8gbG9nb3V0IG9uIGFub3RoZXIgZGV2aWNlLiBJZiB5b3UmIzAzOTtyZSBsb29raW5nIHRvIGxvZ291dCB0aGUgdXNlciBvbiBhbGwgZGV2aWNlcywgdXNlIFtEZWxldGUgU2Vzc2lvbnNdKGh0dHBzOi8vYXBwd3JpdGUuaW8vZG9jcy9yZWZlcmVuY2VzL2Nsb3VkL2NsaWVudC13ZWIvYWNjb3VudCNkZWxldGVTZXNzaW9ucykgaW5zdGVhZC5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBzZXNzaW9uSWRcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTx7fT59XG4gICAgICovXG4gICAgZGVsZXRlU2Vzc2lvbihzZXNzaW9uSWQpIHtcbiAgICAgICAgaWYgKHR5cGVvZiBzZXNzaW9uSWQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcInNlc3Npb25JZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvYWNjb3VudC9zZXNzaW9ucy97c2Vzc2lvbklkfScucmVwbGFjZSgne3Nlc3Npb25JZH0nLCBzZXNzaW9uSWQpO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7XG4gICAgICAgICAgICAnY29udGVudC10eXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgnZGVsZXRlJywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogQmxvY2sgdGhlIGN1cnJlbnRseSBsb2dnZWQgaW4gdXNlciBhY2NvdW50LiBCZWhpbmQgdGhlIHNjZW5lLCB0aGUgdXNlciByZWNvcmQgaXMgbm90IGRlbGV0ZWQgYnV0IHBlcm1hbmVudGx5IGJsb2NrZWQgZnJvbSBhbnkgYWNjZXNzLiBUbyBjb21wbGV0ZWx5IGRlbGV0ZSBhIHVzZXIsIHVzZSB0aGUgVXNlcnMgQVBJIGluc3RlYWQuXG4gICAgICpcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTxNb2RlbHMuVXNlcjxQcmVmZXJlbmNlcz4+fVxuICAgICAqL1xuICAgIHVwZGF0ZVN0YXR1cygpIHtcbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvYWNjb3VudC9zdGF0dXMnO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7XG4gICAgICAgICAgICAnY29udGVudC10eXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgncGF0Y2gnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBVc2UgdGhpcyBlbmRwb2ludCB0byByZWdpc3RlciBhIGRldmljZSBmb3IgcHVzaCBub3RpZmljYXRpb25zLiBQcm92aWRlIGEgdGFyZ2V0IElEIChjdXN0b20gb3IgZ2VuZXJhdGVkIHVzaW5nIElELnVuaXF1ZSgpKSwgYSBkZXZpY2UgaWRlbnRpZmllciAodXN1YWxseSBhIGRldmljZSB0b2tlbiksIGFuZCBvcHRpb25hbGx5IHNwZWNpZnkgd2hpY2ggcHJvdmlkZXIgc2hvdWxkIHNlbmQgbm90aWZpY2F0aW9ucyB0byB0aGlzIHRhcmdldC4gVGhlIHRhcmdldCBpcyBhdXRvbWF0aWNhbGx5IGxpbmtlZCB0byB0aGUgY3VycmVudCBzZXNzaW9uIGFuZCBpbmNsdWRlcyBkZXZpY2UgaW5mb3JtYXRpb24gbGlrZSBicmFuZCBhbmQgbW9kZWwuXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gdGFyZ2V0SWRcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gaWRlbnRpZmllclxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBwcm92aWRlcklkXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8TW9kZWxzLlRhcmdldD59XG4gICAgICovXG4gICAgY3JlYXRlUHVzaFRhcmdldCh0YXJnZXRJZCwgaWRlbnRpZmllciwgcHJvdmlkZXJJZCkge1xuICAgICAgICBpZiAodHlwZW9mIHRhcmdldElkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJ0YXJnZXRJZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBpZGVudGlmaWVyID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJpZGVudGlmaWVyXCInKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy9hY2NvdW50L3RhcmdldHMvcHVzaCc7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgaWYgKHR5cGVvZiB0YXJnZXRJZCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3RhcmdldElkJ10gPSB0YXJnZXRJZDtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIGlkZW50aWZpZXIgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydpZGVudGlmaWVyJ10gPSBpZGVudGlmaWVyO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgcHJvdmlkZXJJZCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3Byb3ZpZGVySWQnXSA9IHByb3ZpZGVySWQ7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgY29uc3QgYXBpSGVhZGVycyA9IHtcbiAgICAgICAgICAgICdjb250ZW50LXR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgIH07XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdwb3N0JywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogVXBkYXRlIHRoZSBjdXJyZW50bHkgbG9nZ2VkIGluIHVzZXImIzAzOTtzIHB1c2ggbm90aWZpY2F0aW9uIHRhcmdldC4gWW91IGNhbiBtb2RpZnkgdGhlIHRhcmdldCYjMDM5O3MgaWRlbnRpZmllciAoZGV2aWNlIHRva2VuKSBhbmQgcHJvdmlkZXIgSUQgKHRva2VuLCBlbWFpbCwgcGhvbmUgZXRjLikuIFRoZSB0YXJnZXQgbXVzdCBleGlzdCBhbmQgYmVsb25nIHRvIHRoZSBjdXJyZW50IHVzZXIuIElmIHlvdSBjaGFuZ2UgdGhlIHByb3ZpZGVyIElELCBub3RpZmljYXRpb25zIHdpbGwgYmUgc2VudCB0aHJvdWdoIHRoZSBuZXcgbWVzc2FnaW5nIHByb3ZpZGVyIGluc3RlYWQuXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gdGFyZ2V0SWRcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gaWRlbnRpZmllclxuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPE1vZGVscy5UYXJnZXQ+fVxuICAgICAqL1xuICAgIHVwZGF0ZVB1c2hUYXJnZXQodGFyZ2V0SWQsIGlkZW50aWZpZXIpIHtcbiAgICAgICAgaWYgKHR5cGVvZiB0YXJnZXRJZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwidGFyZ2V0SWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgaWRlbnRpZmllciA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwiaWRlbnRpZmllclwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvYWNjb3VudC90YXJnZXRzL3t0YXJnZXRJZH0vcHVzaCcucmVwbGFjZSgne3RhcmdldElkfScsIHRhcmdldElkKTtcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBpZiAodHlwZW9mIGlkZW50aWZpZXIgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydpZGVudGlmaWVyJ10gPSBpZGVudGlmaWVyO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7XG4gICAgICAgICAgICAnY29udGVudC10eXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgncHV0JywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogRGVsZXRlIGEgcHVzaCBub3RpZmljYXRpb24gdGFyZ2V0IGZvciB0aGUgY3VycmVudGx5IGxvZ2dlZCBpbiB1c2VyLiBBZnRlciBkZWxldGlvbiwgdGhlIGRldmljZSB3aWxsIG5vIGxvbmdlciByZWNlaXZlIHB1c2ggbm90aWZpY2F0aW9ucy4gVGhlIHRhcmdldCBtdXN0IGV4aXN0IGFuZCBiZWxvbmcgdG8gdGhlIGN1cnJlbnQgdXNlci5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSB0YXJnZXRJZFxuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPHt9Pn1cbiAgICAgKi9cbiAgICBkZWxldGVQdXNoVGFyZ2V0KHRhcmdldElkKSB7XG4gICAgICAgIGlmICh0eXBlb2YgdGFyZ2V0SWQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcInRhcmdldElkXCInKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy9hY2NvdW50L3RhcmdldHMve3RhcmdldElkfS9wdXNoJy5yZXBsYWNlKCd7dGFyZ2V0SWR9JywgdGFyZ2V0SWQpO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7XG4gICAgICAgICAgICAnY29udGVudC10eXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgnZGVsZXRlJywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogU2VuZHMgdGhlIHVzZXIgYW4gZW1haWwgd2l0aCBhIHNlY3JldCBrZXkgZm9yIGNyZWF0aW5nIGEgc2Vzc2lvbi4gSWYgdGhlIHByb3ZpZGVkIHVzZXIgSUQgaGFzIG5vdCBiZSByZWdpc3RlcmVkLCBhIG5ldyB1c2VyIHdpbGwgYmUgY3JlYXRlZC4gVXNlIHRoZSByZXR1cm5lZCB1c2VyIElEIGFuZCBzZWNyZXQgYW5kIHN1Ym1pdCBhIHJlcXVlc3QgdG8gdGhlIFtQT1NUIC92MS9hY2NvdW50L3Nlc3Npb25zL3Rva2VuXShodHRwczovL2FwcHdyaXRlLmlvL2RvY3MvcmVmZXJlbmNlcy9jbG91ZC9jbGllbnQtd2ViL2FjY291bnQjY3JlYXRlU2Vzc2lvbikgZW5kcG9pbnQgdG8gY29tcGxldGUgdGhlIGxvZ2luIHByb2Nlc3MuIFRoZSBzZWNyZXQgc2VudCB0byB0aGUgdXNlciYjMDM5O3MgZW1haWwgaXMgdmFsaWQgZm9yIDE1IG1pbnV0ZXMuXG4gICAgICpcbiAgICAgKiBBIHVzZXIgaXMgbGltaXRlZCB0byAxMCBhY3RpdmUgc2Vzc2lvbnMgYXQgYSB0aW1lIGJ5IGRlZmF1bHQuIFtMZWFybiBtb3JlIGFib3V0IHNlc3Npb24gbGltaXRzXShodHRwczovL2FwcHdyaXRlLmlvL2RvY3MvYXV0aGVudGljYXRpb24tc2VjdXJpdHkjbGltaXRzKS5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSB1c2VySWRcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gZW1haWxcbiAgICAgKiBAcGFyYW0ge2Jvb2xlYW59IHBocmFzZVxuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPE1vZGVscy5Ub2tlbj59XG4gICAgICovXG4gICAgY3JlYXRlRW1haWxUb2tlbih1c2VySWQsIGVtYWlsLCBwaHJhc2UpIHtcbiAgICAgICAgaWYgKHR5cGVvZiB1c2VySWQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcInVzZXJJZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBlbWFpbCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwiZW1haWxcIicpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2FjY291bnQvdG9rZW5zL2VtYWlsJztcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBpZiAodHlwZW9mIHVzZXJJZCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3VzZXJJZCddID0gdXNlcklkO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgZW1haWwgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydlbWFpbCddID0gZW1haWw7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBwaHJhc2UgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydwaHJhc2UnXSA9IHBocmFzZTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge1xuICAgICAgICAgICAgJ2NvbnRlbnQtdHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ3Bvc3QnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBTZW5kcyB0aGUgdXNlciBhbiBlbWFpbCB3aXRoIGEgc2VjcmV0IGtleSBmb3IgY3JlYXRpbmcgYSBzZXNzaW9uLiBJZiB0aGUgcHJvdmlkZWQgdXNlciBJRCBoYXMgbm90IGJlZW4gcmVnaXN0ZXJlZCwgYSBuZXcgdXNlciB3aWxsIGJlIGNyZWF0ZWQuIFdoZW4gdGhlIHVzZXIgY2xpY2tzIHRoZSBsaW5rIGluIHRoZSBlbWFpbCwgdGhlIHVzZXIgaXMgcmVkaXJlY3RlZCBiYWNrIHRvIHRoZSBVUkwgeW91IHByb3ZpZGVkIHdpdGggdGhlIHNlY3JldCBrZXkgYW5kIHVzZXJJZCB2YWx1ZXMgYXR0YWNoZWQgdG8gdGhlIFVSTCBxdWVyeSBzdHJpbmcuIFVzZSB0aGUgcXVlcnkgc3RyaW5nIHBhcmFtZXRlcnMgdG8gc3VibWl0IGEgcmVxdWVzdCB0byB0aGUgW1BPU1QgL3YxL2FjY291bnQvc2Vzc2lvbnMvdG9rZW5dKGh0dHBzOi8vYXBwd3JpdGUuaW8vZG9jcy9yZWZlcmVuY2VzL2Nsb3VkL2NsaWVudC13ZWIvYWNjb3VudCNjcmVhdGVTZXNzaW9uKSBlbmRwb2ludCB0byBjb21wbGV0ZSB0aGUgbG9naW4gcHJvY2Vzcy4gVGhlIGxpbmsgc2VudCB0byB0aGUgdXNlciYjMDM5O3MgZW1haWwgYWRkcmVzcyBpcyB2YWxpZCBmb3IgMSBob3VyLlxuICAgICAqXG4gICAgICogQSB1c2VyIGlzIGxpbWl0ZWQgdG8gMTAgYWN0aXZlIHNlc3Npb25zIGF0IGEgdGltZSBieSBkZWZhdWx0LiBbTGVhcm4gbW9yZSBhYm91dCBzZXNzaW9uIGxpbWl0c10oaHR0cHM6Ly9hcHB3cml0ZS5pby9kb2NzL2F1dGhlbnRpY2F0aW9uLXNlY3VyaXR5I2xpbWl0cykuXG4gICAgICpcbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSB1c2VySWRcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gZW1haWxcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gdXJsXG4gICAgICogQHBhcmFtIHtib29sZWFufSBwaHJhc2VcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTxNb2RlbHMuVG9rZW4+fVxuICAgICAqL1xuICAgIGNyZWF0ZU1hZ2ljVVJMVG9rZW4odXNlcklkLCBlbWFpbCwgdXJsLCBwaHJhc2UpIHtcbiAgICAgICAgaWYgKHR5cGVvZiB1c2VySWQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcInVzZXJJZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBlbWFpbCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwiZW1haWxcIicpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2FjY291bnQvdG9rZW5zL21hZ2ljLXVybCc7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgaWYgKHR5cGVvZiB1c2VySWQgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWyd1c2VySWQnXSA9IHVzZXJJZDtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIGVtYWlsICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsnZW1haWwnXSA9IGVtYWlsO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgdXJsICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsndXJsJ10gPSB1cmw7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBwaHJhc2UgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydwaHJhc2UnXSA9IHBocmFzZTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge1xuICAgICAgICAgICAgJ2NvbnRlbnQtdHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ3Bvc3QnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBBbGxvdyB0aGUgdXNlciB0byBsb2dpbiB0byB0aGVpciBhY2NvdW50IHVzaW5nIHRoZSBPQXV0aDIgcHJvdmlkZXIgb2YgdGhlaXIgY2hvaWNlLiBFYWNoIE9BdXRoMiBwcm92aWRlciBzaG91bGQgYmUgZW5hYmxlZCBmcm9tIHRoZSBBcHB3cml0ZSBjb25zb2xlIGZpcnN0LiBVc2UgdGhlIHN1Y2Nlc3MgYW5kIGZhaWx1cmUgYXJndW1lbnRzIHRvIHByb3ZpZGUgYSByZWRpcmVjdCBVUkwmIzAzOTtzIGJhY2sgdG8geW91ciBhcHAgd2hlbiBsb2dpbiBpcyBjb21wbGV0ZWQuXG4gICAgICpcbiAgICAgKiBJZiBhdXRoZW50aWNhdGlvbiBzdWNjZWVkcywgYHVzZXJJZGAgYW5kIGBzZWNyZXRgIG9mIGEgdG9rZW4gd2lsbCBiZSBhcHBlbmRlZCB0byB0aGUgc3VjY2VzcyBVUkwgYXMgcXVlcnkgcGFyYW1ldGVycy4gVGhlc2UgY2FuIGJlIHVzZWQgdG8gY3JlYXRlIGEgbmV3IHNlc3Npb24gdXNpbmcgdGhlIFtDcmVhdGUgc2Vzc2lvbl0oaHR0cHM6Ly9hcHB3cml0ZS5pby9kb2NzL3JlZmVyZW5jZXMvY2xvdWQvY2xpZW50LXdlYi9hY2NvdW50I2NyZWF0ZVNlc3Npb24pIGVuZHBvaW50LlxuICAgICAqXG4gICAgICogQSB1c2VyIGlzIGxpbWl0ZWQgdG8gMTAgYWN0aXZlIHNlc3Npb25zIGF0IGEgdGltZSBieSBkZWZhdWx0LiBbTGVhcm4gbW9yZSBhYm91dCBzZXNzaW9uIGxpbWl0c10oaHR0cHM6Ly9hcHB3cml0ZS5pby9kb2NzL2F1dGhlbnRpY2F0aW9uLXNlY3VyaXR5I2xpbWl0cykuXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge09BdXRoUHJvdmlkZXJ9IHByb3ZpZGVyXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IHN1Y2Nlc3NcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gZmFpbHVyZVxuICAgICAqIEBwYXJhbSB7c3RyaW5nW119IHNjb3Blc1xuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHt2b2lkIHwgc3RyaW5nfVxuICAgICAqL1xuICAgIGNyZWF0ZU9BdXRoMlRva2VuKHByb3ZpZGVyLCBzdWNjZXNzLCBmYWlsdXJlLCBzY29wZXMpIHtcbiAgICAgICAgaWYgKHR5cGVvZiBwcm92aWRlciA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwicHJvdmlkZXJcIicpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2FjY291bnQvdG9rZW5zL29hdXRoMi97cHJvdmlkZXJ9Jy5yZXBsYWNlKCd7cHJvdmlkZXJ9JywgcHJvdmlkZXIpO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGlmICh0eXBlb2Ygc3VjY2VzcyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3N1Y2Nlc3MnXSA9IHN1Y2Nlc3M7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBmYWlsdXJlICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsnZmFpbHVyZSddID0gZmFpbHVyZTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIHNjb3BlcyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3Njb3BlcyddID0gc2NvcGVzO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIHBheWxvYWRbJ3Byb2plY3QnXSA9IHRoaXMuY2xpZW50LmNvbmZpZy5wcm9qZWN0O1xuICAgICAgICBmb3IgKGNvbnN0IFtrZXksIHZhbHVlXSBvZiBPYmplY3QuZW50cmllcyhTZXJ2aWNlLmZsYXR0ZW4ocGF5bG9hZCkpKSB7XG4gICAgICAgICAgICB1cmkuc2VhcmNoUGFyYW1zLmFwcGVuZChrZXksIHZhbHVlKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcgJiYgKHdpbmRvdyA9PT0gbnVsbCB8fCB3aW5kb3cgPT09IHZvaWQgMCA/IHZvaWQgMCA6IHdpbmRvdy5sb2NhdGlvbikpIHtcbiAgICAgICAgICAgIHdpbmRvdy5sb2NhdGlvbi5ocmVmID0gdXJpLnRvU3RyaW5nKCk7XG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICByZXR1cm4gdXJpLnRvU3RyaW5nKCk7XG4gICAgICAgIH1cbiAgICB9XG4gICAgLyoqXG4gICAgICogU2VuZHMgdGhlIHVzZXIgYW4gU01TIHdpdGggYSBzZWNyZXQga2V5IGZvciBjcmVhdGluZyBhIHNlc3Npb24uIElmIHRoZSBwcm92aWRlZCB1c2VyIElEIGhhcyBub3QgYmUgcmVnaXN0ZXJlZCwgYSBuZXcgdXNlciB3aWxsIGJlIGNyZWF0ZWQuIFVzZSB0aGUgcmV0dXJuZWQgdXNlciBJRCBhbmQgc2VjcmV0IGFuZCBzdWJtaXQgYSByZXF1ZXN0IHRvIHRoZSBbUE9TVCAvdjEvYWNjb3VudC9zZXNzaW9ucy90b2tlbl0oaHR0cHM6Ly9hcHB3cml0ZS5pby9kb2NzL3JlZmVyZW5jZXMvY2xvdWQvY2xpZW50LXdlYi9hY2NvdW50I2NyZWF0ZVNlc3Npb24pIGVuZHBvaW50IHRvIGNvbXBsZXRlIHRoZSBsb2dpbiBwcm9jZXNzLiBUaGUgc2VjcmV0IHNlbnQgdG8gdGhlIHVzZXImIzAzOTtzIHBob25lIGlzIHZhbGlkIGZvciAxNSBtaW51dGVzLlxuICAgICAqXG4gICAgICogQSB1c2VyIGlzIGxpbWl0ZWQgdG8gMTAgYWN0aXZlIHNlc3Npb25zIGF0IGEgdGltZSBieSBkZWZhdWx0LiBbTGVhcm4gbW9yZSBhYm91dCBzZXNzaW9uIGxpbWl0c10oaHR0cHM6Ly9hcHB3cml0ZS5pby9kb2NzL2F1dGhlbnRpY2F0aW9uLXNlY3VyaXR5I2xpbWl0cykuXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gdXNlcklkXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IHBob25lXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8TW9kZWxzLlRva2VuPn1cbiAgICAgKi9cbiAgICBjcmVhdGVQaG9uZVRva2VuKHVzZXJJZCwgcGhvbmUpIHtcbiAgICAgICAgaWYgKHR5cGVvZiB1c2VySWQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcInVzZXJJZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBwaG9uZSA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwicGhvbmVcIicpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2FjY291bnQvdG9rZW5zL3Bob25lJztcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBpZiAodHlwZW9mIHVzZXJJZCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3VzZXJJZCddID0gdXNlcklkO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgcGhvbmUgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydwaG9uZSddID0gcGhvbmU7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgY29uc3QgYXBpSGVhZGVycyA9IHtcbiAgICAgICAgICAgICdjb250ZW50LXR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgIH07XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdwb3N0JywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogVXNlIHRoaXMgZW5kcG9pbnQgdG8gc2VuZCBhIHZlcmlmaWNhdGlvbiBtZXNzYWdlIHRvIHlvdXIgdXNlciBlbWFpbCBhZGRyZXNzIHRvIGNvbmZpcm0gdGhleSBhcmUgdGhlIHZhbGlkIG93bmVycyBvZiB0aGF0IGFkZHJlc3MuIEJvdGggdGhlICoqdXNlcklkKiogYW5kICoqc2VjcmV0KiogYXJndW1lbnRzIHdpbGwgYmUgcGFzc2VkIGFzIHF1ZXJ5IHBhcmFtZXRlcnMgdG8gdGhlIFVSTCB5b3UgaGF2ZSBwcm92aWRlZCB0byBiZSBhdHRhY2hlZCB0byB0aGUgdmVyaWZpY2F0aW9uIGVtYWlsLiBUaGUgcHJvdmlkZWQgVVJMIHNob3VsZCByZWRpcmVjdCB0aGUgdXNlciBiYWNrIHRvIHlvdXIgYXBwIGFuZCBhbGxvdyB5b3UgdG8gY29tcGxldGUgdGhlIHZlcmlmaWNhdGlvbiBwcm9jZXNzIGJ5IHZlcmlmeWluZyBib3RoIHRoZSAqKnVzZXJJZCoqIGFuZCAqKnNlY3JldCoqIHBhcmFtZXRlcnMuIExlYXJuIG1vcmUgYWJvdXQgaG93IHRvIFtjb21wbGV0ZSB0aGUgdmVyaWZpY2F0aW9uIHByb2Nlc3NdKGh0dHBzOi8vYXBwd3JpdGUuaW8vZG9jcy9yZWZlcmVuY2VzL2Nsb3VkL2NsaWVudC13ZWIvYWNjb3VudCN1cGRhdGVWZXJpZmljYXRpb24pLiBUaGUgdmVyaWZpY2F0aW9uIGxpbmsgc2VudCB0byB0aGUgdXNlciYjMDM5O3MgZW1haWwgYWRkcmVzcyBpcyB2YWxpZCBmb3IgNyBkYXlzLlxuICAgICAqXG4gICAgICogUGxlYXNlIG5vdGUgdGhhdCBpbiBvcmRlciB0byBhdm9pZCBhIFtSZWRpcmVjdCBBdHRhY2tdKGh0dHBzOi8vZ2l0aHViLmNvbS9PV0FTUC9DaGVhdFNoZWV0U2VyaWVzL2Jsb2IvbWFzdGVyL2NoZWF0c2hlZXRzL1VudmFsaWRhdGVkX1JlZGlyZWN0c19hbmRfRm9yd2FyZHNfQ2hlYXRfU2hlZXQubWQpLCB0aGUgb25seSB2YWxpZCByZWRpcmVjdCBVUkxzIGFyZSB0aGUgb25lcyBmcm9tIGRvbWFpbnMgeW91IGhhdmUgc2V0IHdoZW4gYWRkaW5nIHlvdXIgcGxhdGZvcm1zIGluIHRoZSBjb25zb2xlIGludGVyZmFjZS5cbiAgICAgKlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IHVybFxuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPE1vZGVscy5Ub2tlbj59XG4gICAgICovXG4gICAgY3JlYXRlVmVyaWZpY2F0aW9uKHVybCkge1xuICAgICAgICBpZiAodHlwZW9mIHVybCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwidXJsXCInKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy9hY2NvdW50L3ZlcmlmaWNhdGlvbic7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgaWYgKHR5cGVvZiB1cmwgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWyd1cmwnXSA9IHVybDtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge1xuICAgICAgICAgICAgJ2NvbnRlbnQtdHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ3Bvc3QnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBVc2UgdGhpcyBlbmRwb2ludCB0byBjb21wbGV0ZSB0aGUgdXNlciBlbWFpbCB2ZXJpZmljYXRpb24gcHJvY2Vzcy4gVXNlIGJvdGggdGhlICoqdXNlcklkKiogYW5kICoqc2VjcmV0KiogcGFyYW1ldGVycyB0aGF0IHdlcmUgYXR0YWNoZWQgdG8geW91ciBhcHAgVVJMIHRvIHZlcmlmeSB0aGUgdXNlciBlbWFpbCBvd25lcnNoaXAuIElmIGNvbmZpcm1lZCB0aGlzIHJvdXRlIHdpbGwgcmV0dXJuIGEgMjAwIHN0YXR1cyBjb2RlLlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IHVzZXJJZFxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBzZWNyZXRcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTxNb2RlbHMuVG9rZW4+fVxuICAgICAqL1xuICAgIHVwZGF0ZVZlcmlmaWNhdGlvbih1c2VySWQsIHNlY3JldCkge1xuICAgICAgICBpZiAodHlwZW9mIHVzZXJJZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwidXNlcklkXCInKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIHNlY3JldCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwic2VjcmV0XCInKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy9hY2NvdW50L3ZlcmlmaWNhdGlvbic7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgaWYgKHR5cGVvZiB1c2VySWQgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWyd1c2VySWQnXSA9IHVzZXJJZDtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIHNlY3JldCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3NlY3JldCddID0gc2VjcmV0O1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7XG4gICAgICAgICAgICAnY29udGVudC10eXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgncHV0JywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogVXNlIHRoaXMgZW5kcG9pbnQgdG8gc2VuZCBhIHZlcmlmaWNhdGlvbiBTTVMgdG8gdGhlIGN1cnJlbnRseSBsb2dnZWQgaW4gdXNlci4gVGhpcyBlbmRwb2ludCBpcyBtZWFudCBmb3IgdXNlIGFmdGVyIHVwZGF0aW5nIGEgdXNlciYjMDM5O3MgcGhvbmUgbnVtYmVyIHVzaW5nIHRoZSBbYWNjb3VudFVwZGF0ZVBob25lXShodHRwczovL2FwcHdyaXRlLmlvL2RvY3MvcmVmZXJlbmNlcy9jbG91ZC9jbGllbnQtd2ViL2FjY291bnQjdXBkYXRlUGhvbmUpIGVuZHBvaW50LiBMZWFybiBtb3JlIGFib3V0IGhvdyB0byBbY29tcGxldGUgdGhlIHZlcmlmaWNhdGlvbiBwcm9jZXNzXShodHRwczovL2FwcHdyaXRlLmlvL2RvY3MvcmVmZXJlbmNlcy9jbG91ZC9jbGllbnQtd2ViL2FjY291bnQjdXBkYXRlUGhvbmVWZXJpZmljYXRpb24pLiBUaGUgdmVyaWZpY2F0aW9uIGNvZGUgc2VudCB0byB0aGUgdXNlciYjMDM5O3MgcGhvbmUgbnVtYmVyIGlzIHZhbGlkIGZvciAxNSBtaW51dGVzLlxuICAgICAqXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8TW9kZWxzLlRva2VuPn1cbiAgICAgKi9cbiAgICBjcmVhdGVQaG9uZVZlcmlmaWNhdGlvbigpIHtcbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvYWNjb3VudC92ZXJpZmljYXRpb24vcGhvbmUnO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7XG4gICAgICAgICAgICAnY29udGVudC10eXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgncG9zdCcsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFVzZSB0aGlzIGVuZHBvaW50IHRvIGNvbXBsZXRlIHRoZSB1c2VyIHBob25lIHZlcmlmaWNhdGlvbiBwcm9jZXNzLiBVc2UgdGhlICoqdXNlcklkKiogYW5kICoqc2VjcmV0KiogdGhhdCB3ZXJlIHNlbnQgdG8geW91ciB1c2VyJiMwMzk7cyBwaG9uZSBudW1iZXIgdG8gdmVyaWZ5IHRoZSB1c2VyIGVtYWlsIG93bmVyc2hpcC4gSWYgY29uZmlybWVkIHRoaXMgcm91dGUgd2lsbCByZXR1cm4gYSAyMDAgc3RhdHVzIGNvZGUuXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gdXNlcklkXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IHNlY3JldFxuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPE1vZGVscy5Ub2tlbj59XG4gICAgICovXG4gICAgdXBkYXRlUGhvbmVWZXJpZmljYXRpb24odXNlcklkLCBzZWNyZXQpIHtcbiAgICAgICAgaWYgKHR5cGVvZiB1c2VySWQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcInVzZXJJZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBzZWNyZXQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcInNlY3JldFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvYWNjb3VudC92ZXJpZmljYXRpb24vcGhvbmUnO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGlmICh0eXBlb2YgdXNlcklkICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsndXNlcklkJ10gPSB1c2VySWQ7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBzZWNyZXQgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydzZWNyZXQnXSA9IHNlY3JldDtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge1xuICAgICAgICAgICAgJ2NvbnRlbnQtdHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ3B1dCcsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxufVxuXG5jbGFzcyBBdmF0YXJzIHtcbiAgICBjb25zdHJ1Y3RvcihjbGllbnQpIHtcbiAgICAgICAgdGhpcy5jbGllbnQgPSBjbGllbnQ7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFlvdSBjYW4gdXNlIHRoaXMgZW5kcG9pbnQgdG8gc2hvdyBkaWZmZXJlbnQgYnJvd3NlciBpY29ucyB0byB5b3VyIHVzZXJzLiBUaGUgY29kZSBhcmd1bWVudCByZWNlaXZlcyB0aGUgYnJvd3NlciBjb2RlIGFzIGl0IGFwcGVhcnMgaW4geW91ciB1c2VyIFtHRVQgL2FjY291bnQvc2Vzc2lvbnNdKGh0dHBzOi8vYXBwd3JpdGUuaW8vZG9jcy9yZWZlcmVuY2VzL2Nsb3VkL2NsaWVudC13ZWIvYWNjb3VudCNnZXRTZXNzaW9ucykgZW5kcG9pbnQuIFVzZSB3aWR0aCwgaGVpZ2h0IGFuZCBxdWFsaXR5IGFyZ3VtZW50cyB0byBjaGFuZ2UgdGhlIG91dHB1dCBzZXR0aW5ncy5cbiAgICAgKlxuICAgICAqIFdoZW4gb25lIGRpbWVuc2lvbiBpcyBzcGVjaWZpZWQgYW5kIHRoZSBvdGhlciBpcyAwLCB0aGUgaW1hZ2UgaXMgc2NhbGVkIHdpdGggcHJlc2VydmVkIGFzcGVjdCByYXRpby4gSWYgYm90aCBkaW1lbnNpb25zIGFyZSAwLCB0aGUgQVBJIHByb3ZpZGVzIGFuIGltYWdlIGF0IHNvdXJjZSBxdWFsaXR5LiBJZiBkaW1lbnNpb25zIGFyZSBub3Qgc3BlY2lmaWVkLCB0aGUgZGVmYXVsdCBzaXplIG9mIGltYWdlIHJldHVybmVkIGlzIDEwMHgxMDBweC5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7QnJvd3Nlcn0gY29kZVxuICAgICAqIEBwYXJhbSB7bnVtYmVyfSB3aWR0aFxuICAgICAqIEBwYXJhbSB7bnVtYmVyfSBoZWlnaHRcbiAgICAgKiBAcGFyYW0ge251bWJlcn0gcXVhbGl0eVxuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtzdHJpbmd9XG4gICAgICovXG4gICAgZ2V0QnJvd3Nlcihjb2RlLCB3aWR0aCwgaGVpZ2h0LCBxdWFsaXR5KSB7XG4gICAgICAgIGlmICh0eXBlb2YgY29kZSA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwiY29kZVwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvYXZhdGFycy9icm93c2Vycy97Y29kZX0nLnJlcGxhY2UoJ3tjb2RlfScsIGNvZGUpO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGlmICh0eXBlb2Ygd2lkdGggIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWyd3aWR0aCddID0gd2lkdGg7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBoZWlnaHQgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydoZWlnaHQnXSA9IGhlaWdodDtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIHF1YWxpdHkgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydxdWFsaXR5J10gPSBxdWFsaXR5O1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIHBheWxvYWRbJ3Byb2plY3QnXSA9IHRoaXMuY2xpZW50LmNvbmZpZy5wcm9qZWN0O1xuICAgICAgICBmb3IgKGNvbnN0IFtrZXksIHZhbHVlXSBvZiBPYmplY3QuZW50cmllcyhTZXJ2aWNlLmZsYXR0ZW4ocGF5bG9hZCkpKSB7XG4gICAgICAgICAgICB1cmkuc2VhcmNoUGFyYW1zLmFwcGVuZChrZXksIHZhbHVlKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gdXJpLnRvU3RyaW5nKCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFRoZSBjcmVkaXQgY2FyZCBlbmRwb2ludCB3aWxsIHJldHVybiB5b3UgdGhlIGljb24gb2YgdGhlIGNyZWRpdCBjYXJkIHByb3ZpZGVyIHlvdSBuZWVkLiBVc2Ugd2lkdGgsIGhlaWdodCBhbmQgcXVhbGl0eSBhcmd1bWVudHMgdG8gY2hhbmdlIHRoZSBvdXRwdXQgc2V0dGluZ3MuXG4gICAgICpcbiAgICAgKiBXaGVuIG9uZSBkaW1lbnNpb24gaXMgc3BlY2lmaWVkIGFuZCB0aGUgb3RoZXIgaXMgMCwgdGhlIGltYWdlIGlzIHNjYWxlZCB3aXRoIHByZXNlcnZlZCBhc3BlY3QgcmF0aW8uIElmIGJvdGggZGltZW5zaW9ucyBhcmUgMCwgdGhlIEFQSSBwcm92aWRlcyBhbiBpbWFnZSBhdCBzb3VyY2UgcXVhbGl0eS4gSWYgZGltZW5zaW9ucyBhcmUgbm90IHNwZWNpZmllZCwgdGhlIGRlZmF1bHQgc2l6ZSBvZiBpbWFnZSByZXR1cm5lZCBpcyAxMDB4MTAwcHguXG4gICAgICpcbiAgICAgKlxuICAgICAqIEBwYXJhbSB7Q3JlZGl0Q2FyZH0gY29kZVxuICAgICAqIEBwYXJhbSB7bnVtYmVyfSB3aWR0aFxuICAgICAqIEBwYXJhbSB7bnVtYmVyfSBoZWlnaHRcbiAgICAgKiBAcGFyYW0ge251bWJlcn0gcXVhbGl0eVxuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtzdHJpbmd9XG4gICAgICovXG4gICAgZ2V0Q3JlZGl0Q2FyZChjb2RlLCB3aWR0aCwgaGVpZ2h0LCBxdWFsaXR5KSB7XG4gICAgICAgIGlmICh0eXBlb2YgY29kZSA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwiY29kZVwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvYXZhdGFycy9jcmVkaXQtY2FyZHMve2NvZGV9Jy5yZXBsYWNlKCd7Y29kZX0nLCBjb2RlKTtcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBpZiAodHlwZW9mIHdpZHRoICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsnd2lkdGgnXSA9IHdpZHRoO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgaGVpZ2h0ICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsnaGVpZ2h0J10gPSBoZWlnaHQ7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBxdWFsaXR5ICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsncXVhbGl0eSddID0gcXVhbGl0eTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBwYXlsb2FkWydwcm9qZWN0J10gPSB0aGlzLmNsaWVudC5jb25maWcucHJvamVjdDtcbiAgICAgICAgZm9yIChjb25zdCBba2V5LCB2YWx1ZV0gb2YgT2JqZWN0LmVudHJpZXMoU2VydmljZS5mbGF0dGVuKHBheWxvYWQpKSkge1xuICAgICAgICAgICAgdXJpLnNlYXJjaFBhcmFtcy5hcHBlbmQoa2V5LCB2YWx1ZSk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHVyaS50b1N0cmluZygpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBVc2UgdGhpcyBlbmRwb2ludCB0byBmZXRjaCB0aGUgZmF2b3JpdGUgaWNvbiAoQUtBIGZhdmljb24pIG9mIGFueSByZW1vdGUgd2Vic2l0ZSBVUkwuXG4gICAgICpcbiAgICAgKiBUaGlzIGVuZHBvaW50IGRvZXMgbm90IGZvbGxvdyBIVFRQIHJlZGlyZWN0cy5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSB1cmxcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7c3RyaW5nfVxuICAgICAqL1xuICAgIGdldEZhdmljb24odXJsKSB7XG4gICAgICAgIGlmICh0eXBlb2YgdXJsID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJ1cmxcIicpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2F2YXRhcnMvZmF2aWNvbic7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgaWYgKHR5cGVvZiB1cmwgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWyd1cmwnXSA9IHVybDtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBwYXlsb2FkWydwcm9qZWN0J10gPSB0aGlzLmNsaWVudC5jb25maWcucHJvamVjdDtcbiAgICAgICAgZm9yIChjb25zdCBba2V5LCB2YWx1ZV0gb2YgT2JqZWN0LmVudHJpZXMoU2VydmljZS5mbGF0dGVuKHBheWxvYWQpKSkge1xuICAgICAgICAgICAgdXJpLnNlYXJjaFBhcmFtcy5hcHBlbmQoa2V5LCB2YWx1ZSk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHVyaS50b1N0cmluZygpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBZb3UgY2FuIHVzZSB0aGlzIGVuZHBvaW50IHRvIHNob3cgZGlmZmVyZW50IGNvdW50cnkgZmxhZ3MgaWNvbnMgdG8geW91ciB1c2Vycy4gVGhlIGNvZGUgYXJndW1lbnQgcmVjZWl2ZXMgdGhlIDIgbGV0dGVyIGNvdW50cnkgY29kZS4gVXNlIHdpZHRoLCBoZWlnaHQgYW5kIHF1YWxpdHkgYXJndW1lbnRzIHRvIGNoYW5nZSB0aGUgb3V0cHV0IHNldHRpbmdzLiBDb3VudHJ5IGNvZGVzIGZvbGxvdyB0aGUgW0lTTyAzMTY2LTFdKGh0dHBzOi8vZW4ud2lraXBlZGlhLm9yZy93aWtpL0lTT18zMTY2LTEpIHN0YW5kYXJkLlxuICAgICAqXG4gICAgICogV2hlbiBvbmUgZGltZW5zaW9uIGlzIHNwZWNpZmllZCBhbmQgdGhlIG90aGVyIGlzIDAsIHRoZSBpbWFnZSBpcyBzY2FsZWQgd2l0aCBwcmVzZXJ2ZWQgYXNwZWN0IHJhdGlvLiBJZiBib3RoIGRpbWVuc2lvbnMgYXJlIDAsIHRoZSBBUEkgcHJvdmlkZXMgYW4gaW1hZ2UgYXQgc291cmNlIHF1YWxpdHkuIElmIGRpbWVuc2lvbnMgYXJlIG5vdCBzcGVjaWZpZWQsIHRoZSBkZWZhdWx0IHNpemUgb2YgaW1hZ2UgcmV0dXJuZWQgaXMgMTAweDEwMHB4LlxuICAgICAqXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge0ZsYWd9IGNvZGVcbiAgICAgKiBAcGFyYW0ge251bWJlcn0gd2lkdGhcbiAgICAgKiBAcGFyYW0ge251bWJlcn0gaGVpZ2h0XG4gICAgICogQHBhcmFtIHtudW1iZXJ9IHF1YWxpdHlcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7c3RyaW5nfVxuICAgICAqL1xuICAgIGdldEZsYWcoY29kZSwgd2lkdGgsIGhlaWdodCwgcXVhbGl0eSkge1xuICAgICAgICBpZiAodHlwZW9mIGNvZGUgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcImNvZGVcIicpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2F2YXRhcnMvZmxhZ3Mve2NvZGV9Jy5yZXBsYWNlKCd7Y29kZX0nLCBjb2RlKTtcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBpZiAodHlwZW9mIHdpZHRoICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsnd2lkdGgnXSA9IHdpZHRoO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgaGVpZ2h0ICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsnaGVpZ2h0J10gPSBoZWlnaHQ7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBxdWFsaXR5ICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsncXVhbGl0eSddID0gcXVhbGl0eTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBwYXlsb2FkWydwcm9qZWN0J10gPSB0aGlzLmNsaWVudC5jb25maWcucHJvamVjdDtcbiAgICAgICAgZm9yIChjb25zdCBba2V5LCB2YWx1ZV0gb2YgT2JqZWN0LmVudHJpZXMoU2VydmljZS5mbGF0dGVuKHBheWxvYWQpKSkge1xuICAgICAgICAgICAgdXJpLnNlYXJjaFBhcmFtcy5hcHBlbmQoa2V5LCB2YWx1ZSk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHVyaS50b1N0cmluZygpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBVc2UgdGhpcyBlbmRwb2ludCB0byBmZXRjaCBhIHJlbW90ZSBpbWFnZSBVUkwgYW5kIGNyb3AgaXQgdG8gYW55IGltYWdlIHNpemUgeW91IHdhbnQuIFRoaXMgZW5kcG9pbnQgaXMgdmVyeSB1c2VmdWwgaWYgeW91IG5lZWQgdG8gY3JvcCBhbmQgZGlzcGxheSByZW1vdGUgaW1hZ2VzIGluIHlvdXIgYXBwIG9yIGluIGNhc2UgeW91IHdhbnQgdG8gbWFrZSBzdXJlIGEgM3JkIHBhcnR5IGltYWdlIGlzIHByb3Blcmx5IHNlcnZlZCB1c2luZyBhIFRMUyBwcm90b2NvbC5cbiAgICAgKlxuICAgICAqIFdoZW4gb25lIGRpbWVuc2lvbiBpcyBzcGVjaWZpZWQgYW5kIHRoZSBvdGhlciBpcyAwLCB0aGUgaW1hZ2UgaXMgc2NhbGVkIHdpdGggcHJlc2VydmVkIGFzcGVjdCByYXRpby4gSWYgYm90aCBkaW1lbnNpb25zIGFyZSAwLCB0aGUgQVBJIHByb3ZpZGVzIGFuIGltYWdlIGF0IHNvdXJjZSBxdWFsaXR5LiBJZiBkaW1lbnNpb25zIGFyZSBub3Qgc3BlY2lmaWVkLCB0aGUgZGVmYXVsdCBzaXplIG9mIGltYWdlIHJldHVybmVkIGlzIDQwMHg0MDBweC5cbiAgICAgKlxuICAgICAqIFRoaXMgZW5kcG9pbnQgZG9lcyBub3QgZm9sbG93IEhUVFAgcmVkaXJlY3RzLlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IHVybFxuICAgICAqIEBwYXJhbSB7bnVtYmVyfSB3aWR0aFxuICAgICAqIEBwYXJhbSB7bnVtYmVyfSBoZWlnaHRcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7c3RyaW5nfVxuICAgICAqL1xuICAgIGdldEltYWdlKHVybCwgd2lkdGgsIGhlaWdodCkge1xuICAgICAgICBpZiAodHlwZW9mIHVybCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwidXJsXCInKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy9hdmF0YXJzL2ltYWdlJztcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBpZiAodHlwZW9mIHVybCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3VybCddID0gdXJsO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2Ygd2lkdGggIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWyd3aWR0aCddID0gd2lkdGg7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBoZWlnaHQgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydoZWlnaHQnXSA9IGhlaWdodDtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBwYXlsb2FkWydwcm9qZWN0J10gPSB0aGlzLmNsaWVudC5jb25maWcucHJvamVjdDtcbiAgICAgICAgZm9yIChjb25zdCBba2V5LCB2YWx1ZV0gb2YgT2JqZWN0LmVudHJpZXMoU2VydmljZS5mbGF0dGVuKHBheWxvYWQpKSkge1xuICAgICAgICAgICAgdXJpLnNlYXJjaFBhcmFtcy5hcHBlbmQoa2V5LCB2YWx1ZSk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHVyaS50b1N0cmluZygpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBVc2UgdGhpcyBlbmRwb2ludCB0byBzaG93IHlvdXIgdXNlciBpbml0aWFscyBhdmF0YXIgaWNvbiBvbiB5b3VyIHdlYnNpdGUgb3IgYXBwLiBCeSBkZWZhdWx0LCB0aGlzIHJvdXRlIHdpbGwgdHJ5IHRvIHByaW50IHlvdXIgbG9nZ2VkLWluIHVzZXIgbmFtZSBvciBlbWFpbCBpbml0aWFscy4gWW91IGNhbiBhbHNvIG92ZXJ3cml0ZSB0aGUgdXNlciBuYW1lIGlmIHlvdSBwYXNzIHRoZSAmIzAzOTtuYW1lJiMwMzk7IHBhcmFtZXRlci4gSWYgbm8gbmFtZSBpcyBnaXZlbiBhbmQgbm8gdXNlciBpcyBsb2dnZWQsIGFuIGVtcHR5IGF2YXRhciB3aWxsIGJlIHJldHVybmVkLlxuICAgICAqXG4gICAgICogWW91IGNhbiB1c2UgdGhlIGNvbG9yIGFuZCBiYWNrZ3JvdW5kIHBhcmFtcyB0byBjaGFuZ2UgdGhlIGF2YXRhciBjb2xvcnMuIEJ5IGRlZmF1bHQsIGEgcmFuZG9tIHRoZW1lIHdpbGwgYmUgc2VsZWN0ZWQuIFRoZSByYW5kb20gdGhlbWUgd2lsbCBwZXJzaXN0IGZvciB0aGUgdXNlciYjMDM5O3MgaW5pdGlhbHMgd2hlbiByZWxvYWRpbmcgdGhlIHNhbWUgdGhlbWUgd2lsbCBhbHdheXMgcmV0dXJuIGZvciB0aGUgc2FtZSBpbml0aWFscy5cbiAgICAgKlxuICAgICAqIFdoZW4gb25lIGRpbWVuc2lvbiBpcyBzcGVjaWZpZWQgYW5kIHRoZSBvdGhlciBpcyAwLCB0aGUgaW1hZ2UgaXMgc2NhbGVkIHdpdGggcHJlc2VydmVkIGFzcGVjdCByYXRpby4gSWYgYm90aCBkaW1lbnNpb25zIGFyZSAwLCB0aGUgQVBJIHByb3ZpZGVzIGFuIGltYWdlIGF0IHNvdXJjZSBxdWFsaXR5LiBJZiBkaW1lbnNpb25zIGFyZSBub3Qgc3BlY2lmaWVkLCB0aGUgZGVmYXVsdCBzaXplIG9mIGltYWdlIHJldHVybmVkIGlzIDEwMHgxMDBweC5cbiAgICAgKlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IG5hbWVcbiAgICAgKiBAcGFyYW0ge251bWJlcn0gd2lkdGhcbiAgICAgKiBAcGFyYW0ge251bWJlcn0gaGVpZ2h0XG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGJhY2tncm91bmRcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7c3RyaW5nfVxuICAgICAqL1xuICAgIGdldEluaXRpYWxzKG5hbWUsIHdpZHRoLCBoZWlnaHQsIGJhY2tncm91bmQpIHtcbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvYXZhdGFycy9pbml0aWFscyc7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgaWYgKHR5cGVvZiBuYW1lICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsnbmFtZSddID0gbmFtZTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIHdpZHRoICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsnd2lkdGgnXSA9IHdpZHRoO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgaGVpZ2h0ICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsnaGVpZ2h0J10gPSBoZWlnaHQ7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBiYWNrZ3JvdW5kICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsnYmFja2dyb3VuZCddID0gYmFja2dyb3VuZDtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBwYXlsb2FkWydwcm9qZWN0J10gPSB0aGlzLmNsaWVudC5jb25maWcucHJvamVjdDtcbiAgICAgICAgZm9yIChjb25zdCBba2V5LCB2YWx1ZV0gb2YgT2JqZWN0LmVudHJpZXMoU2VydmljZS5mbGF0dGVuKHBheWxvYWQpKSkge1xuICAgICAgICAgICAgdXJpLnNlYXJjaFBhcmFtcy5hcHBlbmQoa2V5LCB2YWx1ZSk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHVyaS50b1N0cmluZygpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBDb252ZXJ0cyBhIGdpdmVuIHBsYWluIHRleHQgdG8gYSBRUiBjb2RlIGltYWdlLiBZb3UgY2FuIHVzZSB0aGUgcXVlcnkgcGFyYW1ldGVycyB0byBjaGFuZ2UgdGhlIHNpemUgYW5kIHN0eWxlIG9mIHRoZSByZXN1bHRpbmcgaW1hZ2UuXG4gICAgICpcbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSB0ZXh0XG4gICAgICogQHBhcmFtIHtudW1iZXJ9IHNpemVcbiAgICAgKiBAcGFyYW0ge251bWJlcn0gbWFyZ2luXG4gICAgICogQHBhcmFtIHtib29sZWFufSBkb3dubG9hZFxuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtzdHJpbmd9XG4gICAgICovXG4gICAgZ2V0UVIodGV4dCwgc2l6ZSwgbWFyZ2luLCBkb3dubG9hZCkge1xuICAgICAgICBpZiAodHlwZW9mIHRleHQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcInRleHRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2F2YXRhcnMvcXInO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGlmICh0eXBlb2YgdGV4dCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3RleHQnXSA9IHRleHQ7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBzaXplICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsnc2l6ZSddID0gc2l6ZTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIG1hcmdpbiAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ21hcmdpbiddID0gbWFyZ2luO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgZG93bmxvYWQgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydkb3dubG9hZCddID0gZG93bmxvYWQ7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgcGF5bG9hZFsncHJvamVjdCddID0gdGhpcy5jbGllbnQuY29uZmlnLnByb2plY3Q7XG4gICAgICAgIGZvciAoY29uc3QgW2tleSwgdmFsdWVdIG9mIE9iamVjdC5lbnRyaWVzKFNlcnZpY2UuZmxhdHRlbihwYXlsb2FkKSkpIHtcbiAgICAgICAgICAgIHVyaS5zZWFyY2hQYXJhbXMuYXBwZW5kKGtleSwgdmFsdWUpO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiB1cmkudG9TdHJpbmcoKTtcbiAgICB9XG59XG5cbmNsYXNzIERhdGFiYXNlcyB7XG4gICAgY29uc3RydWN0b3IoY2xpZW50KSB7XG4gICAgICAgIHRoaXMuY2xpZW50ID0gY2xpZW50O1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBHZXQgYSBsaXN0IG9mIGFsbCB0aGUgdXNlciYjMDM5O3MgZG9jdW1lbnRzIGluIGEgZ2l2ZW4gY29sbGVjdGlvbi4gWW91IGNhbiB1c2UgdGhlIHF1ZXJ5IHBhcmFtcyB0byBmaWx0ZXIgeW91ciByZXN1bHRzLlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGRhdGFiYXNlSWRcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gY29sbGVjdGlvbklkXG4gICAgICogQHBhcmFtIHtzdHJpbmdbXX0gcXVlcmllc1xuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPE1vZGVscy5Eb2N1bWVudExpc3Q8RG9jdW1lbnQ+Pn1cbiAgICAgKi9cbiAgICBsaXN0RG9jdW1lbnRzKGRhdGFiYXNlSWQsIGNvbGxlY3Rpb25JZCwgcXVlcmllcykge1xuICAgICAgICBpZiAodHlwZW9mIGRhdGFiYXNlSWQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcImRhdGFiYXNlSWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgY29sbGVjdGlvbklkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJjb2xsZWN0aW9uSWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2RhdGFiYXNlcy97ZGF0YWJhc2VJZH0vY29sbGVjdGlvbnMve2NvbGxlY3Rpb25JZH0vZG9jdW1lbnRzJy5yZXBsYWNlKCd7ZGF0YWJhc2VJZH0nLCBkYXRhYmFzZUlkKS5yZXBsYWNlKCd7Y29sbGVjdGlvbklkfScsIGNvbGxlY3Rpb25JZCk7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgaWYgKHR5cGVvZiBxdWVyaWVzICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsncXVlcmllcyddID0gcXVlcmllcztcbiAgICAgICAgfVxuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge307XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdnZXQnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBDcmVhdGUgYSBuZXcgRG9jdW1lbnQuIEJlZm9yZSB1c2luZyB0aGlzIHJvdXRlLCB5b3Ugc2hvdWxkIGNyZWF0ZSBhIG5ldyBjb2xsZWN0aW9uIHJlc291cmNlIHVzaW5nIGVpdGhlciBhIFtzZXJ2ZXIgaW50ZWdyYXRpb25dKGh0dHBzOi8vYXBwd3JpdGUuaW8vZG9jcy9zZXJ2ZXIvZGF0YWJhc2VzI2RhdGFiYXNlc0NyZWF0ZUNvbGxlY3Rpb24pIEFQSSBvciBkaXJlY3RseSBmcm9tIHlvdXIgZGF0YWJhc2UgY29uc29sZS5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBkYXRhYmFzZUlkXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGNvbGxlY3Rpb25JZFxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBkb2N1bWVudElkXG4gICAgICogQHBhcmFtIHtEb2N1bWVudCBleHRlbmRzIE1vZGVscy5EZWZhdWx0RG9jdW1lbnQgPyBNb2RlbHMuRGF0YVdpdGhvdXREb2N1bWVudEtleXMgOiBPbWl0PERvY3VtZW50LCBrZXlvZiBNb2RlbHMuRG9jdW1lbnQ+fSBkYXRhXG4gICAgICogQHBhcmFtIHtzdHJpbmdbXX0gcGVybWlzc2lvbnNcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTxEb2N1bWVudD59XG4gICAgICovXG4gICAgY3JlYXRlRG9jdW1lbnQoZGF0YWJhc2VJZCwgY29sbGVjdGlvbklkLCBkb2N1bWVudElkLCBkYXRhLCBwZXJtaXNzaW9ucykge1xuICAgICAgICBpZiAodHlwZW9mIGRhdGFiYXNlSWQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcImRhdGFiYXNlSWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgY29sbGVjdGlvbklkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJjb2xsZWN0aW9uSWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgZG9jdW1lbnRJZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwiZG9jdW1lbnRJZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBkYXRhID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJkYXRhXCInKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy9kYXRhYmFzZXMve2RhdGFiYXNlSWR9L2NvbGxlY3Rpb25zL3tjb2xsZWN0aW9uSWR9L2RvY3VtZW50cycucmVwbGFjZSgne2RhdGFiYXNlSWR9JywgZGF0YWJhc2VJZCkucmVwbGFjZSgne2NvbGxlY3Rpb25JZH0nLCBjb2xsZWN0aW9uSWQpO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGlmICh0eXBlb2YgZG9jdW1lbnRJZCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ2RvY3VtZW50SWQnXSA9IGRvY3VtZW50SWQ7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBkYXRhICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsnZGF0YSddID0gZGF0YTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIHBlcm1pc3Npb25zICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsncGVybWlzc2lvbnMnXSA9IHBlcm1pc3Npb25zO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7XG4gICAgICAgICAgICAnY29udGVudC10eXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgncG9zdCcsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIEdldCBhIGRvY3VtZW50IGJ5IGl0cyB1bmlxdWUgSUQuIFRoaXMgZW5kcG9pbnQgcmVzcG9uc2UgcmV0dXJucyBhIEpTT04gb2JqZWN0IHdpdGggdGhlIGRvY3VtZW50IGRhdGEuXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gZGF0YWJhc2VJZFxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBjb2xsZWN0aW9uSWRcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gZG9jdW1lbnRJZFxuICAgICAqIEBwYXJhbSB7c3RyaW5nW119IHF1ZXJpZXNcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTxEb2N1bWVudD59XG4gICAgICovXG4gICAgZ2V0RG9jdW1lbnQoZGF0YWJhc2VJZCwgY29sbGVjdGlvbklkLCBkb2N1bWVudElkLCBxdWVyaWVzKSB7XG4gICAgICAgIGlmICh0eXBlb2YgZGF0YWJhc2VJZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwiZGF0YWJhc2VJZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBjb2xsZWN0aW9uSWQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcImNvbGxlY3Rpb25JZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBkb2N1bWVudElkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJkb2N1bWVudElkXCInKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy9kYXRhYmFzZXMve2RhdGFiYXNlSWR9L2NvbGxlY3Rpb25zL3tjb2xsZWN0aW9uSWR9L2RvY3VtZW50cy97ZG9jdW1lbnRJZH0nLnJlcGxhY2UoJ3tkYXRhYmFzZUlkfScsIGRhdGFiYXNlSWQpLnJlcGxhY2UoJ3tjb2xsZWN0aW9uSWR9JywgY29sbGVjdGlvbklkKS5yZXBsYWNlKCd7ZG9jdW1lbnRJZH0nLCBkb2N1bWVudElkKTtcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBpZiAodHlwZW9mIHF1ZXJpZXMgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydxdWVyaWVzJ10gPSBxdWVyaWVzO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7fTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ2dldCcsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqICoqV0FSTklORzogRXhwZXJpbWVudGFsIEZlYXR1cmUqKiAtIFRoaXMgZW5kcG9pbnQgaXMgZXhwZXJpbWVudGFsIGFuZCBub3QgeWV0IG9mZmljaWFsbHkgc3VwcG9ydGVkLiBJdCBtYXkgYmUgc3ViamVjdCB0byBicmVha2luZyBjaGFuZ2VzIG9yIHJlbW92YWwgaW4gZnV0dXJlIHZlcnNpb25zLlxuICAgICAqXG4gICAgICogQ3JlYXRlIG9yIHVwZGF0ZSBhIERvY3VtZW50LiBCZWZvcmUgdXNpbmcgdGhpcyByb3V0ZSwgeW91IHNob3VsZCBjcmVhdGUgYSBuZXcgY29sbGVjdGlvbiByZXNvdXJjZSB1c2luZyBlaXRoZXIgYSBbc2VydmVyIGludGVncmF0aW9uXShodHRwczovL2FwcHdyaXRlLmlvL2RvY3Mvc2VydmVyL2RhdGFiYXNlcyNkYXRhYmFzZXNDcmVhdGVDb2xsZWN0aW9uKSBBUEkgb3IgZGlyZWN0bHkgZnJvbSB5b3VyIGRhdGFiYXNlIGNvbnNvbGUuXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gZGF0YWJhc2VJZFxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBjb2xsZWN0aW9uSWRcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gZG9jdW1lbnRJZFxuICAgICAqIEBwYXJhbSB7b2JqZWN0fSBkYXRhXG4gICAgICogQHBhcmFtIHtzdHJpbmdbXX0gcGVybWlzc2lvbnNcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTxEb2N1bWVudD59XG4gICAgICovXG4gICAgdXBzZXJ0RG9jdW1lbnQoZGF0YWJhc2VJZCwgY29sbGVjdGlvbklkLCBkb2N1bWVudElkLCBkYXRhLCBwZXJtaXNzaW9ucykge1xuICAgICAgICBpZiAodHlwZW9mIGRhdGFiYXNlSWQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcImRhdGFiYXNlSWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgY29sbGVjdGlvbklkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJjb2xsZWN0aW9uSWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgZG9jdW1lbnRJZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwiZG9jdW1lbnRJZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBkYXRhID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJkYXRhXCInKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy9kYXRhYmFzZXMve2RhdGFiYXNlSWR9L2NvbGxlY3Rpb25zL3tjb2xsZWN0aW9uSWR9L2RvY3VtZW50cy97ZG9jdW1lbnRJZH0nLnJlcGxhY2UoJ3tkYXRhYmFzZUlkfScsIGRhdGFiYXNlSWQpLnJlcGxhY2UoJ3tjb2xsZWN0aW9uSWR9JywgY29sbGVjdGlvbklkKS5yZXBsYWNlKCd7ZG9jdW1lbnRJZH0nLCBkb2N1bWVudElkKTtcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBpZiAodHlwZW9mIGRhdGEgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydkYXRhJ10gPSBkYXRhO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgcGVybWlzc2lvbnMgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydwZXJtaXNzaW9ucyddID0gcGVybWlzc2lvbnM7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgY29uc3QgYXBpSGVhZGVycyA9IHtcbiAgICAgICAgICAgICdjb250ZW50LXR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgIH07XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdwdXQnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBVcGRhdGUgYSBkb2N1bWVudCBieSBpdHMgdW5pcXVlIElELiBVc2luZyB0aGUgcGF0Y2ggbWV0aG9kIHlvdSBjYW4gcGFzcyBvbmx5IHNwZWNpZmljIGZpZWxkcyB0aGF0IHdpbGwgZ2V0IHVwZGF0ZWQuXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gZGF0YWJhc2VJZFxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBjb2xsZWN0aW9uSWRcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gZG9jdW1lbnRJZFxuICAgICAqIEBwYXJhbSB7UGFydGlhbDxEb2N1bWVudCBleHRlbmRzIE1vZGVscy5EZWZhdWx0RG9jdW1lbnQgPyBNb2RlbHMuRGF0YVdpdGhvdXREb2N1bWVudEtleXMgOiBPbWl0PERvY3VtZW50LCBrZXlvZiBNb2RlbHMuRG9jdW1lbnQ+Pn0gZGF0YVxuICAgICAqIEBwYXJhbSB7c3RyaW5nW119IHBlcm1pc3Npb25zXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8RG9jdW1lbnQ+fVxuICAgICAqL1xuICAgIHVwZGF0ZURvY3VtZW50KGRhdGFiYXNlSWQsIGNvbGxlY3Rpb25JZCwgZG9jdW1lbnRJZCwgZGF0YSwgcGVybWlzc2lvbnMpIHtcbiAgICAgICAgaWYgKHR5cGVvZiBkYXRhYmFzZUlkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJkYXRhYmFzZUlkXCInKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIGNvbGxlY3Rpb25JZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwiY29sbGVjdGlvbklkXCInKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIGRvY3VtZW50SWQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcImRvY3VtZW50SWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2RhdGFiYXNlcy97ZGF0YWJhc2VJZH0vY29sbGVjdGlvbnMve2NvbGxlY3Rpb25JZH0vZG9jdW1lbnRzL3tkb2N1bWVudElkfScucmVwbGFjZSgne2RhdGFiYXNlSWR9JywgZGF0YWJhc2VJZCkucmVwbGFjZSgne2NvbGxlY3Rpb25JZH0nLCBjb2xsZWN0aW9uSWQpLnJlcGxhY2UoJ3tkb2N1bWVudElkfScsIGRvY3VtZW50SWQpO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGlmICh0eXBlb2YgZGF0YSAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ2RhdGEnXSA9IGRhdGE7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBwZXJtaXNzaW9ucyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3Blcm1pc3Npb25zJ10gPSBwZXJtaXNzaW9ucztcbiAgICAgICAgfVxuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge1xuICAgICAgICAgICAgJ2NvbnRlbnQtdHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ3BhdGNoJywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogRGVsZXRlIGEgZG9jdW1lbnQgYnkgaXRzIHVuaXF1ZSBJRC5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBkYXRhYmFzZUlkXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGNvbGxlY3Rpb25JZFxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBkb2N1bWVudElkXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8e30+fVxuICAgICAqL1xuICAgIGRlbGV0ZURvY3VtZW50KGRhdGFiYXNlSWQsIGNvbGxlY3Rpb25JZCwgZG9jdW1lbnRJZCkge1xuICAgICAgICBpZiAodHlwZW9mIGRhdGFiYXNlSWQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcImRhdGFiYXNlSWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgY29sbGVjdGlvbklkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJjb2xsZWN0aW9uSWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgZG9jdW1lbnRJZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwiZG9jdW1lbnRJZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvZGF0YWJhc2VzL3tkYXRhYmFzZUlkfS9jb2xsZWN0aW9ucy97Y29sbGVjdGlvbklkfS9kb2N1bWVudHMve2RvY3VtZW50SWR9Jy5yZXBsYWNlKCd7ZGF0YWJhc2VJZH0nLCBkYXRhYmFzZUlkKS5yZXBsYWNlKCd7Y29sbGVjdGlvbklkfScsIGNvbGxlY3Rpb25JZCkucmVwbGFjZSgne2RvY3VtZW50SWR9JywgZG9jdW1lbnRJZCk7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgY29uc3QgYXBpSGVhZGVycyA9IHtcbiAgICAgICAgICAgICdjb250ZW50LXR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgIH07XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdkZWxldGUnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBEZWNyZW1lbnQgYSBzcGVjaWZpYyBhdHRyaWJ1dGUgb2YgYSBkb2N1bWVudCBieSBhIGdpdmVuIHZhbHVlLlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGRhdGFiYXNlSWRcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gY29sbGVjdGlvbklkXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGRvY3VtZW50SWRcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gYXR0cmlidXRlXG4gICAgICogQHBhcmFtIHtudW1iZXJ9IHZhbHVlXG4gICAgICogQHBhcmFtIHtudW1iZXJ9IG1pblxuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPERvY3VtZW50Pn1cbiAgICAgKi9cbiAgICBkZWNyZW1lbnREb2N1bWVudEF0dHJpYnV0ZShkYXRhYmFzZUlkLCBjb2xsZWN0aW9uSWQsIGRvY3VtZW50SWQsIGF0dHJpYnV0ZSwgdmFsdWUsIG1pbikge1xuICAgICAgICBpZiAodHlwZW9mIGRhdGFiYXNlSWQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcImRhdGFiYXNlSWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgY29sbGVjdGlvbklkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJjb2xsZWN0aW9uSWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgZG9jdW1lbnRJZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwiZG9jdW1lbnRJZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBhdHRyaWJ1dGUgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcImF0dHJpYnV0ZVwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvZGF0YWJhc2VzL3tkYXRhYmFzZUlkfS9jb2xsZWN0aW9ucy97Y29sbGVjdGlvbklkfS9kb2N1bWVudHMve2RvY3VtZW50SWR9L3thdHRyaWJ1dGV9L2RlY3JlbWVudCcucmVwbGFjZSgne2RhdGFiYXNlSWR9JywgZGF0YWJhc2VJZCkucmVwbGFjZSgne2NvbGxlY3Rpb25JZH0nLCBjb2xsZWN0aW9uSWQpLnJlcGxhY2UoJ3tkb2N1bWVudElkfScsIGRvY3VtZW50SWQpLnJlcGxhY2UoJ3thdHRyaWJ1dGV9JywgYXR0cmlidXRlKTtcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBpZiAodHlwZW9mIHZhbHVlICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsndmFsdWUnXSA9IHZhbHVlO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgbWluICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsnbWluJ10gPSBtaW47XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgY29uc3QgYXBpSGVhZGVycyA9IHtcbiAgICAgICAgICAgICdjb250ZW50LXR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgIH07XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdwYXRjaCcsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIEluY3JlbWVudCBhIHNwZWNpZmljIGF0dHJpYnV0ZSBvZiBhIGRvY3VtZW50IGJ5IGEgZ2l2ZW4gdmFsdWUuXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gZGF0YWJhc2VJZFxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBjb2xsZWN0aW9uSWRcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gZG9jdW1lbnRJZFxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBhdHRyaWJ1dGVcbiAgICAgKiBAcGFyYW0ge251bWJlcn0gdmFsdWVcbiAgICAgKiBAcGFyYW0ge251bWJlcn0gbWF4XG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8RG9jdW1lbnQ+fVxuICAgICAqL1xuICAgIGluY3JlbWVudERvY3VtZW50QXR0cmlidXRlKGRhdGFiYXNlSWQsIGNvbGxlY3Rpb25JZCwgZG9jdW1lbnRJZCwgYXR0cmlidXRlLCB2YWx1ZSwgbWF4KSB7XG4gICAgICAgIGlmICh0eXBlb2YgZGF0YWJhc2VJZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwiZGF0YWJhc2VJZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBjb2xsZWN0aW9uSWQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcImNvbGxlY3Rpb25JZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBkb2N1bWVudElkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJkb2N1bWVudElkXCInKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIGF0dHJpYnV0ZSA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwiYXR0cmlidXRlXCInKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy9kYXRhYmFzZXMve2RhdGFiYXNlSWR9L2NvbGxlY3Rpb25zL3tjb2xsZWN0aW9uSWR9L2RvY3VtZW50cy97ZG9jdW1lbnRJZH0ve2F0dHJpYnV0ZX0vaW5jcmVtZW50Jy5yZXBsYWNlKCd7ZGF0YWJhc2VJZH0nLCBkYXRhYmFzZUlkKS5yZXBsYWNlKCd7Y29sbGVjdGlvbklkfScsIGNvbGxlY3Rpb25JZCkucmVwbGFjZSgne2RvY3VtZW50SWR9JywgZG9jdW1lbnRJZCkucmVwbGFjZSgne2F0dHJpYnV0ZX0nLCBhdHRyaWJ1dGUpO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGlmICh0eXBlb2YgdmFsdWUgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWyd2YWx1ZSddID0gdmFsdWU7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBtYXggIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydtYXgnXSA9IG1heDtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge1xuICAgICAgICAgICAgJ2NvbnRlbnQtdHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ3BhdGNoJywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG59XG5cbmNsYXNzIEZ1bmN0aW9ucyB7XG4gICAgY29uc3RydWN0b3IoY2xpZW50KSB7XG4gICAgICAgIHRoaXMuY2xpZW50ID0gY2xpZW50O1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBHZXQgYSBsaXN0IG9mIGFsbCB0aGUgY3VycmVudCB1c2VyIGZ1bmN0aW9uIGV4ZWN1dGlvbiBsb2dzLiBZb3UgY2FuIHVzZSB0aGUgcXVlcnkgcGFyYW1zIHRvIGZpbHRlciB5b3VyIHJlc3VsdHMuXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gZnVuY3Rpb25JZFxuICAgICAqIEBwYXJhbSB7c3RyaW5nW119IHF1ZXJpZXNcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTxNb2RlbHMuRXhlY3V0aW9uTGlzdD59XG4gICAgICovXG4gICAgbGlzdEV4ZWN1dGlvbnMoZnVuY3Rpb25JZCwgcXVlcmllcykge1xuICAgICAgICBpZiAodHlwZW9mIGZ1bmN0aW9uSWQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcImZ1bmN0aW9uSWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2Z1bmN0aW9ucy97ZnVuY3Rpb25JZH0vZXhlY3V0aW9ucycucmVwbGFjZSgne2Z1bmN0aW9uSWR9JywgZnVuY3Rpb25JZCk7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgaWYgKHR5cGVvZiBxdWVyaWVzICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsncXVlcmllcyddID0gcXVlcmllcztcbiAgICAgICAgfVxuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge307XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdnZXQnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBUcmlnZ2VyIGEgZnVuY3Rpb24gZXhlY3V0aW9uLiBUaGUgcmV0dXJuZWQgb2JqZWN0IHdpbGwgcmV0dXJuIHlvdSB0aGUgY3VycmVudCBleGVjdXRpb24gc3RhdHVzLiBZb3UgY2FuIHBpbmcgdGhlIGBHZXQgRXhlY3V0aW9uYCBlbmRwb2ludCB0byBnZXQgdXBkYXRlcyBvbiB0aGUgY3VycmVudCBleGVjdXRpb24gc3RhdHVzLiBPbmNlIHRoaXMgZW5kcG9pbnQgaXMgY2FsbGVkLCB5b3VyIGZ1bmN0aW9uIGV4ZWN1dGlvbiBwcm9jZXNzIHdpbGwgc3RhcnQgYXN5bmNocm9ub3VzbHkuXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gZnVuY3Rpb25JZFxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBib2R5XG4gICAgICogQHBhcmFtIHtib29sZWFufSBhc3luY1xuICAgICAqIEBwYXJhbSB7c3RyaW5nfSB4cGF0aFxuICAgICAqIEBwYXJhbSB7RXhlY3V0aW9uTWV0aG9kfSBtZXRob2RcbiAgICAgKiBAcGFyYW0ge29iamVjdH0gaGVhZGVyc1xuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBzY2hlZHVsZWRBdFxuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPE1vZGVscy5FeGVjdXRpb24+fVxuICAgICAqL1xuICAgIGNyZWF0ZUV4ZWN1dGlvbihmdW5jdGlvbklkLCBib2R5LCBhc3luYywgeHBhdGgsIG1ldGhvZCwgaGVhZGVycywgc2NoZWR1bGVkQXQpIHtcbiAgICAgICAgaWYgKHR5cGVvZiBmdW5jdGlvbklkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJmdW5jdGlvbklkXCInKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy9mdW5jdGlvbnMve2Z1bmN0aW9uSWR9L2V4ZWN1dGlvbnMnLnJlcGxhY2UoJ3tmdW5jdGlvbklkfScsIGZ1bmN0aW9uSWQpO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGlmICh0eXBlb2YgYm9keSAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ2JvZHknXSA9IGJvZHk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBhc3luYyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ2FzeW5jJ10gPSBhc3luYztcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIHhwYXRoICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsncGF0aCddID0geHBhdGg7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBtZXRob2QgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydtZXRob2QnXSA9IG1ldGhvZDtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIGhlYWRlcnMgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydoZWFkZXJzJ10gPSBoZWFkZXJzO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2Ygc2NoZWR1bGVkQXQgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydzY2hlZHVsZWRBdCddID0gc2NoZWR1bGVkQXQ7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgY29uc3QgYXBpSGVhZGVycyA9IHtcbiAgICAgICAgICAgICdjb250ZW50LXR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgIH07XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdwb3N0JywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogR2V0IGEgZnVuY3Rpb24gZXhlY3V0aW9uIGxvZyBieSBpdHMgdW5pcXVlIElELlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGZ1bmN0aW9uSWRcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gZXhlY3V0aW9uSWRcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTxNb2RlbHMuRXhlY3V0aW9uPn1cbiAgICAgKi9cbiAgICBnZXRFeGVjdXRpb24oZnVuY3Rpb25JZCwgZXhlY3V0aW9uSWQpIHtcbiAgICAgICAgaWYgKHR5cGVvZiBmdW5jdGlvbklkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJmdW5jdGlvbklkXCInKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIGV4ZWN1dGlvbklkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJleGVjdXRpb25JZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvZnVuY3Rpb25zL3tmdW5jdGlvbklkfS9leGVjdXRpb25zL3tleGVjdXRpb25JZH0nLnJlcGxhY2UoJ3tmdW5jdGlvbklkfScsIGZ1bmN0aW9uSWQpLnJlcGxhY2UoJ3tleGVjdXRpb25JZH0nLCBleGVjdXRpb25JZCk7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgY29uc3QgYXBpSGVhZGVycyA9IHt9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgnZ2V0JywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG59XG5cbmNsYXNzIEdyYXBocWwge1xuICAgIGNvbnN0cnVjdG9yKGNsaWVudCkge1xuICAgICAgICB0aGlzLmNsaWVudCA9IGNsaWVudDtcbiAgICB9XG4gICAgLyoqXG4gICAgICogRXhlY3V0ZSBhIEdyYXBoUUwgbXV0YXRpb24uXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge29iamVjdH0gcXVlcnlcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTx7fT59XG4gICAgICovXG4gICAgcXVlcnkocXVlcnkpIHtcbiAgICAgICAgaWYgKHR5cGVvZiBxdWVyeSA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwicXVlcnlcIicpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2dyYXBocWwnO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGlmICh0eXBlb2YgcXVlcnkgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydxdWVyeSddID0gcXVlcnk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgY29uc3QgYXBpSGVhZGVycyA9IHtcbiAgICAgICAgICAgICd4LXNkay1ncmFwaHFsJzogJ3RydWUnLFxuICAgICAgICAgICAgJ2NvbnRlbnQtdHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ3Bvc3QnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBFeGVjdXRlIGEgR3JhcGhRTCBtdXRhdGlvbi5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7b2JqZWN0fSBxdWVyeVxuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPHt9Pn1cbiAgICAgKi9cbiAgICBtdXRhdGlvbihxdWVyeSkge1xuICAgICAgICBpZiAodHlwZW9mIHF1ZXJ5ID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJxdWVyeVwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvZ3JhcGhxbC9tdXRhdGlvbic7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgaWYgKHR5cGVvZiBxdWVyeSAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3F1ZXJ5J10gPSBxdWVyeTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge1xuICAgICAgICAgICAgJ3gtc2RrLWdyYXBocWwnOiAndHJ1ZScsXG4gICAgICAgICAgICAnY29udGVudC10eXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgncG9zdCcsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxufVxuXG5jbGFzcyBMb2NhbGUge1xuICAgIGNvbnN0cnVjdG9yKGNsaWVudCkge1xuICAgICAgICB0aGlzLmNsaWVudCA9IGNsaWVudDtcbiAgICB9XG4gICAgLyoqXG4gICAgICogR2V0IHRoZSBjdXJyZW50IHVzZXIgbG9jYXRpb24gYmFzZWQgb24gSVAuIFJldHVybnMgYW4gb2JqZWN0IHdpdGggdXNlciBjb3VudHJ5IGNvZGUsIGNvdW50cnkgbmFtZSwgY29udGluZW50IG5hbWUsIGNvbnRpbmVudCBjb2RlLCBpcCBhZGRyZXNzIGFuZCBzdWdnZXN0ZWQgY3VycmVuY3kuIFlvdSBjYW4gdXNlIHRoZSBsb2NhbGUgaGVhZGVyIHRvIGdldCB0aGUgZGF0YSBpbiBhIHN1cHBvcnRlZCBsYW5ndWFnZS5cbiAgICAgKlxuICAgICAqIChbSVAgR2VvbG9jYXRpb24gYnkgREItSVBdKGh0dHBzOi8vZGItaXAuY29tKSlcbiAgICAgKlxuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPE1vZGVscy5Mb2NhbGU+fVxuICAgICAqL1xuICAgIGdldCgpIHtcbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvbG9jYWxlJztcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge307XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdnZXQnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBMaXN0IG9mIGFsbCBsb2NhbGUgY29kZXMgaW4gW0lTTyA2MzktMV0oaHR0cHM6Ly9lbi53aWtpcGVkaWEub3JnL3dpa2kvTGlzdF9vZl9JU09fNjM5LTFfY29kZXMpLlxuICAgICAqXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8TW9kZWxzLkxvY2FsZUNvZGVMaXN0Pn1cbiAgICAgKi9cbiAgICBsaXN0Q29kZXMoKSB7XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2xvY2FsZS9jb2Rlcyc7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgY29uc3QgYXBpSGVhZGVycyA9IHt9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgnZ2V0JywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogTGlzdCBvZiBhbGwgY29udGluZW50cy4gWW91IGNhbiB1c2UgdGhlIGxvY2FsZSBoZWFkZXIgdG8gZ2V0IHRoZSBkYXRhIGluIGEgc3VwcG9ydGVkIGxhbmd1YWdlLlxuICAgICAqXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8TW9kZWxzLkNvbnRpbmVudExpc3Q+fVxuICAgICAqL1xuICAgIGxpc3RDb250aW5lbnRzKCkge1xuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy9sb2NhbGUvY29udGluZW50cyc7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgY29uc3QgYXBpSGVhZGVycyA9IHt9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgnZ2V0JywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogTGlzdCBvZiBhbGwgY291bnRyaWVzLiBZb3UgY2FuIHVzZSB0aGUgbG9jYWxlIGhlYWRlciB0byBnZXQgdGhlIGRhdGEgaW4gYSBzdXBwb3J0ZWQgbGFuZ3VhZ2UuXG4gICAgICpcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTxNb2RlbHMuQ291bnRyeUxpc3Q+fVxuICAgICAqL1xuICAgIGxpc3RDb3VudHJpZXMoKSB7XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2xvY2FsZS9jb3VudHJpZXMnO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7fTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ2dldCcsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIExpc3Qgb2YgYWxsIGNvdW50cmllcyB0aGF0IGFyZSBjdXJyZW50bHkgbWVtYmVycyBvZiB0aGUgRVUuIFlvdSBjYW4gdXNlIHRoZSBsb2NhbGUgaGVhZGVyIHRvIGdldCB0aGUgZGF0YSBpbiBhIHN1cHBvcnRlZCBsYW5ndWFnZS5cbiAgICAgKlxuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPE1vZGVscy5Db3VudHJ5TGlzdD59XG4gICAgICovXG4gICAgbGlzdENvdW50cmllc0VVKCkge1xuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy9sb2NhbGUvY291bnRyaWVzL2V1JztcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge307XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdnZXQnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBMaXN0IG9mIGFsbCBjb3VudHJpZXMgcGhvbmUgY29kZXMuIFlvdSBjYW4gdXNlIHRoZSBsb2NhbGUgaGVhZGVyIHRvIGdldCB0aGUgZGF0YSBpbiBhIHN1cHBvcnRlZCBsYW5ndWFnZS5cbiAgICAgKlxuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPE1vZGVscy5QaG9uZUxpc3Q+fVxuICAgICAqL1xuICAgIGxpc3RDb3VudHJpZXNQaG9uZXMoKSB7XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2xvY2FsZS9jb3VudHJpZXMvcGhvbmVzJztcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge307XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdnZXQnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBMaXN0IG9mIGFsbCBjdXJyZW5jaWVzLCBpbmNsdWRpbmcgY3VycmVuY3kgc3ltYm9sLCBuYW1lLCBwbHVyYWwsIGFuZCBkZWNpbWFsIGRpZ2l0cyBmb3IgYWxsIG1ham9yIGFuZCBtaW5vciBjdXJyZW5jaWVzLiBZb3UgY2FuIHVzZSB0aGUgbG9jYWxlIGhlYWRlciB0byBnZXQgdGhlIGRhdGEgaW4gYSBzdXBwb3J0ZWQgbGFuZ3VhZ2UuXG4gICAgICpcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTxNb2RlbHMuQ3VycmVuY3lMaXN0Pn1cbiAgICAgKi9cbiAgICBsaXN0Q3VycmVuY2llcygpIHtcbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvbG9jYWxlL2N1cnJlbmNpZXMnO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7fTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ2dldCcsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIExpc3Qgb2YgYWxsIGxhbmd1YWdlcyBjbGFzc2lmaWVkIGJ5IElTTyA2MzktMSBpbmNsdWRpbmcgMi1sZXR0ZXIgY29kZSwgbmFtZSBpbiBFbmdsaXNoLCBhbmQgbmFtZSBpbiB0aGUgcmVzcGVjdGl2ZSBsYW5ndWFnZS5cbiAgICAgKlxuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPE1vZGVscy5MYW5ndWFnZUxpc3Q+fVxuICAgICAqL1xuICAgIGxpc3RMYW5ndWFnZXMoKSB7XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2xvY2FsZS9sYW5ndWFnZXMnO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7fTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ2dldCcsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxufVxuXG5jbGFzcyBNZXNzYWdpbmcge1xuICAgIGNvbnN0cnVjdG9yKGNsaWVudCkge1xuICAgICAgICB0aGlzLmNsaWVudCA9IGNsaWVudDtcbiAgICB9XG4gICAgLyoqXG4gICAgICogQ3JlYXRlIGEgbmV3IHN1YnNjcmliZXIuXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gdG9waWNJZFxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBzdWJzY3JpYmVySWRcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gdGFyZ2V0SWRcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTxNb2RlbHMuU3Vic2NyaWJlcj59XG4gICAgICovXG4gICAgY3JlYXRlU3Vic2NyaWJlcih0b3BpY0lkLCBzdWJzY3JpYmVySWQsIHRhcmdldElkKSB7XG4gICAgICAgIGlmICh0eXBlb2YgdG9waWNJZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwidG9waWNJZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBzdWJzY3JpYmVySWQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcInN1YnNjcmliZXJJZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiB0YXJnZXRJZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwidGFyZ2V0SWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL21lc3NhZ2luZy90b3BpY3Mve3RvcGljSWR9L3N1YnNjcmliZXJzJy5yZXBsYWNlKCd7dG9waWNJZH0nLCB0b3BpY0lkKTtcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBpZiAodHlwZW9mIHN1YnNjcmliZXJJZCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3N1YnNjcmliZXJJZCddID0gc3Vic2NyaWJlcklkO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgdGFyZ2V0SWQgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWyd0YXJnZXRJZCddID0gdGFyZ2V0SWQ7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgY29uc3QgYXBpSGVhZGVycyA9IHtcbiAgICAgICAgICAgICdjb250ZW50LXR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgIH07XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdwb3N0JywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogRGVsZXRlIGEgc3Vic2NyaWJlciBieSBpdHMgdW5pcXVlIElELlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IHRvcGljSWRcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gc3Vic2NyaWJlcklkXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8e30+fVxuICAgICAqL1xuICAgIGRlbGV0ZVN1YnNjcmliZXIodG9waWNJZCwgc3Vic2NyaWJlcklkKSB7XG4gICAgICAgIGlmICh0eXBlb2YgdG9waWNJZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwidG9waWNJZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBzdWJzY3JpYmVySWQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcInN1YnNjcmliZXJJZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvbWVzc2FnaW5nL3RvcGljcy97dG9waWNJZH0vc3Vic2NyaWJlcnMve3N1YnNjcmliZXJJZH0nLnJlcGxhY2UoJ3t0b3BpY0lkfScsIHRvcGljSWQpLnJlcGxhY2UoJ3tzdWJzY3JpYmVySWR9Jywgc3Vic2NyaWJlcklkKTtcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge1xuICAgICAgICAgICAgJ2NvbnRlbnQtdHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ2RlbGV0ZScsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxufVxuXG5jbGFzcyBTdG9yYWdlIHtcbiAgICBjb25zdHJ1Y3RvcihjbGllbnQpIHtcbiAgICAgICAgdGhpcy5jbGllbnQgPSBjbGllbnQ7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIEdldCBhIGxpc3Qgb2YgYWxsIHRoZSB1c2VyIGZpbGVzLiBZb3UgY2FuIHVzZSB0aGUgcXVlcnkgcGFyYW1zIHRvIGZpbHRlciB5b3VyIHJlc3VsdHMuXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gYnVja2V0SWRcbiAgICAgKiBAcGFyYW0ge3N0cmluZ1tdfSBxdWVyaWVzXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IHNlYXJjaFxuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPE1vZGVscy5GaWxlTGlzdD59XG4gICAgICovXG4gICAgbGlzdEZpbGVzKGJ1Y2tldElkLCBxdWVyaWVzLCBzZWFyY2gpIHtcbiAgICAgICAgaWYgKHR5cGVvZiBidWNrZXRJZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwiYnVja2V0SWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL3N0b3JhZ2UvYnVja2V0cy97YnVja2V0SWR9L2ZpbGVzJy5yZXBsYWNlKCd7YnVja2V0SWR9JywgYnVja2V0SWQpO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGlmICh0eXBlb2YgcXVlcmllcyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3F1ZXJpZXMnXSA9IHF1ZXJpZXM7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBzZWFyY2ggIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydzZWFyY2gnXSA9IHNlYXJjaDtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge307XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdnZXQnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBDcmVhdGUgYSBuZXcgZmlsZS4gQmVmb3JlIHVzaW5nIHRoaXMgcm91dGUsIHlvdSBzaG91bGQgY3JlYXRlIGEgbmV3IGJ1Y2tldCByZXNvdXJjZSB1c2luZyBlaXRoZXIgYSBbc2VydmVyIGludGVncmF0aW9uXShodHRwczovL2FwcHdyaXRlLmlvL2RvY3Mvc2VydmVyL3N0b3JhZ2Ujc3RvcmFnZUNyZWF0ZUJ1Y2tldCkgQVBJIG9yIGRpcmVjdGx5IGZyb20geW91ciBBcHB3cml0ZSBjb25zb2xlLlxuICAgICAqXG4gICAgICogTGFyZ2VyIGZpbGVzIHNob3VsZCBiZSB1cGxvYWRlZCB1c2luZyBtdWx0aXBsZSByZXF1ZXN0cyB3aXRoIHRoZSBbY29udGVudC1yYW5nZV0oaHR0cHM6Ly9kZXZlbG9wZXIubW96aWxsYS5vcmcvZW4tVVMvZG9jcy9XZWIvSFRUUC9IZWFkZXJzL0NvbnRlbnQtUmFuZ2UpIGhlYWRlciB0byBzZW5kIGEgcGFydGlhbCByZXF1ZXN0IHdpdGggYSBtYXhpbXVtIHN1cHBvcnRlZCBjaHVuayBvZiBgNU1CYC4gVGhlIGBjb250ZW50LXJhbmdlYCBoZWFkZXIgdmFsdWVzIHNob3VsZCBhbHdheXMgYmUgaW4gYnl0ZXMuXG4gICAgICpcbiAgICAgKiBXaGVuIHRoZSBmaXJzdCByZXF1ZXN0IGlzIHNlbnQsIHRoZSBzZXJ2ZXIgd2lsbCByZXR1cm4gdGhlICoqRmlsZSoqIG9iamVjdCwgYW5kIHRoZSBzdWJzZXF1ZW50IHBhcnQgcmVxdWVzdCBtdXN0IGluY2x1ZGUgdGhlIGZpbGUmIzAzOTtzICoqaWQqKiBpbiBgeC1hcHB3cml0ZS1pZGAgaGVhZGVyIHRvIGFsbG93IHRoZSBzZXJ2ZXIgdG8ga25vdyB0aGF0IHRoZSBwYXJ0aWFsIHVwbG9hZCBpcyBmb3IgdGhlIGV4aXN0aW5nIGZpbGUgYW5kIG5vdCBmb3IgYSBuZXcgb25lLlxuICAgICAqXG4gICAgICogSWYgeW91JiMwMzk7cmUgY3JlYXRpbmcgYSBuZXcgZmlsZSB1c2luZyBvbmUgb2YgdGhlIEFwcHdyaXRlIFNES3MsIGFsbCB0aGUgY2h1bmtpbmcgbG9naWMgd2lsbCBiZSBtYW5hZ2VkIGJ5IHRoZSBTREsgaW50ZXJuYWxseS5cbiAgICAgKlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGJ1Y2tldElkXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGZpbGVJZFxuICAgICAqIEBwYXJhbSB7RmlsZX0gZmlsZVxuICAgICAqIEBwYXJhbSB7c3RyaW5nW119IHBlcm1pc3Npb25zXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8TW9kZWxzLkZpbGU+fVxuICAgICAqL1xuICAgIGNyZWF0ZUZpbGUoYnVja2V0SWQsIGZpbGVJZCwgZmlsZSwgcGVybWlzc2lvbnMsIG9uUHJvZ3Jlc3MgPSAocHJvZ3Jlc3MpID0+IHsgfSkge1xuICAgICAgICBpZiAodHlwZW9mIGJ1Y2tldElkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJidWNrZXRJZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBmaWxlSWQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcImZpbGVJZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBmaWxlID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJmaWxlXCInKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy9zdG9yYWdlL2J1Y2tldHMve2J1Y2tldElkfS9maWxlcycucmVwbGFjZSgne2J1Y2tldElkfScsIGJ1Y2tldElkKTtcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBpZiAodHlwZW9mIGZpbGVJZCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ2ZpbGVJZCddID0gZmlsZUlkO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgZmlsZSAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ2ZpbGUnXSA9IGZpbGU7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBwZXJtaXNzaW9ucyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3Blcm1pc3Npb25zJ10gPSBwZXJtaXNzaW9ucztcbiAgICAgICAgfVxuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge1xuICAgICAgICAgICAgJ2NvbnRlbnQtdHlwZSc6ICdtdWx0aXBhcnQvZm9ybS1kYXRhJyxcbiAgICAgICAgfTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNodW5rZWRVcGxvYWQoJ3Bvc3QnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQsIG9uUHJvZ3Jlc3MpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBHZXQgYSBmaWxlIGJ5IGl0cyB1bmlxdWUgSUQuIFRoaXMgZW5kcG9pbnQgcmVzcG9uc2UgcmV0dXJucyBhIEpTT04gb2JqZWN0IHdpdGggdGhlIGZpbGUgbWV0YWRhdGEuXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gYnVja2V0SWRcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gZmlsZUlkXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8TW9kZWxzLkZpbGU+fVxuICAgICAqL1xuICAgIGdldEZpbGUoYnVja2V0SWQsIGZpbGVJZCkge1xuICAgICAgICBpZiAodHlwZW9mIGJ1Y2tldElkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJidWNrZXRJZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBmaWxlSWQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcImZpbGVJZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvc3RvcmFnZS9idWNrZXRzL3tidWNrZXRJZH0vZmlsZXMve2ZpbGVJZH0nLnJlcGxhY2UoJ3tidWNrZXRJZH0nLCBidWNrZXRJZCkucmVwbGFjZSgne2ZpbGVJZH0nLCBmaWxlSWQpO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7fTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ2dldCcsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFVwZGF0ZSBhIGZpbGUgYnkgaXRzIHVuaXF1ZSBJRC4gT25seSB1c2VycyB3aXRoIHdyaXRlIHBlcm1pc3Npb25zIGhhdmUgYWNjZXNzIHRvIHVwZGF0ZSB0aGlzIHJlc291cmNlLlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGJ1Y2tldElkXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGZpbGVJZFxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBuYW1lXG4gICAgICogQHBhcmFtIHtzdHJpbmdbXX0gcGVybWlzc2lvbnNcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTxNb2RlbHMuRmlsZT59XG4gICAgICovXG4gICAgdXBkYXRlRmlsZShidWNrZXRJZCwgZmlsZUlkLCBuYW1lLCBwZXJtaXNzaW9ucykge1xuICAgICAgICBpZiAodHlwZW9mIGJ1Y2tldElkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJidWNrZXRJZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBmaWxlSWQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcImZpbGVJZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvc3RvcmFnZS9idWNrZXRzL3tidWNrZXRJZH0vZmlsZXMve2ZpbGVJZH0nLnJlcGxhY2UoJ3tidWNrZXRJZH0nLCBidWNrZXRJZCkucmVwbGFjZSgne2ZpbGVJZH0nLCBmaWxlSWQpO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGlmICh0eXBlb2YgbmFtZSAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ25hbWUnXSA9IG5hbWU7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBwZXJtaXNzaW9ucyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3Blcm1pc3Npb25zJ10gPSBwZXJtaXNzaW9ucztcbiAgICAgICAgfVxuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge1xuICAgICAgICAgICAgJ2NvbnRlbnQtdHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ3B1dCcsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIERlbGV0ZSBhIGZpbGUgYnkgaXRzIHVuaXF1ZSBJRC4gT25seSB1c2VycyB3aXRoIHdyaXRlIHBlcm1pc3Npb25zIGhhdmUgYWNjZXNzIHRvIGRlbGV0ZSB0aGlzIHJlc291cmNlLlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGJ1Y2tldElkXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGZpbGVJZFxuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPHt9Pn1cbiAgICAgKi9cbiAgICBkZWxldGVGaWxlKGJ1Y2tldElkLCBmaWxlSWQpIHtcbiAgICAgICAgaWYgKHR5cGVvZiBidWNrZXRJZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwiYnVja2V0SWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgZmlsZUlkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJmaWxlSWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL3N0b3JhZ2UvYnVja2V0cy97YnVja2V0SWR9L2ZpbGVzL3tmaWxlSWR9Jy5yZXBsYWNlKCd7YnVja2V0SWR9JywgYnVja2V0SWQpLnJlcGxhY2UoJ3tmaWxlSWR9JywgZmlsZUlkKTtcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge1xuICAgICAgICAgICAgJ2NvbnRlbnQtdHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ2RlbGV0ZScsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIEdldCBhIGZpbGUgY29udGVudCBieSBpdHMgdW5pcXVlIElELiBUaGUgZW5kcG9pbnQgcmVzcG9uc2UgcmV0dXJuIHdpdGggYSAmIzAzOTtDb250ZW50LURpc3Bvc2l0aW9uOiBhdHRhY2htZW50JiMwMzk7IGhlYWRlciB0aGF0IHRlbGxzIHRoZSBicm93c2VyIHRvIHN0YXJ0IGRvd25sb2FkaW5nIHRoZSBmaWxlIHRvIHVzZXIgZG93bmxvYWRzIGRpcmVjdG9yeS5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBidWNrZXRJZFxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBmaWxlSWRcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gdG9rZW5cbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7c3RyaW5nfVxuICAgICAqL1xuICAgIGdldEZpbGVEb3dubG9hZChidWNrZXRJZCwgZmlsZUlkLCB0b2tlbikge1xuICAgICAgICBpZiAodHlwZW9mIGJ1Y2tldElkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJidWNrZXRJZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBmaWxlSWQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcImZpbGVJZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvc3RvcmFnZS9idWNrZXRzL3tidWNrZXRJZH0vZmlsZXMve2ZpbGVJZH0vZG93bmxvYWQnLnJlcGxhY2UoJ3tidWNrZXRJZH0nLCBidWNrZXRJZCkucmVwbGFjZSgne2ZpbGVJZH0nLCBmaWxlSWQpO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGlmICh0eXBlb2YgdG9rZW4gIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWyd0b2tlbiddID0gdG9rZW47XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgcGF5bG9hZFsncHJvamVjdCddID0gdGhpcy5jbGllbnQuY29uZmlnLnByb2plY3Q7XG4gICAgICAgIGZvciAoY29uc3QgW2tleSwgdmFsdWVdIG9mIE9iamVjdC5lbnRyaWVzKFNlcnZpY2UuZmxhdHRlbihwYXlsb2FkKSkpIHtcbiAgICAgICAgICAgIHVyaS5zZWFyY2hQYXJhbXMuYXBwZW5kKGtleSwgdmFsdWUpO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiB1cmkudG9TdHJpbmcoKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogR2V0IGEgZmlsZSBwcmV2aWV3IGltYWdlLiBDdXJyZW50bHksIHRoaXMgbWV0aG9kIHN1cHBvcnRzIHByZXZpZXcgZm9yIGltYWdlIGZpbGVzIChqcGcsIHBuZywgYW5kIGdpZiksIG90aGVyIHN1cHBvcnRlZCBmb3JtYXRzLCBsaWtlIHBkZiwgZG9jcywgc2xpZGVzLCBhbmQgc3ByZWFkc2hlZXRzLCB3aWxsIHJldHVybiB0aGUgZmlsZSBpY29uIGltYWdlLiBZb3UgY2FuIGFsc28gcGFzcyBxdWVyeSBzdHJpbmcgYXJndW1lbnRzIGZvciBjdXR0aW5nIGFuZCByZXNpemluZyB5b3VyIHByZXZpZXcgaW1hZ2UuIFByZXZpZXcgaXMgc3VwcG9ydGVkIG9ubHkgZm9yIGltYWdlIGZpbGVzIHNtYWxsZXIgdGhhbiAxME1CLlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGJ1Y2tldElkXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGZpbGVJZFxuICAgICAqIEBwYXJhbSB7bnVtYmVyfSB3aWR0aFxuICAgICAqIEBwYXJhbSB7bnVtYmVyfSBoZWlnaHRcbiAgICAgKiBAcGFyYW0ge0ltYWdlR3Jhdml0eX0gZ3Jhdml0eVxuICAgICAqIEBwYXJhbSB7bnVtYmVyfSBxdWFsaXR5XG4gICAgICogQHBhcmFtIHtudW1iZXJ9IGJvcmRlcldpZHRoXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGJvcmRlckNvbG9yXG4gICAgICogQHBhcmFtIHtudW1iZXJ9IGJvcmRlclJhZGl1c1xuICAgICAqIEBwYXJhbSB7bnVtYmVyfSBvcGFjaXR5XG4gICAgICogQHBhcmFtIHtudW1iZXJ9IHJvdGF0aW9uXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGJhY2tncm91bmRcbiAgICAgKiBAcGFyYW0ge0ltYWdlRm9ybWF0fSBvdXRwdXRcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gdG9rZW5cbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7c3RyaW5nfVxuICAgICAqL1xuICAgIGdldEZpbGVQcmV2aWV3KGJ1Y2tldElkLCBmaWxlSWQsIHdpZHRoLCBoZWlnaHQsIGdyYXZpdHksIHF1YWxpdHksIGJvcmRlcldpZHRoLCBib3JkZXJDb2xvciwgYm9yZGVyUmFkaXVzLCBvcGFjaXR5LCByb3RhdGlvbiwgYmFja2dyb3VuZCwgb3V0cHV0LCB0b2tlbikge1xuICAgICAgICBpZiAodHlwZW9mIGJ1Y2tldElkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJidWNrZXRJZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBmaWxlSWQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcImZpbGVJZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvc3RvcmFnZS9idWNrZXRzL3tidWNrZXRJZH0vZmlsZXMve2ZpbGVJZH0vcHJldmlldycucmVwbGFjZSgne2J1Y2tldElkfScsIGJ1Y2tldElkKS5yZXBsYWNlKCd7ZmlsZUlkfScsIGZpbGVJZCk7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgaWYgKHR5cGVvZiB3aWR0aCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3dpZHRoJ10gPSB3aWR0aDtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIGhlaWdodCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ2hlaWdodCddID0gaGVpZ2h0O1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgZ3Jhdml0eSAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ2dyYXZpdHknXSA9IGdyYXZpdHk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBxdWFsaXR5ICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsncXVhbGl0eSddID0gcXVhbGl0eTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIGJvcmRlcldpZHRoICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsnYm9yZGVyV2lkdGgnXSA9IGJvcmRlcldpZHRoO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgYm9yZGVyQ29sb3IgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydib3JkZXJDb2xvciddID0gYm9yZGVyQ29sb3I7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBib3JkZXJSYWRpdXMgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydib3JkZXJSYWRpdXMnXSA9IGJvcmRlclJhZGl1cztcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIG9wYWNpdHkgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydvcGFjaXR5J10gPSBvcGFjaXR5O1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2Ygcm90YXRpb24gIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydyb3RhdGlvbiddID0gcm90YXRpb247XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBiYWNrZ3JvdW5kICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsnYmFja2dyb3VuZCddID0gYmFja2dyb3VuZDtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIG91dHB1dCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ291dHB1dCddID0gb3V0cHV0O1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgdG9rZW4gIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWyd0b2tlbiddID0gdG9rZW47XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgcGF5bG9hZFsncHJvamVjdCddID0gdGhpcy5jbGllbnQuY29uZmlnLnByb2plY3Q7XG4gICAgICAgIGZvciAoY29uc3QgW2tleSwgdmFsdWVdIG9mIE9iamVjdC5lbnRyaWVzKFNlcnZpY2UuZmxhdHRlbihwYXlsb2FkKSkpIHtcbiAgICAgICAgICAgIHVyaS5zZWFyY2hQYXJhbXMuYXBwZW5kKGtleSwgdmFsdWUpO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiB1cmkudG9TdHJpbmcoKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogR2V0IGEgZmlsZSBjb250ZW50IGJ5IGl0cyB1bmlxdWUgSUQuIFRoaXMgZW5kcG9pbnQgaXMgc2ltaWxhciB0byB0aGUgZG93bmxvYWQgbWV0aG9kIGJ1dCByZXR1cm5zIHdpdGggbm8gICYjMDM5O0NvbnRlbnQtRGlzcG9zaXRpb246IGF0dGFjaG1lbnQmIzAzOTsgaGVhZGVyLlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGJ1Y2tldElkXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGZpbGVJZFxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSB0b2tlblxuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtzdHJpbmd9XG4gICAgICovXG4gICAgZ2V0RmlsZVZpZXcoYnVja2V0SWQsIGZpbGVJZCwgdG9rZW4pIHtcbiAgICAgICAgaWYgKHR5cGVvZiBidWNrZXRJZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwiYnVja2V0SWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgZmlsZUlkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJmaWxlSWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL3N0b3JhZ2UvYnVja2V0cy97YnVja2V0SWR9L2ZpbGVzL3tmaWxlSWR9L3ZpZXcnLnJlcGxhY2UoJ3tidWNrZXRJZH0nLCBidWNrZXRJZCkucmVwbGFjZSgne2ZpbGVJZH0nLCBmaWxlSWQpO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGlmICh0eXBlb2YgdG9rZW4gIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWyd0b2tlbiddID0gdG9rZW47XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgcGF5bG9hZFsncHJvamVjdCddID0gdGhpcy5jbGllbnQuY29uZmlnLnByb2plY3Q7XG4gICAgICAgIGZvciAoY29uc3QgW2tleSwgdmFsdWVdIG9mIE9iamVjdC5lbnRyaWVzKFNlcnZpY2UuZmxhdHRlbihwYXlsb2FkKSkpIHtcbiAgICAgICAgICAgIHVyaS5zZWFyY2hQYXJhbXMuYXBwZW5kKGtleSwgdmFsdWUpO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiB1cmkudG9TdHJpbmcoKTtcbiAgICB9XG59XG5cbmNsYXNzIFRlYW1zIHtcbiAgICBjb25zdHJ1Y3RvcihjbGllbnQpIHtcbiAgICAgICAgdGhpcy5jbGllbnQgPSBjbGllbnQ7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIEdldCBhIGxpc3Qgb2YgYWxsIHRoZSB0ZWFtcyBpbiB3aGljaCB0aGUgY3VycmVudCB1c2VyIGlzIGEgbWVtYmVyLiBZb3UgY2FuIHVzZSB0aGUgcGFyYW1ldGVycyB0byBmaWx0ZXIgeW91ciByZXN1bHRzLlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmdbXX0gcXVlcmllc1xuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBzZWFyY2hcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTxNb2RlbHMuVGVhbUxpc3Q8UHJlZmVyZW5jZXM+Pn1cbiAgICAgKi9cbiAgICBsaXN0KHF1ZXJpZXMsIHNlYXJjaCkge1xuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy90ZWFtcyc7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgaWYgKHR5cGVvZiBxdWVyaWVzICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsncXVlcmllcyddID0gcXVlcmllcztcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIHNlYXJjaCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3NlYXJjaCddID0gc2VhcmNoO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7fTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ2dldCcsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIENyZWF0ZSBhIG5ldyB0ZWFtLiBUaGUgdXNlciB3aG8gY3JlYXRlcyB0aGUgdGVhbSB3aWxsIGF1dG9tYXRpY2FsbHkgYmUgYXNzaWduZWQgYXMgdGhlIG93bmVyIG9mIHRoZSB0ZWFtLiBPbmx5IHRoZSB1c2VycyB3aXRoIHRoZSBvd25lciByb2xlIGNhbiBpbnZpdGUgbmV3IG1lbWJlcnMsIGFkZCBuZXcgb3duZXJzIGFuZCBkZWxldGUgb3IgdXBkYXRlIHRoZSB0ZWFtLlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IHRlYW1JZFxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBuYW1lXG4gICAgICogQHBhcmFtIHtzdHJpbmdbXX0gcm9sZXNcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTxNb2RlbHMuVGVhbTxQcmVmZXJlbmNlcz4+fVxuICAgICAqL1xuICAgIGNyZWF0ZSh0ZWFtSWQsIG5hbWUsIHJvbGVzKSB7XG4gICAgICAgIGlmICh0eXBlb2YgdGVhbUlkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJ0ZWFtSWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgbmFtZSA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwibmFtZVwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvdGVhbXMnO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGlmICh0eXBlb2YgdGVhbUlkICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsndGVhbUlkJ10gPSB0ZWFtSWQ7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBuYW1lICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsnbmFtZSddID0gbmFtZTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIHJvbGVzICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsncm9sZXMnXSA9IHJvbGVzO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7XG4gICAgICAgICAgICAnY29udGVudC10eXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgncG9zdCcsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIEdldCBhIHRlYW0gYnkgaXRzIElELiBBbGwgdGVhbSBtZW1iZXJzIGhhdmUgcmVhZCBhY2Nlc3MgZm9yIHRoaXMgcmVzb3VyY2UuXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gdGVhbUlkXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8TW9kZWxzLlRlYW08UHJlZmVyZW5jZXM+Pn1cbiAgICAgKi9cbiAgICBnZXQodGVhbUlkKSB7XG4gICAgICAgIGlmICh0eXBlb2YgdGVhbUlkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJ0ZWFtSWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL3RlYW1zL3t0ZWFtSWR9Jy5yZXBsYWNlKCd7dGVhbUlkfScsIHRlYW1JZCk7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgY29uc3QgYXBpSGVhZGVycyA9IHt9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgnZ2V0JywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogVXBkYXRlIHRoZSB0ZWFtJiMwMzk7cyBuYW1lIGJ5IGl0cyB1bmlxdWUgSUQuXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gdGVhbUlkXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IG5hbWVcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTxNb2RlbHMuVGVhbTxQcmVmZXJlbmNlcz4+fVxuICAgICAqL1xuICAgIHVwZGF0ZU5hbWUodGVhbUlkLCBuYW1lKSB7XG4gICAgICAgIGlmICh0eXBlb2YgdGVhbUlkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJ0ZWFtSWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgbmFtZSA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwibmFtZVwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvdGVhbXMve3RlYW1JZH0nLnJlcGxhY2UoJ3t0ZWFtSWR9JywgdGVhbUlkKTtcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBpZiAodHlwZW9mIG5hbWUgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWyduYW1lJ10gPSBuYW1lO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7XG4gICAgICAgICAgICAnY29udGVudC10eXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgncHV0JywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogRGVsZXRlIGEgdGVhbSB1c2luZyBpdHMgSUQuIE9ubHkgdGVhbSBtZW1iZXJzIHdpdGggdGhlIG93bmVyIHJvbGUgY2FuIGRlbGV0ZSB0aGUgdGVhbS5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSB0ZWFtSWRcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTx7fT59XG4gICAgICovXG4gICAgZGVsZXRlKHRlYW1JZCkge1xuICAgICAgICBpZiAodHlwZW9mIHRlYW1JZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwidGVhbUlkXCInKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy90ZWFtcy97dGVhbUlkfScucmVwbGFjZSgne3RlYW1JZH0nLCB0ZWFtSWQpO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7XG4gICAgICAgICAgICAnY29udGVudC10eXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgnZGVsZXRlJywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogVXNlIHRoaXMgZW5kcG9pbnQgdG8gbGlzdCBhIHRlYW0mIzAzOTtzIG1lbWJlcnMgdXNpbmcgdGhlIHRlYW0mIzAzOTtzIElELiBBbGwgdGVhbSBtZW1iZXJzIGhhdmUgcmVhZCBhY2Nlc3MgdG8gdGhpcyBlbmRwb2ludC4gSGlkZSBzZW5zaXRpdmUgYXR0cmlidXRlcyBmcm9tIHRoZSByZXNwb25zZSBieSB0b2dnbGluZyBtZW1iZXJzaGlwIHByaXZhY3kgaW4gdGhlIENvbnNvbGUuXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gdGVhbUlkXG4gICAgICogQHBhcmFtIHtzdHJpbmdbXX0gcXVlcmllc1xuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBzZWFyY2hcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTxNb2RlbHMuTWVtYmVyc2hpcExpc3Q+fVxuICAgICAqL1xuICAgIGxpc3RNZW1iZXJzaGlwcyh0ZWFtSWQsIHF1ZXJpZXMsIHNlYXJjaCkge1xuICAgICAgICBpZiAodHlwZW9mIHRlYW1JZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwidGVhbUlkXCInKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy90ZWFtcy97dGVhbUlkfS9tZW1iZXJzaGlwcycucmVwbGFjZSgne3RlYW1JZH0nLCB0ZWFtSWQpO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGlmICh0eXBlb2YgcXVlcmllcyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3F1ZXJpZXMnXSA9IHF1ZXJpZXM7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBzZWFyY2ggIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydzZWFyY2gnXSA9IHNlYXJjaDtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge307XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdnZXQnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBJbnZpdGUgYSBuZXcgbWVtYmVyIHRvIGpvaW4geW91ciB0ZWFtLiBQcm92aWRlIGFuIElEIGZvciBleGlzdGluZyB1c2Vycywgb3IgaW52aXRlIHVucmVnaXN0ZXJlZCB1c2VycyB1c2luZyBhbiBlbWFpbCBvciBwaG9uZSBudW1iZXIuIElmIGluaXRpYXRlZCBmcm9tIGEgQ2xpZW50IFNESywgQXBwd3JpdGUgd2lsbCBzZW5kIGFuIGVtYWlsIG9yIHNtcyB3aXRoIGEgbGluayB0byBqb2luIHRoZSB0ZWFtIHRvIHRoZSBpbnZpdGVkIHVzZXIsIGFuZCBhbiBhY2NvdW50IHdpbGwgYmUgY3JlYXRlZCBmb3IgdGhlbSBpZiBvbmUgZG9lc24mIzAzOTt0IGV4aXN0LiBJZiBpbml0aWF0ZWQgZnJvbSBhIFNlcnZlciBTREssIHRoZSBuZXcgbWVtYmVyIHdpbGwgYmUgYWRkZWQgYXV0b21hdGljYWxseSB0byB0aGUgdGVhbS5cbiAgICAgKlxuICAgICAqIFlvdSBvbmx5IG5lZWQgdG8gcHJvdmlkZSBvbmUgb2YgYSB1c2VyIElELCBlbWFpbCwgb3IgcGhvbmUgbnVtYmVyLiBBcHB3cml0ZSB3aWxsIHByaW9yaXRpemUgYWNjZXB0aW5nIHRoZSB1c2VyIElEICZndDsgZW1haWwgJmd0OyBwaG9uZSBudW1iZXIgaWYgeW91IHByb3ZpZGUgbW9yZSB0aGFuIG9uZSBvZiB0aGVzZSBwYXJhbWV0ZXJzLlxuICAgICAqXG4gICAgICogVXNlIHRoZSBgdXJsYCBwYXJhbWV0ZXIgdG8gcmVkaXJlY3QgdGhlIHVzZXIgZnJvbSB0aGUgaW52aXRhdGlvbiBlbWFpbCB0byB5b3VyIGFwcC4gQWZ0ZXIgdGhlIHVzZXIgaXMgcmVkaXJlY3RlZCwgdXNlIHRoZSBbVXBkYXRlIFRlYW0gTWVtYmVyc2hpcCBTdGF0dXNdKGh0dHBzOi8vYXBwd3JpdGUuaW8vZG9jcy9yZWZlcmVuY2VzL2Nsb3VkL2NsaWVudC13ZWIvdGVhbXMjdXBkYXRlTWVtYmVyc2hpcFN0YXR1cykgZW5kcG9pbnQgdG8gYWxsb3cgdGhlIHVzZXIgdG8gYWNjZXB0IHRoZSBpbnZpdGF0aW9uIHRvIHRoZSB0ZWFtLlxuICAgICAqXG4gICAgICogUGxlYXNlIG5vdGUgdGhhdCB0byBhdm9pZCBhIFtSZWRpcmVjdCBBdHRhY2tdKGh0dHBzOi8vZ2l0aHViLmNvbS9PV0FTUC9DaGVhdFNoZWV0U2VyaWVzL2Jsb2IvbWFzdGVyL2NoZWF0c2hlZXRzL1VudmFsaWRhdGVkX1JlZGlyZWN0c19hbmRfRm9yd2FyZHNfQ2hlYXRfU2hlZXQubWQpIEFwcHdyaXRlIHdpbGwgYWNjZXB0IHRoZSBvbmx5IHJlZGlyZWN0IFVSTHMgdW5kZXIgdGhlIGRvbWFpbnMgeW91IGhhdmUgYWRkZWQgYXMgYSBwbGF0Zm9ybSBvbiB0aGUgQXBwd3JpdGUgQ29uc29sZS5cbiAgICAgKlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IHRlYW1JZFxuICAgICAqIEBwYXJhbSB7c3RyaW5nW119IHJvbGVzXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGVtYWlsXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IHVzZXJJZFxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBwaG9uZVxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSB1cmxcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gbmFtZVxuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPE1vZGVscy5NZW1iZXJzaGlwPn1cbiAgICAgKi9cbiAgICBjcmVhdGVNZW1iZXJzaGlwKHRlYW1JZCwgcm9sZXMsIGVtYWlsLCB1c2VySWQsIHBob25lLCB1cmwsIG5hbWUpIHtcbiAgICAgICAgaWYgKHR5cGVvZiB0ZWFtSWQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcInRlYW1JZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiByb2xlcyA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwicm9sZXNcIicpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL3RlYW1zL3t0ZWFtSWR9L21lbWJlcnNoaXBzJy5yZXBsYWNlKCd7dGVhbUlkfScsIHRlYW1JZCk7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgaWYgKHR5cGVvZiBlbWFpbCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ2VtYWlsJ10gPSBlbWFpbDtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIHVzZXJJZCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3VzZXJJZCddID0gdXNlcklkO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgcGhvbmUgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydwaG9uZSddID0gcGhvbmU7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiByb2xlcyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3JvbGVzJ10gPSByb2xlcztcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIHVybCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3VybCddID0gdXJsO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgbmFtZSAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ25hbWUnXSA9IG5hbWU7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgY29uc3QgYXBpSGVhZGVycyA9IHtcbiAgICAgICAgICAgICdjb250ZW50LXR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgIH07XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdwb3N0JywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogR2V0IGEgdGVhbSBtZW1iZXIgYnkgdGhlIG1lbWJlcnNoaXAgdW5pcXVlIGlkLiBBbGwgdGVhbSBtZW1iZXJzIGhhdmUgcmVhZCBhY2Nlc3MgZm9yIHRoaXMgcmVzb3VyY2UuIEhpZGUgc2Vuc2l0aXZlIGF0dHJpYnV0ZXMgZnJvbSB0aGUgcmVzcG9uc2UgYnkgdG9nZ2xpbmcgbWVtYmVyc2hpcCBwcml2YWN5IGluIHRoZSBDb25zb2xlLlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IHRlYW1JZFxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBtZW1iZXJzaGlwSWRcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTxNb2RlbHMuTWVtYmVyc2hpcD59XG4gICAgICovXG4gICAgZ2V0TWVtYmVyc2hpcCh0ZWFtSWQsIG1lbWJlcnNoaXBJZCkge1xuICAgICAgICBpZiAodHlwZW9mIHRlYW1JZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwidGVhbUlkXCInKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIG1lbWJlcnNoaXBJZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwibWVtYmVyc2hpcElkXCInKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy90ZWFtcy97dGVhbUlkfS9tZW1iZXJzaGlwcy97bWVtYmVyc2hpcElkfScucmVwbGFjZSgne3RlYW1JZH0nLCB0ZWFtSWQpLnJlcGxhY2UoJ3ttZW1iZXJzaGlwSWR9JywgbWVtYmVyc2hpcElkKTtcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge307XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdnZXQnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBNb2RpZnkgdGhlIHJvbGVzIG9mIGEgdGVhbSBtZW1iZXIuIE9ubHkgdGVhbSBtZW1iZXJzIHdpdGggdGhlIG93bmVyIHJvbGUgaGF2ZSBhY2Nlc3MgdG8gdGhpcyBlbmRwb2ludC4gTGVhcm4gbW9yZSBhYm91dCBbcm9sZXMgYW5kIHBlcm1pc3Npb25zXShodHRwczovL2FwcHdyaXRlLmlvL2RvY3MvcGVybWlzc2lvbnMpLlxuICAgICAqXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gdGVhbUlkXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IG1lbWJlcnNoaXBJZFxuICAgICAqIEBwYXJhbSB7c3RyaW5nW119IHJvbGVzXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8TW9kZWxzLk1lbWJlcnNoaXA+fVxuICAgICAqL1xuICAgIHVwZGF0ZU1lbWJlcnNoaXAodGVhbUlkLCBtZW1iZXJzaGlwSWQsIHJvbGVzKSB7XG4gICAgICAgIGlmICh0eXBlb2YgdGVhbUlkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJ0ZWFtSWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgbWVtYmVyc2hpcElkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJtZW1iZXJzaGlwSWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2Ygcm9sZXMgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcInJvbGVzXCInKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy90ZWFtcy97dGVhbUlkfS9tZW1iZXJzaGlwcy97bWVtYmVyc2hpcElkfScucmVwbGFjZSgne3RlYW1JZH0nLCB0ZWFtSWQpLnJlcGxhY2UoJ3ttZW1iZXJzaGlwSWR9JywgbWVtYmVyc2hpcElkKTtcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBpZiAodHlwZW9mIHJvbGVzICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsncm9sZXMnXSA9IHJvbGVzO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7XG4gICAgICAgICAgICAnY29udGVudC10eXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgncGF0Y2gnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBUaGlzIGVuZHBvaW50IGFsbG93cyBhIHVzZXIgdG8gbGVhdmUgYSB0ZWFtIG9yIGZvciBhIHRlYW0gb3duZXIgdG8gZGVsZXRlIHRoZSBtZW1iZXJzaGlwIG9mIGFueSBvdGhlciB0ZWFtIG1lbWJlci4gWW91IGNhbiBhbHNvIHVzZSB0aGlzIGVuZHBvaW50IHRvIGRlbGV0ZSBhIHVzZXIgbWVtYmVyc2hpcCBldmVuIGlmIGl0IGlzIG5vdCBhY2NlcHRlZC5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSB0ZWFtSWRcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gbWVtYmVyc2hpcElkXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8e30+fVxuICAgICAqL1xuICAgIGRlbGV0ZU1lbWJlcnNoaXAodGVhbUlkLCBtZW1iZXJzaGlwSWQpIHtcbiAgICAgICAgaWYgKHR5cGVvZiB0ZWFtSWQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcInRlYW1JZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBtZW1iZXJzaGlwSWQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcIm1lbWJlcnNoaXBJZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvdGVhbXMve3RlYW1JZH0vbWVtYmVyc2hpcHMve21lbWJlcnNoaXBJZH0nLnJlcGxhY2UoJ3t0ZWFtSWR9JywgdGVhbUlkKS5yZXBsYWNlKCd7bWVtYmVyc2hpcElkfScsIG1lbWJlcnNoaXBJZCk7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgY29uc3QgYXBpSGVhZGVycyA9IHtcbiAgICAgICAgICAgICdjb250ZW50LXR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgIH07XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdkZWxldGUnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBVc2UgdGhpcyBlbmRwb2ludCB0byBhbGxvdyBhIHVzZXIgdG8gYWNjZXB0IGFuIGludml0YXRpb24gdG8gam9pbiBhIHRlYW0gYWZ0ZXIgYmVpbmcgcmVkaXJlY3RlZCBiYWNrIHRvIHlvdXIgYXBwIGZyb20gdGhlIGludml0YXRpb24gZW1haWwgcmVjZWl2ZWQgYnkgdGhlIHVzZXIuXG4gICAgICpcbiAgICAgKiBJZiB0aGUgcmVxdWVzdCBpcyBzdWNjZXNzZnVsLCBhIHNlc3Npb24gZm9yIHRoZSB1c2VyIGlzIGF1dG9tYXRpY2FsbHkgY3JlYXRlZC5cbiAgICAgKlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IHRlYW1JZFxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBtZW1iZXJzaGlwSWRcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gdXNlcklkXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IHNlY3JldFxuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPE1vZGVscy5NZW1iZXJzaGlwPn1cbiAgICAgKi9cbiAgICB1cGRhdGVNZW1iZXJzaGlwU3RhdHVzKHRlYW1JZCwgbWVtYmVyc2hpcElkLCB1c2VySWQsIHNlY3JldCkge1xuICAgICAgICBpZiAodHlwZW9mIHRlYW1JZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwidGVhbUlkXCInKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIG1lbWJlcnNoaXBJZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwibWVtYmVyc2hpcElkXCInKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIHVzZXJJZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwidXNlcklkXCInKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIHNlY3JldCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwic2VjcmV0XCInKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy90ZWFtcy97dGVhbUlkfS9tZW1iZXJzaGlwcy97bWVtYmVyc2hpcElkfS9zdGF0dXMnLnJlcGxhY2UoJ3t0ZWFtSWR9JywgdGVhbUlkKS5yZXBsYWNlKCd7bWVtYmVyc2hpcElkfScsIG1lbWJlcnNoaXBJZCk7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgaWYgKHR5cGVvZiB1c2VySWQgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWyd1c2VySWQnXSA9IHVzZXJJZDtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIHNlY3JldCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3NlY3JldCddID0gc2VjcmV0O1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7XG4gICAgICAgICAgICAnY29udGVudC10eXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgncGF0Y2gnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBHZXQgdGhlIHRlYW0mIzAzOTtzIHNoYXJlZCBwcmVmZXJlbmNlcyBieSBpdHMgdW5pcXVlIElELiBJZiBhIHByZWZlcmVuY2UgZG9lc24mIzAzOTt0IG5lZWQgdG8gYmUgc2hhcmVkIGJ5IGFsbCB0ZWFtIG1lbWJlcnMsIHByZWZlciBzdG9yaW5nIHRoZW0gaW4gW3VzZXIgcHJlZmVyZW5jZXNdKGh0dHBzOi8vYXBwd3JpdGUuaW8vZG9jcy9yZWZlcmVuY2VzL2Nsb3VkL2NsaWVudC13ZWIvYWNjb3VudCNnZXRQcmVmcykuXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gdGVhbUlkXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8UHJlZmVyZW5jZXM+fVxuICAgICAqL1xuICAgIGdldFByZWZzKHRlYW1JZCkge1xuICAgICAgICBpZiAodHlwZW9mIHRlYW1JZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwidGVhbUlkXCInKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy90ZWFtcy97dGVhbUlkfS9wcmVmcycucmVwbGFjZSgne3RlYW1JZH0nLCB0ZWFtSWQpO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7fTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ2dldCcsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFVwZGF0ZSB0aGUgdGVhbSYjMDM5O3MgcHJlZmVyZW5jZXMgYnkgaXRzIHVuaXF1ZSBJRC4gVGhlIG9iamVjdCB5b3UgcGFzcyBpcyBzdG9yZWQgYXMgaXMgYW5kIHJlcGxhY2VzIGFueSBwcmV2aW91cyB2YWx1ZS4gVGhlIG1heGltdW0gYWxsb3dlZCBwcmVmcyBzaXplIGlzIDY0a0IgYW5kIHRocm93cyBhbiBlcnJvciBpZiBleGNlZWRlZC5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSB0ZWFtSWRcbiAgICAgKiBAcGFyYW0ge29iamVjdH0gcHJlZnNcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTxQcmVmZXJlbmNlcz59XG4gICAgICovXG4gICAgdXBkYXRlUHJlZnModGVhbUlkLCBwcmVmcykge1xuICAgICAgICBpZiAodHlwZW9mIHRlYW1JZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwidGVhbUlkXCInKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIHByZWZzID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJwcmVmc1wiJyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvdGVhbXMve3RlYW1JZH0vcHJlZnMnLnJlcGxhY2UoJ3t0ZWFtSWR9JywgdGVhbUlkKTtcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBpZiAodHlwZW9mIHByZWZzICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsncHJlZnMnXSA9IHByZWZzO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7XG4gICAgICAgICAgICAnY29udGVudC10eXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgncHV0JywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG59XG5cbi8qKlxuICogSGVscGVyIGNsYXNzIHRvIGdlbmVyYXRlIHBlcm1pc3Npb24gc3RyaW5ncyBmb3IgcmVzb3VyY2VzLlxuICovXG5jbGFzcyBQZXJtaXNzaW9uIHtcbn1cbi8qKlxuICogR2VuZXJhdGUgcmVhZCBwZXJtaXNzaW9uIHN0cmluZyBmb3IgdGhlIHByb3ZpZGVkIHJvbGUuXG4gKlxuICogQHBhcmFtIHtzdHJpbmd9IHJvbGVcbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKi9cblBlcm1pc3Npb24ucmVhZCA9IChyb2xlKSA9PiB7XG4gICAgcmV0dXJuIGByZWFkKFwiJHtyb2xlfVwiKWA7XG59O1xuLyoqXG4gKiBHZW5lcmF0ZSB3cml0ZSBwZXJtaXNzaW9uIHN0cmluZyBmb3IgdGhlIHByb3ZpZGVkIHJvbGUuXG4gKlxuICogVGhpcyBpcyBhbiBhbGlhcyBvZiB1cGRhdGUsIGRlbGV0ZSwgYW5kIHBvc3NpYmx5IGNyZWF0ZS5cbiAqIERvbid0IHVzZSB3cml0ZSBpbiBjb21iaW5hdGlvbiB3aXRoIHVwZGF0ZSwgZGVsZXRlLCBvciBjcmVhdGUuXG4gKlxuICogQHBhcmFtIHtzdHJpbmd9IHJvbGVcbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKi9cblBlcm1pc3Npb24ud3JpdGUgPSAocm9sZSkgPT4ge1xuICAgIHJldHVybiBgd3JpdGUoXCIke3JvbGV9XCIpYDtcbn07XG4vKipcbiAqIEdlbmVyYXRlIGNyZWF0ZSBwZXJtaXNzaW9uIHN0cmluZyBmb3IgdGhlIHByb3ZpZGVkIHJvbGUuXG4gKlxuICogQHBhcmFtIHtzdHJpbmd9IHJvbGVcbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKi9cblBlcm1pc3Npb24uY3JlYXRlID0gKHJvbGUpID0+IHtcbiAgICByZXR1cm4gYGNyZWF0ZShcIiR7cm9sZX1cIilgO1xufTtcbi8qKlxuICogR2VuZXJhdGUgdXBkYXRlIHBlcm1pc3Npb24gc3RyaW5nIGZvciB0aGUgcHJvdmlkZWQgcm9sZS5cbiAqXG4gKiBAcGFyYW0ge3N0cmluZ30gcm9sZVxuICogQHJldHVybnMge3N0cmluZ31cbiAqL1xuUGVybWlzc2lvbi51cGRhdGUgPSAocm9sZSkgPT4ge1xuICAgIHJldHVybiBgdXBkYXRlKFwiJHtyb2xlfVwiKWA7XG59O1xuLyoqXG4gKiBHZW5lcmF0ZSBkZWxldGUgcGVybWlzc2lvbiBzdHJpbmcgZm9yIHRoZSBwcm92aWRlZCByb2xlLlxuICpcbiAqIEBwYXJhbSB7c3RyaW5nfSByb2xlXG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICovXG5QZXJtaXNzaW9uLmRlbGV0ZSA9IChyb2xlKSA9PiB7XG4gICAgcmV0dXJuIGBkZWxldGUoXCIke3JvbGV9XCIpYDtcbn07XG5cbi8qKlxuICogSGVscGVyIGNsYXNzIHRvIGdlbmVyYXRlIHJvbGUgc3RyaW5ncyBmb3IgYFBlcm1pc3Npb25gLlxuICovXG5jbGFzcyBSb2xlIHtcbiAgICAvKipcbiAgICAgKiBHcmFudHMgYWNjZXNzIHRvIGFueW9uZS5cbiAgICAgKlxuICAgICAqIFRoaXMgaW5jbHVkZXMgYXV0aGVudGljYXRlZCBhbmQgdW5hdXRoZW50aWNhdGVkIHVzZXJzLlxuICAgICAqXG4gICAgICogQHJldHVybnMge3N0cmluZ31cbiAgICAgKi9cbiAgICBzdGF0aWMgYW55KCkge1xuICAgICAgICByZXR1cm4gJ2FueSc7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIEdyYW50cyBhY2Nlc3MgdG8gYSBzcGVjaWZpYyB1c2VyIGJ5IHVzZXIgSUQuXG4gICAgICpcbiAgICAgKiBZb3UgY2FuIG9wdGlvbmFsbHkgcGFzcyB2ZXJpZmllZCBvciB1bnZlcmlmaWVkIGZvclxuICAgICAqIGBzdGF0dXNgIHRvIHRhcmdldCBzcGVjaWZpYyB0eXBlcyBvZiB1c2Vycy5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBpZFxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBzdGF0dXNcbiAgICAgKiBAcmV0dXJucyB7c3RyaW5nfVxuICAgICAqL1xuICAgIHN0YXRpYyB1c2VyKGlkLCBzdGF0dXMgPSAnJykge1xuICAgICAgICBpZiAoc3RhdHVzID09PSAnJykge1xuICAgICAgICAgICAgcmV0dXJuIGB1c2VyOiR7aWR9YDtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gYHVzZXI6JHtpZH0vJHtzdGF0dXN9YDtcbiAgICB9XG4gICAgLyoqXG4gICAgICogR3JhbnRzIGFjY2VzcyB0byBhbnkgYXV0aGVudGljYXRlZCBvciBhbm9ueW1vdXMgdXNlci5cbiAgICAgKlxuICAgICAqIFlvdSBjYW4gb3B0aW9uYWxseSBwYXNzIHZlcmlmaWVkIG9yIHVudmVyaWZpZWQgZm9yXG4gICAgICogYHN0YXR1c2AgdG8gdGFyZ2V0IHNwZWNpZmljIHR5cGVzIG9mIHVzZXJzLlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IHN0YXR1c1xuICAgICAqIEByZXR1cm5zIHtzdHJpbmd9XG4gICAgICovXG4gICAgc3RhdGljIHVzZXJzKHN0YXR1cyA9ICcnKSB7XG4gICAgICAgIGlmIChzdGF0dXMgPT09ICcnKSB7XG4gICAgICAgICAgICByZXR1cm4gJ3VzZXJzJztcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gYHVzZXJzLyR7c3RhdHVzfWA7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIEdyYW50cyBhY2Nlc3MgdG8gYW55IGd1ZXN0IHVzZXIgd2l0aG91dCBhIHNlc3Npb24uXG4gICAgICpcbiAgICAgKiBBdXRoZW50aWNhdGVkIHVzZXJzIGRvbid0IGhhdmUgYWNjZXNzIHRvIHRoaXMgcm9sZS5cbiAgICAgKlxuICAgICAqIEByZXR1cm5zIHtzdHJpbmd9XG4gICAgICovXG4gICAgc3RhdGljIGd1ZXN0cygpIHtcbiAgICAgICAgcmV0dXJuICdndWVzdHMnO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBHcmFudHMgYWNjZXNzIHRvIGEgdGVhbSBieSB0ZWFtIElELlxuICAgICAqXG4gICAgICogWW91IGNhbiBvcHRpb25hbGx5IHBhc3MgYSByb2xlIGZvciBgcm9sZWAgdG8gdGFyZ2V0XG4gICAgICogdGVhbSBtZW1iZXJzIHdpdGggdGhlIHNwZWNpZmllZCByb2xlLlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGlkXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IHJvbGVcbiAgICAgKiBAcmV0dXJucyB7c3RyaW5nfVxuICAgICAqL1xuICAgIHN0YXRpYyB0ZWFtKGlkLCByb2xlID0gJycpIHtcbiAgICAgICAgaWYgKHJvbGUgPT09ICcnKSB7XG4gICAgICAgICAgICByZXR1cm4gYHRlYW06JHtpZH1gO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiBgdGVhbToke2lkfS8ke3JvbGV9YDtcbiAgICB9XG4gICAgLyoqXG4gICAgICogR3JhbnRzIGFjY2VzcyB0byBhIHNwZWNpZmljIG1lbWJlciBvZiBhIHRlYW0uXG4gICAgICpcbiAgICAgKiBXaGVuIHRoZSBtZW1iZXIgaXMgcmVtb3ZlZCBmcm9tIHRoZSB0ZWFtLCB0aGV5IHdpbGxcbiAgICAgKiBubyBsb25nZXIgaGF2ZSBhY2Nlc3MuXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gaWRcbiAgICAgKiBAcmV0dXJucyB7c3RyaW5nfVxuICAgICAqL1xuICAgIHN0YXRpYyBtZW1iZXIoaWQpIHtcbiAgICAgICAgcmV0dXJuIGBtZW1iZXI6JHtpZH1gO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBHcmFudHMgYWNjZXNzIHRvIGEgdXNlciB3aXRoIHRoZSBzcGVjaWZpZWQgbGFiZWwuXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gbmFtZVxuICAgICAqIEByZXR1cm5zICB7c3RyaW5nfVxuICAgICAqL1xuICAgIHN0YXRpYyBsYWJlbChuYW1lKSB7XG4gICAgICAgIHJldHVybiBgbGFiZWw6JHtuYW1lfWA7XG4gICAgfVxufVxuXG52YXIgX2EsIF9JRF9oZXhUaW1lc3RhbXA7XG4vKipcbiAqIEhlbHBlciBjbGFzcyB0byBnZW5lcmF0ZSBJRCBzdHJpbmdzIGZvciByZXNvdXJjZXMuXG4gKi9cbmNsYXNzIElEIHtcbiAgICAvKipcbiAgICAgKiBVc2VzIHRoZSBwcm92aWRlZCBJRCBhcyB0aGUgSUQgZm9yIHRoZSByZXNvdXJjZS5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBpZFxuICAgICAqIEByZXR1cm5zIHtzdHJpbmd9XG4gICAgICovXG4gICAgc3RhdGljIGN1c3RvbShpZCkge1xuICAgICAgICByZXR1cm4gaWQ7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIEhhdmUgQXBwd3JpdGUgZ2VuZXJhdGUgYSB1bmlxdWUgSUQgZm9yIHlvdS5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7bnVtYmVyfSBwYWRkaW5nLiBEZWZhdWx0IGlzIDcuXG4gICAgICogQHJldHVybnMge3N0cmluZ31cbiAgICAgKi9cbiAgICBzdGF0aWMgdW5pcXVlKHBhZGRpbmcgPSA3KSB7XG4gICAgICAgIC8vIEdlbmVyYXRlIGEgdW5pcXVlIElEIHdpdGggcGFkZGluZyB0byBoYXZlIGEgbG9uZ2VyIElEXG4gICAgICAgIGNvbnN0IGJhc2VJZCA9IF9fY2xhc3NQcml2YXRlRmllbGRHZXQoSUQsIF9hLCBcIm1cIiwgX0lEX2hleFRpbWVzdGFtcCkuY2FsbChJRCk7XG4gICAgICAgIGxldCByYW5kb21QYWRkaW5nID0gJyc7XG4gICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgcGFkZGluZzsgaSsrKSB7XG4gICAgICAgICAgICBjb25zdCByYW5kb21IZXhEaWdpdCA9IE1hdGguZmxvb3IoTWF0aC5yYW5kb20oKSAqIDE2KS50b1N0cmluZygxNik7XG4gICAgICAgICAgICByYW5kb21QYWRkaW5nICs9IHJhbmRvbUhleERpZ2l0O1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiBiYXNlSWQgKyByYW5kb21QYWRkaW5nO1xuICAgIH1cbn1cbl9hID0gSUQsIF9JRF9oZXhUaW1lc3RhbXAgPSBmdW5jdGlvbiBfSURfaGV4VGltZXN0YW1wKCkge1xuICAgIGNvbnN0IG5vdyA9IG5ldyBEYXRlKCk7XG4gICAgY29uc3Qgc2VjID0gTWF0aC5mbG9vcihub3cuZ2V0VGltZSgpIC8gMTAwMCk7XG4gICAgY29uc3QgbXNlYyA9IG5vdy5nZXRNaWxsaXNlY29uZHMoKTtcbiAgICAvLyBDb252ZXJ0IHRvIGhleGFkZWNpbWFsXG4gICAgY29uc3QgaGV4VGltZXN0YW1wID0gc2VjLnRvU3RyaW5nKDE2KSArIG1zZWMudG9TdHJpbmcoMTYpLnBhZFN0YXJ0KDUsICcwJyk7XG4gICAgcmV0dXJuIGhleFRpbWVzdGFtcDtcbn07XG5cbnZhciBBdXRoZW50aWNhdG9yVHlwZTtcbihmdW5jdGlvbiAoQXV0aGVudGljYXRvclR5cGUpIHtcbiAgICBBdXRoZW50aWNhdG9yVHlwZVtcIlRvdHBcIl0gPSBcInRvdHBcIjtcbn0pKEF1dGhlbnRpY2F0b3JUeXBlIHx8IChBdXRoZW50aWNhdG9yVHlwZSA9IHt9KSk7XG5cbnZhciBBdXRoZW50aWNhdGlvbkZhY3RvcjtcbihmdW5jdGlvbiAoQXV0aGVudGljYXRpb25GYWN0b3IpIHtcbiAgICBBdXRoZW50aWNhdGlvbkZhY3RvcltcIkVtYWlsXCJdID0gXCJlbWFpbFwiO1xuICAgIEF1dGhlbnRpY2F0aW9uRmFjdG9yW1wiUGhvbmVcIl0gPSBcInBob25lXCI7XG4gICAgQXV0aGVudGljYXRpb25GYWN0b3JbXCJUb3RwXCJdID0gXCJ0b3RwXCI7XG4gICAgQXV0aGVudGljYXRpb25GYWN0b3JbXCJSZWNvdmVyeWNvZGVcIl0gPSBcInJlY292ZXJ5Y29kZVwiO1xufSkoQXV0aGVudGljYXRpb25GYWN0b3IgfHwgKEF1dGhlbnRpY2F0aW9uRmFjdG9yID0ge30pKTtcblxudmFyIE9BdXRoUHJvdmlkZXI7XG4oZnVuY3Rpb24gKE9BdXRoUHJvdmlkZXIpIHtcbiAgICBPQXV0aFByb3ZpZGVyW1wiQW1hem9uXCJdID0gXCJhbWF6b25cIjtcbiAgICBPQXV0aFByb3ZpZGVyW1wiQXBwbGVcIl0gPSBcImFwcGxlXCI7XG4gICAgT0F1dGhQcm92aWRlcltcIkF1dGgwXCJdID0gXCJhdXRoMFwiO1xuICAgIE9BdXRoUHJvdmlkZXJbXCJBdXRoZW50aWtcIl0gPSBcImF1dGhlbnRpa1wiO1xuICAgIE9BdXRoUHJvdmlkZXJbXCJBdXRvZGVza1wiXSA9IFwiYXV0b2Rlc2tcIjtcbiAgICBPQXV0aFByb3ZpZGVyW1wiQml0YnVja2V0XCJdID0gXCJiaXRidWNrZXRcIjtcbiAgICBPQXV0aFByb3ZpZGVyW1wiQml0bHlcIl0gPSBcImJpdGx5XCI7XG4gICAgT0F1dGhQcm92aWRlcltcIkJveFwiXSA9IFwiYm94XCI7XG4gICAgT0F1dGhQcm92aWRlcltcIkRhaWx5bW90aW9uXCJdID0gXCJkYWlseW1vdGlvblwiO1xuICAgIE9BdXRoUHJvdmlkZXJbXCJEaXNjb3JkXCJdID0gXCJkaXNjb3JkXCI7XG4gICAgT0F1dGhQcm92aWRlcltcIkRpc3F1c1wiXSA9IFwiZGlzcXVzXCI7XG4gICAgT0F1dGhQcm92aWRlcltcIkRyb3Bib3hcIl0gPSBcImRyb3Bib3hcIjtcbiAgICBPQXV0aFByb3ZpZGVyW1wiRXRzeVwiXSA9IFwiZXRzeVwiO1xuICAgIE9BdXRoUHJvdmlkZXJbXCJGYWNlYm9va1wiXSA9IFwiZmFjZWJvb2tcIjtcbiAgICBPQXV0aFByb3ZpZGVyW1wiRmlnbWFcIl0gPSBcImZpZ21hXCI7XG4gICAgT0F1dGhQcm92aWRlcltcIkdpdGh1YlwiXSA9IFwiZ2l0aHViXCI7XG4gICAgT0F1dGhQcm92aWRlcltcIkdpdGxhYlwiXSA9IFwiZ2l0bGFiXCI7XG4gICAgT0F1dGhQcm92aWRlcltcIkdvb2dsZVwiXSA9IFwiZ29vZ2xlXCI7XG4gICAgT0F1dGhQcm92aWRlcltcIkxpbmtlZGluXCJdID0gXCJsaW5rZWRpblwiO1xuICAgIE9BdXRoUHJvdmlkZXJbXCJNaWNyb3NvZnRcIl0gPSBcIm1pY3Jvc29mdFwiO1xuICAgIE9BdXRoUHJvdmlkZXJbXCJOb3Rpb25cIl0gPSBcIm5vdGlvblwiO1xuICAgIE9BdXRoUHJvdmlkZXJbXCJPaWRjXCJdID0gXCJvaWRjXCI7XG4gICAgT0F1dGhQcm92aWRlcltcIk9rdGFcIl0gPSBcIm9rdGFcIjtcbiAgICBPQXV0aFByb3ZpZGVyW1wiUGF5cGFsXCJdID0gXCJwYXlwYWxcIjtcbiAgICBPQXV0aFByb3ZpZGVyW1wiUGF5cGFsU2FuZGJveFwiXSA9IFwicGF5cGFsU2FuZGJveFwiO1xuICAgIE9BdXRoUHJvdmlkZXJbXCJQb2Rpb1wiXSA9IFwicG9kaW9cIjtcbiAgICBPQXV0aFByb3ZpZGVyW1wiU2FsZXNmb3JjZVwiXSA9IFwic2FsZXNmb3JjZVwiO1xuICAgIE9BdXRoUHJvdmlkZXJbXCJTbGFja1wiXSA9IFwic2xhY2tcIjtcbiAgICBPQXV0aFByb3ZpZGVyW1wiU3BvdGlmeVwiXSA9IFwic3BvdGlmeVwiO1xuICAgIE9BdXRoUHJvdmlkZXJbXCJTdHJpcGVcIl0gPSBcInN0cmlwZVwiO1xuICAgIE9BdXRoUHJvdmlkZXJbXCJUcmFkZXNoaWZ0XCJdID0gXCJ0cmFkZXNoaWZ0XCI7XG4gICAgT0F1dGhQcm92aWRlcltcIlRyYWRlc2hpZnRCb3hcIl0gPSBcInRyYWRlc2hpZnRCb3hcIjtcbiAgICBPQXV0aFByb3ZpZGVyW1wiVHdpdGNoXCJdID0gXCJ0d2l0Y2hcIjtcbiAgICBPQXV0aFByb3ZpZGVyW1wiV29yZHByZXNzXCJdID0gXCJ3b3JkcHJlc3NcIjtcbiAgICBPQXV0aFByb3ZpZGVyW1wiWWFob29cIl0gPSBcInlhaG9vXCI7XG4gICAgT0F1dGhQcm92aWRlcltcIllhbW1lclwiXSA9IFwieWFtbWVyXCI7XG4gICAgT0F1dGhQcm92aWRlcltcIllhbmRleFwiXSA9IFwieWFuZGV4XCI7XG4gICAgT0F1dGhQcm92aWRlcltcIlpvaG9cIl0gPSBcInpvaG9cIjtcbiAgICBPQXV0aFByb3ZpZGVyW1wiWm9vbVwiXSA9IFwiem9vbVwiO1xuICAgIE9BdXRoUHJvdmlkZXJbXCJNb2NrXCJdID0gXCJtb2NrXCI7XG59KShPQXV0aFByb3ZpZGVyIHx8IChPQXV0aFByb3ZpZGVyID0ge30pKTtcblxudmFyIEJyb3dzZXI7XG4oZnVuY3Rpb24gKEJyb3dzZXIpIHtcbiAgICBCcm93c2VyW1wiQXZhbnRCcm93c2VyXCJdID0gXCJhYVwiO1xuICAgIEJyb3dzZXJbXCJBbmRyb2lkV2ViVmlld0JldGFcIl0gPSBcImFuXCI7XG4gICAgQnJvd3NlcltcIkdvb2dsZUNocm9tZVwiXSA9IFwiY2hcIjtcbiAgICBCcm93c2VyW1wiR29vZ2xlQ2hyb21lSU9TXCJdID0gXCJjaVwiO1xuICAgIEJyb3dzZXJbXCJHb29nbGVDaHJvbWVNb2JpbGVcIl0gPSBcImNtXCI7XG4gICAgQnJvd3NlcltcIkNocm9taXVtXCJdID0gXCJjclwiO1xuICAgIEJyb3dzZXJbXCJNb3ppbGxhRmlyZWZveFwiXSA9IFwiZmZcIjtcbiAgICBCcm93c2VyW1wiU2FmYXJpXCJdID0gXCJzZlwiO1xuICAgIEJyb3dzZXJbXCJNb2JpbGVTYWZhcmlcIl0gPSBcIm1mXCI7XG4gICAgQnJvd3NlcltcIk1pY3Jvc29mdEVkZ2VcIl0gPSBcInBzXCI7XG4gICAgQnJvd3NlcltcIk1pY3Jvc29mdEVkZ2VJT1NcIl0gPSBcIm9pXCI7XG4gICAgQnJvd3NlcltcIk9wZXJhTWluaVwiXSA9IFwib21cIjtcbiAgICBCcm93c2VyW1wiT3BlcmFcIl0gPSBcIm9wXCI7XG4gICAgQnJvd3NlcltcIk9wZXJhTmV4dFwiXSA9IFwib25cIjtcbn0pKEJyb3dzZXIgfHwgKEJyb3dzZXIgPSB7fSkpO1xuXG52YXIgQ3JlZGl0Q2FyZDtcbihmdW5jdGlvbiAoQ3JlZGl0Q2FyZCkge1xuICAgIENyZWRpdENhcmRbXCJBbWVyaWNhbkV4cHJlc3NcIl0gPSBcImFtZXhcIjtcbiAgICBDcmVkaXRDYXJkW1wiQXJnZW5jYXJkXCJdID0gXCJhcmdlbmNhcmRcIjtcbiAgICBDcmVkaXRDYXJkW1wiQ2FiYWxcIl0gPSBcImNhYmFsXCI7XG4gICAgQ3JlZGl0Q2FyZFtcIkNlbmNvc3VkXCJdID0gXCJjZW5jb3N1ZFwiO1xuICAgIENyZWRpdENhcmRbXCJEaW5lcnNDbHViXCJdID0gXCJkaW5lcnNcIjtcbiAgICBDcmVkaXRDYXJkW1wiRGlzY292ZXJcIl0gPSBcImRpc2NvdmVyXCI7XG4gICAgQ3JlZGl0Q2FyZFtcIkVsb1wiXSA9IFwiZWxvXCI7XG4gICAgQ3JlZGl0Q2FyZFtcIkhpcGVyY2FyZFwiXSA9IFwiaGlwZXJjYXJkXCI7XG4gICAgQ3JlZGl0Q2FyZFtcIkpDQlwiXSA9IFwiamNiXCI7XG4gICAgQ3JlZGl0Q2FyZFtcIk1hc3RlcmNhcmRcIl0gPSBcIm1hc3RlcmNhcmRcIjtcbiAgICBDcmVkaXRDYXJkW1wiTmFyYW5qYVwiXSA9IFwibmFyYW5qYVwiO1xuICAgIENyZWRpdENhcmRbXCJUYXJqZXRhU2hvcHBpbmdcIl0gPSBcInRhcmdldGEtc2hvcHBpbmdcIjtcbiAgICBDcmVkaXRDYXJkW1wiVW5pb25DaGluYVBheVwiXSA9IFwidW5pb24tY2hpbmEtcGF5XCI7XG4gICAgQ3JlZGl0Q2FyZFtcIlZpc2FcIl0gPSBcInZpc2FcIjtcbiAgICBDcmVkaXRDYXJkW1wiTUlSXCJdID0gXCJtaXJcIjtcbiAgICBDcmVkaXRDYXJkW1wiTWFlc3Ryb1wiXSA9IFwibWFlc3Ryb1wiO1xuICAgIENyZWRpdENhcmRbXCJSdXBheVwiXSA9IFwicnVwYXlcIjtcbn0pKENyZWRpdENhcmQgfHwgKENyZWRpdENhcmQgPSB7fSkpO1xuXG52YXIgRmxhZztcbihmdW5jdGlvbiAoRmxhZykge1xuICAgIEZsYWdbXCJBZmdoYW5pc3RhblwiXSA9IFwiYWZcIjtcbiAgICBGbGFnW1wiQW5nb2xhXCJdID0gXCJhb1wiO1xuICAgIEZsYWdbXCJBbGJhbmlhXCJdID0gXCJhbFwiO1xuICAgIEZsYWdbXCJBbmRvcnJhXCJdID0gXCJhZFwiO1xuICAgIEZsYWdbXCJVbml0ZWRBcmFiRW1pcmF0ZXNcIl0gPSBcImFlXCI7XG4gICAgRmxhZ1tcIkFyZ2VudGluYVwiXSA9IFwiYXJcIjtcbiAgICBGbGFnW1wiQXJtZW5pYVwiXSA9IFwiYW1cIjtcbiAgICBGbGFnW1wiQW50aWd1YUFuZEJhcmJ1ZGFcIl0gPSBcImFnXCI7XG4gICAgRmxhZ1tcIkF1c3RyYWxpYVwiXSA9IFwiYXVcIjtcbiAgICBGbGFnW1wiQXVzdHJpYVwiXSA9IFwiYXRcIjtcbiAgICBGbGFnW1wiQXplcmJhaWphblwiXSA9IFwiYXpcIjtcbiAgICBGbGFnW1wiQnVydW5kaVwiXSA9IFwiYmlcIjtcbiAgICBGbGFnW1wiQmVsZ2l1bVwiXSA9IFwiYmVcIjtcbiAgICBGbGFnW1wiQmVuaW5cIl0gPSBcImJqXCI7XG4gICAgRmxhZ1tcIkJ1cmtpbmFGYXNvXCJdID0gXCJiZlwiO1xuICAgIEZsYWdbXCJCYW5nbGFkZXNoXCJdID0gXCJiZFwiO1xuICAgIEZsYWdbXCJCdWxnYXJpYVwiXSA9IFwiYmdcIjtcbiAgICBGbGFnW1wiQmFocmFpblwiXSA9IFwiYmhcIjtcbiAgICBGbGFnW1wiQmFoYW1hc1wiXSA9IFwiYnNcIjtcbiAgICBGbGFnW1wiQm9zbmlhQW5kSGVyemVnb3ZpbmFcIl0gPSBcImJhXCI7XG4gICAgRmxhZ1tcIkJlbGFydXNcIl0gPSBcImJ5XCI7XG4gICAgRmxhZ1tcIkJlbGl6ZVwiXSA9IFwiYnpcIjtcbiAgICBGbGFnW1wiQm9saXZpYVwiXSA9IFwiYm9cIjtcbiAgICBGbGFnW1wiQnJhemlsXCJdID0gXCJiclwiO1xuICAgIEZsYWdbXCJCYXJiYWRvc1wiXSA9IFwiYmJcIjtcbiAgICBGbGFnW1wiQnJ1bmVpRGFydXNzYWxhbVwiXSA9IFwiYm5cIjtcbiAgICBGbGFnW1wiQmh1dGFuXCJdID0gXCJidFwiO1xuICAgIEZsYWdbXCJCb3Rzd2FuYVwiXSA9IFwiYndcIjtcbiAgICBGbGFnW1wiQ2VudHJhbEFmcmljYW5SZXB1YmxpY1wiXSA9IFwiY2ZcIjtcbiAgICBGbGFnW1wiQ2FuYWRhXCJdID0gXCJjYVwiO1xuICAgIEZsYWdbXCJTd2l0emVybGFuZFwiXSA9IFwiY2hcIjtcbiAgICBGbGFnW1wiQ2hpbGVcIl0gPSBcImNsXCI7XG4gICAgRmxhZ1tcIkNoaW5hXCJdID0gXCJjblwiO1xuICAgIEZsYWdbXCJDb3RlREl2b2lyZVwiXSA9IFwiY2lcIjtcbiAgICBGbGFnW1wiQ2FtZXJvb25cIl0gPSBcImNtXCI7XG4gICAgRmxhZ1tcIkRlbW9jcmF0aWNSZXB1YmxpY09mVGhlQ29uZ29cIl0gPSBcImNkXCI7XG4gICAgRmxhZ1tcIlJlcHVibGljT2ZUaGVDb25nb1wiXSA9IFwiY2dcIjtcbiAgICBGbGFnW1wiQ29sb21iaWFcIl0gPSBcImNvXCI7XG4gICAgRmxhZ1tcIkNvbW9yb3NcIl0gPSBcImttXCI7XG4gICAgRmxhZ1tcIkNhcGVWZXJkZVwiXSA9IFwiY3ZcIjtcbiAgICBGbGFnW1wiQ29zdGFSaWNhXCJdID0gXCJjclwiO1xuICAgIEZsYWdbXCJDdWJhXCJdID0gXCJjdVwiO1xuICAgIEZsYWdbXCJDeXBydXNcIl0gPSBcImN5XCI7XG4gICAgRmxhZ1tcIkN6ZWNoUmVwdWJsaWNcIl0gPSBcImN6XCI7XG4gICAgRmxhZ1tcIkdlcm1hbnlcIl0gPSBcImRlXCI7XG4gICAgRmxhZ1tcIkRqaWJvdXRpXCJdID0gXCJkalwiO1xuICAgIEZsYWdbXCJEb21pbmljYVwiXSA9IFwiZG1cIjtcbiAgICBGbGFnW1wiRGVubWFya1wiXSA9IFwiZGtcIjtcbiAgICBGbGFnW1wiRG9taW5pY2FuUmVwdWJsaWNcIl0gPSBcImRvXCI7XG4gICAgRmxhZ1tcIkFsZ2VyaWFcIl0gPSBcImR6XCI7XG4gICAgRmxhZ1tcIkVjdWFkb3JcIl0gPSBcImVjXCI7XG4gICAgRmxhZ1tcIkVneXB0XCJdID0gXCJlZ1wiO1xuICAgIEZsYWdbXCJFcml0cmVhXCJdID0gXCJlclwiO1xuICAgIEZsYWdbXCJTcGFpblwiXSA9IFwiZXNcIjtcbiAgICBGbGFnW1wiRXN0b25pYVwiXSA9IFwiZWVcIjtcbiAgICBGbGFnW1wiRXRoaW9waWFcIl0gPSBcImV0XCI7XG4gICAgRmxhZ1tcIkZpbmxhbmRcIl0gPSBcImZpXCI7XG4gICAgRmxhZ1tcIkZpamlcIl0gPSBcImZqXCI7XG4gICAgRmxhZ1tcIkZyYW5jZVwiXSA9IFwiZnJcIjtcbiAgICBGbGFnW1wiTWljcm9uZXNpYUZlZGVyYXRlZFN0YXRlc09mXCJdID0gXCJmbVwiO1xuICAgIEZsYWdbXCJHYWJvblwiXSA9IFwiZ2FcIjtcbiAgICBGbGFnW1wiVW5pdGVkS2luZ2RvbVwiXSA9IFwiZ2JcIjtcbiAgICBGbGFnW1wiR2VvcmdpYVwiXSA9IFwiZ2VcIjtcbiAgICBGbGFnW1wiR2hhbmFcIl0gPSBcImdoXCI7XG4gICAgRmxhZ1tcIkd1aW5lYVwiXSA9IFwiZ25cIjtcbiAgICBGbGFnW1wiR2FtYmlhXCJdID0gXCJnbVwiO1xuICAgIEZsYWdbXCJHdWluZWFCaXNzYXVcIl0gPSBcImd3XCI7XG4gICAgRmxhZ1tcIkVxdWF0b3JpYWxHdWluZWFcIl0gPSBcImdxXCI7XG4gICAgRmxhZ1tcIkdyZWVjZVwiXSA9IFwiZ3JcIjtcbiAgICBGbGFnW1wiR3JlbmFkYVwiXSA9IFwiZ2RcIjtcbiAgICBGbGFnW1wiR3VhdGVtYWxhXCJdID0gXCJndFwiO1xuICAgIEZsYWdbXCJHdXlhbmFcIl0gPSBcImd5XCI7XG4gICAgRmxhZ1tcIkhvbmR1cmFzXCJdID0gXCJoblwiO1xuICAgIEZsYWdbXCJDcm9hdGlhXCJdID0gXCJoclwiO1xuICAgIEZsYWdbXCJIYWl0aVwiXSA9IFwiaHRcIjtcbiAgICBGbGFnW1wiSHVuZ2FyeVwiXSA9IFwiaHVcIjtcbiAgICBGbGFnW1wiSW5kb25lc2lhXCJdID0gXCJpZFwiO1xuICAgIEZsYWdbXCJJbmRpYVwiXSA9IFwiaW5cIjtcbiAgICBGbGFnW1wiSXJlbGFuZFwiXSA9IFwiaWVcIjtcbiAgICBGbGFnW1wiSXJhbklzbGFtaWNSZXB1YmxpY09mXCJdID0gXCJpclwiO1xuICAgIEZsYWdbXCJJcmFxXCJdID0gXCJpcVwiO1xuICAgIEZsYWdbXCJJY2VsYW5kXCJdID0gXCJpc1wiO1xuICAgIEZsYWdbXCJJc3JhZWxcIl0gPSBcImlsXCI7XG4gICAgRmxhZ1tcIkl0YWx5XCJdID0gXCJpdFwiO1xuICAgIEZsYWdbXCJKYW1haWNhXCJdID0gXCJqbVwiO1xuICAgIEZsYWdbXCJKb3JkYW5cIl0gPSBcImpvXCI7XG4gICAgRmxhZ1tcIkphcGFuXCJdID0gXCJqcFwiO1xuICAgIEZsYWdbXCJLYXpha2hzdGFuXCJdID0gXCJrelwiO1xuICAgIEZsYWdbXCJLZW55YVwiXSA9IFwia2VcIjtcbiAgICBGbGFnW1wiS3lyZ3l6c3RhblwiXSA9IFwia2dcIjtcbiAgICBGbGFnW1wiQ2FtYm9kaWFcIl0gPSBcImtoXCI7XG4gICAgRmxhZ1tcIktpcmliYXRpXCJdID0gXCJraVwiO1xuICAgIEZsYWdbXCJTYWludEtpdHRzQW5kTmV2aXNcIl0gPSBcImtuXCI7XG4gICAgRmxhZ1tcIlNvdXRoS29yZWFcIl0gPSBcImtyXCI7XG4gICAgRmxhZ1tcIkt1d2FpdFwiXSA9IFwia3dcIjtcbiAgICBGbGFnW1wiTGFvUGVvcGxlU0RlbW9jcmF0aWNSZXB1YmxpY1wiXSA9IFwibGFcIjtcbiAgICBGbGFnW1wiTGViYW5vblwiXSA9IFwibGJcIjtcbiAgICBGbGFnW1wiTGliZXJpYVwiXSA9IFwibHJcIjtcbiAgICBGbGFnW1wiTGlieWFcIl0gPSBcImx5XCI7XG4gICAgRmxhZ1tcIlNhaW50THVjaWFcIl0gPSBcImxjXCI7XG4gICAgRmxhZ1tcIkxpZWNodGVuc3RlaW5cIl0gPSBcImxpXCI7XG4gICAgRmxhZ1tcIlNyaUxhbmthXCJdID0gXCJsa1wiO1xuICAgIEZsYWdbXCJMZXNvdGhvXCJdID0gXCJsc1wiO1xuICAgIEZsYWdbXCJMaXRodWFuaWFcIl0gPSBcImx0XCI7XG4gICAgRmxhZ1tcIkx1eGVtYm91cmdcIl0gPSBcImx1XCI7XG4gICAgRmxhZ1tcIkxhdHZpYVwiXSA9IFwibHZcIjtcbiAgICBGbGFnW1wiTW9yb2Njb1wiXSA9IFwibWFcIjtcbiAgICBGbGFnW1wiTW9uYWNvXCJdID0gXCJtY1wiO1xuICAgIEZsYWdbXCJNb2xkb3ZhXCJdID0gXCJtZFwiO1xuICAgIEZsYWdbXCJNYWRhZ2FzY2FyXCJdID0gXCJtZ1wiO1xuICAgIEZsYWdbXCJNYWxkaXZlc1wiXSA9IFwibXZcIjtcbiAgICBGbGFnW1wiTWV4aWNvXCJdID0gXCJteFwiO1xuICAgIEZsYWdbXCJNYXJzaGFsbElzbGFuZHNcIl0gPSBcIm1oXCI7XG4gICAgRmxhZ1tcIk5vcnRoTWFjZWRvbmlhXCJdID0gXCJta1wiO1xuICAgIEZsYWdbXCJNYWxpXCJdID0gXCJtbFwiO1xuICAgIEZsYWdbXCJNYWx0YVwiXSA9IFwibXRcIjtcbiAgICBGbGFnW1wiTXlhbm1hclwiXSA9IFwibW1cIjtcbiAgICBGbGFnW1wiTW9udGVuZWdyb1wiXSA9IFwibWVcIjtcbiAgICBGbGFnW1wiTW9uZ29saWFcIl0gPSBcIm1uXCI7XG4gICAgRmxhZ1tcIk1vemFtYmlxdWVcIl0gPSBcIm16XCI7XG4gICAgRmxhZ1tcIk1hdXJpdGFuaWFcIl0gPSBcIm1yXCI7XG4gICAgRmxhZ1tcIk1hdXJpdGl1c1wiXSA9IFwibXVcIjtcbiAgICBGbGFnW1wiTWFsYXdpXCJdID0gXCJtd1wiO1xuICAgIEZsYWdbXCJNYWxheXNpYVwiXSA9IFwibXlcIjtcbiAgICBGbGFnW1wiTmFtaWJpYVwiXSA9IFwibmFcIjtcbiAgICBGbGFnW1wiTmlnZXJcIl0gPSBcIm5lXCI7XG4gICAgRmxhZ1tcIk5pZ2VyaWFcIl0gPSBcIm5nXCI7XG4gICAgRmxhZ1tcIk5pY2FyYWd1YVwiXSA9IFwibmlcIjtcbiAgICBGbGFnW1wiTmV0aGVybGFuZHNcIl0gPSBcIm5sXCI7XG4gICAgRmxhZ1tcIk5vcndheVwiXSA9IFwibm9cIjtcbiAgICBGbGFnW1wiTmVwYWxcIl0gPSBcIm5wXCI7XG4gICAgRmxhZ1tcIk5hdXJ1XCJdID0gXCJuclwiO1xuICAgIEZsYWdbXCJOZXdaZWFsYW5kXCJdID0gXCJuelwiO1xuICAgIEZsYWdbXCJPbWFuXCJdID0gXCJvbVwiO1xuICAgIEZsYWdbXCJQYWtpc3RhblwiXSA9IFwicGtcIjtcbiAgICBGbGFnW1wiUGFuYW1hXCJdID0gXCJwYVwiO1xuICAgIEZsYWdbXCJQZXJ1XCJdID0gXCJwZVwiO1xuICAgIEZsYWdbXCJQaGlsaXBwaW5lc1wiXSA9IFwicGhcIjtcbiAgICBGbGFnW1wiUGFsYXVcIl0gPSBcInB3XCI7XG4gICAgRmxhZ1tcIlBhcHVhTmV3R3VpbmVhXCJdID0gXCJwZ1wiO1xuICAgIEZsYWdbXCJQb2xhbmRcIl0gPSBcInBsXCI7XG4gICAgRmxhZ1tcIkZyZW5jaFBvbHluZXNpYVwiXSA9IFwicGZcIjtcbiAgICBGbGFnW1wiTm9ydGhLb3JlYVwiXSA9IFwia3BcIjtcbiAgICBGbGFnW1wiUG9ydHVnYWxcIl0gPSBcInB0XCI7XG4gICAgRmxhZ1tcIlBhcmFndWF5XCJdID0gXCJweVwiO1xuICAgIEZsYWdbXCJRYXRhclwiXSA9IFwicWFcIjtcbiAgICBGbGFnW1wiUm9tYW5pYVwiXSA9IFwicm9cIjtcbiAgICBGbGFnW1wiUnVzc2lhXCJdID0gXCJydVwiO1xuICAgIEZsYWdbXCJSd2FuZGFcIl0gPSBcInJ3XCI7XG4gICAgRmxhZ1tcIlNhdWRpQXJhYmlhXCJdID0gXCJzYVwiO1xuICAgIEZsYWdbXCJTdWRhblwiXSA9IFwic2RcIjtcbiAgICBGbGFnW1wiU2VuZWdhbFwiXSA9IFwic25cIjtcbiAgICBGbGFnW1wiU2luZ2Fwb3JlXCJdID0gXCJzZ1wiO1xuICAgIEZsYWdbXCJTb2xvbW9uSXNsYW5kc1wiXSA9IFwic2JcIjtcbiAgICBGbGFnW1wiU2llcnJhTGVvbmVcIl0gPSBcInNsXCI7XG4gICAgRmxhZ1tcIkVsU2FsdmFkb3JcIl0gPSBcInN2XCI7XG4gICAgRmxhZ1tcIlNhbk1hcmlub1wiXSA9IFwic21cIjtcbiAgICBGbGFnW1wiU29tYWxpYVwiXSA9IFwic29cIjtcbiAgICBGbGFnW1wiU2VyYmlhXCJdID0gXCJyc1wiO1xuICAgIEZsYWdbXCJTb3V0aFN1ZGFuXCJdID0gXCJzc1wiO1xuICAgIEZsYWdbXCJTYW9Ub21lQW5kUHJpbmNpcGVcIl0gPSBcInN0XCI7XG4gICAgRmxhZ1tcIlN1cmluYW1lXCJdID0gXCJzclwiO1xuICAgIEZsYWdbXCJTbG92YWtpYVwiXSA9IFwic2tcIjtcbiAgICBGbGFnW1wiU2xvdmVuaWFcIl0gPSBcInNpXCI7XG4gICAgRmxhZ1tcIlN3ZWRlblwiXSA9IFwic2VcIjtcbiAgICBGbGFnW1wiRXN3YXRpbmlcIl0gPSBcInN6XCI7XG4gICAgRmxhZ1tcIlNleWNoZWxsZXNcIl0gPSBcInNjXCI7XG4gICAgRmxhZ1tcIlN5cmlhXCJdID0gXCJzeVwiO1xuICAgIEZsYWdbXCJDaGFkXCJdID0gXCJ0ZFwiO1xuICAgIEZsYWdbXCJUb2dvXCJdID0gXCJ0Z1wiO1xuICAgIEZsYWdbXCJUaGFpbGFuZFwiXSA9IFwidGhcIjtcbiAgICBGbGFnW1wiVGFqaWtpc3RhblwiXSA9IFwidGpcIjtcbiAgICBGbGFnW1wiVHVya21lbmlzdGFuXCJdID0gXCJ0bVwiO1xuICAgIEZsYWdbXCJUaW1vckxlc3RlXCJdID0gXCJ0bFwiO1xuICAgIEZsYWdbXCJUb25nYVwiXSA9IFwidG9cIjtcbiAgICBGbGFnW1wiVHJpbmlkYWRBbmRUb2JhZ29cIl0gPSBcInR0XCI7XG4gICAgRmxhZ1tcIlR1bmlzaWFcIl0gPSBcInRuXCI7XG4gICAgRmxhZ1tcIlR1cmtleVwiXSA9IFwidHJcIjtcbiAgICBGbGFnW1wiVHV2YWx1XCJdID0gXCJ0dlwiO1xuICAgIEZsYWdbXCJUYW56YW5pYVwiXSA9IFwidHpcIjtcbiAgICBGbGFnW1wiVWdhbmRhXCJdID0gXCJ1Z1wiO1xuICAgIEZsYWdbXCJVa3JhaW5lXCJdID0gXCJ1YVwiO1xuICAgIEZsYWdbXCJVcnVndWF5XCJdID0gXCJ1eVwiO1xuICAgIEZsYWdbXCJVbml0ZWRTdGF0ZXNcIl0gPSBcInVzXCI7XG4gICAgRmxhZ1tcIlV6YmVraXN0YW5cIl0gPSBcInV6XCI7XG4gICAgRmxhZ1tcIlZhdGljYW5DaXR5XCJdID0gXCJ2YVwiO1xuICAgIEZsYWdbXCJTYWludFZpbmNlbnRBbmRUaGVHcmVuYWRpbmVzXCJdID0gXCJ2Y1wiO1xuICAgIEZsYWdbXCJWZW5lenVlbGFcIl0gPSBcInZlXCI7XG4gICAgRmxhZ1tcIlZpZXRuYW1cIl0gPSBcInZuXCI7XG4gICAgRmxhZ1tcIlZhbnVhdHVcIl0gPSBcInZ1XCI7XG4gICAgRmxhZ1tcIlNhbW9hXCJdID0gXCJ3c1wiO1xuICAgIEZsYWdbXCJZZW1lblwiXSA9IFwieWVcIjtcbiAgICBGbGFnW1wiU291dGhBZnJpY2FcIl0gPSBcInphXCI7XG4gICAgRmxhZ1tcIlphbWJpYVwiXSA9IFwiem1cIjtcbiAgICBGbGFnW1wiWmltYmFid2VcIl0gPSBcInp3XCI7XG59KShGbGFnIHx8IChGbGFnID0ge30pKTtcblxudmFyIEV4ZWN1dGlvbk1ldGhvZDtcbihmdW5jdGlvbiAoRXhlY3V0aW9uTWV0aG9kKSB7XG4gICAgRXhlY3V0aW9uTWV0aG9kW1wiR0VUXCJdID0gXCJHRVRcIjtcbiAgICBFeGVjdXRpb25NZXRob2RbXCJQT1NUXCJdID0gXCJQT1NUXCI7XG4gICAgRXhlY3V0aW9uTWV0aG9kW1wiUFVUXCJdID0gXCJQVVRcIjtcbiAgICBFeGVjdXRpb25NZXRob2RbXCJQQVRDSFwiXSA9IFwiUEFUQ0hcIjtcbiAgICBFeGVjdXRpb25NZXRob2RbXCJERUxFVEVcIl0gPSBcIkRFTEVURVwiO1xuICAgIEV4ZWN1dGlvbk1ldGhvZFtcIk9QVElPTlNcIl0gPSBcIk9QVElPTlNcIjtcbn0pKEV4ZWN1dGlvbk1ldGhvZCB8fCAoRXhlY3V0aW9uTWV0aG9kID0ge30pKTtcblxudmFyIEltYWdlR3Jhdml0eTtcbihmdW5jdGlvbiAoSW1hZ2VHcmF2aXR5KSB7XG4gICAgSW1hZ2VHcmF2aXR5W1wiQ2VudGVyXCJdID0gXCJjZW50ZXJcIjtcbiAgICBJbWFnZUdyYXZpdHlbXCJUb3BsZWZ0XCJdID0gXCJ0b3AtbGVmdFwiO1xuICAgIEltYWdlR3Jhdml0eVtcIlRvcFwiXSA9IFwidG9wXCI7XG4gICAgSW1hZ2VHcmF2aXR5W1wiVG9wcmlnaHRcIl0gPSBcInRvcC1yaWdodFwiO1xuICAgIEltYWdlR3Jhdml0eVtcIkxlZnRcIl0gPSBcImxlZnRcIjtcbiAgICBJbWFnZUdyYXZpdHlbXCJSaWdodFwiXSA9IFwicmlnaHRcIjtcbiAgICBJbWFnZUdyYXZpdHlbXCJCb3R0b21sZWZ0XCJdID0gXCJib3R0b20tbGVmdFwiO1xuICAgIEltYWdlR3Jhdml0eVtcIkJvdHRvbVwiXSA9IFwiYm90dG9tXCI7XG4gICAgSW1hZ2VHcmF2aXR5W1wiQm90dG9tcmlnaHRcIl0gPSBcImJvdHRvbS1yaWdodFwiO1xufSkoSW1hZ2VHcmF2aXR5IHx8IChJbWFnZUdyYXZpdHkgPSB7fSkpO1xuXG52YXIgSW1hZ2VGb3JtYXQ7XG4oZnVuY3Rpb24gKEltYWdlRm9ybWF0KSB7XG4gICAgSW1hZ2VGb3JtYXRbXCJKcGdcIl0gPSBcImpwZ1wiO1xuICAgIEltYWdlRm9ybWF0W1wiSnBlZ1wiXSA9IFwianBlZ1wiO1xuICAgIEltYWdlRm9ybWF0W1wiUG5nXCJdID0gXCJwbmdcIjtcbiAgICBJbWFnZUZvcm1hdFtcIldlYnBcIl0gPSBcIndlYnBcIjtcbiAgICBJbWFnZUZvcm1hdFtcIkhlaWNcIl0gPSBcImhlaWNcIjtcbiAgICBJbWFnZUZvcm1hdFtcIkF2aWZcIl0gPSBcImF2aWZcIjtcbiAgICBJbWFnZUZvcm1hdFtcIkdpZlwiXSA9IFwiZ2lmXCI7XG59KShJbWFnZUZvcm1hdCB8fCAoSW1hZ2VGb3JtYXQgPSB7fSkpO1xuXG5leHBvcnQgeyBBY2NvdW50LCBBcHB3cml0ZUV4Y2VwdGlvbiwgQXV0aGVudGljYXRpb25GYWN0b3IsIEF1dGhlbnRpY2F0b3JUeXBlLCBBdmF0YXJzLCBCcm93c2VyLCBDbGllbnQsIENyZWRpdENhcmQsIERhdGFiYXNlcywgRXhlY3V0aW9uTWV0aG9kLCBGbGFnLCBGdW5jdGlvbnMsIEdyYXBocWwsIElELCBJbWFnZUZvcm1hdCwgSW1hZ2VHcmF2aXR5LCBMb2NhbGUsIE1lc3NhZ2luZywgT0F1dGhQcm92aWRlciwgUGVybWlzc2lvbiwgUXVlcnksIFJvbGUsIFN0b3JhZ2UsIFRlYW1zIH07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1zZGsuanMubWFwXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/appwrite/dist/esm/sdk.js\n"));

/***/ })

});