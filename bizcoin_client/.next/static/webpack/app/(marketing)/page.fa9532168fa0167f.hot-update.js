"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(marketing)/page",{

/***/ "(app-pages-browser)/./components/EmailVerificationModal.tsx":
/*!***********************************************!*\
  !*** ./components/EmailVerificationModal.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Dialog_Tab_headlessui_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Dialog,Tab!=!@headlessui/react */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/components/dialog/dialog.js\");\n/* harmony import */ var _barrel_optimize_names_Dialog_Tab_headlessui_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Dialog,Tab!=!@headlessui/react */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/components/tabs/tabs.js\");\n/* harmony import */ var _clerk_nextjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @clerk/nextjs */ \"(app-pages-browser)/./node_modules/@clerk/clerk-react/dist/index.mjs\");\n/* harmony import */ var convex_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! convex/react */ \"(app-pages-browser)/./node_modules/convex/dist/esm/react/index.js\");\n/* harmony import */ var _convex_generated_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/convex/_generated/api */ \"(app-pages-browser)/./convex/_generated/api.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst EmailVerificationModal = (param)=>{\n    let { isOpen, onClose, onSuccess } = param;\n    _s();\n    const { isLoaded: signUpLoaded, signUp, setActive: setSignUpActive } = (0,_clerk_nextjs__WEBPACK_IMPORTED_MODULE_4__.useSignUp)();\n    const { isLoaded: signInLoaded, signIn, setActive: setSignInActive } = (0,_clerk_nextjs__WEBPACK_IMPORTED_MODULE_4__.useSignIn)();\n    const upsertProfile = (0,convex_react__WEBPACK_IMPORTED_MODULE_2__.useMutation)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_3__.api.myFunctions.upsertUserProfile);\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [otp, setOtp] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        \"\",\n        \"\",\n        \"\",\n        \"\",\n        \"\",\n        \"\"\n    ]);\n    const [isOtpSent, setIsOtpSent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // const [showProfileCreation, setShowProfileCreation] = useState(false);\n    const [isSignIn, setIsSignIn] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedTab, setSelectedTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"signin\");\n    const otpRefs = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    const handleEmailChange = (e)=>{\n        // Remove any spaces from the input value\n        const value = e.target.value.replace(/\\s+/g, \"\");\n        setEmail(value);\n    };\n    const handleOtpChange = (index, value)=>{\n        if (value.length > 1) value = value[0];\n        const newOtp = [\n            ...otp\n        ];\n        newOtp[index] = value;\n        setOtp(newOtp);\n        if (value !== \"\" && index < 5) {\n            var _otpRefs_current_;\n            (_otpRefs_current_ = otpRefs.current[index + 1]) === null || _otpRefs_current_ === void 0 ? void 0 : _otpRefs_current_.focus();\n        }\n    };\n    const handleKeyDown = (index, e)=>{\n        if (e.key === \"Backspace\" && !otp[index] && index > 0) {\n            var _otpRefs_current_;\n            (_otpRefs_current_ = otpRefs.current[index - 1]) === null || _otpRefs_current_ === void 0 ? void 0 : _otpRefs_current_.focus();\n        }\n    };\n    const handleOtpPaste = (e)=>{\n        const paste = e.clipboardData.getData(\"text\");\n        if (/^\\d{6}$/.test(paste)) {\n            setOtp(paste.split(\"\"));\n            // Focus the last input\n            setTimeout(()=>{\n                var _otpRefs_current_;\n                (_otpRefs_current_ = otpRefs.current[5]) === null || _otpRefs_current_ === void 0 ? void 0 : _otpRefs_current_.focus();\n            }, 0);\n            e.preventDefault();\n        }\n    };\n    const handleSendOtp = async (e)=>{\n        e.preventDefault();\n        setError(\"\");\n        setLoading(true);\n        try {\n            if (selectedTab === \"signup\") {\n                if (!signUpLoaded) {\n                    setError(\"Sign up is not loaded yet\");\n                    return;\n                }\n                await signUp.create({\n                    emailAddress: email\n                });\n                await signUp.prepareEmailAddressVerification();\n                setIsSignIn(false);\n                setIsOtpSent(true);\n            } else {\n                if (!signInLoaded) {\n                    setError(\"Sign in is not loaded yet\");\n                    return;\n                }\n                const { supportedFirstFactors } = await signIn.create({\n                    identifier: email\n                });\n                const emailCodeFactor = supportedFirstFactors === null || supportedFirstFactors === void 0 ? void 0 : supportedFirstFactors.find((factor)=>typeof factor === \"object\" && factor !== null && \"strategy\" in factor && factor.strategy === \"email_code\" && \"emailAddressId\" in factor);\n                if (emailCodeFactor) {\n                    await signIn.prepareFirstFactor({\n                        strategy: \"email_code\",\n                        emailAddressId: emailCodeFactor.emailAddressId\n                    });\n                    setIsSignIn(true);\n                    setIsOtpSent(true);\n                } else {\n                    setError(\"No email code factor available. Please make sure email verification is enabled in your Clerk settings.\");\n                }\n            }\n        } catch (err) {\n            var _err_;\n            const errorMessage = err instanceof Array ? ((_err_ = err[0]) === null || _err_ === void 0 ? void 0 : _err_.message) || \"Failed to send OTP.\" : err instanceof Error ? err.message : \"Failed to send OTP.\";\n            console.error(\"Error:\", err);\n            setError(errorMessage);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleVerifyOtp = async (e)=>{\n        e.preventDefault();\n        setError(\"\");\n        setLoading(true);\n        const otpString = otp.join(\"\");\n        try {\n            if (isSignIn) {\n                if (!signInLoaded) return;\n                const attempt = await signIn.attemptFirstFactor({\n                    strategy: \"email_code\",\n                    code: otpString\n                });\n                if (attempt.status === \"complete\") {\n                    await setSignInActive({\n                        session: attempt.createdSessionId\n                    });\n                    onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess();\n                    onClose();\n                } else {\n                    console.error(\"Sign-in attempt failed:\", attempt);\n                    setError(\"Verification incomplete. Status: \".concat(attempt.status));\n                }\n            } else {\n                if (!signUpLoaded) return;\n                const attempt = await signUp.attemptEmailAddressVerification({\n                    code: otpString\n                });\n                if (attempt.status === \"complete\") {\n                    await setSignUpActive({\n                        session: attempt.createdSessionId\n                    });\n                    // Update Convex database with user profile\n                    try {\n                        await upsertProfile({\n                            email\n                        });\n                        onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess();\n                        onClose();\n                    } catch (convexError) {\n                        console.error(\"Failed to update Convex database:\", convexError);\n                        setError(\"Account created but profile update failed. Please update your profile later.\");\n                    }\n                } else {\n                    console.error(\"Sign-up attempt failed:\", attempt);\n                    setError(\"Verification incomplete. Status: \".concat(attempt.status));\n                }\n            }\n        } catch (err) {\n            var _err_;\n            setError(err instanceof Array ? ((_err_ = err[0]) === null || _err_ === void 0 ? void 0 : _err_.message) || \"Failed to verify OTP.\" : \"Failed to verify OTP.\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    // const handleProfileCreation = async () => {\n    //   try {\n    //     // You may want to call your backend to create a profile here\n    //     onSuccess?.();\n    //     onClose();\n    //     // No redirect after profile creation\n    //   } catch (err: unknown) {\n    //     setError(err instanceof Error ? err.message || 'Failed to create profile' : 'Failed to create profile');\n    //     setShowProfileCreation(false);\n    //   }\n    // };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EmailVerificationModal.useEffect\": ()=>{\n            if (isOtpSent) {\n                var _otpRefs_current_;\n                (_otpRefs_current_ = otpRefs.current[0]) === null || _otpRefs_current_ === void 0 ? void 0 : _otpRefs_current_.focus();\n            }\n        }\n    }[\"EmailVerificationModal.useEffect\"], [\n        isOtpSent\n    ]);\n    if (!isOpen) return null;\n    // if (showProfileCreation) {\n    //   return (\n    //     <ProfileCreationModal\n    //       isOpen={true}\n    //       onClose={() => {}}\n    //       onSubmit={handleProfileCreation}\n    //       phoneNumber={email} // Pass email as phoneNumber for compatibility\n    //     />\n    //   );\n    // }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Tab_headlessui_react__WEBPACK_IMPORTED_MODULE_5__.Dialog, {\n        open: isOpen,\n        onClose: onClose,\n        className: \"relative z-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/30 backdrop-blur-sm\",\n                \"aria-hidden\": \"true\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                lineNumber: 236,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 flex items-center justify-center p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Tab_headlessui_react__WEBPACK_IMPORTED_MODULE_5__.Dialog.Panel, {\n                    className: \"mx-auto w-full max-w-md rounded-2xl bg-white dark:bg-stone-800 p-8 shadow-2xl\",\n                    children: !isOtpSent ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Tab_headlessui_react__WEBPACK_IMPORTED_MODULE_5__.Dialog.Title, {\n                                className: \"text-2xl font-semibold mb-6 text-primary dark:text-primary-light\",\n                                children: selectedTab === \"signup\" ? \"Create Account\" : \"Sign In\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 15\n                            }, undefined),\n                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6 p-4 bg-red-50 dark:bg-red-900/50 text-red-600 dark:text-red-400 text-sm rounded-lg border border-red-100\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Tab_headlessui_react__WEBPACK_IMPORTED_MODULE_6__.Tab.Group, {\n                                selectedIndex: selectedTab === \"signin\" ? 0 : 1,\n                                onChange: (index)=>setSelectedTab(index === 0 ? \"signin\" : \"signup\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Tab_headlessui_react__WEBPACK_IMPORTED_MODULE_6__.Tab.List, {\n                                        className: \"flex space-x-1 rounded-xl bg-stone-100 dark:bg-stone-700 p-1 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Tab_headlessui_react__WEBPACK_IMPORTED_MODULE_6__.Tab, {\n                                                className: (param)=>{\n                                                    let { selected } = param;\n                                                    return \"w-full rounded-lg py-2.5 text-sm font-medium leading-5\\n                     \".concat(selected ? \"bg-white dark:bg-stone-600 shadow text-primary dark:text-primary-light\" : \"text-gray-600 dark:text-gray-400 hover:bg-white/[0.12] hover:text-primary\");\n                                                },\n                                                children: \"Sign In\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Tab_headlessui_react__WEBPACK_IMPORTED_MODULE_6__.Tab, {\n                                                className: (param)=>{\n                                                    let { selected } = param;\n                                                    return \"w-full rounded-lg py-2.5 text-sm font-medium leading-5\\n                     \".concat(selected ? \"bg-white dark:bg-stone-600 shadow text-primary dark:text-primary-light\" : \"text-gray-600 dark:text-gray-400 hover:bg-white/[0.12] hover:text-primary\");\n                                                },\n                                                children: \"Sign Up\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                                lineNumber: 271,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Tab_headlessui_react__WEBPACK_IMPORTED_MODULE_6__.Tab.Panels, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Tab_headlessui_react__WEBPACK_IMPORTED_MODULE_6__.Tab.Panel, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                                    onSubmit: handleSendOtp,\n                                                    className: \"space-y-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    htmlFor: \"signin-email\",\n                                                                    className: \"block text-sm font-medium text-primary dark:text-primary-light mb-2\",\n                                                                    children: \"Email Address\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                                                    lineNumber: 288,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"email\",\n                                                                    id: \"signin-email\",\n                                                                    value: email,\n                                                                    onChange: handleEmailChange,\n                                                                    onKeyDown: (e)=>{\n                                                                        if (e.key === \" \") {\n                                                                            e.preventDefault();\n                                                                        }\n                                                                    },\n                                                                    placeholder: \"Enter your email address\",\n                                                                    className: \"flex-1 w-full h-14 px-4 bg-white dark:bg-stone-700 border border-gray-300 dark:border-stone-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary text-lg\",\n                                                                    required: true\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                                                    lineNumber: 294,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                                            lineNumber: 287,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            id: \"clerk-captcha\",\n                                                            className: \"mt-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                                            lineNumber: 309,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"submit\",\n                                                            disabled: loading || !email,\n                                                            className: \"w-full flex items-center justify-center bg-stone-900 hover:bg-stone-700 text-white font-medium py-2.5 px-4 rounded-lg transition-colors dark:bg-white dark:text-stone-800 dark:hover:bg-stone-300 h-14 text-base disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                            children: loading ? \"Sending...\" : \"Continue\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                                            lineNumber: 310,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                                    lineNumber: 286,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Tab_headlessui_react__WEBPACK_IMPORTED_MODULE_6__.Tab.Panel, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                                    onSubmit: handleSendOtp,\n                                                    className: \"space-y-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    htmlFor: \"signup-email\",\n                                                                    className: \"block text-sm font-medium text-primary dark:text-primary-light mb-2\",\n                                                                    children: \"Email Address\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                                                    lineNumber: 322,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"email\",\n                                                                    id: \"signup-email\",\n                                                                    value: email,\n                                                                    onChange: handleEmailChange,\n                                                                    onKeyDown: (e)=>{\n                                                                        if (e.key === \" \") {\n                                                                            e.preventDefault();\n                                                                        }\n                                                                    },\n                                                                    placeholder: \"Enter your email address\",\n                                                                    className: \"flex-1 w-full h-14 px-4 bg-white dark:bg-stone-700 border border-gray-300 dark:border-stone-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary text-lg\",\n                                                                    required: true\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                                                    lineNumber: 328,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                                            lineNumber: 321,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            id: \"clerk-captcha\",\n                                                            className: \"mt-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                                            lineNumber: 344,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"submit\",\n                                                            disabled: loading || !email,\n                                                            className: \"w-full flex items-center justify-center bg-stone-900 hover:bg-stone-700 text-white font-medium py-2.5 px-4 rounded-lg transition-colors dark:bg-white dark:text-stone-800 dark:hover:bg-stone-300 h-14 text-base disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                            children: loading ? \"Sending...\" : \"Continue\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                                            lineNumber: 345,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                                    lineNumber: 320,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                                lineNumber: 319,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                        lineNumber: 284,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                lineNumber: 252,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleVerifyOtp,\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Tab_headlessui_react__WEBPACK_IMPORTED_MODULE_5__.Dialog.Title, {\n                                className: \"text-2xl font-semibold mb-6 text-primary dark:text-primary-light\",\n                                children: \"Enter Verification Code\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                lineNumber: 359,\n                                columnNumber: 15\n                            }, undefined),\n                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6 p-4 bg-red-50 dark:bg-red-900/50 text-red-600 dark:text-red-400 text-sm rounded-lg border border-red-100\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                lineNumber: 363,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 dark:text-gray-400 mb-4\",\n                                        children: [\n                                            \"Enter the verification code sent to \",\n                                            email\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                        lineNumber: 368,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2 justify-between mb-4\",\n                                        children: otp.map((digit, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                ref: (el)=>{\n                                                    otpRefs.current[index] = el;\n                                                },\n                                                type: \"text\",\n                                                maxLength: 1,\n                                                value: digit,\n                                                onChange: (e)=>handleOtpChange(index, e.target.value),\n                                                onKeyDown: (e)=>handleKeyDown(index, e),\n                                                onPaste: handleOtpPaste,\n                                                className: \"w-12 h-14 text-center text-2xl font-semibold bg-white dark:bg-stone-700 border-2 border-gray-300 dark:border-stone-600 rounded-lg focus:border-primary focus:ring-2 focus:ring-primary\",\n                                                required: true\n                                            }, index, false, {\n                                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                                lineNumber: 373,\n                                                columnNumber: 21\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                        lineNumber: 371,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: ()=>{\n                                            setIsOtpSent(false);\n                                            setOtp([\n                                                \"\",\n                                                \"\",\n                                                \"\",\n                                                \"\",\n                                                \"\",\n                                                \"\"\n                                            ]);\n                                        },\n                                        className: \"text-primary dark:text-primary-light hover:text-primary-dark text-sm font-medium\",\n                                        children: \"Didn't receive the code? Send again\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                        lineNumber: 389,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                lineNumber: 367,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                disabled: loading || otp.some((digit)=>!digit),\n                                className: \"w-full flex items-center justify-center bg-stone-900 hover:bg-stone-700 text-white font-medium py-2.5 px-4 rounded-lg transition-colors dark:bg-white dark:text-stone-800 dark:hover:bg-stone-300 h-14 text-base disabled:opacity-50 disabled:cursor-not-allowed\",\n                                children: loading ? \"Verifying...\" : \"Verify\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                lineNumber: 400,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                        lineNumber: 358,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                    lineNumber: 241,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                lineNumber: 240,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n        lineNumber: 235,\n        columnNumber: 5\n    }, undefined);\n};\n_s(EmailVerificationModal, \"UfhtYhfbAxnwj9aaP1NkHp18L/o=\", false, function() {\n    return [\n        _clerk_nextjs__WEBPACK_IMPORTED_MODULE_4__.useSignUp,\n        _clerk_nextjs__WEBPACK_IMPORTED_MODULE_4__.useSignIn,\n        convex_react__WEBPACK_IMPORTED_MODULE_2__.useMutation\n    ];\n});\n_c = EmailVerificationModal;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (EmailVerificationModal);\nvar _c;\n$RefreshReg$(_c, \"EmailVerificationModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/EmailVerificationModal.tsx\n"));

/***/ })

});