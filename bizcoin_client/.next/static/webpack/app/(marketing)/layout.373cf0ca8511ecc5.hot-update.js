"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(marketing)/layout",{

/***/ "(app-pages-browser)/./components/EmailVerificationModal.tsx":
/*!***********************************************!*\
  !*** ./components/EmailVerificationModal.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Dialog_Tab_headlessui_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Dialog,Tab!=!@headlessui/react */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/components/dialog/dialog.js\");\n/* harmony import */ var _barrel_optimize_names_Dialog_Tab_headlessui_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Dialog,Tab!=!@headlessui/react */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/components/tabs/tabs.js\");\n/* harmony import */ var _clerk_nextjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @clerk/nextjs */ \"(app-pages-browser)/./node_modules/@clerk/clerk-react/dist/index.mjs\");\n/* harmony import */ var convex_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! convex/react */ \"(app-pages-browser)/./node_modules/convex/dist/esm/react/index.js\");\n/* harmony import */ var _convex_generated_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/convex/_generated/api */ \"(app-pages-browser)/./convex/_generated/api.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst EmailVerificationModal = (param)=>{\n    let { isOpen, onClose, onSuccess } = param;\n    _s();\n    const { isLoaded: signUpLoaded, signUp, setActive: setSignUpActive } = (0,_clerk_nextjs__WEBPACK_IMPORTED_MODULE_4__.useSignUp)();\n    const { isLoaded: signInLoaded, signIn, setActive: setSignInActive } = (0,_clerk_nextjs__WEBPACK_IMPORTED_MODULE_4__.useSignIn)();\n    const upsertProfile = (0,convex_react__WEBPACK_IMPORTED_MODULE_2__.useMutation)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_3__.api.myFunctions.upsertUserProfile);\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [otp, setOtp] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        \"\",\n        \"\",\n        \"\",\n        \"\",\n        \"\",\n        \"\"\n    ]);\n    const [isOtpSent, setIsOtpSent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // const [showProfileCreation, setShowProfileCreation] = useState(false);\n    const [isSignIn, setIsSignIn] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedTab, setSelectedTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"signin\");\n    const otpRefs = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    const handleEmailChange = (e)=>{\n        // Remove any spaces from the input value\n        const value = e.target.value.replace(/\\s+/g, \"\");\n        setEmail(value);\n    };\n    const handleOtpChange = (index, value)=>{\n        if (value.length > 1) value = value[0];\n        const newOtp = [\n            ...otp\n        ];\n        newOtp[index] = value;\n        setOtp(newOtp);\n        if (value !== \"\" && index < 5) {\n            var _otpRefs_current_;\n            (_otpRefs_current_ = otpRefs.current[index + 1]) === null || _otpRefs_current_ === void 0 ? void 0 : _otpRefs_current_.focus();\n        }\n    };\n    const handleKeyDown = (index, e)=>{\n        if (e.key === \"Backspace\" && !otp[index] && index > 0) {\n            var _otpRefs_current_;\n            (_otpRefs_current_ = otpRefs.current[index - 1]) === null || _otpRefs_current_ === void 0 ? void 0 : _otpRefs_current_.focus();\n        }\n    };\n    const handleOtpPaste = (e)=>{\n        const paste = e.clipboardData.getData(\"text\");\n        if (/^\\d{6}$/.test(paste)) {\n            setOtp(paste.split(\"\"));\n            // Focus the last input\n            setTimeout(()=>{\n                var _otpRefs_current_;\n                (_otpRefs_current_ = otpRefs.current[5]) === null || _otpRefs_current_ === void 0 ? void 0 : _otpRefs_current_.focus();\n            }, 0);\n            e.preventDefault();\n        }\n    };\n    const handleSendOtp = async (e)=>{\n        e.preventDefault();\n        setError(\"\");\n        setLoading(true);\n        console.log(\"Sending OTP for:\", email, \"Tab:\", selectedTab);\n        try {\n            if (selectedTab === \"signup\") {\n                if (!signUpLoaded) {\n                    setError(\"Sign up is not loaded yet\");\n                    return;\n                }\n                console.log(\"Creating sign-up...\");\n                const signUpResult = await signUp.create({\n                    emailAddress: email\n                });\n                console.log(\"Sign-up created:\", signUpResult);\n                console.log(\"Preparing email verification...\");\n                const prepareResult = await signUp.prepareEmailAddressVerification();\n                console.log(\"Email verification prepared:\", prepareResult);\n                setIsSignIn(false);\n                setIsOtpSent(true);\n            } else {\n                if (!signInLoaded) {\n                    setError(\"Sign in is not loaded yet\");\n                    return;\n                }\n                console.log(\"Creating sign-in...\");\n                const signInResult = await signIn.create({\n                    identifier: email\n                });\n                console.log(\"Sign-in created:\", signInResult);\n                const { supportedFirstFactors } = signInResult;\n                console.log(\"Supported first factors:\", supportedFirstFactors);\n                const emailCodeFactor = supportedFirstFactors === null || supportedFirstFactors === void 0 ? void 0 : supportedFirstFactors.find((factor)=>typeof factor === \"object\" && factor !== null && \"strategy\" in factor && factor.strategy === \"email_code\" && \"emailAddressId\" in factor);\n                console.log(\"Email code factor found:\", emailCodeFactor);\n                if (emailCodeFactor) {\n                    console.log(\"Preparing first factor...\");\n                    const prepareResult = await signIn.prepareFirstFactor({\n                        strategy: \"email_code\",\n                        emailAddressId: emailCodeFactor.emailAddressId\n                    });\n                    console.log(\"First factor prepared:\", prepareResult);\n                    setIsSignIn(true);\n                    setIsOtpSent(true);\n                } else {\n                    setError(\"No email code factor available. Please make sure email verification is enabled in your Clerk settings.\");\n                }\n            }\n        } catch (err) {\n            var _err_;\n            const errorMessage = err instanceof Array ? ((_err_ = err[0]) === null || _err_ === void 0 ? void 0 : _err_.message) || \"Failed to send OTP.\" : err instanceof Error ? err.message : \"Failed to send OTP.\";\n            console.error(\"Error sending OTP:\", err);\n            setError(errorMessage);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleVerifyOtp = async (e)=>{\n        e.preventDefault();\n        setError(\"\");\n        setLoading(true);\n        const otpString = otp.join(\"\");\n        console.log(\"Attempting verification with code:\", otpString);\n        console.log(\"Is sign in?\", isSignIn);\n        console.log(\"Sign up loaded?\", signUpLoaded);\n        console.log(\"Sign in loaded?\", signInLoaded);\n        try {\n            if (isSignIn) {\n                if (!signInLoaded) {\n                    console.error(\"Sign in not loaded\");\n                    setError(\"Sign in not loaded. Please try again.\");\n                    return;\n                }\n                console.log(\"Attempting sign-in verification...\");\n                const attempt = await signIn.attemptFirstFactor({\n                    strategy: \"email_code\",\n                    code: otpString\n                });\n                console.log(\"Sign-in attempt result:\", attempt);\n                if (attempt.status === \"complete\") {\n                    await setSignInActive({\n                        session: attempt.createdSessionId\n                    });\n                    onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess();\n                    onClose();\n                } else {\n                    console.error(\"Sign-in attempt failed:\", attempt);\n                    setError(\"Verification incomplete. Status: \".concat(attempt.status));\n                }\n            } else {\n                if (!signUpLoaded) {\n                    console.error(\"Sign up not loaded\");\n                    setError(\"Sign up not loaded. Please try again.\");\n                    return;\n                }\n                console.log(\"Attempting sign-up verification...\");\n                console.log(\"SignUp object:\", signUp);\n                const attempt = await signUp.attemptEmailAddressVerification({\n                    code: otpString\n                });\n                console.log(\"Sign-up attempt result:\", attempt);\n                if (attempt.status === \"complete\") {\n                    await setSignUpActive({\n                        session: attempt.createdSessionId\n                    });\n                    // Update Convex database with user profile\n                    try {\n                        await upsertProfile({\n                            email\n                        });\n                        onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess();\n                        onClose();\n                    } catch (convexError) {\n                        console.error(\"Failed to update Convex database:\", convexError);\n                        setError(\"Account created but profile update failed. Please update your profile later.\");\n                    }\n                } else {\n                    console.error(\"Sign-up attempt failed:\", attempt);\n                    setError(\"Verification incomplete. Status: \".concat(attempt.status || 'unknown'));\n                }\n            }\n        } catch (err) {\n            var _err_;\n            console.error(\"Verification error:\", err);\n            const errorMessage = err instanceof Array ? ((_err_ = err[0]) === null || _err_ === void 0 ? void 0 : _err_.message) || \"Failed to verify OTP.\" : err instanceof Error ? err.message : \"Failed to verify OTP.\";\n            setError(errorMessage);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // const handleProfileCreation = async () => {\n    //   try {\n    //     // You may want to call your backend to create a profile here\n    //     onSuccess?.();\n    //     onClose();\n    //     // No redirect after profile creation\n    //   } catch (err: unknown) {\n    //     setError(err instanceof Error ? err.message || 'Failed to create profile' : 'Failed to create profile');\n    //     setShowProfileCreation(false);\n    //   }\n    // };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EmailVerificationModal.useEffect\": ()=>{\n            if (isOtpSent) {\n                var _otpRefs_current_;\n                (_otpRefs_current_ = otpRefs.current[0]) === null || _otpRefs_current_ === void 0 ? void 0 : _otpRefs_current_.focus();\n            }\n        }\n    }[\"EmailVerificationModal.useEffect\"], [\n        isOtpSent\n    ]);\n    if (!isOpen) return null;\n    // if (showProfileCreation) {\n    //   return (\n    //     <ProfileCreationModal\n    //       isOpen={true}\n    //       onClose={() => {}}\n    //       onSubmit={handleProfileCreation}\n    //       phoneNumber={email} // Pass email as phoneNumber for compatibility\n    //     />\n    //   );\n    // }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Tab_headlessui_react__WEBPACK_IMPORTED_MODULE_5__.Dialog, {\n        open: isOpen,\n        onClose: onClose,\n        className: \"relative z-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/30 backdrop-blur-sm\",\n                \"aria-hidden\": \"true\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                lineNumber: 280,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 flex items-center justify-center p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Tab_headlessui_react__WEBPACK_IMPORTED_MODULE_5__.Dialog.Panel, {\n                    className: \"mx-auto w-full max-w-md rounded-2xl bg-white dark:bg-stone-800 p-8 shadow-2xl\",\n                    children: !isOtpSent ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Tab_headlessui_react__WEBPACK_IMPORTED_MODULE_5__.Dialog.Title, {\n                                className: \"text-2xl font-semibold mb-6 text-primary dark:text-primary-light\",\n                                children: selectedTab === \"signup\" ? \"Create Account\" : \"Sign In\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                lineNumber: 288,\n                                columnNumber: 15\n                            }, undefined),\n                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6 p-4 bg-red-50 dark:bg-red-900/50 text-red-600 dark:text-red-400 text-sm rounded-lg border border-red-100\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Tab_headlessui_react__WEBPACK_IMPORTED_MODULE_6__.Tab.Group, {\n                                selectedIndex: selectedTab === \"signin\" ? 0 : 1,\n                                onChange: (index)=>setSelectedTab(index === 0 ? \"signin\" : \"signup\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Tab_headlessui_react__WEBPACK_IMPORTED_MODULE_6__.Tab.List, {\n                                        className: \"flex space-x-1 rounded-xl bg-stone-100 dark:bg-stone-700 p-1 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Tab_headlessui_react__WEBPACK_IMPORTED_MODULE_6__.Tab, {\n                                                className: (param)=>{\n                                                    let { selected } = param;\n                                                    return \"w-full rounded-lg py-2.5 text-sm font-medium leading-5\\n                     \".concat(selected ? \"bg-white dark:bg-stone-600 shadow text-primary dark:text-primary-light\" : \"text-gray-600 dark:text-gray-400 hover:bg-white/[0.12] hover:text-primary\");\n                                                },\n                                                children: \"Sign In\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                                lineNumber: 303,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Tab_headlessui_react__WEBPACK_IMPORTED_MODULE_6__.Tab, {\n                                                className: (param)=>{\n                                                    let { selected } = param;\n                                                    return \"w-full rounded-lg py-2.5 text-sm font-medium leading-5\\n                     \".concat(selected ? \"bg-white dark:bg-stone-600 shadow text-primary dark:text-primary-light\" : \"text-gray-600 dark:text-gray-400 hover:bg-white/[0.12] hover:text-primary\");\n                                                },\n                                                children: \"Sign Up\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                                lineNumber: 315,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                        lineNumber: 302,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Tab_headlessui_react__WEBPACK_IMPORTED_MODULE_6__.Tab.Panels, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Tab_headlessui_react__WEBPACK_IMPORTED_MODULE_6__.Tab.Panel, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                                    onSubmit: handleSendOtp,\n                                                    className: \"space-y-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    htmlFor: \"signin-email\",\n                                                                    className: \"block text-sm font-medium text-primary dark:text-primary-light mb-2\",\n                                                                    children: \"Email Address\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                                                    lineNumber: 332,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"email\",\n                                                                    id: \"signin-email\",\n                                                                    value: email,\n                                                                    onChange: handleEmailChange,\n                                                                    onKeyDown: (e)=>{\n                                                                        if (e.key === \" \") {\n                                                                            e.preventDefault();\n                                                                        }\n                                                                    },\n                                                                    placeholder: \"Enter your email address\",\n                                                                    className: \"flex-1 w-full h-14 px-4 bg-white dark:bg-stone-700 border border-gray-300 dark:border-stone-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary text-lg\",\n                                                                    required: true\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                                                    lineNumber: 338,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                                            lineNumber: 331,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            id: \"clerk-captcha\",\n                                                            className: \"mt-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                                            lineNumber: 353,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"submit\",\n                                                            disabled: loading || !email,\n                                                            className: \"w-full flex items-center justify-center bg-stone-900 hover:bg-stone-700 text-white font-medium py-2.5 px-4 rounded-lg transition-colors dark:bg-white dark:text-stone-800 dark:hover:bg-stone-300 h-14 text-base disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                            children: loading ? \"Sending...\" : \"Continue\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                                            lineNumber: 354,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                                    lineNumber: 330,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                                lineNumber: 329,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Tab_headlessui_react__WEBPACK_IMPORTED_MODULE_6__.Tab.Panel, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                                    onSubmit: handleSendOtp,\n                                                    className: \"space-y-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    htmlFor: \"signup-email\",\n                                                                    className: \"block text-sm font-medium text-primary dark:text-primary-light mb-2\",\n                                                                    children: \"Email Address\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                                                    lineNumber: 366,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"email\",\n                                                                    id: \"signup-email\",\n                                                                    value: email,\n                                                                    onChange: handleEmailChange,\n                                                                    onKeyDown: (e)=>{\n                                                                        if (e.key === \" \") {\n                                                                            e.preventDefault();\n                                                                        }\n                                                                    },\n                                                                    placeholder: \"Enter your email address\",\n                                                                    className: \"flex-1 w-full h-14 px-4 bg-white dark:bg-stone-700 border border-gray-300 dark:border-stone-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary text-lg\",\n                                                                    required: true\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                                                    lineNumber: 372,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                                            lineNumber: 365,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            id: \"clerk-captcha\",\n                                                            className: \"mt-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                                            lineNumber: 388,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"submit\",\n                                                            disabled: loading || !email,\n                                                            className: \"w-full flex items-center justify-center bg-stone-900 hover:bg-stone-700 text-white font-medium py-2.5 px-4 rounded-lg transition-colors dark:bg-white dark:text-stone-800 dark:hover:bg-stone-300 h-14 text-base disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                            children: loading ? \"Sending...\" : \"Continue\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                                            lineNumber: 389,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                                lineNumber: 363,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                        lineNumber: 328,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                lineNumber: 296,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleVerifyOtp,\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Tab_headlessui_react__WEBPACK_IMPORTED_MODULE_5__.Dialog.Title, {\n                                className: \"text-2xl font-semibold mb-6 text-primary dark:text-primary-light\",\n                                children: \"Enter Verification Code\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                lineNumber: 403,\n                                columnNumber: 15\n                            }, undefined),\n                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6 p-4 bg-red-50 dark:bg-red-900/50 text-red-600 dark:text-red-400 text-sm rounded-lg border border-red-100\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                lineNumber: 407,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 dark:text-gray-400 mb-4\",\n                                        children: [\n                                            \"Enter the verification code sent to \",\n                                            email\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                        lineNumber: 412,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2 justify-between mb-4\",\n                                        children: otp.map((digit, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                ref: (el)=>{\n                                                    otpRefs.current[index] = el;\n                                                },\n                                                type: \"text\",\n                                                maxLength: 1,\n                                                value: digit,\n                                                onChange: (e)=>handleOtpChange(index, e.target.value),\n                                                onKeyDown: (e)=>handleKeyDown(index, e),\n                                                onPaste: handleOtpPaste,\n                                                className: \"w-12 h-14 text-center text-2xl font-semibold bg-white dark:bg-stone-700 border-2 border-gray-300 dark:border-stone-600 rounded-lg focus:border-primary focus:ring-2 focus:ring-primary\",\n                                                required: true\n                                            }, index, false, {\n                                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                                lineNumber: 417,\n                                                columnNumber: 21\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                        lineNumber: 415,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: ()=>{\n                                            setIsOtpSent(false);\n                                            setOtp([\n                                                \"\",\n                                                \"\",\n                                                \"\",\n                                                \"\",\n                                                \"\",\n                                                \"\"\n                                            ]);\n                                        },\n                                        className: \"text-primary dark:text-primary-light hover:text-primary-dark text-sm font-medium\",\n                                        children: \"Didn't receive the code? Send again\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                        lineNumber: 433,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                lineNumber: 411,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                disabled: loading || otp.some((digit)=>!digit),\n                                className: \"w-full flex items-center justify-center bg-stone-900 hover:bg-stone-700 text-white font-medium py-2.5 px-4 rounded-lg transition-colors dark:bg-white dark:text-stone-800 dark:hover:bg-stone-300 h-14 text-base disabled:opacity-50 disabled:cursor-not-allowed\",\n                                children: loading ? \"Verifying...\" : \"Verify\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                lineNumber: 444,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                        lineNumber: 402,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                    lineNumber: 285,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                lineNumber: 284,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n        lineNumber: 279,\n        columnNumber: 5\n    }, undefined);\n};\n_s(EmailVerificationModal, \"UfhtYhfbAxnwj9aaP1NkHp18L/o=\", false, function() {\n    return [\n        _clerk_nextjs__WEBPACK_IMPORTED_MODULE_4__.useSignUp,\n        _clerk_nextjs__WEBPACK_IMPORTED_MODULE_4__.useSignIn,\n        convex_react__WEBPACK_IMPORTED_MODULE_2__.useMutation\n    ];\n});\n_c = EmailVerificationModal;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (EmailVerificationModal);\nvar _c;\n$RefreshReg$(_c, \"EmailVerificationModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/EmailVerificationModal.tsx\n"));

/***/ })

});