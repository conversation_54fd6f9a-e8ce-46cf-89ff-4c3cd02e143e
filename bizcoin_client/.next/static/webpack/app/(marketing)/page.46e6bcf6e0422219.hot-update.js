"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(marketing)/page",{

/***/ "(app-pages-browser)/./app/(marketing)/page.tsx":
/*!**********************************!*\
  !*** ./app/(marketing)/page.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LandingPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_Bot_DollarSign_Monitor_MouseIcon_Settings_Shield_Smartphone_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,Bot,DollarSign,Monitor,MouseIcon,Settings,Shield,Smartphone,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_Bot_DollarSign_Monitor_MouseIcon_Settings_Shield_Smartphone_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,Bot,DollarSign,Monitor,MouseIcon,Settings,Shield,Smartphone,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_Bot_DollarSign_Monitor_MouseIcon_Settings_Shield_Smartphone_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,Bot,DollarSign,Monitor,MouseIcon,Settings,Shield,Smartphone,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_Bot_DollarSign_Monitor_MouseIcon_Settings_Shield_Smartphone_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,Bot,DollarSign,Monitor,MouseIcon,Settings,Shield,Smartphone,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_Bot_DollarSign_Monitor_MouseIcon_Settings_Shield_Smartphone_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,Bot,DollarSign,Monitor,MouseIcon,Settings,Shield,Smartphone,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_Bot_DollarSign_Monitor_MouseIcon_Settings_Shield_Smartphone_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,Bot,DollarSign,Monitor,MouseIcon,Settings,Shield,Smartphone,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_Bot_DollarSign_Monitor_MouseIcon_Settings_Shield_Smartphone_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,Bot,DollarSign,Monitor,MouseIcon,Settings,Shield,Smartphone,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_Bot_DollarSign_Monitor_MouseIcon_Settings_Shield_Smartphone_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,Bot,DollarSign,Monitor,MouseIcon,Settings,Shield,Smartphone,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mouse.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_Bot_DollarSign_Monitor_MouseIcon_Settings_Shield_Smartphone_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,Bot,DollarSign,Monitor,MouseIcon,Settings,Shield,Smartphone,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_Bot_DollarSign_Monitor_MouseIcon_Settings_Shield_Smartphone_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,Bot,DollarSign,Monitor,MouseIcon,Settings,Shield,Smartphone,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/monitor.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_Bot_DollarSign_Monitor_MouseIcon_Settings_Shield_Smartphone_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,Bot,DollarSign,Monitor,MouseIcon,Settings,Shield,Smartphone,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/smartphone.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_Bot_DollarSign_Monitor_MouseIcon_Settings_Shield_Smartphone_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,Bot,DollarSign,Monitor,MouseIcon,Settings,Shield,Smartphone,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst fadeInUp = {\n    initial: {\n        opacity: 0,\n        y: 60\n    },\n    animate: {\n        opacity: 1,\n        y: 0\n    },\n    transition: {\n        duration: 0.6,\n        ease: \"easeOut\"\n    }\n};\nconst staggerContainer = {\n    animate: {\n        transition: {\n            staggerChildren: 0.1\n        }\n    }\n};\n// const stats = [\n//   { label: \"Active Traders\", value: \"10,000+\", icon: Users },\n//   { label: \"Total Volume\", value: \"$50M+\", icon: TrendingUp },\n//   { label: \"Success Rate\", value: \"94.2%\", icon: Star },\n// ];\nconst features = [\n    {\n        icon: _barrel_optimize_names_Activity_ArrowRight_BarChart3_Bot_DollarSign_Monitor_MouseIcon_Settings_Shield_Smartphone_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        title: \"Trading Automation\",\n        description: \"Advanced algorithms that analyze market patterns and execute trades with precision, maximizing your profits while you sleep.\",\n        gradient: \"from-blue-500 to-purple-600\"\n    },\n    {\n        icon: _barrel_optimize_names_Activity_ArrowRight_BarChart3_Bot_DollarSign_Monitor_MouseIcon_Settings_Shield_Smartphone_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        title: \"Decentralized Security\",\n        description: \"Your funds remain in your wallet. No custodial risks, no centralized points of failure. Trade with complete peace of mind.\",\n        gradient: \"from-purple-500 to-pink-600\"\n    },\n    {\n        icon: _barrel_optimize_names_Activity_ArrowRight_BarChart3_Bot_DollarSign_Monitor_MouseIcon_Settings_Shield_Smartphone_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        title: \"Lightning Fast Execution\",\n        description: \"Execute trades in milliseconds on Solana&apos;s high-performance blockchain. Never miss an opportunity again.\",\n        gradient: \"from-pink-500 to-orange-600\"\n    }\n];\nconst appFeatures = [\n    {\n        icon: _barrel_optimize_names_Activity_ArrowRight_BarChart3_Bot_DollarSign_Monitor_MouseIcon_Settings_Shield_Smartphone_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        title: \"Smart Indicator Bot\",\n        description: \"Intuitive bot setup with customizable strategies, risk management, and automated execution.\",\n        features: [\n            \"Strategy templates\",\n            \"Risk controls\",\n            \"Backtesting\",\n            \"Performance optimization\"\n        ],\n        image: \"/screen-2.png\",\n        gradient: \"from-blue-500 to-cyan-500\"\n    },\n    {\n        icon: _barrel_optimize_names_Activity_ArrowRight_BarChart3_Bot_DollarSign_Monitor_MouseIcon_Settings_Shield_Smartphone_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        title: \"AI-Powered Coin Sniper\",\n        description: \"Real-time market analysis with comprehensive charts, indicators, and performance metrics.\",\n        features: [\n            \"Live price charts\",\n            \"Technical indicators\",\n            \"Portfolio tracking\",\n            \"P&L analytics\"\n        ],\n        image: \"/screen-1.png\",\n        gradient: \"from-purple-500 to-blue-500\"\n    },\n    {\n        icon: _barrel_optimize_names_Activity_ArrowRight_BarChart3_Bot_DollarSign_Monitor_MouseIcon_Settings_Shield_Smartphone_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        title: \"Real-Time Trading Feed\",\n        description: \"Live transaction monitoring with detailed trade history and execution analytics.\",\n        features: [\n            \"Live trade feed\",\n            \"Transaction history\",\n            \"Execution analytics\",\n            \"Performance metrics\"\n        ],\n        image: \"/screen-3.png\",\n        gradient: \"from-pink-500 to-purple-500\"\n    },\n    {\n        icon: _barrel_optimize_names_Activity_ArrowRight_BarChart3_Bot_DollarSign_Monitor_MouseIcon_Settings_Shield_Smartphone_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        title: \"Token Discovery\",\n        description: \"Discover new tokens on Solana with real-time data from Pump.fun, Raydium, and DexScreener.\",\n        features: [\n            \"New token alerts\",\n            \"Market cap tracking\",\n            \"Volume analysis\",\n            \"Social sentiment\"\n        ],\n        image: \"/screen-4.png\",\n        gradient: \"from-orange-500 to-red-500\"\n    },\n    {\n        icon: _barrel_optimize_names_Activity_ArrowRight_BarChart3_Bot_DollarSign_Monitor_MouseIcon_Settings_Shield_Smartphone_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        title: \"Portfolio Management\",\n        description: \"Comprehensive portfolio tracking with detailed analytics and performance insights.\",\n        features: [\n            \"Asset allocation\",\n            \"Performance tracking\",\n            \"Risk analysis\",\n            \"Rebalancing tools\"\n        ],\n        image: \"/screen-5.png\",\n        gradient: \"from-green-500 to-emerald-500\"\n    }\n];\nfunction LandingPage() {\n    _s();\n    const [isEmailVerificationOpen, setIsEmailVerificationOpen] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-background via-background to-background/80\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-primary-solid/5 via-transparent to-primary-solid/10\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-16 pb-32\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                            initial: \"initial\",\n                            animate: \"animate\",\n                            variants: staggerContainer,\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                    variants: fadeInUp,\n                                    className: \"mb-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                        variant: \"secondary\",\n                                        className: \"hidden mx-auto md:flex mb-4 px-4 py-2 text-sm font-medium bg-primary-solid/10 text-primary-solid border-primary-solid/20 \",\n                                        children: \"\\uD83D\\uDE80 World's First Decentralized Indicator-Based Trading\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.h1, {\n                                    variants: fadeInUp,\n                                    className: \"text-4xl sm:text-6xl lg:text-7xl font-bold tracking-tight mb-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"block text-foreground\",\n                                            children: \"Trade Strong with\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \" bg-primary-gradient bg-clip-text text-transparent\",\n                                            children: \"DexTrip Intelligence\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.p, {\n                                    variants: fadeInUp,\n                                    className: \"text-xl sm:text-2xl text-muted-foreground max-w-3xl mx-auto mb-24 leading-relaxed\",\n                                    children: \"The most powerful decentralized trading platform on Solana. AI-driven bots, advanced indicators, and complete control over your assets.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                    variants: fadeInUp,\n                                    animate: {\n                                        y: [\n                                            0,\n                                            50,\n                                            0\n                                        ]\n                                    },\n                                    initial: {\n                                        opacity: 0,\n                                        y: 60\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    transition: {\n                                        duration: 2,\n                                        repeat: Infinity,\n                                        repeatType: \"reverse\",\n                                        ease: \"easeInOut\"\n                                    },\n                                    className: \"flex flex-col sm:flex-row gap-4 justify-center items-center my-16\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bot_DollarSign_Monitor_MouseIcon_Settings_Shield_Smartphone_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"w-16 h-16\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                lineNumber: 162,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-24 relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-b from-transparent via-primary-solid/5 to-transparent\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                        lineNumber: 243,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                initial: \"initial\",\n                                whileInView: \"animate\",\n                                viewport: {\n                                    once: true\n                                },\n                                variants: staggerContainer,\n                                className: \"text-center mb-16\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.h2, {\n                                        variants: fadeInUp,\n                                        className: \"text-3xl sm:text-5xl font-bold mb-6\",\n                                        children: \"The Power in Your Hands\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.p, {\n                                        variants: fadeInUp,\n                                        className: \"text-xl text-muted-foreground max-w-3xl mx-auto\",\n                                        children: \"DexTrip is your sword and shield in the decentralized trading arena\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                initial: \"initial\",\n                                whileInView: \"animate\",\n                                viewport: {\n                                    once: true\n                                },\n                                variants: staggerContainer,\n                                className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                                children: features.map((feature)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                        variants: fadeInUp,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                            className: \"h-full bg-card/50 backdrop-blur-sm border-border/50 hover:border-primary-solid/30 transition-all duration-300 group hover:shadow-2xl hover:shadow-primary-solid/10\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                className: \"p-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-16 h-16 rounded-2xl bg-gradient-to-br \".concat(feature.gradient, \" p-4 mb-6 group-hover:scale-110 transition-transform duration-300\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                            className: \"w-8 h-8 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                                            lineNumber: 281,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                                        lineNumber: 278,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-2xl font-bold mb-4 text-foreground\",\n                                                        children: feature.title\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                                        lineNumber: 283,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-muted-foreground leading-relaxed\",\n                                                        children: feature.description\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                                        lineNumber: 286,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                                lineNumber: 277,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, feature.title, false, {\n                                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                        lineNumber: 275,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                lineNumber: 267,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                        lineNumber: 244,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                lineNumber: 242,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-24 relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-b from-primary-solid/5 via-transparent to-primary-solid/5\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                        lineNumber: 299,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                initial: \"initial\",\n                                whileInView: \"animate\",\n                                viewport: {\n                                    once: true\n                                },\n                                variants: staggerContainer,\n                                className: \"text-center mb-16\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.h2, {\n                                        variants: fadeInUp,\n                                        className: \"text-3xl sm:text-5xl font-bold mb-6\",\n                                        children: \"Experience the Platform\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.p, {\n                                        variants: fadeInUp,\n                                        className: \"text-xl text-muted-foreground max-w-3xl mx-auto\",\n                                        children: \"Explore DexTrip's comprehensive suite of trading tools and analytics\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                lineNumber: 301,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-24\",\n                                children: appFeatures.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                        initial: \"initial\",\n                                        whileInView: \"animate\",\n                                        viewport: {\n                                            once: true\n                                        },\n                                        variants: staggerContainer,\n                                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center \".concat(index % 2 === 1 ? \"lg:grid-flow-col-dense\" : \"\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                                variants: fadeInUp,\n                                                className: \"space-y-6 \".concat(index % 2 === 1 ? \"lg:col-start-2\" : \"\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-16 h-16 rounded-2xl bg-gradient-to-br \".concat(feature.gradient, \" p-4 mb-6\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                            className: \"w-8 h-8 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                                            lineNumber: 345,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                                        lineNumber: 342,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-3xl font-bold text-foreground\",\n                                                        children: feature.title\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                                        lineNumber: 348,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg text-muted-foreground leading-relaxed\",\n                                                        children: feature.description\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                                        lineNumber: 351,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 gap-3\",\n                                                        children: feature.features.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-2 h-2 rounded-full bg-gradient-to-r \".concat(feature.gradient)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                                                        lineNumber: 358,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm text-foreground\",\n                                                                        children: item\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                                                        lineNumber: 361,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, item, true, {\n                                                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                                                lineNumber: 357,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                                        lineNumber: 355,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                                lineNumber: 336,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                                variants: fadeInUp,\n                                                className: \"relative \".concat(index % 2 === 1 ? \"lg:col-start-1 lg:row-start-1\" : \"\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative bg-gradient-to-br from-card via-card/80 to-card/60 backdrop-blur-sm rounded-3xl p-4 border border-border/50 shadow-2xl\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-0 bg-gradient-to-br \".concat(feature.gradient, \"/5 rounded-3xl\")\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                                            lineNumber: 382,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative bg-background/50 rounded-2xl overflow-hidden border border-border/30\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                src: feature.image,\n                                                                alt: \"\".concat(feature.title, \" Screenshot\"),\n                                                                width: 800,\n                                                                height: 600,\n                                                                className: \"w-full h-auto\",\n                                                                priority: index < 2\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                                                lineNumber: 388,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                                            lineNumber: 387,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                                    lineNumber: 381,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                                lineNumber: 375,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, feature.title, true, {\n                                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                        lineNumber: 325,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                lineNumber: 323,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                        lineNumber: 300,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                lineNumber: 298,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-24 relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-b from-transparent via-primary-solid/5 to-transparent\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                        lineNumber: 407,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                initial: \"initial\",\n                                whileInView: \"animate\",\n                                viewport: {\n                                    once: true\n                                },\n                                variants: staggerContainer,\n                                className: \"text-center mb-16\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.h2, {\n                                        variants: fadeInUp,\n                                        className: \"text-3xl sm:text-5xl font-bold mb-6\",\n                                        children: \"Why Choose DexTrip?\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                        lineNumber: 416,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.p, {\n                                        variants: fadeInUp,\n                                        className: \"text-xl text-muted-foreground max-w-3xl mx-auto\",\n                                        children: \"The most comprehensive decentralized trading platform with cutting-edge features\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                        lineNumber: 422,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                lineNumber: 409,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                initial: \"initial\",\n                                whileInView: \"animate\",\n                                viewport: {\n                                    once: true\n                                },\n                                variants: staggerContainer,\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                                children: [\n                                    {\n                                        icon: _barrel_optimize_names_Activity_ArrowRight_BarChart3_Bot_DollarSign_Monitor_MouseIcon_Settings_Shield_Smartphone_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                                        title: \"Automated Trading\",\n                                        desc: \"Set it and forget it with AI-powered bots\",\n                                        color: \"from-blue-500 to-cyan-500\"\n                                    },\n                                    {\n                                        icon: _barrel_optimize_names_Activity_ArrowRight_BarChart3_Bot_DollarSign_Monitor_MouseIcon_Settings_Shield_Smartphone_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                                        title: \"Non-Custodial\",\n                                        desc: \"Your keys, your crypto, your control\",\n                                        color: \"from-green-500 to-emerald-500\"\n                                    },\n                                    {\n                                        icon: _barrel_optimize_names_Activity_ArrowRight_BarChart3_Bot_DollarSign_Monitor_MouseIcon_Settings_Shield_Smartphone_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                                        title: \"Lightning Fast\",\n                                        desc: \"Execute trades in milliseconds on Solana\",\n                                        color: \"from-yellow-500 to-orange-500\"\n                                    },\n                                    {\n                                        icon: _barrel_optimize_names_Activity_ArrowRight_BarChart3_Bot_DollarSign_Monitor_MouseIcon_Settings_Shield_Smartphone_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n                                        title: \"Advanced Analytics\",\n                                        desc: \"Professional-grade charts and indicators\",\n                                        color: \"from-purple-500 to-pink-500\"\n                                    },\n                                    {\n                                        icon: _barrel_optimize_names_Activity_ArrowRight_BarChart3_Bot_DollarSign_Monitor_MouseIcon_Settings_Shield_Smartphone_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                                        title: \"Smart Strategies\",\n                                        desc: \"Pre-built and custom trading strategies\",\n                                        color: \"from-indigo-500 to-purple-500\"\n                                    },\n                                    {\n                                        icon: _barrel_optimize_names_Activity_ArrowRight_BarChart3_Bot_DollarSign_Monitor_MouseIcon_Settings_Shield_Smartphone_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                                        title: \"Low Fees\",\n                                        desc: \"Minimal fees on the fastest blockchain\",\n                                        color: \"from-red-500 to-pink-500\"\n                                    }\n                                ].map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                        variants: fadeInUp,\n                                        className: \"group\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                            className: \"h-full bg-card/50 backdrop-blur-sm border-border/50 hover:border-primary-solid/30 transition-all duration-300 group-hover:shadow-2xl group-hover:shadow-primary-solid/10\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                className: \"p-6 text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-16 h-16 mx-auto rounded-2xl bg-gradient-to-br \".concat(item.color, \" p-4 mb-4 group-hover:scale-110 transition-transform duration-300\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                            className: \"w-8 h-8 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                                            lineNumber: 486,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                                        lineNumber: 483,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-bold mb-2 text-foreground\",\n                                                        children: item.title\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                                        lineNumber: 488,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-muted-foreground text-sm\",\n                                                        children: item.desc\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                                        lineNumber: 491,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                                lineNumber: 482,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                            lineNumber: 481,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, item.title, false, {\n                                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                        lineNumber: 476,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                lineNumber: 431,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                        lineNumber: 408,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                lineNumber: 406,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-24 relative overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-primary-gradient\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                        lineNumber: 502,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-2 gap-16 items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        x: -60\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    transition: {\n                                        duration: 0.8\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-3xl sm:text-5xl font-bold mb-6 text-white\",\n                                            children: \"A Revolution in Financial Freedom\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                            lineNumber: 517,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xl text-white/80 mb-8 leading-relaxed\",\n                                            children: \"Solana is the fastest-growing blockchain ecosystem in DeFi and Web3. DexTrip harnesses this power to give you unprecedented control over your trading destiny.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                            lineNumber: 521,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                                            lineNumber: 529,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white\",\n                                                            children: \"Lightning-fast transactions on Solana\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                                            lineNumber: 530,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                                    lineNumber: 528,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-gradient-to-r from-purple-500 to-pink-600 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                                            lineNumber: 535,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white\",\n                                                            children: \"Near-zero trading fees\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                                            lineNumber: 536,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                                    lineNumber: 534,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-gradient-to-r from-pink-500 to-orange-600 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                                            lineNumber: 539,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white\",\n                                                            children: \"Complete custody of your assets\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                                            lineNumber: 540,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                                    lineNumber: 538,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                            lineNumber: 527,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                    lineNumber: 505,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        x: 60\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    transition: {\n                                        duration: 0.8\n                                    },\n                                    className: \"relative\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative bg-gradient-to-br from-card via-card/80 to-card/60 backdrop-blur-sm rounded-3xl p-8 border border-border/50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-gradient-to-br from-primary-solid/5 to-transparent rounded-3xl\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                                lineNumber: 555,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-2xl font-bold mb-6 text-white\",\n                                                        children: \"Keys to the New World\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                                        lineNumber: 557,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-muted-foreground mb-6 leading-relaxed\",\n                                                        children: \"Your DexTrip account is your all-access pass to decentralized trading. It's a secure way to deploy bots, analyze markets, and explore the future of finance.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                                        lineNumber: 560,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-primary-solid font-medium\",\n                                                        children: \"The power is in your hands.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                                        lineNumber: 565,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                                lineNumber: 556,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                        lineNumber: 554,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                    lineNumber: 547,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                            lineNumber: 504,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                        lineNumber: 503,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                lineNumber: 501,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-24 relative overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-r from-blue-500/5 via-purple-500/5 to-pink-500/5\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                        lineNumber: 577,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                initial: \"initial\",\n                                whileInView: \"animate\",\n                                viewport: {\n                                    once: true\n                                },\n                                variants: staggerContainer,\n                                className: \"text-center mb-16\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.h2, {\n                                        variants: fadeInUp,\n                                        className: \"text-3xl sm:text-5xl font-bold mb-6\",\n                                        children: \"Trade Anywhere, Anytime\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                        lineNumber: 586,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.p, {\n                                        variants: fadeInUp,\n                                        className: \"text-xl text-muted-foreground max-w-3xl mx-auto\",\n                                        children: \"Access DexTrip on any device with our responsive web platform and upcoming mobile apps\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                        lineNumber: 592,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                lineNumber: 579,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-3 gap-8 mb-16\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 60\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        transition: {\n                                            duration: 0.6,\n                                            delay: 0.1\n                                        },\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative mb-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-24 h-24 mx-auto rounded-3xl bg-gradient-to-br from-blue-500 to-purple-600 p-6 shadow-2xl\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bot_DollarSign_Monitor_MouseIcon_Settings_Shield_Smartphone_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"w-12 h-12 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                                        lineNumber: 611,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                                    lineNumber: 610,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                                lineNumber: 609,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-bold mb-4 text-foreground\",\n                                                children: \"Desktop Web\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                                lineNumber: 615,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-muted-foreground mb-4\",\n                                                children: \"Full-featured trading platform with advanced analytics and comprehensive bot management\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                                lineNumber: 618,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                variant: \"secondary\",\n                                                className: \"bg-orange-500/10 text-orange-500 border-orange-500/20\",\n                                                children: \"Beta Access\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                                lineNumber: 627,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                        lineNumber: 602,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 60\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        transition: {\n                                            duration: 0.6,\n                                            delay: 0.2\n                                        },\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative mb-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-24 h-24 mx-auto rounded-3xl bg-gradient-to-br from-purple-500 to-pink-600 p-6 shadow-2xl\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bot_DollarSign_Monitor_MouseIcon_Settings_Shield_Smartphone_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"w-12 h-12 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                                        lineNumber: 643,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                                    lineNumber: 642,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                                lineNumber: 641,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-bold mb-4 text-foreground\",\n                                                children: \"Mobile App\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                                lineNumber: 647,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-muted-foreground mb-4\",\n                                                children: \"Trade on the go with our mobile-optimized interface and push notifications\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                                lineNumber: 650,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                variant: \"secondary\",\n                                                className: \"bg-blue-500/10 text-blue-500 border-blue-500/20\",\n                                                children: \"Coming Soon\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                                lineNumber: 654,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                        lineNumber: 634,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 60\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        transition: {\n                                            duration: 0.6,\n                                            delay: 0.3\n                                        },\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative mb-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-24 h-24 mx-auto rounded-3xl bg-gradient-to-br from-pink-500 to-orange-600 p-6 shadow-2xl\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bot_DollarSign_Monitor_MouseIcon_Settings_Shield_Smartphone_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"w-12 h-12 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                                        lineNumber: 670,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                                    lineNumber: 669,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                                lineNumber: 668,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-bold mb-4 text-foreground\",\n                                                children: \"Developer API\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                                lineNumber: 674,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-muted-foreground mb-4\",\n                                                children: \"Build custom integrations with our comprehensive REST API and WebSocket feeds\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                                lineNumber: 677,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                variant: \"secondary\",\n                                                className: \"bg-blue-500/10 text-blue-500 border-blue-500/20\",\n                                                children: \"Coming Soon\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                                lineNumber: 686,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                        lineNumber: 661,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                lineNumber: 601,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                initial: \"initial\",\n                                whileInView: \"animate\",\n                                viewport: {\n                                    once: true\n                                },\n                                variants: staggerContainer,\n                                className: \"grid grid-cols-2 lg:grid-cols-4 gap-8 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                        variants: fadeInUp,\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-3xl font-bold text-foreground\",\n                                                children: \"99.9%\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                                lineNumber: 704,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-muted-foreground\",\n                                                children: \"Uptime\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                                lineNumber: 705,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                        lineNumber: 703,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                        variants: fadeInUp,\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-3xl font-bold text-foreground\",\n                                                children: \"<50ms\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                                lineNumber: 709,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-muted-foreground\",\n                                                children: \"Latency\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                                lineNumber: 710,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                        lineNumber: 708,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                        variants: fadeInUp,\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-3xl font-bold text-foreground\",\n                                                children: \"24/7\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                                lineNumber: 714,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-muted-foreground\",\n                                                children: \"Support\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                                lineNumber: 715,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                        lineNumber: 713,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                        variants: fadeInUp,\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-3xl font-bold text-foreground\",\n                                                children: \"100+\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                                lineNumber: 719,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-muted-foreground\",\n                                                children: \"Indicators\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                                lineNumber: 720,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                        lineNumber: 718,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                lineNumber: 696,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                        lineNumber: 578,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                lineNumber: 576,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-24 relative overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-primary-solid/10 via-transparent to-primary-solid/5\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                        lineNumber: 728,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                            initial: \"initial\",\n                            whileInView: \"animate\",\n                            viewport: {\n                                once: true\n                            },\n                            variants: staggerContainer,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.h2, {\n                                    variants: fadeInUp,\n                                    className: \"text-3xl sm:text-5xl font-bold mb-6\",\n                                    children: [\n                                        \"In Every Wallet a Kingdom,\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                            lineNumber: 741,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"bg-gradient-to-r from-blue-400 via-purple-500 to-pink-500 bg-clip-text text-transparent\",\n                                            children: \"On Every Head a Crown\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                            lineNumber: 742,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                    lineNumber: 736,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.p, {\n                                    variants: fadeInUp,\n                                    className: \"text-xl text-muted-foreground mb-12 max-w-2xl mx-auto leading-relaxed\",\n                                    children: \"Master your own crypto destiny with DexTrip's advanced trading intelligence.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                    lineNumber: 747,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                    variants: fadeInUp,\n                                    className: \"space-y-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col sm:flex-row gap-4 justify-center items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                onClick: ()=>setIsEmailVerificationOpen(true),\n                                                size: \"lg\",\n                                                className: \"bg-primary-gradient text-white px-12 py-4 font-semibold shadow-2xl hover:shadow-purple-500/25 transition-all duration-300 group\",\n                                                children: [\n                                                    \"Join Waitlist\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bot_DollarSign_Monitor_MouseIcon_Settings_Shield_Smartphone_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                                        lineNumber: 769,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                                lineNumber: 763,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EmailVerificationModal, {\n                                                isOpen: isEmailVerificationOpen,\n                                                onClose: ()=>setIsEmailVerificationOpen(false)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                                lineNumber: 771,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                        lineNumber: 762,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                                    lineNumber: 755,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                            lineNumber: 730,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                        lineNumber: 729,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n                lineNumber: 727,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/app/(marketing)/page.tsx\",\n        lineNumber: 160,\n        columnNumber: 5\n    }, this);\n}\n_s(LandingPage, \"Jk3l0kYvs3F+31afkWbq+NPhz3I=\");\n_c = LandingPage;\nvar _c;\n$RefreshReg$(_c, \"LandingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/(marketing)/page.tsx\n"));

/***/ })

});