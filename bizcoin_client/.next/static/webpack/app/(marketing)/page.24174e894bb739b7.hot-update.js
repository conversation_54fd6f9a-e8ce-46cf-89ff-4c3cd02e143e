"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(marketing)/page",{

/***/ "(app-pages-browser)/./components/EmailVerificationModal.tsx":
/*!***********************************************!*\
  !*** ./components/EmailVerificationModal.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Dialog_Tab_headlessui_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Dialog,Tab!=!@headlessui/react */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/components/dialog/dialog.js\");\n/* harmony import */ var _barrel_optimize_names_Dialog_Tab_headlessui_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Dialog,Tab!=!@headlessui/react */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/components/tabs/tabs.js\");\n/* harmony import */ var _clerk_nextjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @clerk/nextjs */ \"(app-pages-browser)/./node_modules/@clerk/clerk-react/dist/index.mjs\");\n/* harmony import */ var convex_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! convex/react */ \"(app-pages-browser)/./node_modules/convex/dist/esm/react/index.js\");\n/* harmony import */ var _convex_generated_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/convex/_generated/api */ \"(app-pages-browser)/./convex/_generated/api.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst EmailVerificationModal = (param)=>{\n    let { isOpen, onClose, onSuccess } = param;\n    _s();\n    const { isLoaded: signUpLoaded, signUp, setActive: setSignUpActive } = (0,_clerk_nextjs__WEBPACK_IMPORTED_MODULE_4__.useSignUp)();\n    const { isLoaded: signInLoaded, signIn, setActive: setSignInActive } = (0,_clerk_nextjs__WEBPACK_IMPORTED_MODULE_4__.useSignIn)();\n    const upsertProfile = (0,convex_react__WEBPACK_IMPORTED_MODULE_2__.useMutation)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_3__.api.myFunctions.upsertUserProfile);\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [otp, setOtp] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        \"\",\n        \"\",\n        \"\",\n        \"\",\n        \"\",\n        \"\"\n    ]);\n    const [isOtpSent, setIsOtpSent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // const [showProfileCreation, setShowProfileCreation] = useState(false);\n    const [isSignIn, setIsSignIn] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedTab, setSelectedTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"signin\");\n    const otpRefs = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    const handleEmailChange = (e)=>{\n        // Remove any spaces from the input value\n        const value = e.target.value.replace(/\\s+/g, \"\");\n        setEmail(value);\n    };\n    const handleOtpChange = (index, value)=>{\n        if (value.length > 1) value = value[0];\n        const newOtp = [\n            ...otp\n        ];\n        newOtp[index] = value;\n        setOtp(newOtp);\n        if (value !== \"\" && index < 5) {\n            var _otpRefs_current_;\n            (_otpRefs_current_ = otpRefs.current[index + 1]) === null || _otpRefs_current_ === void 0 ? void 0 : _otpRefs_current_.focus();\n        }\n    };\n    const handleKeyDown = (index, e)=>{\n        if (e.key === \"Backspace\" && !otp[index] && index > 0) {\n            var _otpRefs_current_;\n            (_otpRefs_current_ = otpRefs.current[index - 1]) === null || _otpRefs_current_ === void 0 ? void 0 : _otpRefs_current_.focus();\n        }\n    };\n    const handleOtpPaste = (e)=>{\n        const paste = e.clipboardData.getData(\"text\");\n        if (/^\\d{6}$/.test(paste)) {\n            setOtp(paste.split(\"\"));\n            // Focus the last input\n            setTimeout(()=>{\n                var _otpRefs_current_;\n                (_otpRefs_current_ = otpRefs.current[5]) === null || _otpRefs_current_ === void 0 ? void 0 : _otpRefs_current_.focus();\n            }, 0);\n            e.preventDefault();\n        }\n    };\n    const handleSendOtp = async (e)=>{\n        e.preventDefault();\n        setError(\"\");\n        setLoading(true);\n        try {\n            if (selectedTab === \"signup\") {\n                if (!signUpLoaded) {\n                    setError(\"Sign up is not loaded yet\");\n                    return;\n                }\n                await signUp.create({\n                    emailAddress: email\n                });\n                await signUp.prepareEmailAddressVerification();\n                setIsSignIn(false);\n                setIsOtpSent(true);\n            } else {\n                if (!signInLoaded) {\n                    setError(\"Sign in is not loaded yet\");\n                    return;\n                }\n                const { supportedFirstFactors } = await signIn.create({\n                    identifier: email\n                });\n                const emailCodeFactor = supportedFirstFactors === null || supportedFirstFactors === void 0 ? void 0 : supportedFirstFactors.find((factor)=>typeof factor === \"object\" && factor !== null && \"strategy\" in factor && factor.strategy === \"email_code\" && \"emailAddressId\" in factor);\n                if (emailCodeFactor) {\n                    await signIn.prepareFirstFactor({\n                        strategy: \"email_code\",\n                        emailAddressId: emailCodeFactor.emailAddressId\n                    });\n                    setIsSignIn(true);\n                    setIsOtpSent(true);\n                } else {\n                    setError(\"No email code factor available. Please make sure email verification is enabled in your Clerk settings.\");\n                }\n            }\n        } catch (err) {\n            var _err_;\n            const errorMessage = err instanceof Array ? ((_err_ = err[0]) === null || _err_ === void 0 ? void 0 : _err_.message) || \"Failed to send OTP.\" : err instanceof Error ? err.message : \"Failed to send OTP.\";\n            console.error(\"Error:\", err);\n            setError(errorMessage);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleVerifyOtp = async (e)=>{\n        e.preventDefault();\n        setError(\"\");\n        setLoading(true);\n        const otpString = otp.join(\"\");\n        console.log(\"Attempting verification with code:\", otpString);\n        console.log(\"Is sign in?\", isSignIn);\n        console.log(\"Sign up loaded?\", signUpLoaded);\n        console.log(\"Sign in loaded?\", signInLoaded);\n        try {\n            if (isSignIn) {\n                if (!signInLoaded) {\n                    console.error(\"Sign in not loaded\");\n                    setError(\"Sign in not loaded. Please try again.\");\n                    return;\n                }\n                console.log(\"Attempting sign-in verification...\");\n                const attempt = await signIn.attemptFirstFactor({\n                    strategy: \"email_code\",\n                    code: otpString\n                });\n                console.log(\"Sign-in attempt result:\", attempt);\n                if (attempt.status === \"complete\") {\n                    await setSignInActive({\n                        session: attempt.createdSessionId\n                    });\n                    onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess();\n                    onClose();\n                } else {\n                    console.error(\"Sign-in attempt failed:\", attempt);\n                    setError(\"Verification incomplete. Status: \".concat(attempt.status));\n                }\n            } else {\n                if (!signUpLoaded) {\n                    console.error(\"Sign up not loaded\");\n                    setError(\"Sign up not loaded. Please try again.\");\n                    return;\n                }\n                console.log(\"Attempting sign-up verification...\");\n                console.log(\"SignUp object:\", signUp);\n                const attempt = await signUp.attemptEmailAddressVerification({\n                    code: otpString\n                });\n                console.log(\"Sign-up attempt result:\", attempt);\n                if (attempt.status === \"complete\") {\n                    await setSignUpActive({\n                        session: attempt.createdSessionId\n                    });\n                    // Update Convex database with user profile\n                    try {\n                        await upsertProfile({\n                            email\n                        });\n                        onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess();\n                        onClose();\n                    } catch (convexError) {\n                        console.error(\"Failed to update Convex database:\", convexError);\n                        setError(\"Account created but profile update failed. Please update your profile later.\");\n                    }\n                } else {\n                    console.error(\"Sign-up attempt failed:\", attempt);\n                    setError(\"Verification incomplete. Status: \".concat(attempt.status || 'unknown'));\n                }\n            }\n        } catch (err) {\n            var _err_;\n            console.error(\"Verification error:\", err);\n            const errorMessage = err instanceof Array ? ((_err_ = err[0]) === null || _err_ === void 0 ? void 0 : _err_.message) || \"Failed to verify OTP.\" : err instanceof Error ? err.message : \"Failed to verify OTP.\";\n            setError(errorMessage);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // const handleProfileCreation = async () => {\n    //   try {\n    //     // You may want to call your backend to create a profile here\n    //     onSuccess?.();\n    //     onClose();\n    //     // No redirect after profile creation\n    //   } catch (err: unknown) {\n    //     setError(err instanceof Error ? err.message || 'Failed to create profile' : 'Failed to create profile');\n    //     setShowProfileCreation(false);\n    //   }\n    // };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EmailVerificationModal.useEffect\": ()=>{\n            if (isOtpSent) {\n                var _otpRefs_current_;\n                (_otpRefs_current_ = otpRefs.current[0]) === null || _otpRefs_current_ === void 0 ? void 0 : _otpRefs_current_.focus();\n            }\n        }\n    }[\"EmailVerificationModal.useEffect\"], [\n        isOtpSent\n    ]);\n    if (!isOpen) return null;\n    // if (showProfileCreation) {\n    //   return (\n    //     <ProfileCreationModal\n    //       isOpen={true}\n    //       onClose={() => {}}\n    //       onSubmit={handleProfileCreation}\n    //       phoneNumber={email} // Pass email as phoneNumber for compatibility\n    //     />\n    //   );\n    // }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Tab_headlessui_react__WEBPACK_IMPORTED_MODULE_5__.Dialog, {\n        open: isOpen,\n        onClose: onClose,\n        className: \"relative z-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/30 backdrop-blur-sm\",\n                \"aria-hidden\": \"true\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                lineNumber: 260,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 flex items-center justify-center p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Tab_headlessui_react__WEBPACK_IMPORTED_MODULE_5__.Dialog.Panel, {\n                    className: \"mx-auto w-full max-w-md rounded-2xl bg-white dark:bg-stone-800 p-8 shadow-2xl\",\n                    children: !isOtpSent ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Tab_headlessui_react__WEBPACK_IMPORTED_MODULE_5__.Dialog.Title, {\n                                className: \"text-2xl font-semibold mb-6 text-primary dark:text-primary-light\",\n                                children: selectedTab === \"signup\" ? \"Create Account\" : \"Sign In\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                lineNumber: 268,\n                                columnNumber: 15\n                            }, undefined),\n                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6 p-4 bg-red-50 dark:bg-red-900/50 text-red-600 dark:text-red-400 text-sm rounded-lg border border-red-100\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                lineNumber: 272,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Tab_headlessui_react__WEBPACK_IMPORTED_MODULE_6__.Tab.Group, {\n                                selectedIndex: selectedTab === \"signin\" ? 0 : 1,\n                                onChange: (index)=>setSelectedTab(index === 0 ? \"signin\" : \"signup\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Tab_headlessui_react__WEBPACK_IMPORTED_MODULE_6__.Tab.List, {\n                                        className: \"flex space-x-1 rounded-xl bg-stone-100 dark:bg-stone-700 p-1 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Tab_headlessui_react__WEBPACK_IMPORTED_MODULE_6__.Tab, {\n                                                className: (param)=>{\n                                                    let { selected } = param;\n                                                    return \"w-full rounded-lg py-2.5 text-sm font-medium leading-5\\n                     \".concat(selected ? \"bg-white dark:bg-stone-600 shadow text-primary dark:text-primary-light\" : \"text-gray-600 dark:text-gray-400 hover:bg-white/[0.12] hover:text-primary\");\n                                                },\n                                                children: \"Sign In\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                                lineNumber: 283,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Tab_headlessui_react__WEBPACK_IMPORTED_MODULE_6__.Tab, {\n                                                className: (param)=>{\n                                                    let { selected } = param;\n                                                    return \"w-full rounded-lg py-2.5 text-sm font-medium leading-5\\n                     \".concat(selected ? \"bg-white dark:bg-stone-600 shadow text-primary dark:text-primary-light\" : \"text-gray-600 dark:text-gray-400 hover:bg-white/[0.12] hover:text-primary\");\n                                                },\n                                                children: \"Sign Up\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Tab_headlessui_react__WEBPACK_IMPORTED_MODULE_6__.Tab.Panels, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Tab_headlessui_react__WEBPACK_IMPORTED_MODULE_6__.Tab.Panel, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                                    onSubmit: handleSendOtp,\n                                                    className: \"space-y-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    htmlFor: \"signin-email\",\n                                                                    className: \"block text-sm font-medium text-primary dark:text-primary-light mb-2\",\n                                                                    children: \"Email Address\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                                                    lineNumber: 312,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"email\",\n                                                                    id: \"signin-email\",\n                                                                    value: email,\n                                                                    onChange: handleEmailChange,\n                                                                    onKeyDown: (e)=>{\n                                                                        if (e.key === \" \") {\n                                                                            e.preventDefault();\n                                                                        }\n                                                                    },\n                                                                    placeholder: \"Enter your email address\",\n                                                                    className: \"flex-1 w-full h-14 px-4 bg-white dark:bg-stone-700 border border-gray-300 dark:border-stone-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary text-lg\",\n                                                                    required: true\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                                                    lineNumber: 318,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                                            lineNumber: 311,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            id: \"clerk-captcha\",\n                                                            className: \"mt-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                                            lineNumber: 333,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"submit\",\n                                                            disabled: loading || !email,\n                                                            className: \"w-full flex items-center justify-center bg-stone-900 hover:bg-stone-700 text-white font-medium py-2.5 px-4 rounded-lg transition-colors dark:bg-white dark:text-stone-800 dark:hover:bg-stone-300 h-14 text-base disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                            children: loading ? \"Sending...\" : \"Continue\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                                            lineNumber: 334,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                                    lineNumber: 310,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                                lineNumber: 309,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Tab_headlessui_react__WEBPACK_IMPORTED_MODULE_6__.Tab.Panel, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                                    onSubmit: handleSendOtp,\n                                                    className: \"space-y-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    htmlFor: \"signup-email\",\n                                                                    className: \"block text-sm font-medium text-primary dark:text-primary-light mb-2\",\n                                                                    children: \"Email Address\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                                                    lineNumber: 346,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"email\",\n                                                                    id: \"signup-email\",\n                                                                    value: email,\n                                                                    onChange: handleEmailChange,\n                                                                    onKeyDown: (e)=>{\n                                                                        if (e.key === \" \") {\n                                                                            e.preventDefault();\n                                                                        }\n                                                                    },\n                                                                    placeholder: \"Enter your email address\",\n                                                                    className: \"flex-1 w-full h-14 px-4 bg-white dark:bg-stone-700 border border-gray-300 dark:border-stone-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary text-lg\",\n                                                                    required: true\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                                                    lineNumber: 352,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                                            lineNumber: 345,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            id: \"clerk-captcha\",\n                                                            className: \"mt-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                                            lineNumber: 368,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"submit\",\n                                                            disabled: loading || !email,\n                                                            className: \"w-full flex items-center justify-center bg-stone-900 hover:bg-stone-700 text-white font-medium py-2.5 px-4 rounded-lg transition-colors dark:bg-white dark:text-stone-800 dark:hover:bg-stone-300 h-14 text-base disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                            children: loading ? \"Sending...\" : \"Continue\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                                            lineNumber: 369,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                                    lineNumber: 344,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                                lineNumber: 343,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                lineNumber: 276,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleVerifyOtp,\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Tab_headlessui_react__WEBPACK_IMPORTED_MODULE_5__.Dialog.Title, {\n                                className: \"text-2xl font-semibold mb-6 text-primary dark:text-primary-light\",\n                                children: \"Enter Verification Code\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                lineNumber: 383,\n                                columnNumber: 15\n                            }, undefined),\n                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6 p-4 bg-red-50 dark:bg-red-900/50 text-red-600 dark:text-red-400 text-sm rounded-lg border border-red-100\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                lineNumber: 387,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 dark:text-gray-400 mb-4\",\n                                        children: [\n                                            \"Enter the verification code sent to \",\n                                            email\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                        lineNumber: 392,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2 justify-between mb-4\",\n                                        children: otp.map((digit, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                ref: (el)=>{\n                                                    otpRefs.current[index] = el;\n                                                },\n                                                type: \"text\",\n                                                maxLength: 1,\n                                                value: digit,\n                                                onChange: (e)=>handleOtpChange(index, e.target.value),\n                                                onKeyDown: (e)=>handleKeyDown(index, e),\n                                                onPaste: handleOtpPaste,\n                                                className: \"w-12 h-14 text-center text-2xl font-semibold bg-white dark:bg-stone-700 border-2 border-gray-300 dark:border-stone-600 rounded-lg focus:border-primary focus:ring-2 focus:ring-primary\",\n                                                required: true\n                                            }, index, false, {\n                                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                                lineNumber: 397,\n                                                columnNumber: 21\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                        lineNumber: 395,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: ()=>{\n                                            setIsOtpSent(false);\n                                            setOtp([\n                                                \"\",\n                                                \"\",\n                                                \"\",\n                                                \"\",\n                                                \"\",\n                                                \"\"\n                                            ]);\n                                        },\n                                        className: \"text-primary dark:text-primary-light hover:text-primary-dark text-sm font-medium\",\n                                        children: \"Didn't receive the code? Send again\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                        lineNumber: 413,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                lineNumber: 391,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                disabled: loading || otp.some((digit)=>!digit),\n                                className: \"w-full flex items-center justify-center bg-stone-900 hover:bg-stone-700 text-white font-medium py-2.5 px-4 rounded-lg transition-colors dark:bg-white dark:text-stone-800 dark:hover:bg-stone-300 h-14 text-base disabled:opacity-50 disabled:cursor-not-allowed\",\n                                children: loading ? \"Verifying...\" : \"Verify\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                                lineNumber: 424,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                        lineNumber: 382,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                    lineNumber: 265,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n                lineNumber: 264,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Developer/bizcoin/bizcoin_client/components/EmailVerificationModal.tsx\",\n        lineNumber: 259,\n        columnNumber: 5\n    }, undefined);\n};\n_s(EmailVerificationModal, \"UfhtYhfbAxnwj9aaP1NkHp18L/o=\", false, function() {\n    return [\n        _clerk_nextjs__WEBPACK_IMPORTED_MODULE_4__.useSignUp,\n        _clerk_nextjs__WEBPACK_IMPORTED_MODULE_4__.useSignIn,\n        convex_react__WEBPACK_IMPORTED_MODULE_2__.useMutation\n    ];\n});\n_c = EmailVerificationModal;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (EmailVerificationModal);\nvar _c;\n$RefreshReg$(_c, \"EmailVerificationModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2NvbXBvbmVudHMvRW1haWxWZXJpZmljYXRpb25Nb2RhbC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFFMkQ7QUFDWDtBQUNLO0FBQ1Y7QUFDRztBQWM5QyxNQUFNVSx5QkFBZ0U7UUFBQyxFQUNyRUMsTUFBTSxFQUNOQyxPQUFPLEVBQ1BDLFNBQVMsRUFDVjs7SUFDQyxNQUFNLEVBQ0pDLFVBQVVDLFlBQVksRUFDdEJDLE1BQU0sRUFDTkMsV0FBV0MsZUFBZSxFQUMzQixHQUFHWix3REFBU0E7SUFDYixNQUFNLEVBQ0pRLFVBQVVLLFlBQVksRUFDdEJDLE1BQU0sRUFDTkgsV0FBV0ksZUFBZSxFQUMzQixHQUFHZCx3REFBU0E7SUFDYixNQUFNZSxnQkFBZ0JkLHlEQUFXQSxDQUFDQyxzREFBR0EsQ0FBQ2MsV0FBVyxDQUFDQyxpQkFBaUI7SUFDbkUsTUFBTSxDQUFDQyxPQUFPQyxTQUFTLEdBQUd6QiwrQ0FBUUEsQ0FBQztJQUNuQyxNQUFNLENBQUMwQixLQUFLQyxPQUFPLEdBQUczQiwrQ0FBUUEsQ0FBQztRQUFDO1FBQUk7UUFBSTtRQUFJO1FBQUk7UUFBSTtLQUFHO0lBQ3ZELE1BQU0sQ0FBQzRCLFdBQVdDLGFBQWEsR0FBRzdCLCtDQUFRQSxDQUFDO0lBQzNDLE1BQU0sQ0FBQzhCLE9BQU9DLFNBQVMsR0FBRy9CLCtDQUFRQSxDQUFDO0lBQ25DLE1BQU0sQ0FBQ2dDLFNBQVNDLFdBQVcsR0FBR2pDLCtDQUFRQSxDQUFDO0lBQ3ZDLHlFQUF5RTtJQUN6RSxNQUFNLENBQUNrQyxVQUFVQyxZQUFZLEdBQUduQywrQ0FBUUEsQ0FBQztJQUN6QyxNQUFNLENBQUNvQyxhQUFhQyxlQUFlLEdBQUdyQywrQ0FBUUEsQ0FBQztJQUMvQyxNQUFNc0MsVUFBVXJDLDZDQUFNQSxDQUE4QixFQUFFO0lBRXRELE1BQU1zQyxvQkFBb0IsQ0FBQ0M7UUFDekIseUNBQXlDO1FBQ3pDLE1BQU1DLFFBQVFELEVBQUVFLE1BQU0sQ0FBQ0QsS0FBSyxDQUFDRSxPQUFPLENBQUMsUUFBUTtRQUM3Q2xCLFNBQVNnQjtJQUNYO0lBRUEsTUFBTUcsa0JBQWtCLENBQUNDLE9BQWVKO1FBQ3RDLElBQUlBLE1BQU1LLE1BQU0sR0FBRyxHQUFHTCxRQUFRQSxLQUFLLENBQUMsRUFBRTtRQUN0QyxNQUFNTSxTQUFTO2VBQUlyQjtTQUFJO1FBQ3ZCcUIsTUFBTSxDQUFDRixNQUFNLEdBQUdKO1FBQ2hCZCxPQUFPb0I7UUFDUCxJQUFJTixVQUFVLE1BQU1JLFFBQVEsR0FBRztnQkFDN0JQO2FBQUFBLG9CQUFBQSxRQUFRVSxPQUFPLENBQUNILFFBQVEsRUFBRSxjQUExQlAsd0NBQUFBLGtCQUE0QlcsS0FBSztRQUNuQztJQUNGO0lBRUEsTUFBTUMsZ0JBQWdCLENBQ3BCTCxPQUNBTDtRQUVBLElBQUlBLEVBQUVXLEdBQUcsS0FBSyxlQUFlLENBQUN6QixHQUFHLENBQUNtQixNQUFNLElBQUlBLFFBQVEsR0FBRztnQkFDckRQO2FBQUFBLG9CQUFBQSxRQUFRVSxPQUFPLENBQUNILFFBQVEsRUFBRSxjQUExQlAsd0NBQUFBLGtCQUE0QlcsS0FBSztRQUNuQztJQUNGO0lBRUEsTUFBTUcsaUJBQWlCLENBQUNaO1FBQ3RCLE1BQU1hLFFBQVFiLEVBQUVjLGFBQWEsQ0FBQ0MsT0FBTyxDQUFDO1FBQ3RDLElBQUksVUFBVUMsSUFBSSxDQUFDSCxRQUFRO1lBQ3pCMUIsT0FBTzBCLE1BQU1JLEtBQUssQ0FBQztZQUNuQix1QkFBdUI7WUFDdkJDLFdBQVc7b0JBQ1RwQjtpQkFBQUEsb0JBQUFBLFFBQVFVLE9BQU8sQ0FBQyxFQUFFLGNBQWxCVix3Q0FBQUEsa0JBQW9CVyxLQUFLO1lBQzNCLEdBQUc7WUFDSFQsRUFBRW1CLGNBQWM7UUFDbEI7SUFDRjtJQUVBLE1BQU1DLGdCQUFnQixPQUFPcEI7UUFDM0JBLEVBQUVtQixjQUFjO1FBQ2hCNUIsU0FBUztRQUNURSxXQUFXO1FBQ1gsSUFBSTtZQUNGLElBQUlHLGdCQUFnQixVQUFVO2dCQUM1QixJQUFJLENBQUN0QixjQUFjO29CQUNqQmlCLFNBQVM7b0JBQ1Q7Z0JBQ0Y7Z0JBQ0EsTUFBTWhCLE9BQU84QyxNQUFNLENBQUM7b0JBQ2xCQyxjQUFjdEM7Z0JBQ2hCO2dCQUVBLE1BQU1ULE9BQU9nRCwrQkFBK0I7Z0JBQzVDNUIsWUFBWTtnQkFDWk4sYUFBYTtZQUNmLE9BQU87Z0JBQ0wsSUFBSSxDQUFDWCxjQUFjO29CQUNqQmEsU0FBUztvQkFDVDtnQkFDRjtnQkFDQSxNQUFNLEVBQUVpQyxxQkFBcUIsRUFBRSxHQUFHLE1BQU03QyxPQUFPMEMsTUFBTSxDQUFDO29CQUNwREksWUFBWXpDO2dCQUNkO2dCQUVBLE1BQU0wQyxrQkFBa0JGLGtDQUFBQSw0Q0FBQUEsc0JBQXVCRyxJQUFJLENBQ2pELENBQUNDLFNBQ0MsT0FBT0EsV0FBVyxZQUNsQkEsV0FBVyxRQUNYLGNBQWNBLFVBQ2RBLE9BQU9DLFFBQVEsS0FBSyxnQkFDcEIsb0JBQW9CRDtnQkFHeEIsSUFBSUYsaUJBQWlCO29CQUNuQixNQUFNL0MsT0FBT21ELGtCQUFrQixDQUFDO3dCQUM5QkQsVUFBVTt3QkFDVkUsZ0JBQWdCTCxnQkFBZ0JLLGNBQWM7b0JBQ2hEO29CQUNBcEMsWUFBWTtvQkFDWk4sYUFBYTtnQkFDZixPQUFPO29CQUNMRSxTQUNFO2dCQUVKO1lBQ0Y7UUFDRixFQUFFLE9BQU95QyxLQUFjO2dCQUdmQTtZQUZOLE1BQU1DLGVBQ0pELGVBQWVFLFFBQ1hGLEVBQUFBLFFBQUFBLEdBQUcsQ0FBQyxFQUFFLGNBQU5BLDRCQUFBQSxNQUFRRyxPQUFPLEtBQUksd0JBQ25CSCxlQUFlSSxRQUNiSixJQUFJRyxPQUFPLEdBQ1g7WUFDUkUsUUFBUS9DLEtBQUssQ0FBQyxVQUFVMEM7WUFDeEJ6QyxTQUFTMEM7UUFDWCxTQUFVO1lBQ1J4QyxXQUFXO1FBQ2I7SUFDRjtJQUVBLE1BQU02QyxrQkFBa0IsT0FBT3RDO1FBQzdCQSxFQUFFbUIsY0FBYztRQUNoQjVCLFNBQVM7UUFDVEUsV0FBVztRQUNYLE1BQU04QyxZQUFZckQsSUFBSXNELElBQUksQ0FBQztRQUUzQkgsUUFBUUksR0FBRyxDQUFDLHNDQUFzQ0Y7UUFDbERGLFFBQVFJLEdBQUcsQ0FBQyxlQUFlL0M7UUFDM0IyQyxRQUFRSSxHQUFHLENBQUMsbUJBQW1CbkU7UUFDL0IrRCxRQUFRSSxHQUFHLENBQUMsbUJBQW1CL0Q7UUFFL0IsSUFBSTtZQUNGLElBQUlnQixVQUFVO2dCQUNaLElBQUksQ0FBQ2hCLGNBQWM7b0JBQ2pCMkQsUUFBUS9DLEtBQUssQ0FBQztvQkFDZEMsU0FBUztvQkFDVDtnQkFDRjtnQkFDQThDLFFBQVFJLEdBQUcsQ0FBQztnQkFDWixNQUFNQyxVQUFVLE1BQU0vRCxPQUFPZ0Usa0JBQWtCLENBQUM7b0JBQzlDZCxVQUFVO29CQUNWZSxNQUFNTDtnQkFDUjtnQkFDQUYsUUFBUUksR0FBRyxDQUFDLDJCQUEyQkM7Z0JBRXZDLElBQUlBLFFBQVFHLE1BQU0sS0FBSyxZQUFZO29CQUNqQyxNQUFNakUsZ0JBQWdCO3dCQUFFa0UsU0FBU0osUUFBUUssZ0JBQWdCO29CQUFDO29CQUMxRDNFLHNCQUFBQSxnQ0FBQUE7b0JBQ0FEO2dCQUNGLE9BQU87b0JBQ0xrRSxRQUFRL0MsS0FBSyxDQUFDLDJCQUEyQm9EO29CQUN6Q25ELFNBQVMsb0NBQW1ELE9BQWZtRCxRQUFRRyxNQUFNO2dCQUM3RDtZQUNGLE9BQU87Z0JBQ0wsSUFBSSxDQUFDdkUsY0FBYztvQkFDakIrRCxRQUFRL0MsS0FBSyxDQUFDO29CQUNkQyxTQUFTO29CQUNUO2dCQUNGO2dCQUNBOEMsUUFBUUksR0FBRyxDQUFDO2dCQUNaSixRQUFRSSxHQUFHLENBQUMsa0JBQWtCbEU7Z0JBRTlCLE1BQU1tRSxVQUFVLE1BQU1uRSxPQUFPeUUsK0JBQStCLENBQUM7b0JBQzNESixNQUFNTDtnQkFDUjtnQkFDQUYsUUFBUUksR0FBRyxDQUFDLDJCQUEyQkM7Z0JBRXZDLElBQUlBLFFBQVFHLE1BQU0sS0FBSyxZQUFZO29CQUNqQyxNQUFNcEUsZ0JBQWdCO3dCQUFFcUUsU0FBU0osUUFBUUssZ0JBQWdCO29CQUFDO29CQUUxRCwyQ0FBMkM7b0JBQzNDLElBQUk7d0JBQ0YsTUFBTWxFLGNBQWM7NEJBQ2xCRzt3QkFDRjt3QkFDQVosc0JBQUFBLGdDQUFBQTt3QkFDQUQ7b0JBQ0YsRUFBRSxPQUFPOEUsYUFBYTt3QkFDcEJaLFFBQVEvQyxLQUFLLENBQUMscUNBQXFDMkQ7d0JBQ25EMUQsU0FDRTtvQkFFSjtnQkFDRixPQUFPO29CQUNMOEMsUUFBUS9DLEtBQUssQ0FBQywyQkFBMkJvRDtvQkFDekNuRCxTQUFTLG9DQUFnRSxPQUE1Qm1ELFFBQVFHLE1BQU0sSUFBSTtnQkFDakU7WUFDRjtRQUNGLEVBQUUsT0FBT2IsS0FBYztnQkFHakJBO1lBRkpLLFFBQVEvQyxLQUFLLENBQUMsdUJBQXVCMEM7WUFDckMsTUFBTUMsZUFBZUQsZUFBZUUsUUFDaENGLEVBQUFBLFFBQUFBLEdBQUcsQ0FBQyxFQUFFLGNBQU5BLDRCQUFBQSxNQUFRRyxPQUFPLEtBQUksMEJBQ25CSCxlQUFlSSxRQUNiSixJQUFJRyxPQUFPLEdBQ1g7WUFDTjVDLFNBQVMwQztRQUNYLFNBQVU7WUFDUnhDLFdBQVc7UUFDYjtJQUNGO0lBRUEsOENBQThDO0lBQzlDLFVBQVU7SUFDVixvRUFBb0U7SUFDcEUscUJBQXFCO0lBQ3JCLGlCQUFpQjtJQUNqQiw0Q0FBNEM7SUFDNUMsNkJBQTZCO0lBQzdCLCtHQUErRztJQUMvRyxxQ0FBcUM7SUFDckMsTUFBTTtJQUNOLEtBQUs7SUFFTC9CLGdEQUFTQTs0Q0FBQztZQUNSLElBQUkwQixXQUFXO29CQUNiVTtpQkFBQUEsb0JBQUFBLFFBQVFVLE9BQU8sQ0FBQyxFQUFFLGNBQWxCVix3Q0FBQUEsa0JBQW9CVyxLQUFLO1lBQzNCO1FBQ0Y7MkNBQUc7UUFBQ3JCO0tBQVU7SUFFZCxJQUFJLENBQUNsQixRQUFRLE9BQU87SUFFcEIsNkJBQTZCO0lBQzdCLGFBQWE7SUFDYiw0QkFBNEI7SUFDNUIsc0JBQXNCO0lBQ3RCLDJCQUEyQjtJQUMzQix5Q0FBeUM7SUFDekMsMkVBQTJFO0lBQzNFLFNBQVM7SUFDVCxPQUFPO0lBQ1AsSUFBSTtJQUVKLHFCQUNFLDhEQUFDUCxzRkFBTUE7UUFBQ3VGLE1BQU1oRjtRQUFRQyxTQUFTQTtRQUFTZ0YsV0FBVTs7MEJBQ2hELDhEQUFDQztnQkFDQ0QsV0FBVTtnQkFDVkUsZUFBWTs7Ozs7OzBCQUVkLDhEQUFDRDtnQkFBSUQsV0FBVTswQkFDYiw0RUFBQ3hGLHNGQUFNQSxDQUFDMkYsS0FBSztvQkFBQ0gsV0FBVTs4QkFDckIsQ0FBQy9ELDBCQUNBOzswQ0FDRSw4REFBQ3pCLHNGQUFNQSxDQUFDNEYsS0FBSztnQ0FBQ0osV0FBVTswQ0FDckJ2RCxnQkFBZ0IsV0FBVyxtQkFBbUI7Ozs7Ozs0QkFFaEROLHVCQUNDLDhEQUFDOEQ7Z0NBQUlELFdBQVU7MENBQ1o3RDs7Ozs7OzBDQUdMLDhEQUFDMUIsbUZBQUdBLENBQUM0RixLQUFLO2dDQUNSQyxlQUFlN0QsZ0JBQWdCLFdBQVcsSUFBSTtnQ0FDOUM4RCxVQUFVLENBQUNyRCxRQUNUUixlQUFlUSxVQUFVLElBQUksV0FBVzs7a0RBRzFDLDhEQUFDekMsbUZBQUdBLENBQUMrRixJQUFJO3dDQUFDUixXQUFVOzswREFDbEIsOERBQUN2RixtRkFBR0E7Z0RBQ0Z1RixXQUFXO3dEQUFDLEVBQUVTLFFBQVEsRUFBRTsyREFDdEIsZ0ZBS0EsT0FIQ0EsV0FDSSwyRUFDQTs7MERBR1I7Ozs7OzswREFHRCw4REFBQ2hHLG1GQUFHQTtnREFDRnVGLFdBQVc7d0RBQUMsRUFBRVMsUUFBUSxFQUFFOzJEQUN0QixnRkFLQSxPQUhDQSxXQUNJLDJFQUNBOzswREFHUjs7Ozs7Ozs7Ozs7O2tEQUlILDhEQUFDaEcsbUZBQUdBLENBQUNpRyxNQUFNOzswREFDVCw4REFBQ2pHLG1GQUFHQSxDQUFDMEYsS0FBSzswREFDUiw0RUFBQ1E7b0RBQUtDLFVBQVUzQztvREFBZStCLFdBQVU7O3NFQUN2Qyw4REFBQ0M7OzhFQUNDLDhEQUFDWTtvRUFDQ0MsU0FBUTtvRUFDUmQsV0FBVTs4RUFDWDs7Ozs7OzhFQUdELDhEQUFDZTtvRUFDQ0MsTUFBSztvRUFDTEMsSUFBRztvRUFDSG5FLE9BQU9qQjtvRUFDUDBFLFVBQVUzRDtvRUFDVnNFLFdBQVcsQ0FBQ3JFO3dFQUNWLElBQUlBLEVBQUVXLEdBQUcsS0FBSyxLQUFLOzRFQUNqQlgsRUFBRW1CLGNBQWM7d0VBQ2xCO29FQUNGO29FQUNBbUQsYUFBWTtvRUFDWm5CLFdBQVU7b0VBQ1ZvQixRQUFROzs7Ozs7Ozs7Ozs7c0VBR1osOERBQUNuQjs0REFBSWdCLElBQUc7NERBQWdCakIsV0FBVTs7Ozs7O3NFQUNsQyw4REFBQ3FCOzREQUNDTCxNQUFLOzREQUNMTSxVQUFVakYsV0FBVyxDQUFDUjs0REFDdEJtRSxXQUFVO3NFQUVUM0QsVUFBVSxlQUFlOzs7Ozs7Ozs7Ozs7Ozs7OzswREFJaEMsOERBQUM1QixtRkFBR0EsQ0FBQzBGLEtBQUs7MERBQ1IsNEVBQUNRO29EQUFLQyxVQUFVM0M7b0RBQWUrQixXQUFVOztzRUFDdkMsOERBQUNDOzs4RUFDQyw4REFBQ1k7b0VBQ0NDLFNBQVE7b0VBQ1JkLFdBQVU7OEVBQ1g7Ozs7Ozs4RUFHRCw4REFBQ2U7b0VBQ0NDLE1BQUs7b0VBQ0xDLElBQUc7b0VBQ0huRSxPQUFPakI7b0VBQ1AwRSxVQUFVM0Q7b0VBQ1ZzRSxXQUFXLENBQUNyRTt3RUFDVixJQUFJQSxFQUFFVyxHQUFHLEtBQUssS0FBSzs0RUFDakJYLEVBQUVtQixjQUFjO3dFQUNsQjtvRUFDRjtvRUFDQW1ELGFBQVk7b0VBQ1puQixXQUFVO29FQUNWb0IsUUFBUTs7Ozs7Ozs7Ozs7O3NFQUlaLDhEQUFDbkI7NERBQUlnQixJQUFHOzREQUFnQmpCLFdBQVU7Ozs7OztzRUFDbEMsOERBQUNxQjs0REFDQ0wsTUFBSzs0REFDTE0sVUFBVWpGLFdBQVcsQ0FBQ1I7NERBQ3RCbUUsV0FBVTtzRUFFVDNELFVBQVUsZUFBZTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3FEQVF0Qyw4REFBQ3NFO3dCQUFLQyxVQUFVekI7d0JBQWlCYSxXQUFVOzswQ0FDekMsOERBQUN4RixzRkFBTUEsQ0FBQzRGLEtBQUs7Z0NBQUNKLFdBQVU7MENBQW1FOzs7Ozs7NEJBRzFGN0QsdUJBQ0MsOERBQUM4RDtnQ0FBSUQsV0FBVTswQ0FDWjdEOzs7Ozs7MENBR0wsOERBQUM4RDs7a0RBQ0MsOERBQUNzQjt3Q0FBRXZCLFdBQVU7OzRDQUFnRDs0Q0FDdEJuRTs7Ozs7OztrREFFdkMsOERBQUNvRTt3Q0FBSUQsV0FBVTtrREFDWmpFLElBQUl5RixHQUFHLENBQUMsQ0FBQ0MsT0FBT3ZFLHNCQUNmLDhEQUFDNkQ7Z0RBRUNXLEtBQUssQ0FBQ0M7b0RBQ0poRixRQUFRVSxPQUFPLENBQUNILE1BQU0sR0FBR3lFO2dEQUMzQjtnREFDQVgsTUFBSztnREFDTFksV0FBVztnREFDWDlFLE9BQU8yRTtnREFDUGxCLFVBQVUsQ0FBQzFELElBQU1JLGdCQUFnQkMsT0FBT0wsRUFBRUUsTUFBTSxDQUFDRCxLQUFLO2dEQUN0RG9FLFdBQVcsQ0FBQ3JFLElBQU1VLGNBQWNMLE9BQU9MO2dEQUN2Q2dGLFNBQVNwRTtnREFDVHVDLFdBQVU7Z0RBQ1ZvQixRQUFROytDQVhIbEU7Ozs7Ozs7Ozs7a0RBZVgsOERBQUNtRTt3Q0FDQ0wsTUFBSzt3Q0FDTGMsU0FBUzs0Q0FDUDVGLGFBQWE7NENBQ2JGLE9BQU87Z0RBQUM7Z0RBQUk7Z0RBQUk7Z0RBQUk7Z0RBQUk7Z0RBQUk7NkNBQUc7d0NBQ2pDO3dDQUNBZ0UsV0FBVTtrREFDWDs7Ozs7Ozs7Ozs7OzBDQUlILDhEQUFDcUI7Z0NBQ0NMLE1BQUs7Z0NBQ0xNLFVBQVVqRixXQUFXTixJQUFJZ0csSUFBSSxDQUFDLENBQUNOLFFBQVUsQ0FBQ0E7Z0NBQzFDekIsV0FBVTswQ0FFVDNELFVBQVUsaUJBQWlCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBUTVDO0dBaGFNdkI7O1FBU0FKLG9EQUFTQTtRQUtUQyxvREFBU0E7UUFDU0MscURBQVdBOzs7S0FmN0JFO0FBa2FOLGlFQUFlQSxzQkFBc0JBLEVBQUMiLCJzb3VyY2VzIjpbIi9Vc2Vycy9zaGF3YXovRGV2ZWxvcGVyL2JpemNvaW4vYml6Y29pbl9jbGllbnQvY29tcG9uZW50cy9FbWFpbFZlcmlmaWNhdGlvbk1vZGFsLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcblxuaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlLCB1c2VSZWYsIHVzZUVmZmVjdCB9IGZyb20gXCJyZWFjdFwiO1xuaW1wb3J0IHsgRGlhbG9nLCBUYWIgfSBmcm9tIFwiQGhlYWRsZXNzdWkvcmVhY3RcIjtcbmltcG9ydCB7IHVzZVNpZ25VcCwgdXNlU2lnbkluIH0gZnJvbSBcIkBjbGVyay9uZXh0anNcIjtcbmltcG9ydCB7IHVzZU11dGF0aW9uIH0gZnJvbSBcImNvbnZleC9yZWFjdFwiO1xuaW1wb3J0IHsgYXBpIH0gZnJvbSBcIkAvY29udmV4L19nZW5lcmF0ZWQvYXBpXCI7XG5cbmludGVyZmFjZSBFbWFpbENvZGVGYWN0b3Ige1xuICBzdHJhdGVneTogXCJlbWFpbF9jb2RlXCI7XG4gIGVtYWlsQWRkcmVzc0lkOiBzdHJpbmc7XG4gIHNhZmVJZGVudGlmaWVyOiBzdHJpbmc7XG59XG5cbmludGVyZmFjZSBFbWFpbFZlcmlmaWNhdGlvbk1vZGFsUHJvcHMge1xuICBpc09wZW46IGJvb2xlYW47XG4gIG9uQ2xvc2U6ICgpID0+IHZvaWQ7XG4gIG9uU3VjY2Vzcz86ICgpID0+IHZvaWQ7XG59XG5cbmNvbnN0IEVtYWlsVmVyaWZpY2F0aW9uTW9kYWw6IFJlYWN0LkZDPEVtYWlsVmVyaWZpY2F0aW9uTW9kYWxQcm9wcz4gPSAoe1xuICBpc09wZW4sXG4gIG9uQ2xvc2UsXG4gIG9uU3VjY2Vzcyxcbn0pID0+IHtcbiAgY29uc3Qge1xuICAgIGlzTG9hZGVkOiBzaWduVXBMb2FkZWQsXG4gICAgc2lnblVwLFxuICAgIHNldEFjdGl2ZTogc2V0U2lnblVwQWN0aXZlLFxuICB9ID0gdXNlU2lnblVwKCk7XG4gIGNvbnN0IHtcbiAgICBpc0xvYWRlZDogc2lnbkluTG9hZGVkLFxuICAgIHNpZ25JbixcbiAgICBzZXRBY3RpdmU6IHNldFNpZ25JbkFjdGl2ZSxcbiAgfSA9IHVzZVNpZ25JbigpO1xuICBjb25zdCB1cHNlcnRQcm9maWxlID0gdXNlTXV0YXRpb24oYXBpLm15RnVuY3Rpb25zLnVwc2VydFVzZXJQcm9maWxlKTtcbiAgY29uc3QgW2VtYWlsLCBzZXRFbWFpbF0gPSB1c2VTdGF0ZShcIlwiKTtcbiAgY29uc3QgW290cCwgc2V0T3RwXSA9IHVzZVN0YXRlKFtcIlwiLCBcIlwiLCBcIlwiLCBcIlwiLCBcIlwiLCBcIlwiXSk7XG4gIGNvbnN0IFtpc090cFNlbnQsIHNldElzT3RwU2VudF0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtlcnJvciwgc2V0RXJyb3JdID0gdXNlU3RhdGUoXCJcIik7XG4gIGNvbnN0IFtsb2FkaW5nLCBzZXRMb2FkaW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgLy8gY29uc3QgW3Nob3dQcm9maWxlQ3JlYXRpb24sIHNldFNob3dQcm9maWxlQ3JlYXRpb25dID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbaXNTaWduSW4sIHNldElzU2lnbkluXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW3NlbGVjdGVkVGFiLCBzZXRTZWxlY3RlZFRhYl0gPSB1c2VTdGF0ZShcInNpZ25pblwiKTtcbiAgY29uc3Qgb3RwUmVmcyA9IHVzZVJlZjwoSFRNTElucHV0RWxlbWVudCB8IG51bGwpW10+KFtdKTtcblxuICBjb25zdCBoYW5kbGVFbWFpbENoYW5nZSA9IChlOiBSZWFjdC5DaGFuZ2VFdmVudDxIVE1MSW5wdXRFbGVtZW50PikgPT4ge1xuICAgIC8vIFJlbW92ZSBhbnkgc3BhY2VzIGZyb20gdGhlIGlucHV0IHZhbHVlXG4gICAgY29uc3QgdmFsdWUgPSBlLnRhcmdldC52YWx1ZS5yZXBsYWNlKC9cXHMrL2csIFwiXCIpO1xuICAgIHNldEVtYWlsKHZhbHVlKTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVPdHBDaGFuZ2UgPSAoaW5kZXg6IG51bWJlciwgdmFsdWU6IHN0cmluZykgPT4ge1xuICAgIGlmICh2YWx1ZS5sZW5ndGggPiAxKSB2YWx1ZSA9IHZhbHVlWzBdO1xuICAgIGNvbnN0IG5ld090cCA9IFsuLi5vdHBdO1xuICAgIG5ld090cFtpbmRleF0gPSB2YWx1ZTtcbiAgICBzZXRPdHAobmV3T3RwKTtcbiAgICBpZiAodmFsdWUgIT09IFwiXCIgJiYgaW5kZXggPCA1KSB7XG4gICAgICBvdHBSZWZzLmN1cnJlbnRbaW5kZXggKyAxXT8uZm9jdXMoKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlS2V5RG93biA9IChcbiAgICBpbmRleDogbnVtYmVyLFxuICAgIGU6IFJlYWN0LktleWJvYXJkRXZlbnQ8SFRNTElucHV0RWxlbWVudD4sXG4gICkgPT4ge1xuICAgIGlmIChlLmtleSA9PT0gXCJCYWNrc3BhY2VcIiAmJiAhb3RwW2luZGV4XSAmJiBpbmRleCA+IDApIHtcbiAgICAgIG90cFJlZnMuY3VycmVudFtpbmRleCAtIDFdPy5mb2N1cygpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVPdHBQYXN0ZSA9IChlOiBSZWFjdC5DbGlwYm9hcmRFdmVudDxIVE1MSW5wdXRFbGVtZW50PikgPT4ge1xuICAgIGNvbnN0IHBhc3RlID0gZS5jbGlwYm9hcmREYXRhLmdldERhdGEoXCJ0ZXh0XCIpO1xuICAgIGlmICgvXlxcZHs2fSQvLnRlc3QocGFzdGUpKSB7XG4gICAgICBzZXRPdHAocGFzdGUuc3BsaXQoXCJcIikpO1xuICAgICAgLy8gRm9jdXMgdGhlIGxhc3QgaW5wdXRcbiAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICBvdHBSZWZzLmN1cnJlbnRbNV0/LmZvY3VzKCk7XG4gICAgICB9LCAwKTtcbiAgICAgIGUucHJldmVudERlZmF1bHQoKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlU2VuZE90cCA9IGFzeW5jIChlOiBSZWFjdC5Gb3JtRXZlbnQpID0+IHtcbiAgICBlLnByZXZlbnREZWZhdWx0KCk7XG4gICAgc2V0RXJyb3IoXCJcIik7XG4gICAgc2V0TG9hZGluZyh0cnVlKTtcbiAgICB0cnkge1xuICAgICAgaWYgKHNlbGVjdGVkVGFiID09PSBcInNpZ251cFwiKSB7XG4gICAgICAgIGlmICghc2lnblVwTG9hZGVkKSB7XG4gICAgICAgICAgc2V0RXJyb3IoXCJTaWduIHVwIGlzIG5vdCBsb2FkZWQgeWV0XCIpO1xuICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgICBhd2FpdCBzaWduVXAuY3JlYXRlKHtcbiAgICAgICAgICBlbWFpbEFkZHJlc3M6IGVtYWlsLFxuICAgICAgICB9KTtcblxuICAgICAgICBhd2FpdCBzaWduVXAucHJlcGFyZUVtYWlsQWRkcmVzc1ZlcmlmaWNhdGlvbigpO1xuICAgICAgICBzZXRJc1NpZ25JbihmYWxzZSk7XG4gICAgICAgIHNldElzT3RwU2VudCh0cnVlKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGlmICghc2lnbkluTG9hZGVkKSB7XG4gICAgICAgICAgc2V0RXJyb3IoXCJTaWduIGluIGlzIG5vdCBsb2FkZWQgeWV0XCIpO1xuICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCB7IHN1cHBvcnRlZEZpcnN0RmFjdG9ycyB9ID0gYXdhaXQgc2lnbkluLmNyZWF0ZSh7XG4gICAgICAgICAgaWRlbnRpZmllcjogZW1haWwsXG4gICAgICAgIH0pO1xuXG4gICAgICAgIGNvbnN0IGVtYWlsQ29kZUZhY3RvciA9IHN1cHBvcnRlZEZpcnN0RmFjdG9ycz8uZmluZChcbiAgICAgICAgICAoZmFjdG9yKTogZmFjdG9yIGlzIEVtYWlsQ29kZUZhY3RvciA9PlxuICAgICAgICAgICAgdHlwZW9mIGZhY3RvciA9PT0gXCJvYmplY3RcIiAmJlxuICAgICAgICAgICAgZmFjdG9yICE9PSBudWxsICYmXG4gICAgICAgICAgICBcInN0cmF0ZWd5XCIgaW4gZmFjdG9yICYmXG4gICAgICAgICAgICBmYWN0b3Iuc3RyYXRlZ3kgPT09IFwiZW1haWxfY29kZVwiICYmXG4gICAgICAgICAgICBcImVtYWlsQWRkcmVzc0lkXCIgaW4gZmFjdG9yLFxuICAgICAgICApO1xuXG4gICAgICAgIGlmIChlbWFpbENvZGVGYWN0b3IpIHtcbiAgICAgICAgICBhd2FpdCBzaWduSW4ucHJlcGFyZUZpcnN0RmFjdG9yKHtcbiAgICAgICAgICAgIHN0cmF0ZWd5OiBcImVtYWlsX2NvZGVcIixcbiAgICAgICAgICAgIGVtYWlsQWRkcmVzc0lkOiBlbWFpbENvZGVGYWN0b3IuZW1haWxBZGRyZXNzSWQsXG4gICAgICAgICAgfSk7XG4gICAgICAgICAgc2V0SXNTaWduSW4odHJ1ZSk7XG4gICAgICAgICAgc2V0SXNPdHBTZW50KHRydWUpO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIHNldEVycm9yKFxuICAgICAgICAgICAgXCJObyBlbWFpbCBjb2RlIGZhY3RvciBhdmFpbGFibGUuIFBsZWFzZSBtYWtlIHN1cmUgZW1haWwgdmVyaWZpY2F0aW9uIGlzIGVuYWJsZWQgaW4geW91ciBDbGVyayBzZXR0aW5ncy5cIixcbiAgICAgICAgICApO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyOiB1bmtub3duKSB7XG4gICAgICBjb25zdCBlcnJvck1lc3NhZ2UgPVxuICAgICAgICBlcnIgaW5zdGFuY2VvZiBBcnJheVxuICAgICAgICAgID8gZXJyWzBdPy5tZXNzYWdlIHx8IFwiRmFpbGVkIHRvIHNlbmQgT1RQLlwiXG4gICAgICAgICAgOiBlcnIgaW5zdGFuY2VvZiBFcnJvclxuICAgICAgICAgICAgPyBlcnIubWVzc2FnZVxuICAgICAgICAgICAgOiBcIkZhaWxlZCB0byBzZW5kIE9UUC5cIjtcbiAgICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvcjpcIiwgZXJyKTtcbiAgICAgIHNldEVycm9yKGVycm9yTWVzc2FnZSk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVWZXJpZnlPdHAgPSBhc3luYyAoZTogUmVhY3QuRm9ybUV2ZW50KSA9PiB7XG4gICAgZS5wcmV2ZW50RGVmYXVsdCgpO1xuICAgIHNldEVycm9yKFwiXCIpO1xuICAgIHNldExvYWRpbmcodHJ1ZSk7XG4gICAgY29uc3Qgb3RwU3RyaW5nID0gb3RwLmpvaW4oXCJcIik7XG5cbiAgICBjb25zb2xlLmxvZyhcIkF0dGVtcHRpbmcgdmVyaWZpY2F0aW9uIHdpdGggY29kZTpcIiwgb3RwU3RyaW5nKTtcbiAgICBjb25zb2xlLmxvZyhcIklzIHNpZ24gaW4/XCIsIGlzU2lnbkluKTtcbiAgICBjb25zb2xlLmxvZyhcIlNpZ24gdXAgbG9hZGVkP1wiLCBzaWduVXBMb2FkZWQpO1xuICAgIGNvbnNvbGUubG9nKFwiU2lnbiBpbiBsb2FkZWQ/XCIsIHNpZ25JbkxvYWRlZCk7XG5cbiAgICB0cnkge1xuICAgICAgaWYgKGlzU2lnbkluKSB7XG4gICAgICAgIGlmICghc2lnbkluTG9hZGVkKSB7XG4gICAgICAgICAgY29uc29sZS5lcnJvcihcIlNpZ24gaW4gbm90IGxvYWRlZFwiKTtcbiAgICAgICAgICBzZXRFcnJvcihcIlNpZ24gaW4gbm90IGxvYWRlZC4gUGxlYXNlIHRyeSBhZ2Fpbi5cIik7XG4gICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG4gICAgICAgIGNvbnNvbGUubG9nKFwiQXR0ZW1wdGluZyBzaWduLWluIHZlcmlmaWNhdGlvbi4uLlwiKTtcbiAgICAgICAgY29uc3QgYXR0ZW1wdCA9IGF3YWl0IHNpZ25Jbi5hdHRlbXB0Rmlyc3RGYWN0b3Ioe1xuICAgICAgICAgIHN0cmF0ZWd5OiBcImVtYWlsX2NvZGVcIixcbiAgICAgICAgICBjb2RlOiBvdHBTdHJpbmcsXG4gICAgICAgIH0pO1xuICAgICAgICBjb25zb2xlLmxvZyhcIlNpZ24taW4gYXR0ZW1wdCByZXN1bHQ6XCIsIGF0dGVtcHQpO1xuXG4gICAgICAgIGlmIChhdHRlbXB0LnN0YXR1cyA9PT0gXCJjb21wbGV0ZVwiKSB7XG4gICAgICAgICAgYXdhaXQgc2V0U2lnbkluQWN0aXZlKHsgc2Vzc2lvbjogYXR0ZW1wdC5jcmVhdGVkU2Vzc2lvbklkIH0pO1xuICAgICAgICAgIG9uU3VjY2Vzcz8uKCk7XG4gICAgICAgICAgb25DbG9zZSgpO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoXCJTaWduLWluIGF0dGVtcHQgZmFpbGVkOlwiLCBhdHRlbXB0KTtcbiAgICAgICAgICBzZXRFcnJvcihgVmVyaWZpY2F0aW9uIGluY29tcGxldGUuIFN0YXR1czogJHthdHRlbXB0LnN0YXR1c31gKTtcbiAgICAgICAgfVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgaWYgKCFzaWduVXBMb2FkZWQpIHtcbiAgICAgICAgICBjb25zb2xlLmVycm9yKFwiU2lnbiB1cCBub3QgbG9hZGVkXCIpO1xuICAgICAgICAgIHNldEVycm9yKFwiU2lnbiB1cCBub3QgbG9hZGVkLiBQbGVhc2UgdHJ5IGFnYWluLlwiKTtcbiAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cbiAgICAgICAgY29uc29sZS5sb2coXCJBdHRlbXB0aW5nIHNpZ24tdXAgdmVyaWZpY2F0aW9uLi4uXCIpO1xuICAgICAgICBjb25zb2xlLmxvZyhcIlNpZ25VcCBvYmplY3Q6XCIsIHNpZ25VcCk7XG5cbiAgICAgICAgY29uc3QgYXR0ZW1wdCA9IGF3YWl0IHNpZ25VcC5hdHRlbXB0RW1haWxBZGRyZXNzVmVyaWZpY2F0aW9uKHtcbiAgICAgICAgICBjb2RlOiBvdHBTdHJpbmcsXG4gICAgICAgIH0pO1xuICAgICAgICBjb25zb2xlLmxvZyhcIlNpZ24tdXAgYXR0ZW1wdCByZXN1bHQ6XCIsIGF0dGVtcHQpO1xuXG4gICAgICAgIGlmIChhdHRlbXB0LnN0YXR1cyA9PT0gXCJjb21wbGV0ZVwiKSB7XG4gICAgICAgICAgYXdhaXQgc2V0U2lnblVwQWN0aXZlKHsgc2Vzc2lvbjogYXR0ZW1wdC5jcmVhdGVkU2Vzc2lvbklkIH0pO1xuXG4gICAgICAgICAgLy8gVXBkYXRlIENvbnZleCBkYXRhYmFzZSB3aXRoIHVzZXIgcHJvZmlsZVxuICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICBhd2FpdCB1cHNlcnRQcm9maWxlKHtcbiAgICAgICAgICAgICAgZW1haWwsXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIG9uU3VjY2Vzcz8uKCk7XG4gICAgICAgICAgICBvbkNsb3NlKCk7XG4gICAgICAgICAgfSBjYXRjaCAoY29udmV4RXJyb3IpIHtcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoXCJGYWlsZWQgdG8gdXBkYXRlIENvbnZleCBkYXRhYmFzZTpcIiwgY29udmV4RXJyb3IpO1xuICAgICAgICAgICAgc2V0RXJyb3IoXG4gICAgICAgICAgICAgIFwiQWNjb3VudCBjcmVhdGVkIGJ1dCBwcm9maWxlIHVwZGF0ZSBmYWlsZWQuIFBsZWFzZSB1cGRhdGUgeW91ciBwcm9maWxlIGxhdGVyLlwiLFxuICAgICAgICAgICAgKTtcbiAgICAgICAgICB9XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgY29uc29sZS5lcnJvcihcIlNpZ24tdXAgYXR0ZW1wdCBmYWlsZWQ6XCIsIGF0dGVtcHQpO1xuICAgICAgICAgIHNldEVycm9yKGBWZXJpZmljYXRpb24gaW5jb21wbGV0ZS4gU3RhdHVzOiAke2F0dGVtcHQuc3RhdHVzIHx8ICd1bmtub3duJ31gKTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycjogdW5rbm93bikge1xuICAgICAgY29uc29sZS5lcnJvcihcIlZlcmlmaWNhdGlvbiBlcnJvcjpcIiwgZXJyKTtcbiAgICAgIGNvbnN0IGVycm9yTWVzc2FnZSA9IGVyciBpbnN0YW5jZW9mIEFycmF5XG4gICAgICAgID8gZXJyWzBdPy5tZXNzYWdlIHx8IFwiRmFpbGVkIHRvIHZlcmlmeSBPVFAuXCJcbiAgICAgICAgOiBlcnIgaW5zdGFuY2VvZiBFcnJvclxuICAgICAgICAgID8gZXJyLm1lc3NhZ2VcbiAgICAgICAgICA6IFwiRmFpbGVkIHRvIHZlcmlmeSBPVFAuXCI7XG4gICAgICBzZXRFcnJvcihlcnJvck1lc3NhZ2UpO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgLy8gY29uc3QgaGFuZGxlUHJvZmlsZUNyZWF0aW9uID0gYXN5bmMgKCkgPT4ge1xuICAvLyAgIHRyeSB7XG4gIC8vICAgICAvLyBZb3UgbWF5IHdhbnQgdG8gY2FsbCB5b3VyIGJhY2tlbmQgdG8gY3JlYXRlIGEgcHJvZmlsZSBoZXJlXG4gIC8vICAgICBvblN1Y2Nlc3M/LigpO1xuICAvLyAgICAgb25DbG9zZSgpO1xuICAvLyAgICAgLy8gTm8gcmVkaXJlY3QgYWZ0ZXIgcHJvZmlsZSBjcmVhdGlvblxuICAvLyAgIH0gY2F0Y2ggKGVycjogdW5rbm93bikge1xuICAvLyAgICAgc2V0RXJyb3IoZXJyIGluc3RhbmNlb2YgRXJyb3IgPyBlcnIubWVzc2FnZSB8fCAnRmFpbGVkIHRvIGNyZWF0ZSBwcm9maWxlJyA6ICdGYWlsZWQgdG8gY3JlYXRlIHByb2ZpbGUnKTtcbiAgLy8gICAgIHNldFNob3dQcm9maWxlQ3JlYXRpb24oZmFsc2UpO1xuICAvLyAgIH1cbiAgLy8gfTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmIChpc090cFNlbnQpIHtcbiAgICAgIG90cFJlZnMuY3VycmVudFswXT8uZm9jdXMoKTtcbiAgICB9XG4gIH0sIFtpc090cFNlbnRdKTtcblxuICBpZiAoIWlzT3BlbikgcmV0dXJuIG51bGw7XG5cbiAgLy8gaWYgKHNob3dQcm9maWxlQ3JlYXRpb24pIHtcbiAgLy8gICByZXR1cm4gKFxuICAvLyAgICAgPFByb2ZpbGVDcmVhdGlvbk1vZGFsXG4gIC8vICAgICAgIGlzT3Blbj17dHJ1ZX1cbiAgLy8gICAgICAgb25DbG9zZT17KCkgPT4ge319XG4gIC8vICAgICAgIG9uU3VibWl0PXtoYW5kbGVQcm9maWxlQ3JlYXRpb259XG4gIC8vICAgICAgIHBob25lTnVtYmVyPXtlbWFpbH0gLy8gUGFzcyBlbWFpbCBhcyBwaG9uZU51bWJlciBmb3IgY29tcGF0aWJpbGl0eVxuICAvLyAgICAgLz5cbiAgLy8gICApO1xuICAvLyB9XG5cbiAgcmV0dXJuIChcbiAgICA8RGlhbG9nIG9wZW49e2lzT3Blbn0gb25DbG9zZT17b25DbG9zZX0gY2xhc3NOYW1lPVwicmVsYXRpdmUgei01MFwiPlxuICAgICAgPGRpdlxuICAgICAgICBjbGFzc05hbWU9XCJmaXhlZCBpbnNldC0wIGJnLWJsYWNrLzMwIGJhY2tkcm9wLWJsdXItc21cIlxuICAgICAgICBhcmlhLWhpZGRlbj1cInRydWVcIlxuICAgICAgLz5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZml4ZWQgaW5zZXQtMCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBwLTRcIj5cbiAgICAgICAgPERpYWxvZy5QYW5lbCBjbGFzc05hbWU9XCJteC1hdXRvIHctZnVsbCBtYXgtdy1tZCByb3VuZGVkLTJ4bCBiZy13aGl0ZSBkYXJrOmJnLXN0b25lLTgwMCBwLTggc2hhZG93LTJ4bFwiPlxuICAgICAgICAgIHshaXNPdHBTZW50ID8gKFxuICAgICAgICAgICAgPD5cbiAgICAgICAgICAgICAgPERpYWxvZy5UaXRsZSBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LXNlbWlib2xkIG1iLTYgdGV4dC1wcmltYXJ5IGRhcms6dGV4dC1wcmltYXJ5LWxpZ2h0XCI+XG4gICAgICAgICAgICAgICAge3NlbGVjdGVkVGFiID09PSBcInNpZ251cFwiID8gXCJDcmVhdGUgQWNjb3VudFwiIDogXCJTaWduIEluXCJ9XG4gICAgICAgICAgICAgIDwvRGlhbG9nLlRpdGxlPlxuICAgICAgICAgICAgICB7ZXJyb3IgJiYgKFxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNiBwLTQgYmctcmVkLTUwIGRhcms6YmctcmVkLTkwMC81MCB0ZXh0LXJlZC02MDAgZGFyazp0ZXh0LXJlZC00MDAgdGV4dC1zbSByb3VuZGVkLWxnIGJvcmRlciBib3JkZXItcmVkLTEwMFwiPlxuICAgICAgICAgICAgICAgICAge2Vycm9yfVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICA8VGFiLkdyb3VwXG4gICAgICAgICAgICAgICAgc2VsZWN0ZWRJbmRleD17c2VsZWN0ZWRUYWIgPT09IFwic2lnbmluXCIgPyAwIDogMX1cbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGluZGV4KSA9PlxuICAgICAgICAgICAgICAgICAgc2V0U2VsZWN0ZWRUYWIoaW5kZXggPT09IDAgPyBcInNpZ25pblwiIDogXCJzaWdudXBcIilcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8VGFiLkxpc3QgY2xhc3NOYW1lPVwiZmxleCBzcGFjZS14LTEgcm91bmRlZC14bCBiZy1zdG9uZS0xMDAgZGFyazpiZy1zdG9uZS03MDAgcC0xIG1iLTZcIj5cbiAgICAgICAgICAgICAgICAgIDxUYWJcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXsoeyBzZWxlY3RlZCB9KSA9PlxuICAgICAgICAgICAgICAgICAgICAgIGB3LWZ1bGwgcm91bmRlZC1sZyBweS0yLjUgdGV4dC1zbSBmb250LW1lZGl1bSBsZWFkaW5nLTVcbiAgICAgICAgICAgICAgICAgICAgICR7XG4gICAgICAgICAgICAgICAgICAgICAgIHNlbGVjdGVkXG4gICAgICAgICAgICAgICAgICAgICAgICAgPyBcImJnLXdoaXRlIGRhcms6Ymctc3RvbmUtNjAwIHNoYWRvdyB0ZXh0LXByaW1hcnkgZGFyazp0ZXh0LXByaW1hcnktbGlnaHRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgIDogXCJ0ZXh0LWdyYXktNjAwIGRhcms6dGV4dC1ncmF5LTQwMCBob3ZlcjpiZy13aGl0ZS9bMC4xMl0gaG92ZXI6dGV4dC1wcmltYXJ5XCJcbiAgICAgICAgICAgICAgICAgICAgIH1gXG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgU2lnbiBJblxuICAgICAgICAgICAgICAgICAgPC9UYWI+XG4gICAgICAgICAgICAgICAgICA8VGFiXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17KHsgc2VsZWN0ZWQgfSkgPT5cbiAgICAgICAgICAgICAgICAgICAgICBgdy1mdWxsIHJvdW5kZWQtbGcgcHktMi41IHRleHQtc20gZm9udC1tZWRpdW0gbGVhZGluZy01XG4gICAgICAgICAgICAgICAgICAgICAke1xuICAgICAgICAgICAgICAgICAgICAgICBzZWxlY3RlZFxuICAgICAgICAgICAgICAgICAgICAgICAgID8gXCJiZy13aGl0ZSBkYXJrOmJnLXN0b25lLTYwMCBzaGFkb3cgdGV4dC1wcmltYXJ5IGRhcms6dGV4dC1wcmltYXJ5LWxpZ2h0XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICA6IFwidGV4dC1ncmF5LTYwMCBkYXJrOnRleHQtZ3JheS00MDAgaG92ZXI6Ymctd2hpdGUvWzAuMTJdIGhvdmVyOnRleHQtcHJpbWFyeVwiXG4gICAgICAgICAgICAgICAgICAgICB9YFxuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIFNpZ24gVXBcbiAgICAgICAgICAgICAgICAgIDwvVGFiPlxuICAgICAgICAgICAgICAgIDwvVGFiLkxpc3Q+XG4gICAgICAgICAgICAgICAgPFRhYi5QYW5lbHM+XG4gICAgICAgICAgICAgICAgICA8VGFiLlBhbmVsPlxuICAgICAgICAgICAgICAgICAgICA8Zm9ybSBvblN1Ym1pdD17aGFuZGxlU2VuZE90cH0gY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxsYWJlbFxuICAgICAgICAgICAgICAgICAgICAgICAgICBodG1sRm9yPVwic2lnbmluLWVtYWlsXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LXByaW1hcnkgZGFyazp0ZXh0LXByaW1hcnktbGlnaHQgbWItMlwiXG4gICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIEVtYWlsIEFkZHJlc3NcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cImVtYWlsXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgaWQ9XCJzaWduaW4tZW1haWxcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17ZW1haWx9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVFbWFpbENoYW5nZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25LZXlEb3duPXsoZSkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmIChlLmtleSA9PT0gXCIgXCIpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGUucHJldmVudERlZmF1bHQoKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiRW50ZXIgeW91ciBlbWFpbCBhZGRyZXNzXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleC0xIHctZnVsbCBoLTE0IHB4LTQgYmctd2hpdGUgZGFyazpiZy1zdG9uZS03MDAgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCBkYXJrOmJvcmRlci1zdG9uZS02MDAgcm91bmRlZC1sZyBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1wcmltYXJ5IGZvY3VzOmJvcmRlci1wcmltYXJ5IHRleHQtbGdcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICByZXF1aXJlZFxuICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGlkPVwiY2xlcmstY2FwdGNoYVwiIGNsYXNzTmFtZT1cIm10LTRcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJzdWJtaXRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2xvYWRpbmcgfHwgIWVtYWlsfVxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGJnLXN0b25lLTkwMCBob3ZlcjpiZy1zdG9uZS03MDAgdGV4dC13aGl0ZSBmb250LW1lZGl1bSBweS0yLjUgcHgtNCByb3VuZGVkLWxnIHRyYW5zaXRpb24tY29sb3JzIGRhcms6Ymctd2hpdGUgZGFyazp0ZXh0LXN0b25lLTgwMCBkYXJrOmhvdmVyOmJnLXN0b25lLTMwMCBoLTE0IHRleHQtYmFzZSBkaXNhYmxlZDpvcGFjaXR5LTUwIGRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZFwiXG4gICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAge2xvYWRpbmcgPyBcIlNlbmRpbmcuLi5cIiA6IFwiQ29udGludWVcIn1cbiAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgPC9mb3JtPlxuICAgICAgICAgICAgICAgICAgPC9UYWIuUGFuZWw+XG4gICAgICAgICAgICAgICAgICA8VGFiLlBhbmVsPlxuICAgICAgICAgICAgICAgICAgICA8Zm9ybSBvblN1Ym1pdD17aGFuZGxlU2VuZE90cH0gY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxsYWJlbFxuICAgICAgICAgICAgICAgICAgICAgICAgICBodG1sRm9yPVwic2lnbnVwLWVtYWlsXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LXByaW1hcnkgZGFyazp0ZXh0LXByaW1hcnktbGlnaHQgbWItMlwiXG4gICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIEVtYWlsIEFkZHJlc3NcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cImVtYWlsXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgaWQ9XCJzaWdudXAtZW1haWxcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17ZW1haWx9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVFbWFpbENoYW5nZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25LZXlEb3duPXsoZSkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmIChlLmtleSA9PT0gXCIgXCIpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGUucHJldmVudERlZmF1bHQoKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiRW50ZXIgeW91ciBlbWFpbCBhZGRyZXNzXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleC0xIHctZnVsbCBoLTE0IHB4LTQgYmctd2hpdGUgZGFyazpiZy1zdG9uZS03MDAgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCBkYXJrOmJvcmRlci1zdG9uZS02MDAgcm91bmRlZC1sZyBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1wcmltYXJ5IGZvY3VzOmJvcmRlci1wcmltYXJ5IHRleHQtbGdcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICByZXF1aXJlZFxuICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgaWQ9XCJjbGVyay1jYXB0Y2hhXCIgY2xhc3NOYW1lPVwibXQtNFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cInN1Ym1pdFwiXG4gICAgICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17bG9hZGluZyB8fCAhZW1haWx9XG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgYmctc3RvbmUtOTAwIGhvdmVyOmJnLXN0b25lLTcwMCB0ZXh0LXdoaXRlIGZvbnQtbWVkaXVtIHB5LTIuNSBweC00IHJvdW5kZWQtbGcgdHJhbnNpdGlvbi1jb2xvcnMgZGFyazpiZy13aGl0ZSBkYXJrOnRleHQtc3RvbmUtODAwIGRhcms6aG92ZXI6Ymctc3RvbmUtMzAwIGgtMTQgdGV4dC1iYXNlIGRpc2FibGVkOm9wYWNpdHktNTAgZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkXCJcbiAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICB7bG9hZGluZyA/IFwiU2VuZGluZy4uLlwiIDogXCJDb250aW51ZVwifVxuICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICA8L2Zvcm0+XG4gICAgICAgICAgICAgICAgICA8L1RhYi5QYW5lbD5cbiAgICAgICAgICAgICAgICA8L1RhYi5QYW5lbHM+XG4gICAgICAgICAgICAgIDwvVGFiLkdyb3VwPlxuICAgICAgICAgICAgPC8+XG4gICAgICAgICAgKSA6IChcbiAgICAgICAgICAgIDxmb3JtIG9uU3VibWl0PXtoYW5kbGVWZXJpZnlPdHB9IGNsYXNzTmFtZT1cInNwYWNlLXktNlwiPlxuICAgICAgICAgICAgICA8RGlhbG9nLlRpdGxlIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtc2VtaWJvbGQgbWItNiB0ZXh0LXByaW1hcnkgZGFyazp0ZXh0LXByaW1hcnktbGlnaHRcIj5cbiAgICAgICAgICAgICAgICBFbnRlciBWZXJpZmljYXRpb24gQ29kZVxuICAgICAgICAgICAgICA8L0RpYWxvZy5UaXRsZT5cbiAgICAgICAgICAgICAge2Vycm9yICYmIChcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTYgcC00IGJnLXJlZC01MCBkYXJrOmJnLXJlZC05MDAvNTAgdGV4dC1yZWQtNjAwIGRhcms6dGV4dC1yZWQtNDAwIHRleHQtc20gcm91bmRlZC1sZyBib3JkZXIgYm9yZGVyLXJlZC0xMDBcIj5cbiAgICAgICAgICAgICAgICAgIHtlcnJvcn1cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDAgZGFyazp0ZXh0LWdyYXktNDAwIG1iLTRcIj5cbiAgICAgICAgICAgICAgICAgIEVudGVyIHRoZSB2ZXJpZmljYXRpb24gY29kZSBzZW50IHRvIHtlbWFpbH1cbiAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGdhcC0yIGp1c3RpZnktYmV0d2VlbiBtYi00XCI+XG4gICAgICAgICAgICAgICAgICB7b3RwLm1hcCgoZGlnaXQsIGluZGV4KSA9PiAoXG4gICAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICAgIGtleT17aW5kZXh9XG4gICAgICAgICAgICAgICAgICAgICAgcmVmPXsoZWwpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgIG90cFJlZnMuY3VycmVudFtpbmRleF0gPSBlbDtcbiAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgICAgICAgICBtYXhMZW5ndGg9ezF9XG4gICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2RpZ2l0fVxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gaGFuZGxlT3RwQ2hhbmdlKGluZGV4LCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgICAgICAgb25LZXlEb3duPXsoZSkgPT4gaGFuZGxlS2V5RG93bihpbmRleCwgZSl9XG4gICAgICAgICAgICAgICAgICAgICAgb25QYXN0ZT17aGFuZGxlT3RwUGFzdGV9XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy0xMiBoLTE0IHRleHQtY2VudGVyIHRleHQtMnhsIGZvbnQtc2VtaWJvbGQgYmctd2hpdGUgZGFyazpiZy1zdG9uZS03MDAgYm9yZGVyLTIgYm9yZGVyLWdyYXktMzAwIGRhcms6Ym9yZGVyLXN0b25lLTYwMCByb3VuZGVkLWxnIGZvY3VzOmJvcmRlci1wcmltYXJ5IGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLXByaW1hcnlcIlxuICAgICAgICAgICAgICAgICAgICAgIHJlcXVpcmVkXG4gICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgc2V0SXNPdHBTZW50KGZhbHNlKTtcbiAgICAgICAgICAgICAgICAgICAgc2V0T3RwKFtcIlwiLCBcIlwiLCBcIlwiLCBcIlwiLCBcIlwiLCBcIlwiXSk7XG4gICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1wcmltYXJ5IGRhcms6dGV4dC1wcmltYXJ5LWxpZ2h0IGhvdmVyOnRleHQtcHJpbWFyeS1kYXJrIHRleHQtc20gZm9udC1tZWRpdW1cIlxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIERpZG4mYXBvczt0IHJlY2VpdmUgdGhlIGNvZGU/IFNlbmQgYWdhaW5cbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICB0eXBlPVwic3VibWl0XCJcbiAgICAgICAgICAgICAgICBkaXNhYmxlZD17bG9hZGluZyB8fCBvdHAuc29tZSgoZGlnaXQpID0+ICFkaWdpdCl9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGJnLXN0b25lLTkwMCBob3ZlcjpiZy1zdG9uZS03MDAgdGV4dC13aGl0ZSBmb250LW1lZGl1bSBweS0yLjUgcHgtNCByb3VuZGVkLWxnIHRyYW5zaXRpb24tY29sb3JzIGRhcms6Ymctd2hpdGUgZGFyazp0ZXh0LXN0b25lLTgwMCBkYXJrOmhvdmVyOmJnLXN0b25lLTMwMCBoLTE0IHRleHQtYmFzZSBkaXNhYmxlZDpvcGFjaXR5LTUwIGRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZFwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICB7bG9hZGluZyA/IFwiVmVyaWZ5aW5nLi4uXCIgOiBcIlZlcmlmeVwifVxuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgIDwvZm9ybT5cbiAgICAgICAgICApfVxuICAgICAgICA8L0RpYWxvZy5QYW5lbD5cbiAgICAgIDwvZGl2PlxuICAgIDwvRGlhbG9nPlxuICApO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgRW1haWxWZXJpZmljYXRpb25Nb2RhbDtcbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwidXNlUmVmIiwidXNlRWZmZWN0IiwiRGlhbG9nIiwiVGFiIiwidXNlU2lnblVwIiwidXNlU2lnbkluIiwidXNlTXV0YXRpb24iLCJhcGkiLCJFbWFpbFZlcmlmaWNhdGlvbk1vZGFsIiwiaXNPcGVuIiwib25DbG9zZSIsIm9uU3VjY2VzcyIsImlzTG9hZGVkIiwic2lnblVwTG9hZGVkIiwic2lnblVwIiwic2V0QWN0aXZlIiwic2V0U2lnblVwQWN0aXZlIiwic2lnbkluTG9hZGVkIiwic2lnbkluIiwic2V0U2lnbkluQWN0aXZlIiwidXBzZXJ0UHJvZmlsZSIsIm15RnVuY3Rpb25zIiwidXBzZXJ0VXNlclByb2ZpbGUiLCJlbWFpbCIsInNldEVtYWlsIiwib3RwIiwic2V0T3RwIiwiaXNPdHBTZW50Iiwic2V0SXNPdHBTZW50IiwiZXJyb3IiLCJzZXRFcnJvciIsImxvYWRpbmciLCJzZXRMb2FkaW5nIiwiaXNTaWduSW4iLCJzZXRJc1NpZ25JbiIsInNlbGVjdGVkVGFiIiwic2V0U2VsZWN0ZWRUYWIiLCJvdHBSZWZzIiwiaGFuZGxlRW1haWxDaGFuZ2UiLCJlIiwidmFsdWUiLCJ0YXJnZXQiLCJyZXBsYWNlIiwiaGFuZGxlT3RwQ2hhbmdlIiwiaW5kZXgiLCJsZW5ndGgiLCJuZXdPdHAiLCJjdXJyZW50IiwiZm9jdXMiLCJoYW5kbGVLZXlEb3duIiwia2V5IiwiaGFuZGxlT3RwUGFzdGUiLCJwYXN0ZSIsImNsaXBib2FyZERhdGEiLCJnZXREYXRhIiwidGVzdCIsInNwbGl0Iiwic2V0VGltZW91dCIsInByZXZlbnREZWZhdWx0IiwiaGFuZGxlU2VuZE90cCIsImNyZWF0ZSIsImVtYWlsQWRkcmVzcyIsInByZXBhcmVFbWFpbEFkZHJlc3NWZXJpZmljYXRpb24iLCJzdXBwb3J0ZWRGaXJzdEZhY3RvcnMiLCJpZGVudGlmaWVyIiwiZW1haWxDb2RlRmFjdG9yIiwiZmluZCIsImZhY3RvciIsInN0cmF0ZWd5IiwicHJlcGFyZUZpcnN0RmFjdG9yIiwiZW1haWxBZGRyZXNzSWQiLCJlcnIiLCJlcnJvck1lc3NhZ2UiLCJBcnJheSIsIm1lc3NhZ2UiLCJFcnJvciIsImNvbnNvbGUiLCJoYW5kbGVWZXJpZnlPdHAiLCJvdHBTdHJpbmciLCJqb2luIiwibG9nIiwiYXR0ZW1wdCIsImF0dGVtcHRGaXJzdEZhY3RvciIsImNvZGUiLCJzdGF0dXMiLCJzZXNzaW9uIiwiY3JlYXRlZFNlc3Npb25JZCIsImF0dGVtcHRFbWFpbEFkZHJlc3NWZXJpZmljYXRpb24iLCJjb252ZXhFcnJvciIsIm9wZW4iLCJjbGFzc05hbWUiLCJkaXYiLCJhcmlhLWhpZGRlbiIsIlBhbmVsIiwiVGl0bGUiLCJHcm91cCIsInNlbGVjdGVkSW5kZXgiLCJvbkNoYW5nZSIsIkxpc3QiLCJzZWxlY3RlZCIsIlBhbmVscyIsImZvcm0iLCJvblN1Ym1pdCIsImxhYmVsIiwiaHRtbEZvciIsImlucHV0IiwidHlwZSIsImlkIiwib25LZXlEb3duIiwicGxhY2Vob2xkZXIiLCJyZXF1aXJlZCIsImJ1dHRvbiIsImRpc2FibGVkIiwicCIsIm1hcCIsImRpZ2l0IiwicmVmIiwiZWwiLCJtYXhMZW5ndGgiLCJvblBhc3RlIiwib25DbGljayIsInNvbWUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/EmailVerificationModal.tsx\n"));

/***/ })

});