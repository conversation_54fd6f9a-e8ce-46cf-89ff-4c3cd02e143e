"use client";

import React, { createContext, useContext, useEffect, useState } from 'react';
import { Models } from 'appwrite';
import { authService, databaseService } from '@/lib/appwrite';

interface AuthContextType {
  user: Models.User<Models.Preferences> | null;
  userProfile: any | null;
  loading: boolean;
  login: (email: string, password: string) => Promise<void>;
  loginWithEmailCode: (email: string) => Promise<{ userId: string; secret: string }>;
  verifyEmailCode: (userId: string, secret: string) => Promise<void>;
  register: (email: string, password: string, name: string) => Promise<void>;
  logout: () => Promise<void>;
  sendVerificationEmail: () => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
  updatePassword: (userId: string, secret: string, password: string) => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<Models.User<Models.Preferences> | null>(null);
  const [userProfile, setUserProfile] = useState<any | null>(null);
  const [loading, setLoading] = useState(true);

  // Check if user is logged in on mount
  useEffect(() => {
    checkAuth();
  }, []);

  const checkAuth = async () => {
    try {
      setLoading(true);
      const currentUser = await authService.getCurrentUser();
      if (currentUser) {
        setUser(currentUser);
        // Try to get user profile
        const profile = await databaseService.getUserProfile(currentUser.$id);
        setUserProfile(profile);
      }
    } catch (error) {
      console.error('Auth check failed:', error);
      setUser(null);
      setUserProfile(null);
    } finally {
      setLoading(false);
    }
  };

  const login = async (email: string, password: string) => {
    try {
      setLoading(true);
      await authService.login(email, password);
      await checkAuth();
    } catch (error) {
      setLoading(false);
      throw error;
    }
  };

  const loginWithEmailCode = async (email: string) => {
    try {
      const token = await authService.loginWithEmailCode(email);
      return {
        userId: token.userId,
        secret: token.secret
      };
    } catch (error) {
      throw error;
    }
  };

  const verifyEmailCode = async (userId: string, secret: string) => {
    try {
      setLoading(true);
      await authService.verifyEmailCode(userId, secret);
      await checkAuth();
    } catch (error) {
      setLoading(false);
      throw error;
    }
  };

  const register = async (email: string, password: string, name: string) => {
    try {
      setLoading(true);
      const newUser = await authService.createAccount(email, password, name);
      
      // Create user profile in database
      try {
        await databaseService.createUserProfile(newUser.$id, {
          email,
          name,
          createdAt: new Date().toISOString(),
        });
      } catch (dbError) {
        console.error('Failed to create user profile:', dbError);
        // Continue even if profile creation fails
      }
      
      await checkAuth();
    } catch (error) {
      setLoading(false);
      throw error;
    }
  };

  const logout = async () => {
    try {
      setLoading(true);
      await authService.logout();
      setUser(null);
      setUserProfile(null);
    } catch (error) {
      console.error('Logout failed:', error);
      // Clear state even if logout fails
      setUser(null);
      setUserProfile(null);
    } finally {
      setLoading(false);
    }
  };

  const sendVerificationEmail = async () => {
    try {
      await authService.sendVerificationEmail();
    } catch (error) {
      throw error;
    }
  };

  const resetPassword = async (email: string) => {
    try {
      await authService.resetPassword(email);
    } catch (error) {
      throw error;
    }
  };

  const updatePassword = async (userId: string, secret: string, password: string) => {
    try {
      await authService.updatePassword(userId, secret, password);
    } catch (error) {
      throw error;
    }
  };

  const value: AuthContextType = {
    user,
    userProfile,
    loading,
    login,
    loginWithEmailCode,
    verifyEmailCode,
    register,
    logout,
    sendVerificationEmail,
    resetPassword,
    updatePassword,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
