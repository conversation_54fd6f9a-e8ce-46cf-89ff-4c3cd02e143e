"use client";

import React, { createContext, useContext, useState, useEffect } from "react";
import { useAuth } from "@/contexts/appwrite-auth-context";
// import { AccountService } from "@/lib/db/accounts"; // Server-side only
import { toast } from "sonner";

// Define Account type locally since schema doesn't exist
interface Account {
  id: string;
  userId: string;
  name: string;
  type: "paper" | "live";
  solBalance: number;
  walletAddress: string | null;
  isDefault: boolean;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

interface AccountContextType {
  accounts: Account[];
  currentAccount: Account | null;
  loading: boolean;
  error: string | null;

  // Account management
  switchAccount: (accountId: string) => void;
  createPaperAccount: (
    name: string,
    initialBalance?: number,
  ) => Promise<Account>;
  createTradingAccount: (
    name: string,
    walletAddress: string,
  ) => Promise<Account>;
  addSolToPaperAccount: (accountId: string, amount: number) => Promise<void>;
  deleteAccount: (accountId: string) => Promise<void>;
  setDefaultAccount: (accountId: string) => Promise<void>;

  // Utilities
  refreshAccounts: () => Promise<void>;
  getAccountBalance: (accountId?: string) => number;
  canCreateTradingAccount: () => boolean;
}

const AccountContext = createContext<AccountContextType | undefined>(undefined);

export function useAccounts() {
  const context = useContext(AccountContext);
  if (context === undefined) {
    throw new Error("useAccounts must be used within an AccountProvider");
  }
  return context;
}

interface AccountProviderProps {
  children: React.ReactNode;
}

export function AccountProvider({ children }: AccountProviderProps) {
  const { user, loading: authLoading } = useAuth();
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [currentAccount, setCurrentAccount] = useState<Account | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Initialize user and accounts
  useEffect(() => {
    if (authLoading) return;

    if (user) {
      initializeUserAccounts();
    } else {
      setLoading(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user, authLoading]);

  const initializeUserAccounts = async () => {
    try {
      setLoading(true);
      setError(null);

      if (!user?.id || !user?.emailAddresses?.[0]?.emailAddress) {
        throw new Error("User information not available");
      }

      // For now, create mock accounts (replace with API calls later)
      const mockAccounts: Account[] = [
        {
          id: "paper-1",
          userId: user.id,
          name: "Paper Trading",
          type: "paper",
          solBalance: 1000,
          walletAddress: null,
          isDefault: true,
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      setAccounts(mockAccounts);
      setCurrentAccount(mockAccounts[0]);
      setLoading(false);

      toast.success("Welcome! Your accounts have been loaded.");
    } catch (err) {
      console.error("Failed to initialize user accounts:", err);
      setError(
        err instanceof Error ? err.message : "Failed to initialize accounts",
      );
      toast.error("Failed to load accounts");
    } finally {
      setLoading(false);
    }
  };

  const refreshAccounts = async () => {
    try {
      if (!user?.id) return;

      // Mock accounts for now (replace with API calls later)
      const mockAccounts: Account[] = [
        {
          id: "paper-1",
          userId: user.id,
          name: "Paper Trading",
          type: "paper",
          solBalance: 1000,
          walletAddress: null,
          isDefault: true,
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      setAccounts(mockAccounts);

      // Set current account to default or first account
      const defaultAccount =
        mockAccounts.find((acc) => acc.isDefault) || mockAccounts[0];
      if (defaultAccount && !currentAccount) {
        setCurrentAccount(defaultAccount);
      }
    } catch (err) {
      console.error("Failed to refresh accounts:", err);
      setError(
        err instanceof Error ? err.message : "Failed to refresh accounts",
      );
    }
  };

  const switchAccount = (accountId: string) => {
    const account = accounts.find((acc) => acc.id === accountId);
    if (account) {
      setCurrentAccount(account);
      toast.success(`Switched to ${account.name}`);
    }
  };

  const createPaperAccount = async (
    name: string,
    initialBalance = 1000,
  ): Promise<Account> => {
    try {
      if (!user?.id) throw new Error("User not authenticated");

      // Mock account creation
      const newAccount: Account = {
        id: `paper-${Date.now()}`,
        userId: user.id,
        name,
        type: "paper",
        solBalance: initialBalance,
        walletAddress: null,
        isDefault: false,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      setAccounts((prev) => [...prev, newAccount]);
      toast.success(
        `Paper account "${name}" created with ${initialBalance} SOL`,
      );

      return newAccount;
    } catch (err) {
      const message =
        err instanceof Error ? err.message : "Failed to create paper account";
      toast.error(message);
      throw err;
    }
  };

  const createTradingAccount = async (
    name: string,
    walletAddress: string,
  ): Promise<Account> => {
    try {
      if (!user?.id) throw new Error("User not authenticated");

      // Mock trading account creation
      const newAccount: Account = {
        id: `trading-${Date.now()}`,
        userId: user.id,
        name,
        type: "live",
        solBalance: 0,
        walletAddress,
        isDefault: false,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      setAccounts((prev) => [...prev, newAccount]);
      toast.success(
        `Trading account "${name}" created and connected to wallet`,
      );

      return newAccount;
    } catch (err) {
      const message =
        err instanceof Error ? err.message : "Failed to create trading account";
      toast.error(message);
      throw err;
    }
  };

  const addSolToPaperAccount = async (accountId: string, amount: number) => {
    try {
      // Mock adding SOL to paper account
      setAccounts((prev) =>
        prev.map((acc) =>
          acc.id === accountId
            ? {
                ...acc,
                solBalance: acc.solBalance + amount,
                updatedAt: new Date(),
              }
            : acc,
        ),
      );

      // Update current account if it's the one being modified
      if (currentAccount?.id === accountId) {
        setCurrentAccount((prev) =>
          prev ? { ...prev, solBalance: prev.solBalance + amount } : null,
        );
      }

      toast.success(`Added ${amount} SOL to account`);
    } catch (err) {
      const message = err instanceof Error ? err.message : "Failed to add SOL";
      toast.error(message);
      throw err;
    }
  };

  const deleteAccount = async (accountId: string) => {
    try {
      // Mock account deletion
      setAccounts((prev) => prev.filter((acc) => acc.id !== accountId));

      // Switch to default account if current account was deleted
      if (currentAccount?.id === accountId) {
        const defaultAccount = accounts.find(
          (acc) => acc.isDefault && acc.id !== accountId,
        );
        if (defaultAccount) {
          setCurrentAccount(defaultAccount);
        }
      }

      toast.success("Account deleted successfully");
    } catch (err) {
      const message =
        err instanceof Error ? err.message : "Failed to delete account";
      toast.error(message);
      throw err;
    }
  };

  const setDefaultAccount = async (accountId: string) => {
    try {
      if (!user?.id) throw new Error("User not authenticated");

      // Mock setting default account
      setAccounts((prev) =>
        prev.map((acc) => ({
          ...acc,
          isDefault: acc.id === accountId,
          updatedAt: new Date(),
        })),
      );

      toast.success("Default account updated");
    } catch (err) {
      const message =
        err instanceof Error ? err.message : "Failed to set default account";
      toast.error(message);
      throw err;
    }
  };

  const getAccountBalance = (accountId?: string): number => {
    const account = accountId
      ? accounts.find((acc) => acc.id === accountId)
      : currentAccount;
    return account?.solBalance || 0;
  };

  const canCreateTradingAccount = (): boolean => {
    return !accounts.some((acc) => acc.type === "live" && acc.isActive);
  };

  const value: AccountContextType = {
    accounts,
    currentAccount,
    loading,
    error,
    switchAccount,
    createPaperAccount,
    createTradingAccount,
    addSolToPaperAccount,
    deleteAccount,
    setDefaultAccount,
    refreshAccounts,
    getAccountBalance,
    canCreateTradingAccount,
  };

  return (
    <AccountContext.Provider value={value}>{children}</AccountContext.Provider>
  );
}
