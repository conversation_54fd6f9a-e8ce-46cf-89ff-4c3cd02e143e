import { Client, Account, Databases, ID } from 'appwrite';

// Initialize Appwrite client
const client = new Client()
  .setEndpoint(process.env.NEXT_PUBLIC_APPWRITE_PUBLIC_ENDPOINT!)
  .setProject(process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID!);

// Initialize services
export const account = new Account(client);
export const databases = new Databases(client);

// Database and collection IDs (you'll need to create these in Appwrite console)
export const DATABASE_ID = 'bizcoin_db';
export const USERS_COLLECTION_ID = 'users';

// Auth functions
export const authService = {
  // Create account with email and password
  async createAccount(email: string, password: string, name: string) {
    try {
      const userAccount = await account.create(ID.unique(), email, password, name);
      
      // Send verification email
      await this.sendVerificationEmail();
      
      return userAccount;
    } catch (error) {
      console.error('Error creating account:', error);
      throw error;
    }
  },

  // Login with email and password
  async login(email: string, password: string) {
    try {
      return await account.createEmailPasswordSession(email, password);
    } catch (error) {
      console.error('Error logging in:', error);
      throw error;
    }
  },

  // Login with email verification code
  async loginWithEmailCode(email: string) {
    try {
      // Create email session (sends verification code)
      const token = await account.createEmailToken(ID.unique(), email);
      return token;
    } catch (error) {
      console.error('Error sending email code:', error);
      throw error;
    }
  },

  // Verify email code and create session
  async verifyEmailCode(userId: string, secret: string) {
    try {
      return await account.createSession(userId, secret);
    } catch (error) {
      console.error('Error verifying email code:', error);
      throw error;
    }
  },

  // Send verification email
  async sendVerificationEmail() {
    try {
      return await account.createVerification(
        `${window.location.origin}/verify-email`
      );
    } catch (error) {
      console.error('Error sending verification email:', error);
      throw error;
    }
  },

  // Verify email with URL
  async verifyEmail(userId: string, secret: string) {
    try {
      return await account.updateVerification(userId, secret);
    } catch (error) {
      console.error('Error verifying email:', error);
      throw error;
    }
  },

  // Get current user
  async getCurrentUser() {
    try {
      return await account.get();
    } catch (error) {
      console.error('Error getting current user:', error);
      return null;
    }
  },

  // Logout
  async logout() {
    try {
      return await account.deleteSession('current');
    } catch (error) {
      console.error('Error logging out:', error);
      throw error;
    }
  },

  // Logout from all sessions
  async logoutAll() {
    try {
      return await account.deleteSessions();
    } catch (error) {
      console.error('Error logging out from all sessions:', error);
      throw error;
    }
  },

  // Reset password
  async resetPassword(email: string) {
    try {
      return await account.createRecovery(
        email,
        `${window.location.origin}/reset-password`
      );
    } catch (error) {
      console.error('Error sending password reset:', error);
      throw error;
    }
  },

  // Update password with recovery
  async updatePassword(userId: string, secret: string, password: string) {
    try {
      return await account.updateRecovery(userId, secret, password);
    } catch (error) {
      console.error('Error updating password:', error);
      throw error;
    }
  }
};

// Database functions
export const databaseService = {
  // Create user profile
  async createUserProfile(userId: string, data: any) {
    try {
      return await databases.createDocument(
        DATABASE_ID,
        USERS_COLLECTION_ID,
        userId,
        data
      );
    } catch (error) {
      console.error('Error creating user profile:', error);
      throw error;
    }
  },

  // Get user profile
  async getUserProfile(userId: string) {
    try {
      return await databases.getDocument(
        DATABASE_ID,
        USERS_COLLECTION_ID,
        userId
      );
    } catch (error) {
      console.error('Error getting user profile:', error);
      return null;
    }
  },

  // Update user profile
  async updateUserProfile(userId: string, data: any) {
    try {
      return await databases.updateDocument(
        DATABASE_ID,
        USERS_COLLECTION_ID,
        userId,
        data
      );
    } catch (error) {
      console.error('Error updating user profile:', error);
      throw error;
    }
  }
};

export { client };
