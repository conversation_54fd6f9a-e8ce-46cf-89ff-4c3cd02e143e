{"name": "dextrip_client", "version": "0.1.0", "private": true, "scripts": {"dev": "npm-run-all --parallel dev:frontend dev:backend", "dev:frontend": "next dev", "dev:backend": "convex dev", "predev": "convex dev --until-success && convex dashboard", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@clerk/clerk-react": "^5.25.0", "@clerk/nextjs": "^6.12.6", "@headlessui/react": "^2.2.4", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@solana/wallet-adapter-base": "^0.9.27", "@solana/wallet-adapter-coinbase": "^0.1.23", "@solana/wallet-adapter-phantom": "^0.9.28", "@solana/wallet-adapter-react": "^0.15.39", "@solana/wallet-adapter-react-ui": "^0.9.39", "@solana/wallet-adapter-solflare": "^0.6.32", "@solana/web3.js": "^1.98.2", "@tanstack/react-table": "^8.21.3", "appwrite": "^18.2.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "convex": "^1.23.0", "framer-motion": "^12.23.3", "lightweight-charts": "^5.0.8", "lucide-react": "^0.525.0", "next": "^15.4.4", "next-themes": "^0.4.6", "openai": "^5.10.2", "react": "^19.0.0", "react-dom": "^19.0.0", "recharts": "^3.1.0", "sonner": "^2.0.6", "table": "^6.9.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.2.3", "npm-run-all": "^4.1.5", "prettier": "^3.5.3", "tailwindcss": "^4", "tw-animate-css": "^1.3.5", "typescript": "^5"}}