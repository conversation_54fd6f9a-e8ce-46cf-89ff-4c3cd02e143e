# Appwrite Setup Guide

This guide will help you set up Appwrite for authentication in your Bizcoin application.

## Prerequisites

You should already have:
- Appwrite server running at `http://*************/v1`
- Project ID: `689a0d8a001fa8d95134`

## Required Setup Steps

### 1. Create Database and Collection

1. **Go to your Appwrite Console**: `http://*************`
2. **Navigate to Databases**
3. **Create a new database**:
   - Database ID: `bizcoin_db`
   - Name: `Bizcoin Database`

4. **Create a collection for users**:
   - Collection ID: `users`
   - Name: `Users`
   - Permissions: 
     - Read: `users` (authenticated users can read their own data)
     - Write: `users` (authenticated users can write their own data)

5. **Add attributes to the users collection**:
   ```
   - email (string, required, size: 255)
   - name (string, required, size: 255)
   - createdAt (datetime, required)
   ```

### 2. Configure Authentication

1. **Go to Auth section** in Appwrite Console
2. **Enable Email/Password authentication**
3. **Configure email settings** (if you want email verification):
   - SMTP settings for sending verification emails
   - Email templates

### 3. Environment Variables

Your `.env.local` should already have:
```env
NEXT_PUBLIC_APPWRITE_PROJECT_ID=689a0d8a001fa8d95134
NEXT_PUBLIC_APPWRITE_PUBLIC_ENDPOINT=http://*************/v1
```

### 4. Test the Setup

1. **Start your development server**:
   ```bash
   npm run dev
   ```

2. **Try the authentication flow**:
   - Go to `http://localhost:3001`
   - Click "Join Waitlist"
   - Try signing up with a new email
   - Try signing in with existing credentials

## Authentication Features

### Sign Up
- Creates a new user account with email and password
- Sends verification email (if configured)
- Creates user profile in database

### Sign In
- Supports email/password login
- Falls back to email code verification if password fails
- Maintains user session

### Email Verification
- Sends verification codes via email
- Supports both sign-up and sign-in flows

## Troubleshooting

### Common Issues

1. **"Project not found" error**:
   - Check your project ID in `.env.local`
   - Verify Appwrite server is running

2. **"Collection not found" error**:
   - Make sure you created the database and collection
   - Check the database and collection IDs match

3. **Email verification not working**:
   - Configure SMTP settings in Appwrite Console
   - Check email templates are set up

4. **Permission errors**:
   - Verify collection permissions allow authenticated users to read/write
   - Check user roles and permissions

### Debug Mode

To enable debug logging, add this to your browser console:
```javascript
localStorage.setItem('debug', 'appwrite:*');
```

## Next Steps

1. **Customize email templates** in Appwrite Console
2. **Add more user profile fields** as needed
3. **Set up proper SMTP** for production email sending
4. **Configure security rules** for production use

## Security Notes

- Never expose your Appwrite API key in client-side code
- Use proper permissions on collections
- Enable rate limiting for authentication endpoints
- Use HTTPS in production
