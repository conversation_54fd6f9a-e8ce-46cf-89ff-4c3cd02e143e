# Changelog

All notable changes to this project will be documented in this file. See [standard-version](https://github.com/conventional-changelog/standard-version) for commit guidelines.

## [6.0.0](https://github.com/istanbuljs/test-exclude/compare/v6.0.0-alpha.3...v6.0.0) (2019-12-20)


### Features

* Version bump only ([#41](https://github.com/istanbuljs/test-exclude/issues/41)) ([5708a16](https://github.com/istanbuljs/test-exclude/commit/5708a16cfc80dfec8fcf7a8a70c13278df0a91ba))

## [6.0.0-alpha.3](https://github.com/istanbuljs/test-exclude/compare/v6.0.0-alhpa.3...v6.0.0-alpha.3) (2019-12-09)

## [6.0.0-alhpa.3](https://github.com/istanbuljs/test-exclude/compare/v6.0.0-alpha.2...v6.0.0-alhpa.3) (2019-12-08)


### Bug Fixes

* Ignore options that are explicitly set undefined. ([#40](https://github.com/istanbuljs/test-exclude/issues/40)) ([b57e936](https://github.com/istanbuljs/test-exclude/commit/b57e9368aacdd548981ffd2ab6447bbd2c1e7de0))

## [6.0.0-alpha.2](https://github.com/istanbuljs/test-exclude/compare/v6.0.0-alpha.1...v6.0.0-alpha.2) (2019-12-07)


### ⚠ BREAKING CHANGES

* `test-exclude` now exports a class so it is necessary
to use `new TestExclude()` when creating an instance.

### Bug Fixes

* Directly export class, document API. ([#39](https://github.com/istanbuljs/test-exclude/issues/39)) ([3acc196](https://github.com/istanbuljs/test-exclude/commit/3acc196482e03be734effd110aa83a4e78d3ebde)), closes [#33](https://github.com/istanbuljs/test-exclude/issues/33)
* Pull default settings from @istanbuljs/schema ([#38](https://github.com/istanbuljs/test-exclude/issues/38)) ([ffca696](https://github.com/istanbuljs/test-exclude/commit/ffca6968175c9030cebf018fb86d2c0386a61620))

## [6.0.0-alpha.1](https://github.com/istanbuljs/test-exclude/compare/v6.0.0-alpha.0...v6.0.0-alpha.1) (2019-09-24)


### Features

* Add async glob function ([#30](https://github.com/istanbuljs/test-exclude/issues/30)) ([e45ac10](https://github.com/istanbuljs/test-exclude/commit/e45ac10))

# [6.0.0-alpha.0](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-exclude@6.0.0-alpha.0) (2019-06-19)


### Bug Fixes

* **win32:** Detect files on different drive as outside project ([#422](https://github.com/istanbuljs/istanbuljs/issues/422)) ([5b4ee88](https://github.com/istanbuljs/istanbuljs/commit/5b4ee88)), closes [#418](https://github.com/istanbuljs/istanbuljs/issues/418)
* Ignore tests matching *.cjs, *.mjs and *.ts by default ([#381](https://github.com/istanbuljs/istanbuljs/issues/381)) ([0f077c2](https://github.com/istanbuljs/istanbuljs/commit/0f077c2))


### Features

* ignore files under test**s** directories by default ([#419](https://github.com/istanbuljs/istanbuljs/issues/419)) ([8ad5fd2](https://github.com/istanbuljs/istanbuljs/commit/8ad5fd2))
* Remove configuration loading functionality ([#398](https://github.com/istanbuljs/istanbuljs/issues/398)) ([f5c93c3](https://github.com/istanbuljs/istanbuljs/commit/f5c93c3)), closes [#392](https://github.com/istanbuljs/istanbuljs/issues/392)
* Update dependencies, require Node.js 8 ([#401](https://github.com/istanbuljs/istanbuljs/issues/401)) ([bf3a539](https://github.com/istanbuljs/istanbuljs/commit/bf3a539))


### BREAKING CHANGES

* Node.js 8 is now required
* Remove configuration loading functionality





## [5.2.3](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-exclude@5.2.3) (2019-04-24)

**Note:** Version bump only for package test-exclude





## [5.2.2](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-exclude@5.2.2) (2019-04-09)

**Note:** Version bump only for package test-exclude





## [5.2.1](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-exclude@5.2.1) (2019-04-03)


### Bug Fixes

* Remove `**/node_modules/**` from defaultExclude. ([#351](https://github.com/istanbuljs/istanbuljs/issues/351)) ([deb3963](https://github.com/istanbuljs/istanbuljs/commit/deb3963)), closes [#347](https://github.com/istanbuljs/istanbuljs/issues/347)





# [5.2.0](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-exclude@5.2.0) (2019-03-12)


### Features

* Add TestExclude.globSync to find all files ([#309](https://github.com/istanbuljs/istanbuljs/issues/309)) ([2d7ea72](https://github.com/istanbuljs/istanbuljs/commit/2d7ea72))
* Support turning of node_modules default exclude via flag ([#213](https://github.com/istanbuljs/istanbuljs/issues/213)) ([9b4b34c](https://github.com/istanbuljs/istanbuljs/commit/9b4b34c))





# [5.1.0](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-exclude@5.1.0) (2019-01-26)


### Features

* Ignore babel.config.js. ([#279](https://github.com/istanbuljs/istanbuljs/issues/279)) ([24af6eb](https://github.com/istanbuljs/istanbuljs/commit/24af6eb))





<a name="5.0.1"></a>
## [5.0.1](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-exclude@5.0.1) (2018-12-25)




**Note:** Version bump only for package test-exclude

<a name="5.0.0"></a>
# [5.0.0](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-exclude@5.0.0) (2018-06-26)


* test-exclude: bump read-pkg-up dependency (#184) ([bb58139](https://github.com/istanbuljs/istanbuljs/commit/bb58139)), closes [#184](https://github.com/istanbuljs/istanbuljs/issues/184)


### BREAKING CHANGES

* Support for Node.js 4.x is dropped.




<a name="4.2.2"></a>
## [4.2.2](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-exclude@4.2.2) (2018-06-06)




**Note:** Version bump only for package test-exclude

<a name="4.2.1"></a>
## [4.2.1](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-exclude@4.2.1) (2018-03-04)


### Bug Fixes

* upgrade micromatch ([#142](https://github.com/istanbuljs/istanbuljs/issues/142)) ([24104a7](https://github.com/istanbuljs/istanbuljs/commit/24104a7))




<a name="4.2.0"></a>
# [4.2.0](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-exclude@4.2.0) (2018-02-13)


### Features

* add additional patterns to default excludes ([#133](https://github.com/istanbuljs/istanbuljs/issues/133)) ([4cedf63](https://github.com/istanbuljs/istanbuljs/commit/4cedf63))




<a name="4.1.1"></a>
## [4.1.1](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-exclude@4.1.1) (2017-05-27)


### Bug Fixes

* add more general support for negated exclude rules ([#58](https://github.com/istanbuljs/istanbuljs/issues/58)) ([08445db](https://github.com/istanbuljs/istanbuljs/commit/08445db))




<a name="4.1.0"></a>
# [4.1.0](https://github.com/istanbuljs/test-exclude/compare/<EMAIL>-exclude@4.1.0) (2017-04-29)


### Features

* add possibility to filter coverage maps when running reports post-hoc ([#24](https://github.com/istanbuljs/istanbuljs/issues/24)) ([e1c99d6](https://github.com/istanbuljs/test-exclude/commit/e1c99d6))




<a name="4.0.3"></a>
## [4.0.3](https://github.com/istanbuljs/test-exclude/compare/<EMAIL>-exclude@4.0.3) (2017-03-21)

<a name="4.0.2"></a>
## [4.0.2](https://github.com/istanbuljs/test-exclude/compare/<EMAIL>-exclude@4.0.2) (2017-03-21)

<a name="4.0.0"></a>
# [4.0.0](https://github.com/istanbuljs/test-exclude/compare/v3.3.0...v4.0.0) (2017-01-19)


### Features

* add coverage to default excludes ([#23](https://github.com/istanbuljs/test-exclude/issues/23)) ([59e8bbf](https://github.com/istanbuljs/test-exclude/commit/59e8bbf))


### BREAKING CHANGES

* additional coverage folder is now excluded



<a name="3.3.0"></a>
# [3.3.0](https://github.com/istanbuljs/test-exclude/compare/v3.2.2...v3.3.0) (2016-11-22)


### Features

* allow include/exclude rules to be a string rather than array ([#22](https://github.com/istanbuljs/test-exclude/issues/22)) ([f8f99c6](https://github.com/istanbuljs/test-exclude/commit/f8f99c6))



<a name="3.2.2"></a>
## [3.2.2](https://github.com/istanbuljs/test-exclude/compare/v3.2.1...v3.2.2) (2016-11-14)


### Bug Fixes

* we no longer need to add node_modules/** rule ([d0cfbc3](https://github.com/istanbuljs/test-exclude/commit/d0cfbc3))



<a name="3.2.1"></a>
## [3.2.1](https://github.com/istanbuljs/test-exclude/compare/v3.2.0...v3.2.1) (2016-11-14)


### Bug Fixes

* fix bug matching files in root, introduced by dotfiles setting ([27b249c](https://github.com/istanbuljs/test-exclude/commit/27b249c))



<a name="3.2.0"></a>
# [3.2.0](https://github.com/istanbuljs/test-exclude/compare/v3.1.0...v3.2.0) (2016-11-14)


### Features

* adds *.test.*.js exclude rule ([#20](https://github.com/istanbuljs/test-exclude/issues/20)) ([34f5cba](https://github.com/istanbuljs/test-exclude/commit/34f5cba))



<a name="3.1.0"></a>
# [3.1.0](https://github.com/istanbuljs/test-exclude/compare/v3.0.0...v3.1.0) (2016-11-14)


### Features

* we now support dot folders ([f2c1598](https://github.com/istanbuljs/test-exclude/commit/f2c1598))



<a name="3.0.0"></a>
# [3.0.0](https://github.com/istanbuljs/test-exclude/compare/v2.1.3...v3.0.0) (2016-11-13)


### Features

* always exclude node_modules ([#18](https://github.com/istanbuljs/test-exclude/issues/18)) ([b86d144](https://github.com/istanbuljs/test-exclude/commit/b86d144))


### BREAKING CHANGES

* `**/node_modules/**` is again added by default, but can be counteracted with `!**/node_modules/**`.



<a name="2.1.3"></a>
## [2.1.3](https://github.com/istanbuljs/test-exclude/compare/v2.1.2...v2.1.3) (2016-09-30)


### Bug Fixes

* switch lodash.assign to object-assign ([#16](https://github.com/istanbuljs/test-exclude/issues/16)) ([45a5488](https://github.com/istanbuljs/test-exclude/commit/45a5488))



<a name="2.1.2"></a>
## [2.1.2](https://github.com/istanbuljs/test-exclude/compare/v2.1.1...v2.1.2) (2016-08-31)


### Bug Fixes

* **exclude-config:** Use the defaultExcludes for anything passed in that is not an array ([#15](https://github.com/istanbuljs/test-exclude/issues/15)) ([227042f](https://github.com/istanbuljs/test-exclude/commit/227042f))



<a name="2.1.1"></a>
# [2.1.1](https://github.com/istanbuljs/test-exclude/compare/v2.1.0...v2.1.1) (2016-08-12)


### Bug Fixes

* it should be possible to cover the node_modules folder ([#13](https://github.com/istanbuljs/test-exclude/issues/13)) ([09f2788](https://github.com/istanbuljs/test-exclude/commit/09f2788))


<a name="2.1.0"></a>
# [2.1.0](https://github.com/istanbuljs/test-exclude/compare/v2.0.0...v2.1.0) (2016-08-12)


### Features

* export defaultExclude, so that it can be used in yargs' default settings ([#12](https://github.com/istanbuljs/test-exclude/issues/12)) ([5b3743b](https://github.com/istanbuljs/test-exclude/commit/5b3743b))



<a name="2.0.0"></a>
# [2.0.0](https://github.com/istanbuljs/test-exclude/compare/v1.1.0...v2.0.0) (2016-08-12)


### Bug Fixes

* use Array#reduce and remove unneeded branch in prepGlobPatterns ([#5](https://github.com/istanbuljs/test-exclude/issues/5)) ([c0f0f59](https://github.com/istanbuljs/test-exclude/commit/c0f0f59))


### Features

* don't exclude anything when empty array passed ([#11](https://github.com/istanbuljs/test-exclude/issues/11)) ([200ec07](https://github.com/istanbuljs/test-exclude/commit/200ec07))


### BREAKING CHANGES

* we now allow an empty array to be passed in, making it possible to disable the default exclude rules -- we will need to be mindful when pulling this logic into nyc.



<a name="1.1.0"></a>
# [1.1.0](https://github.com/bcoe/test-exclude/compare/v1.0.0...v1.1.0) (2016-06-08)


### Features

* set configFound if we find a configuration key in package.json ([#2](https://github.com/bcoe/test-exclude/issues/2)) ([64da7b9](https://github.com/bcoe/test-exclude/commit/64da7b9))



<a name="1.0.0"></a>
# 1.0.0 (2016-06-06)


### Features

* initial commit, pulled over some of the functionality from nyc ([3f1fce3](https://github.com/bcoe/test-exclude/commit/3f1fce3))
* you can now load include/exclude logic from a package.json stanza ([#1](https://github.com/bcoe/test-exclude/issues/1)) ([29b543d](https://github.com/bcoe/test-exclude/commit/29b543d))
