{"version": 3, "file": "dependencies.js", "sourceRoot": "", "sources": ["../../../lib/vocabularies/applicator/dependencies.ts"], "names": [], "mappings": ";;;AAQA,mDAA4C;AAC5C,6CAAoD;AACpD,kCAAmG;AAmBtF,QAAA,KAAK,GAA2B;IAC3C,OAAO,EAAE,CAAC,EAAC,MAAM,EAAE,EAAC,QAAQ,EAAE,SAAS,EAAE,IAAI,EAAC,EAAC,EAAE,EAAE;QACjD,MAAM,YAAY,GAAG,SAAS,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,YAAY,CAAA;QAChE,OAAO,IAAA,aAAG,EAAA,aAAa,YAAY,IAAI,IAAI,kBAAkB,QAAQ,aAAa,CAAA;IACpF,CAAC;IACD,MAAM,EAAE,CAAC,EAAC,MAAM,EAAE,EAAC,QAAQ,EAAE,SAAS,EAAE,IAAI,EAAE,eAAe,EAAC,EAAC,EAAE,EAAE,CACjE,IAAA,WAAC,EAAA,cAAc,QAAQ;uBACJ,eAAe;iBACrB,SAAS;YACd,IAAI,GAAG,EAAE,2BAA2B;CAC/C,CAAA;AAED,MAAM,GAAG,GAA0B;IACjC,OAAO,EAAE,cAAc;IACvB,IAAI,EAAE,QAAQ;IACd,UAAU,EAAE,QAAQ;IACpB,KAAK,EAAL,aAAK;IACL,IAAI,CAAC,GAAe;QAClB,MAAM,CAAC,QAAQ,EAAE,OAAO,CAAC,GAAG,iBAAiB,CAAC,GAAG,CAAC,CAAA;QAClD,oBAAoB,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAA;QACnC,kBAAkB,CAAC,GAAG,EAAE,OAAO,CAAC,CAAA;IAClC,CAAC;CACF,CAAA;AAED,SAAS,iBAAiB,CAAC,EAAC,MAAM,EAAa;IAC7C,MAAM,YAAY,GAAyB,EAAE,CAAA;IAC7C,MAAM,UAAU,GAAuB,EAAE,CAAA;IACzC,KAAK,MAAM,GAAG,IAAI,MAAM,EAAE,CAAC;QACzB,IAAI,GAAG,KAAK,WAAW;YAAE,SAAQ;QACjC,MAAM,IAAI,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,UAAU,CAAA;QACnE,IAAI,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAA;IACzB,CAAC;IACD,OAAO,CAAC,YAAY,EAAE,UAAU,CAAC,CAAA;AACnC,CAAC;AAED,SAAgB,oBAAoB,CAClC,GAAe,EACf,eAA2C,GAAG,CAAC,MAAM;IAErD,MAAM,EAAC,GAAG,EAAE,IAAI,EAAE,EAAE,EAAC,GAAG,GAAG,CAAA;IAC3B,IAAI,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,MAAM,KAAK,CAAC;QAAE,OAAM;IAClD,MAAM,OAAO,GAAG,GAAG,CAAC,GAAG,CAAC,SAAS,CAAC,CAAA;IAClC,KAAK,MAAM,IAAI,IAAI,YAAY,EAAE,CAAC;QAChC,MAAM,IAAI,GAAG,YAAY,CAAC,IAAI,CAAa,CAAA;QAC3C,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC;YAAE,SAAQ;QAC/B,MAAM,WAAW,GAAG,IAAA,qBAAc,EAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,CAAA;QAC1E,GAAG,CAAC,SAAS,CAAC;YACZ,QAAQ,EAAE,IAAI;YACd,SAAS,EAAE,IAAI,CAAC,MAAM;YACtB,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;SACtB,CAAC,CAAA;QACF,IAAI,EAAE,CAAC,SAAS,EAAE,CAAC;YACjB,GAAG,CAAC,EAAE,CAAC,WAAW,EAAE,GAAG,EAAE;gBACvB,KAAK,MAAM,OAAO,IAAI,IAAI,EAAE,CAAC;oBAC3B,IAAA,6BAAsB,EAAC,GAAG,EAAE,OAAO,CAAC,CAAA;gBACtC,CAAC;YACH,CAAC,CAAC,CAAA;QACJ,CAAC;aAAM,CAAC;YACN,GAAG,CAAC,EAAE,CAAC,IAAA,WAAC,EAAA,GAAG,WAAW,QAAQ,IAAA,uBAAgB,EAAC,GAAG,EAAE,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,CAAA;YACtE,IAAA,wBAAiB,EAAC,GAAG,EAAE,OAAO,CAAC,CAAA;YAC/B,GAAG,CAAC,IAAI,EAAE,CAAA;QACZ,CAAC;IACH,CAAC;AACH,CAAC;AA5BD,oDA4BC;AAED,SAAgB,kBAAkB,CAAC,GAAe,EAAE,aAAwB,GAAG,CAAC,MAAM;IACpF,MAAM,EAAC,GAAG,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE,EAAC,GAAG,GAAG,CAAA;IACpC,MAAM,KAAK,GAAG,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;IAC/B,KAAK,MAAM,IAAI,IAAI,UAAU,EAAE,CAAC;QAC9B,IAAI,IAAA,wBAAiB,EAAC,EAAE,EAAE,UAAU,CAAC,IAAI,CAAc,CAAC;YAAE,SAAQ;QAClE,GAAG,CAAC,EAAE,CACJ,IAAA,qBAAc,EAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,EACtD,GAAG,EAAE;YACH,MAAM,MAAM,GAAG,GAAG,CAAC,SAAS,CAAC,EAAC,OAAO,EAAE,UAAU,EAAE,IAAI,EAAC,EAAE,KAAK,CAAC,CAAA;YAChE,GAAG,CAAC,mBAAmB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAA;QACxC,CAAC,EACD,GAAG,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,WAAW;SACvC,CAAA;QACD,GAAG,CAAC,EAAE,CAAC,KAAK,CAAC,CAAA;IACf,CAAC;AACH,CAAC;AAfD,gDAeC;AAED,kBAAe,GAAG,CAAA"}