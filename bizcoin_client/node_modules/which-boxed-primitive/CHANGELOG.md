# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v1.1.1](https://github.com/inspect-js/which-boxed-primitive/compare/v1.1.0...v1.1.1) - 2024-12-15

### Commits

- [Deps] update `is-boolean-object`, `is-number-object`, `is-string`, `is-symbol` [`5266e0c`](https://github.com/inspect-js/which-boxed-primitive/commit/5266e0cb87a814e42b4e8de9574430d27e562070)
- [Dev Deps] update `@arethetypeswrong/cli`, `@ljharb/tsconfig`, `@types/tape` [`a660339`](https://github.com/inspect-js/which-boxed-primitive/commit/a66033981b3f2b4ba4261cf477fd5a6dc40b38d6)

## [v1.1.0](https://github.com/inspect-js/which-boxed-primitive/compare/v1.0.2...v1.1.0) - 2024-12-02

### Commits

- [actions] reuse common workflows [`893df44`](https://github.com/inspect-js/which-boxed-primitive/commit/893df44f4d4ad653878aa0b470fc3437f25ad240)
- [meta] use `npmignore` to autogenerate an npmignore file [`bab1ff8`](https://github.com/inspect-js/which-boxed-primitive/commit/bab1ff84d391d94a419bb22a0be3d589b16732a4)
- [Tests] use `es-value-fixtures` and `for-each` [`ecacfa0`](https://github.com/inspect-js/which-boxed-primitive/commit/ecacfa01438228830b77fc006e3d366c8227c2b3)
- [New] add types [`ab38e78`](https://github.com/inspect-js/which-boxed-primitive/commit/ab38e78885752258f1e90487f2793173a3e81e5e)
- [actions] split out node 10-20, and 20+ [`7ee9c3c`](https://github.com/inspect-js/which-boxed-primitive/commit/7ee9c3c27b31289db6970cb2024621cdef4dc5ae)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`, `object-inspect`, `safe-publish-latest`, `tape` [`142215a`](https://github.com/inspect-js/which-boxed-primitive/commit/142215aeb898604e356e46708a7fad0d1113d764)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`, `has-symbols`, `object-inspect`, `tape` [`3559371`](https://github.com/inspect-js/which-boxed-primitive/commit/3559371556c26688418e6ea06e52dfd51cce29e7)
- [actions] update rebase action to use reusable workflow [`928901a`](https://github.com/inspect-js/which-boxed-primitive/commit/928901a781ddcb75cec158f3d0a273100d8f3f80)
- [Deps] update `is-bigint`, `is-boolean-object`, `is-number-object`, `is-string`, `is-symbol` [`f7b14be`](https://github.com/inspect-js/which-boxed-primitive/commit/f7b14bed64b6f784221dfe229b583c512d2bcb2c)
- [Dev Deps] update `@ljharb/eslint-config`, `auto-changelog`, `npmignore`, `object-inspect`, `tape` [`5296738`](https://github.com/inspect-js/which-boxed-primitive/commit/5296738a192d9d006a5874dcc262080df108da62)
- [Deps] update `is-bigint`, `is-boolean-object`, `is-number-object`, `is-string`, `is-symbol` [`caa6d1c`](https://github.com/inspect-js/which-boxed-primitive/commit/caa6d1cb8e44c6b8100431dd643e9817f361921c)
- [meta] add missing `engines.node` [`ca40880`](https://github.com/inspect-js/which-boxed-primitive/commit/ca40880bb94282871229a7f1d678609b71f4d120)
- [Tests] replace `aud` with `npm audit` [`b0f4069`](https://github.com/inspect-js/which-boxed-primitive/commit/b0f40690697c2a963dd7100eec500f138a79f4ae)
- [Dev Deps] update `aud` [`8d0e336`](https://github.com/inspect-js/which-boxed-primitive/commit/8d0e336ee7385ed6a94d6362e62e25c54d155a2d)
- [Deps] update `is-number-object` [`eafcabf`](https://github.com/inspect-js/which-boxed-primitive/commit/eafcabf3f00c7d716bffdfc0ceeca62387349c7d)
- [Dev Deps] add missing peer dep [`ec4dd52`](https://github.com/inspect-js/which-boxed-primitive/commit/ec4dd520dbe77e33341d94479aec3b42817b4cbf)

## [v1.0.2](https://github.com/inspect-js/which-boxed-primitive/compare/v1.0.1...v1.0.2) - 2020-12-14

### Commits

- [Tests] use shared travis-ci configs [`8674582`](https://github.com/inspect-js/which-boxed-primitive/commit/86745829b6a92cff2cfb0d3c0414ec9afdc2a087)
- [Tests] migrate tests to Github Actions [`dff6643`](https://github.com/inspect-js/which-boxed-primitive/commit/dff6643405ba4d6dc6694a25904c8f72f273ece8)
- [meta] do not publish github action workflow files [`b26112a`](https://github.com/inspect-js/which-boxed-primitive/commit/b26112a4e4ac6beec8f54c734135dbf9e9ba16f9)
- [meta] make `auto-changelog` config consistent [`8d10175`](https://github.com/inspect-js/which-boxed-primitive/commit/8d10175171154cd6c8f8a016aa7fb71b5044acf6)
- [readme] fix repo URLs, remove defunct badges [`ab8db24`](https://github.com/inspect-js/which-boxed-primitive/commit/ab8db247573723dbcda68469118d08c7c2692c67)
- [Tests] run `nyc` on all tests; use `tape` runner [`7d084df`](https://github.com/inspect-js/which-boxed-primitive/commit/7d084dfc5251230e9399a81782c0b9d7ae5d1901)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`, `object-inspect`, `tape` [`576f6f3`](https://github.com/inspect-js/which-boxed-primitive/commit/576f6f308aed35ef1d3392bb9472def59482ed13)
- [actions] add automatic rebasing / merge commit blocking [`97efa53`](https://github.com/inspect-js/which-boxed-primitive/commit/97efa53a307678323e63f576c07db9ff84846fd3)
- [actions] add "Allow Edits" workflow [`fb1b4f7`](https://github.com/inspect-js/which-boxed-primitive/commit/fb1b4f7cd753fcced74ac054b20c8b2bfafe7953)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `has-symbols`, `object-inspect`, `safe-publish-latest` [`1e03c61`](https://github.com/inspect-js/which-boxed-primitive/commit/1e03c6153693d385833acc15178f675e6ce5ddd0)
- [Deps] update `is-boolean-object`, `is-number-object`, `is-string`, `is-symbol` [`13673df`](https://github.com/inspect-js/which-boxed-primitive/commit/13673dff6e43f0a915377c3e5740ec24e86d6bb7)
- [Dev Deps] update `auto-changelog`, `in-publish`, `tape` [`65a0e15`](https://github.com/inspect-js/which-boxed-primitive/commit/65a0e155fc46a9237692233a51ec9573621135d2)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `tape` [`f8a0afe`](https://github.com/inspect-js/which-boxed-primitive/commit/f8a0afea82938d64f3d2d240268afbd346d0c4da)
- [Deps] update `is-bigint`, `is-boolean-object` [`e7a1ce2`](https://github.com/inspect-js/which-boxed-primitive/commit/e7a1ce25371c00ee726f1c0cc5b6acf10d51ec50)
- [actions] switch Automatic Rebase workflow to `pull_request_target` event [`e46f193`](https://github.com/inspect-js/which-boxed-primitive/commit/e46f193298b158db5c8aba889803513e4ee38957)
- [Dev Deps] update `@ljharb/eslint-config`, `tape` [`df3da14`](https://github.com/inspect-js/which-boxed-primitive/commit/df3da1424552a5d22e203a0abf1710106bfd4ae2)
- [Dev Deps] update `auto-changelog`; add `aud` [`e2e8a12`](https://github.com/inspect-js/which-boxed-primitive/commit/e2e8a12c6fbf8c48e760ea1d1ccd5e8d2d6fbf24)
- [meta] add `funding` field [`7df404b`](https://github.com/inspect-js/which-boxed-primitive/commit/7df404b20cd50b2b87e6645b130fefa8ee98810e)
- [Dev Deps] update `auto-changelog` [`0d6b76d`](https://github.com/inspect-js/which-boxed-primitive/commit/0d6b76dbbe760581fa86a0c3f254988fe5d27770)
- [Tests] only audit prod deps [`246151c`](https://github.com/inspect-js/which-boxed-primitive/commit/246151cc1407b3b1ef42014db993f62670bd82ff)
- [meta] fix changelog [`c2d1685`](https://github.com/inspect-js/which-boxed-primitive/commit/c2d16856deffbf86e0b5029e69b65d8aa758ec3d)
- [readme] Fix spelling error [`25fb2b5`](https://github.com/inspect-js/which-boxed-primitive/commit/25fb2b56e1f708c6364923e4bae384f818ecf57f)

## [v1.0.1](https://github.com/inspect-js/which-boxed-primitive/compare/v1.0.0...v1.0.1) - 2019-08-10

### Commits

- [meta] avoid running `safe-publish-latest` when not publishing [`df44b27`](https://github.com/inspect-js/which-boxed-primitive/commit/df44b27875a8f5c3c596663ecb4a063f9fc7bde3)

## v1.0.0 - 2019-08-10

### Commits

- [Tests] add `.travis.yml` [`764b0cf`](https://github.com/inspect-js/which-boxed-primitive/commit/764b0cf75f8d2b3a0ad2056de5f4ad85d5d1b765)
- Initial commit [`da7d068`](https://github.com/inspect-js/which-boxed-primitive/commit/da7d068913d591294bf155db5d438f7804d71b9a)
- readme [`1395bb2`](https://github.com/inspect-js/which-boxed-primitive/commit/1395bb27b72137ac01e48ee398a0f54e93fd87f5)
- [Tests] add tests [`0ff580f`](https://github.com/inspect-js/which-boxed-primitive/commit/0ff580f99579cd4424af7b814bd76fcb69a2b04e)
- implementation [`8811c32`](https://github.com/inspect-js/which-boxed-primitive/commit/8811c3262a57963634cdc83ceb5bb2c5e9ae4e7e)
- npm init [`cffdea9`](https://github.com/inspect-js/which-boxed-primitive/commit/cffdea9755eabfa2f9ec62a6fcbce0c28f04495b)
- [Tests] add `npm run lint` [`a8be993`](https://github.com/inspect-js/which-boxed-primitive/commit/a8be9933fec1b21267acd847df77f6438e07e3b9)
- [meta] add FUNDING.yml [`941258c`](https://github.com/inspect-js/which-boxed-primitive/commit/941258c70c9a397466e05b614126cb8c7be77b99)
- Only apps should have lockfiles [`6857316`](https://github.com/inspect-js/which-boxed-primitive/commit/68573165d8ce842cdf15d94af82f8cccb961b8cf)
- [Tests] use `npx aud` in `posttest` [`ee48a91`](https://github.com/inspect-js/which-boxed-primitive/commit/ee48a9144bea23bde5cc47788a54d5aa7969d489)
