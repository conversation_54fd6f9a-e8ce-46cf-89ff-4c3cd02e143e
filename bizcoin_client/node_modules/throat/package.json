{"name": "throat", "version": "5.0.0", "description": "Throttle the parallelism of an asynchronous (promise returning) function / functions", "keywords": ["promise", "aplus", "then", "throttle", "concurrency", "parallelism", "limit"], "files": ["index.d.ts", "index.js", "index.js.flow"], "devDependencies": {"coveralls": "^3.0.0", "flow-bin": "^0.73.0", "istanbul": "^0.4.5", "jest": "^22.1.4", "promise": "^8.0.0", "sauce-test": "^1.0.0", "test-result": "^2.0.0", "testit": "^3.1.0", "typescript": "^3.4.5"}, "jest": {"testEnvironment": "node"}, "scripts": {"tsc": "tsc --noEmit", "flow": "flow", "test": "node test/index.js && npm run test:types && node test/browser.js", "test:types": "jest", "coverage": "istanbul cover test/index.js", "coveralls": "npm run coverage && cat ./coverage/lcov.info | coveralls"}, "repository": {"type": "git", "url": "https://github.com/ForbesLindesay/throat.git"}, "author": "ForbesLindesay", "license": "MIT"}