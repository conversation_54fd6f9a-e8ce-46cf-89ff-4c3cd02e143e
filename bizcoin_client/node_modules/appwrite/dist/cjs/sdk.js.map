{"version": 3, "file": "sdk.js", "sources": ["../../node_modules/tslib/tslib.es6.js", "../../src/query.ts", "../../src/client.ts", "../../src/service.ts", "../../src/services/account.ts", "../../src/services/avatars.ts", "../../src/services/databases.ts", "../../src/services/functions.ts", "../../src/services/graphql.ts", "../../src/services/locale.ts", "../../src/services/messaging.ts", "../../src/services/storage.ts", "../../src/services/teams.ts", "../../src/permission.ts", "../../src/role.ts", "../../src/id.ts", "../../src/enums/authenticator-type.ts", "../../src/enums/authentication-factor.ts", "../../src/enums/o-auth-provider.ts", "../../src/enums/browser.ts", "../../src/enums/credit-card.ts", "../../src/enums/flag.ts", "../../src/enums/execution-method.ts", "../../src/enums/image-gravity.ts", "../../src/enums/image-format.ts"], "sourcesContent": ["/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    if (typeof b !== \"function\" && b !== null)\r\n        throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    var desc = Object.getOwnPropertyDescriptor(m, k);\r\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\r\n        desc = { enumerable: true, get: function() { return m[k]; } };\r\n    }\r\n    Object.defineProperty(o, k2, desc);\r\n}) : (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n});\r\n\r\nexport function __exportStar(m, o) {\r\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n}\r\n\r\nexport function __spreadArray(to, from, pack) {\r\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\r\n        if (ar || !(i in from)) {\r\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\r\n            ar[i] = from[i];\r\n        }\r\n    }\r\n    return to.concat(ar || Array.prototype.slice.call(from));\r\n}\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nvar __setModuleDefault = Object.create ? (function(o, v) {\r\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\r\n}) : function(o, v) {\r\n    o[\"default\"] = v;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\r\n    __setModuleDefault(result, mod);\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, state, kind, f) {\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\r\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, state, value, kind, f) {\r\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\r\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\r\n}\r\n\r\nexport function __classPrivateFieldIn(state, receiver) {\r\n    if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\r\n    return typeof state === \"function\" ? receiver === state : state.has(receiver);\r\n}\r\n", "type QueryTypesSingle = string | number | boolean;\nexport type QueryTypesList = string[] | number[] | boolean[] | Query[];\nexport type QueryTypes = QueryTypesSingle | QueryTypesList;\ntype AttributesTypes = string | string[];\n\n/**\n * Helper class to generate query strings.\n */\nexport class Query {\n  method: string;\n  attribute: AttributesTypes | undefined;\n  values: QueryTypesList | undefined;\n\n  /**\n   * Constructor for Query class.\n   *\n   * @param {string} method\n   * @param {AttributesTypes} attribute\n   * @param {QueryTypes} values\n   */\n  constructor(\n    method: string,\n    attribute?: AttributesTypes,\n    values?: QueryTypes\n  ) {\n    this.method = method;\n    this.attribute = attribute;\n\n    if (values !== undefined) {\n      if (Array.isArray(values)) {\n        this.values = values;\n      } else {\n        this.values = [values] as QueryTypesList;\n      }\n    }\n  }\n\n  /**\n   * Convert the query object to a JSON string.\n   *\n   * @returns {string}\n   */\n  toString(): string {\n    return JSON.stringify({\n      method: this.method,\n      attribute: this.attribute,\n      values: this.values,\n    });\n  }\n\n  /**\n   * Filter resources where attribute is equal to value.\n   *\n   * @param {string} attribute\n   * @param {QueryTypes} value\n   * @returns {string}\n   */\n  static equal = (attribute: string, value: QueryTypes): string =>\n    new Query(\"equal\", attribute, value).toString();\n\n  /**\n   * Filter resources where attribute is not equal to value.\n   *\n   * @param {string} attribute\n   * @param {QueryTypes} value\n   * @returns {string}\n   */\n  static notEqual = (attribute: string, value: QueryTypes): string =>\n    new Query(\"notEqual\", attribute, value).toString();\n\n  /**\n   * Filter resources where attribute is less than value.\n   *\n   * @param {string} attribute\n   * @param {QueryTypes} value\n   * @returns {string}\n   */\n  static lessThan = (attribute: string, value: QueryTypes): string =>\n    new Query(\"lessThan\", attribute, value).toString();\n\n  /**\n   * Filter resources where attribute is less than or equal to value.\n   *\n   * @param {string} attribute\n   * @param {QueryTypes} value\n   * @returns {string}\n   */\n  static lessThanEqual = (attribute: string, value: QueryTypes): string =>\n    new Query(\"lessThanEqual\", attribute, value).toString();\n\n  /**\n   * Filter resources where attribute is greater than value.\n   *\n   * @param {string} attribute\n   * @param {QueryTypes} value\n   * @returns {string}\n   */\n  static greaterThan = (attribute: string, value: QueryTypes): string =>\n    new Query(\"greaterThan\", attribute, value).toString();\n\n  /**\n   * Filter resources where attribute is greater than or equal to value.\n   *\n   * @param {string} attribute\n   * @param {QueryTypes} value\n   * @returns {string}\n   */\n  static greaterThanEqual = (attribute: string, value: QueryTypes): string =>\n    new Query(\"greaterThanEqual\", attribute, value).toString();\n\n  /**\n   * Filter resources where attribute is null.\n   *\n   * @param {string} attribute\n   * @returns {string}\n   */\n  static isNull = (attribute: string): string =>\n    new Query(\"isNull\", attribute).toString();\n\n  /**\n   * Filter resources where attribute is not null.\n   *\n   * @param {string} attribute\n   * @returns {string}\n   */\n  static isNotNull = (attribute: string): string =>\n    new Query(\"isNotNull\", attribute).toString();\n\n  /**\n   * Filter resources where attribute is between start and end (inclusive).\n   *\n   * @param {string} attribute\n   * @param {string | number} start\n   * @param {string | number} end\n   * @returns {string}\n   */\n  static between = (attribute: string, start: string | number, end: string | number): string =>\n    new Query(\"between\", attribute, [start, end] as QueryTypesList).toString();\n\n  /**\n   * Filter resources where attribute starts with value.\n   *\n   * @param {string} attribute\n   * @param {string} value\n   * @returns {string}\n   */\n  static startsWith = (attribute: string, value: string): string =>\n    new Query(\"startsWith\", attribute, value).toString();\n\n  /**\n   * Filter resources where attribute ends with value.\n   *\n   * @param {string} attribute\n   * @param {string} value\n   * @returns {string}\n   */\n  static endsWith = (attribute: string, value: string): string =>\n    new Query(\"endsWith\", attribute, value).toString();\n\n  /**\n   * Specify which attributes should be returned by the API call.\n   *\n   * @param {string[]} attributes\n   * @returns {string}\n   */\n  static select = (attributes: string[]): string =>\n    new Query(\"select\", undefined, attributes).toString();\n\n  /**\n   * Filter resources by searching attribute for value.\n   * A fulltext index on attribute is required for this query to work.\n   *\n   * @param {string} attribute\n   * @param {string} value\n   * @returns {string}\n   */\n  static search = (attribute: string, value: string): string =>\n    new Query(\"search\", attribute, value).toString();\n\n  /**\n   * Sort results by attribute descending.\n   *\n   * @param {string} attribute\n   * @returns {string}\n   */\n  static orderDesc = (attribute: string): string =>\n    new Query(\"orderDesc\", attribute).toString();\n\n  /**\n   * Sort results by attribute ascending.\n   *\n   * @param {string} attribute\n   * @returns {string}\n   */\n  static orderAsc = (attribute: string): string =>\n    new Query(\"orderAsc\", attribute).toString();\n\n  /**\n   * Return results after documentId.\n   *\n   * @param {string} documentId\n   * @returns {string}\n   */\n  static cursorAfter = (documentId: string): string =>\n    new Query(\"cursorAfter\", undefined, documentId).toString();\n\n  /**\n   * Return results before documentId.\n   *\n   * @param {string} documentId\n   * @returns {string}\n   */\n  static cursorBefore = (documentId: string): string =>\n    new Query(\"cursorBefore\", undefined, documentId).toString();\n\n  /**\n   * Return only limit results.\n   *\n   * @param {number} limit\n   * @returns {string}\n   */\n  static limit = (limit: number): string =>\n    new Query(\"limit\", undefined, limit).toString();\n\n  /**\n   * Filter resources by skipping the first offset results.\n   *\n   * @param {number} offset\n   * @returns {string}\n   */\n  static offset = (offset: number): string =>\n    new Query(\"offset\", undefined, offset).toString();\n\n  /**\n   * Filter resources where attribute contains the specified value.\n   *\n   * @param {string} attribute\n   * @param {string | string[]} value\n   * @returns {string}\n   */\n  static contains = (attribute: string, value: string | string[]): string =>\n    new Query(\"contains\", attribute, value).toString();\n\n  /**\n   * Combine multiple queries using logical OR operator.\n   *\n   * @param {string[]} queries\n   * @returns {string}\n   */\n  static or = (queries: string[]) =>\n    new Query(\"or\", undefined, queries.map((query) => JSON.parse(query))).toString();\n\n  /**\n   * Combine multiple queries using logical AND operator.\n   *\n   * @param {string[]} queries\n   * @returns {string}\n   */\n  static and = (queries: string[]) =>\n    new Query(\"and\", undefined, queries.map((query) => JSON.parse(query))).toString();\n}\n", "import { Models } from './models';\n\n/**\n * Payload type representing a key-value pair with string keys and any values.\n */\ntype Payload = {\n    [key: string]: any;\n}\n\n/**\n * Headers type representing a key-value pair with string keys and string values.\n */\ntype Headers = {\n    [key: string]: string;\n}\n\n/**\n * Realtime response structure with different types.\n */\ntype RealtimeResponse = {\n    /**\n     * Type of the response: 'error', 'event', 'connected', 'response' or 'pong'.\n     */\n    type: 'error' | 'event' | 'connected' | 'response' | 'pong';\n\n    /**\n     * Data associated with the response based on the response type.\n     */\n    data: RealtimeResponseAuthenticated | RealtimeResponseConnected | RealtimeResponseError | RealtimeResponseEvent<unknown> | undefined;\n}\n\n/**\n * Realtime request structure for authentication.\n */\ntype RealtimeRequest = {\n    /**\n     * Type of the request: 'authentication'.\n     */\n    type: 'authentication';\n\n    /**\n     * Data required for authentication.\n     */\n    data: RealtimeRequestAuthenticate;\n}\n\n/**\n * Realtime event response structure with generic payload type.\n */\ntype RealtimeResponseEvent<T extends unknown> = {\n    /**\n     * List of event names associated with the response.\n     */\n    events: string[];\n\n    /**\n     * List of channel names associated with the response.\n     */\n    channels: string[];\n\n    /**\n     * Timestamp indicating the time of the event.\n     */\n    timestamp: number;\n\n    /**\n     * Payload containing event-specific data.\n     */\n    payload: T;\n}\n\n/**\n * Realtime response structure for errors.\n */\ntype RealtimeResponseError = {\n    /**\n     * Numeric error code indicating the type of error.\n     */\n    code: number;\n\n    /**\n     * Error message describing the encountered error.\n     */\n    message: string;\n}\n\n/**\n * Realtime response structure for a successful connection.\n */\ntype RealtimeResponseConnected = {\n    /**\n     * List of channels the user is connected to.\n     */\n    channels: string[];\n\n    /**\n     * User object representing the connected user (optional).\n     */\n    user?: object;\n}\n\n/**\n * Realtime response structure for authenticated connections.\n */\ntype RealtimeResponseAuthenticated = {\n    /**\n     * Destination channel for the response.\n     */\n    to: string;\n\n    /**\n     * Boolean indicating the success of the authentication process.\n     */\n    success: boolean;\n\n    /**\n     * User object representing the authenticated user.\n     */\n    user: object;\n}\n\n/**\n * Realtime request structure for authentication.\n */\ntype RealtimeRequestAuthenticate = {\n    /**\n     * Session identifier for authentication.\n     */\n    session: string;\n}\n\ntype TimeoutHandle = ReturnType<typeof setTimeout> | number;\n\n/**\n * Realtime interface representing the structure of a realtime communication object.\n */\ntype Realtime = {\n    /**\n     * WebSocket instance for realtime communication.\n     */\n    socket?: WebSocket;\n\n    /**\n     * Timeout for reconnect operations.\n     */\n    timeout?: TimeoutHandle;\n\n    /**\n     * Heartbeat interval for the realtime connection.\n    */\n    heartbeat?: TimeoutHandle;\n\n    /**\n     * URL for establishing the WebSocket connection.\n     */\n    url?: string;\n\n    /**\n     * Last received message from the realtime server.\n     */\n    lastMessage?: RealtimeResponse;\n\n    /**\n     * Set of channel names the client is subscribed to.\n     */\n    channels: Set<string>;\n\n    /**\n     * Map of subscriptions containing channel names and corresponding callback functions.\n     */\n    subscriptions: Map<number, {\n        channels: string[];\n        callback: (payload: RealtimeResponseEvent<any>) => void\n    }>;\n\n    /**\n     * Counter for managing subscriptions.\n     */\n    subscriptionsCounter: number;\n\n    /**\n     * Boolean indicating whether automatic reconnection is enabled.\n     */\n    reconnect: boolean;\n\n    /**\n     * Number of reconnection attempts made.\n     */\n    reconnectAttempts: number;\n\n    /**\n     * Function to get the timeout duration for communication operations.\n     */\n    getTimeout: () => number;\n\n    /**\n     * Function to establish a WebSocket connection.\n     */\n    connect: () => void;\n\n    /**\n     * Function to create a new WebSocket instance.\n     */\n    createSocket: () => void;\n\n    /**\n     * Function to create a new heartbeat interval.\n     */\n    createHeartbeat: () => void;\n\n    /**\n     * Function to clean up resources associated with specified channels.\n     *\n     * @param {string[]} channels - List of channel names to clean up.\n     */\n    cleanUp: (channels: string[]) => void;\n\n    /**\n     * Function to handle incoming messages from the WebSocket connection.\n     *\n     * @param {MessageEvent} event - Event containing the received message.\n     */\n    onMessage: (event: MessageEvent) => void;\n}\n\n/**\n * Type representing upload progress information.\n */\ntype UploadProgress = {\n    /**\n     * Identifier for the upload progress.\n     */\n    $id: string;\n\n    /**\n     * Current progress of the upload (in percentage).\n     */\n    progress: number;\n\n    /**\n     * Total size uploaded (in bytes) during the upload process.\n     */\n    sizeUploaded: number;\n\n    /**\n     * Total number of chunks that need to be uploaded.\n     */\n    chunksTotal: number;\n\n    /**\n     * Number of chunks that have been successfully uploaded.\n     */\n    chunksUploaded: number;\n}\n\n/**\n * Exception thrown by the  package\n */\nclass AppwriteException extends Error {\n    /**\n     * The error code associated with the exception.\n     */\n    code: number;\n\n    /**\n     * The response string associated with the exception.\n     */\n    response: string;\n\n    /**\n     * Error type.\n     * See [Error Types](https://appwrite.io/docs/response-codes#errorTypes) for more information.\n     */\n    type: string;\n\n    /**\n     * Initializes a Appwrite Exception.\n     *\n     * @param {string} message - The error message.\n     * @param {number} code - The error code. Default is 0.\n     * @param {string} type - The error type. Default is an empty string.\n     * @param {string} response - The response string. Default is an empty string.\n     */\n    constructor(message: string, code: number = 0, type: string = '', response: string = '') {\n        super(message);\n        this.name = 'AppwriteException';\n        this.message = message;\n        this.code = code;\n        this.type = type;\n        this.response = response;\n    }\n}\n\n/**\n * Client that handles requests to Appwrite\n */\nclass Client {\n    static CHUNK_SIZE = 1024 * 1024 * 5;\n\n    /**\n     * Holds configuration such as project.\n     */\n    config = {\n        endpoint: 'https://cloud.appwrite.io/v1',\n        endpointRealtime: '',\n        project: '',\n        jwt: '',\n        locale: '',\n        session: '',\n        devkey: '',\n    };\n    /**\n     * Custom headers for API requests.\n     */\n    headers: Headers = {\n        'x-sdk-name': 'Web',\n        'x-sdk-platform': 'client',\n        'x-sdk-language': 'web',\n        'x-sdk-version': '18.2.0',\n        'X-Appwrite-Response-Format': '1.7.0',\n    };\n\n    /**\n     * Set Endpoint\n     *\n     * Your project endpoint\n     *\n     * @param {string} endpoint\n     *\n     * @returns {this}\n     */\n    setEndpoint(endpoint: string): this {\n        if (!endpoint.startsWith('http://') && !endpoint.startsWith('https://')) {\n            throw new AppwriteException('Invalid endpoint URL: ' + endpoint);\n        }\n\n        this.config.endpoint = endpoint;\n        this.config.endpointRealtime = endpoint.replace('https://', 'wss://').replace('http://', 'ws://');\n\n        return this;\n    }\n\n    /**\n     * Set Realtime Endpoint\n     *\n     * @param {string} endpointRealtime\n     *\n     * @returns {this}\n     */\n    setEndpointRealtime(endpointRealtime: string): this {\n        if (!endpointRealtime.startsWith('ws://') && !endpointRealtime.startsWith('wss://')) {\n            throw new AppwriteException('Invalid realtime endpoint URL: ' + endpointRealtime);\n        }\n\n        this.config.endpointRealtime = endpointRealtime;\n        return this;\n    }\n\n    /**\n     * Set Project\n     *\n     * Your project ID\n     *\n     * @param value string\n     *\n     * @return {this}\n     */\n    setProject(value: string): this {\n        this.headers['X-Appwrite-Project'] = value;\n        this.config.project = value;\n        return this;\n    }\n    /**\n     * Set JWT\n     *\n     * Your secret JSON Web Token\n     *\n     * @param value string\n     *\n     * @return {this}\n     */\n    setJWT(value: string): this {\n        this.headers['X-Appwrite-JWT'] = value;\n        this.config.jwt = value;\n        return this;\n    }\n    /**\n     * Set Locale\n     *\n     * @param value string\n     *\n     * @return {this}\n     */\n    setLocale(value: string): this {\n        this.headers['X-Appwrite-Locale'] = value;\n        this.config.locale = value;\n        return this;\n    }\n    /**\n     * Set Session\n     *\n     * The user session to authenticate with\n     *\n     * @param value string\n     *\n     * @return {this}\n     */\n    setSession(value: string): this {\n        this.headers['X-Appwrite-Session'] = value;\n        this.config.session = value;\n        return this;\n    }\n    /**\n     * Set DevKey\n     *\n     * Your secret dev API key\n     *\n     * @param value string\n     *\n     * @return {this}\n     */\n    setDevKey(value: string): this {\n        this.headers['X-Appwrite-Dev-Key'] = value;\n        this.config.devkey = value;\n        return this;\n    }\n\n    private realtime: Realtime = {\n        socket: undefined,\n        timeout: undefined,\n        heartbeat: undefined,\n        url: '',\n        channels: new Set(),\n        subscriptions: new Map(),\n        subscriptionsCounter: 0,\n        reconnect: true,\n        reconnectAttempts: 0,\n        lastMessage: undefined,\n        connect: () => {\n            clearTimeout(this.realtime.timeout);\n            this.realtime.timeout = window?.setTimeout(() => {\n                this.realtime.createSocket();\n            }, 50);\n        },\n        getTimeout: () => {\n            switch (true) {\n                case this.realtime.reconnectAttempts < 5:\n                    return 1000;\n                case this.realtime.reconnectAttempts < 15:\n                    return 5000;\n                case this.realtime.reconnectAttempts < 100:\n                    return 10_000;\n                default:\n                    return 60_000;\n            }\n        },\n        createHeartbeat: () => {\n            if (this.realtime.heartbeat) {\n                clearTimeout(this.realtime.heartbeat);\n            }\n\n            this.realtime.heartbeat = window?.setInterval(() => {\n                this.realtime.socket?.send(JSON.stringify({\n                    type: 'ping'\n                }));\n            }, 20_000);\n        },\n        createSocket: () => {\n            if (this.realtime.channels.size < 1) {\n                this.realtime.reconnect = false;\n                this.realtime.socket?.close();\n                return;\n            }\n\n            const channels = new URLSearchParams();\n            channels.set('project', this.config.project);\n            this.realtime.channels.forEach(channel => {\n                channels.append('channels[]', channel);\n            });\n\n            const url = this.config.endpointRealtime + '/realtime?' + channels.toString();\n\n            if (\n                url !== this.realtime.url || // Check if URL is present\n                !this.realtime.socket || // Check if WebSocket has not been created\n                this.realtime.socket?.readyState > WebSocket.OPEN // Check if WebSocket is CLOSING (3) or CLOSED (4)\n            ) {\n                if (\n                    this.realtime.socket &&\n                    this.realtime.socket?.readyState < WebSocket.CLOSING // Close WebSocket if it is CONNECTING (0) or OPEN (1)\n                ) {\n                    this.realtime.reconnect = false;\n                    this.realtime.socket.close();\n                }\n\n                this.realtime.url = url;\n                this.realtime.socket = new WebSocket(url);\n                this.realtime.socket.addEventListener('message', this.realtime.onMessage);\n                this.realtime.socket.addEventListener('open', _event => {\n                    this.realtime.reconnectAttempts = 0;\n                    this.realtime.createHeartbeat();\n                });\n                this.realtime.socket.addEventListener('close', event => {\n                    if (\n                        !this.realtime.reconnect ||\n                        (\n                            this.realtime?.lastMessage?.type === 'error' && // Check if last message was of type error\n                            (<RealtimeResponseError>this.realtime?.lastMessage.data).code === 1008 // Check for policy violation 1008\n                        )\n                    ) {\n                        this.realtime.reconnect = true;\n                        return;\n                    }\n\n                    const timeout = this.realtime.getTimeout();\n                    console.error(`Realtime got disconnected. Reconnect will be attempted in ${timeout / 1000} seconds.`, event.reason);\n\n                    setTimeout(() => {\n                        this.realtime.reconnectAttempts++;\n                        this.realtime.createSocket();\n                    }, timeout);\n                })\n            }\n        },\n        onMessage: (event) => {\n            try {\n                const message: RealtimeResponse = JSON.parse(event.data);\n                this.realtime.lastMessage = message;\n                switch (message.type) {\n                    case 'connected':\n                        const cookie = JSON.parse(window.localStorage.getItem('cookieFallback') ?? '{}');\n                        const session = cookie?.[`a_session_${this.config.project}`];\n                        const messageData = <RealtimeResponseConnected>message.data;\n\n                        if (session && !messageData.user) {\n                            this.realtime.socket?.send(JSON.stringify(<RealtimeRequest>{\n                                type: 'authentication',\n                                data: {\n                                    session\n                                }\n                            }));\n                        }\n                        break;\n                    case 'event':\n                        let data = <RealtimeResponseEvent<unknown>>message.data;\n                        if (data?.channels) {\n                            const isSubscribed = data.channels.some(channel => this.realtime.channels.has(channel));\n                            if (!isSubscribed) return;\n                            this.realtime.subscriptions.forEach(subscription => {\n                                if (data.channels.some(channel => subscription.channels.includes(channel))) {\n                                    setTimeout(() => subscription.callback(data));\n                                }\n                            })\n                        }\n                        break;\n                    case 'pong':\n                        break; // Handle pong response if needed\n                    case 'error':\n                        throw message.data;\n                    default:\n                        break;\n                }\n            } catch (e) {\n                console.error(e);\n            }\n        },\n        cleanUp: channels => {\n            this.realtime.channels.forEach(channel => {\n                if (channels.includes(channel)) {\n                    let found = Array.from(this.realtime.subscriptions).some(([_key, subscription] )=> {\n                        return subscription.channels.includes(channel);\n                    })\n\n                    if (!found) {\n                        this.realtime.channels.delete(channel);\n                    }\n                }\n            })\n        }\n    }\n\n    /**\n     * Subscribes to Appwrite events and passes you the payload in realtime.\n     *\n     * @param {string|string[]} channels\n     * Channel to subscribe - pass a single channel as a string or multiple with an array of strings.\n     *\n     * Possible channels are:\n     * - account\n     * - collections\n     * - collections.[ID]\n     * - collections.[ID].documents\n     * - documents\n     * - documents.[ID]\n     * - files\n     * - files.[ID]\n     * - executions\n     * - executions.[ID]\n     * - functions.[ID]\n     * - teams\n     * - teams.[ID]\n     * - memberships\n     * - memberships.[ID]\n     * @param {(payload: RealtimeMessage) => void} callback Is called on every realtime update.\n     * @returns {() => void} Unsubscribes from events.\n     */\n    subscribe<T extends unknown>(channels: string | string[], callback: (payload: RealtimeResponseEvent<T>) => void): () => void {\n        let channelArray = typeof channels === 'string' ? [channels] : channels;\n        channelArray.forEach(channel => this.realtime.channels.add(channel));\n\n        const counter = this.realtime.subscriptionsCounter++;\n        this.realtime.subscriptions.set(counter, {\n            channels: channelArray,\n            callback\n        });\n\n        this.realtime.connect();\n\n        return () => {\n            this.realtime.subscriptions.delete(counter);\n            this.realtime.cleanUp(channelArray);\n            this.realtime.connect();\n        }\n    }\n\n    prepareRequest(method: string, url: URL, headers: Headers = {}, params: Payload = {}): { uri: string, options: RequestInit } {\n        method = method.toUpperCase();\n\n        headers = Object.assign({}, this.headers, headers);\n\n        if (typeof window !== 'undefined' && window.localStorage) {\n            const cookieFallback = window.localStorage.getItem('cookieFallback');\n            if (cookieFallback) {\n                headers['X-Fallback-Cookies'] = cookieFallback;\n            }\n        }\n\n        let options: RequestInit = {\n            method,\n            headers,\n        };\n\n        if (headers['X-Appwrite-Dev-Key'] === undefined) {\n            options.credentials = 'include';\n        }\n\n        if (method === 'GET') {\n            for (const [key, value] of Object.entries(Client.flatten(params))) {\n                url.searchParams.append(key, value);\n            }\n        } else {\n            switch (headers['content-type']) {\n                case 'application/json':\n                    options.body = JSON.stringify(params);\n                    break;\n\n                case 'multipart/form-data':\n                    const formData = new FormData();\n\n                    for (const [key, value] of Object.entries(params)) {\n                        if (value instanceof File) {\n                            formData.append(key, value, value.name);\n                        } else if (Array.isArray(value)) {\n                            for (const nestedValue of value) {\n                                formData.append(`${key}[]`, nestedValue);\n                            }\n                        } else {\n                            formData.append(key, value);\n                        }\n                    }\n\n                    options.body = formData;\n                    delete headers['content-type'];\n                    break;\n            }\n        }\n\n        return { uri: url.toString(), options };\n    }\n\n    async chunkedUpload(method: string, url: URL, headers: Headers = {}, originalPayload: Payload = {}, onProgress: (progress: UploadProgress) => void) {\n        const [fileParam, file] = Object.entries(originalPayload).find(([_, value]) => value instanceof File) ?? [];\n\n        if (!file || !fileParam) {\n            throw new Error('File not found in payload');\n        }\n\n        if (file.size <= Client.CHUNK_SIZE) {\n            return await this.call(method, url, headers, originalPayload);\n        }\n\n        let start = 0;\n        let response = null;\n\n        while (start < file.size) {\n            let end = start + Client.CHUNK_SIZE; // Prepare end for the next chunk\n            if (end >= file.size) {\n                end = file.size; // Adjust for the last chunk to include the last byte\n            }\n\n            headers['content-range'] = `bytes ${start}-${end-1}/${file.size}`;\n            const chunk = file.slice(start, end);\n\n            let payload = { ...originalPayload };\n            payload[fileParam] = new File([chunk], file.name);\n\n            response = await this.call(method, url, headers, payload);\n\n            if (onProgress && typeof onProgress === 'function') {\n                onProgress({\n                    $id: response.$id,\n                    progress: Math.round((end / file.size) * 100),\n                    sizeUploaded: end,\n                    chunksTotal: Math.ceil(file.size / Client.CHUNK_SIZE),\n                    chunksUploaded: Math.ceil(end / Client.CHUNK_SIZE)\n                });\n            }\n\n            if (response && response.$id) {\n                headers['x-appwrite-id'] = response.$id;\n            }\n\n            start = end;\n        }\n\n        return response;\n    }\n\n    async ping(): Promise<string> {\n        return this.call('GET', new URL(this.config.endpoint + '/ping'));\n    }\n\n    async call(method: string, url: URL, headers: Headers = {}, params: Payload = {}, responseType = 'json'): Promise<any> {\n        const { uri, options } = this.prepareRequest(method, url, headers, params);\n\n        let data: any = null;\n\n        const response = await fetch(uri, options);\n\n        // type opaque: No-CORS, different-origin response (CORS-issue)\n        if (response.type === 'opaque') {\n            throw new AppwriteException(\n                `Invalid Origin. Register your new client (${window.location.host}) as a new Web platform on your project console dashboard`,\n                403,\n                \"forbidden\",\n                \"\"\n            );\n        }\n\n        const warnings = response.headers.get('x-appwrite-warning');\n        if (warnings) {\n            warnings.split(';').forEach((warning: string) => console.warn('Warning: ' + warning));\n        }\n\n        if (response.headers.get('content-type')?.includes('application/json')) {\n            data = await response.json();\n        } else if (responseType === 'arrayBuffer') {\n            data = await response.arrayBuffer();\n        } else {\n            data = {\n                message: await response.text()\n            };\n        }\n\n        if (400 <= response.status) {\n            let responseText = '';\n            if (response.headers.get('content-type')?.includes('application/json') || responseType === 'arrayBuffer') {\n                responseText = JSON.stringify(data);\n            } else {\n                responseText = data?.message;\n            }\n            throw new AppwriteException(data?.message, response.status, data?.type, responseText);\n        }\n\n        const cookieFallback = response.headers.get('X-Fallback-Cookies');\n\n        if (typeof window !== 'undefined' && window.localStorage && cookieFallback) {\n            window.console.warn('Appwrite is using localStorage for session management. Increase your security by adding a custom domain as your API endpoint.');\n            window.localStorage.setItem('cookieFallback', cookieFallback);\n        }\n\n        return data;\n    }\n\n    static flatten(data: Payload, prefix = ''): Payload {\n        let output: Payload = {};\n\n        for (const [key, value] of Object.entries(data)) {\n            let finalKey = prefix ? prefix + '[' + key +']' : key;\n            if (Array.isArray(value)) {\n                output = { ...output, ...Client.flatten(value, finalKey) };\n            } else {\n                output[finalKey] = value;\n            }\n        }\n\n        return output;\n    }\n}\n\nexport { Client, AppwriteException };\nexport { Query } from './query';\nexport type { Models, Payload, UploadProgress };\nexport type { RealtimeResponseEvent };\nexport type { QueryTypes, QueryTypesList } from './query';\n", "import { Client } from './client';\nimport type { Payload } from './client';\n\nexport class Service {\n    /**\n     * The size for chunked uploads in bytes.\n     */\n    static CHUNK_SIZE = 5*1024*1024; // 5MB\n\n    client: Client;\n\n    constructor(client: Client) {\n        this.client = client;\n    }\n\n    static flatten(data: Payload, prefix = ''): Payload {\n        let output: Payload = {};\n\n        for (const [key, value] of Object.entries(data)) {\n            let finalKey = prefix ? prefix + '[' + key +']' : key;\n            if (Array.isArray(value)) {\n                output = { ...output, ...Service.flatten(value, finalKey) };\n            } else {\n                output[finalKey] = value;\n            }\n        }\n\n        return output;\n    }\n}", "import { Service } from '../service';\nimport { AppwriteException, Client, type Payload, UploadProgress } from '../client';\nimport type { Models } from '../models';\nimport { AuthenticatorType } from '../enums/authenticator-type';\nimport { AuthenticationFactor } from '../enums/authentication-factor';\nimport { OA<PERSON>Provider } from '../enums/o-auth-provider';\n\nexport class Account {\n    client: Client;\n\n    constructor(client: Client) {\n        this.client = client;\n    }\n\n    /**\n     * Get the currently logged in user.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.User<Preferences>>}\n     */\n    get<Preferences extends Models.Preferences = Models.DefaultPreferences>(): Promise<Models.User<Preferences>> {\n        const apiPath = '/account';\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n        }\n\n        return this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n\n    /**\n     * Use this endpoint to allow a new user to register a new account in your project. After the user registration completes successfully, you can use the [/account/verfication](https://appwrite.io/docs/references/cloud/client-web/account#createVerification) route to start verifying the user email address. To allow the new user to login to their new account, you need to create a new [account session](https://appwrite.io/docs/references/cloud/client-web/account#createEmailSession).\n     *\n     * @param {string} userId\n     * @param {string} email\n     * @param {string} password\n     * @param {string} name\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.User<Preferences>>}\n     */\n    create<Preferences extends Models.Preferences = Models.DefaultPreferences>(userId: string, email: string, password: string, name?: string): Promise<Models.User<Preferences>> {\n        if (typeof userId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"userId\"');\n        }\n        if (typeof email === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"email\"');\n        }\n        if (typeof password === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"password\"');\n        }\n        const apiPath = '/account';\n        const payload: Payload = {};\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n        if (typeof email !== 'undefined') {\n            payload['email'] = email;\n        }\n        if (typeof password !== 'undefined') {\n            payload['password'] = password;\n        }\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n\n    /**\n     * Update currently logged in user account email address. After changing user address, the user confirmation status will get reset. A new confirmation email is not sent automatically however you can use the send confirmation email endpoint again to send the confirmation email. For security measures, user password is required to complete this request.\n     * This endpoint can also be used to convert an anonymous account to a normal one, by passing an email address and a new password.\n     * \n     *\n     * @param {string} email\n     * @param {string} password\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.User<Preferences>>}\n     */\n    updateEmail<Preferences extends Models.Preferences = Models.DefaultPreferences>(email: string, password: string): Promise<Models.User<Preferences>> {\n        if (typeof email === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"email\"');\n        }\n        if (typeof password === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"password\"');\n        }\n        const apiPath = '/account/email';\n        const payload: Payload = {};\n        if (typeof email !== 'undefined') {\n            payload['email'] = email;\n        }\n        if (typeof password !== 'undefined') {\n            payload['password'] = password;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return this.client.call(\n            'patch',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n\n    /**\n     * Get the list of identities for the currently logged in user.\n     *\n     * @param {string[]} queries\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.IdentityList>}\n     */\n    listIdentities(queries?: string[]): Promise<Models.IdentityList> {\n        const apiPath = '/account/identities';\n        const payload: Payload = {};\n        if (typeof queries !== 'undefined') {\n            payload['queries'] = queries;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n        }\n\n        return this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n\n    /**\n     * Delete an identity by its unique ID.\n     *\n     * @param {string} identityId\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    deleteIdentity(identityId: string): Promise<{}> {\n        if (typeof identityId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"identityId\"');\n        }\n        const apiPath = '/account/identities/{identityId}'.replace('{identityId}', identityId);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return this.client.call(\n            'delete',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n\n    /**\n     * Use this endpoint to create a JSON Web Token. You can use the resulting JWT to authenticate on behalf of the current user when working with the Appwrite server-side API and SDKs. The JWT secret is valid for 15 minutes from its creation and will be invalid if the user will logout in that time frame.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Jwt>}\n     */\n    createJWT(): Promise<Models.Jwt> {\n        const apiPath = '/account/jwts';\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n\n    /**\n     * Get the list of latest security activity logs for the currently logged in user. Each log returns user IP address, location and date and time of log.\n     *\n     * @param {string[]} queries\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.LogList>}\n     */\n    listLogs(queries?: string[]): Promise<Models.LogList> {\n        const apiPath = '/account/logs';\n        const payload: Payload = {};\n        if (typeof queries !== 'undefined') {\n            payload['queries'] = queries;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n        }\n\n        return this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n\n    /**\n     * Enable or disable MFA on an account.\n     *\n     * @param {boolean} mfa\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.User<Preferences>>}\n     */\n    updateMFA<Preferences extends Models.Preferences = Models.DefaultPreferences>(mfa: boolean): Promise<Models.User<Preferences>> {\n        if (typeof mfa === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"mfa\"');\n        }\n        const apiPath = '/account/mfa';\n        const payload: Payload = {};\n        if (typeof mfa !== 'undefined') {\n            payload['mfa'] = mfa;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return this.client.call(\n            'patch',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n\n    /**\n     * Add an authenticator app to be used as an MFA factor. Verify the authenticator using the [verify authenticator](/docs/references/cloud/client-web/account#updateMfaAuthenticator) method.\n     *\n     * @param {AuthenticatorType} type\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.MfaType>}\n     */\n    createMfaAuthenticator(type: AuthenticatorType): Promise<Models.MfaType> {\n        if (typeof type === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"type\"');\n        }\n        const apiPath = '/account/mfa/authenticators/{type}'.replace('{type}', type);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n\n    /**\n     * Verify an authenticator app after adding it using the [add authenticator](/docs/references/cloud/client-web/account#createMfaAuthenticator) method.\n     *\n     * @param {AuthenticatorType} type\n     * @param {string} otp\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.User<Preferences>>}\n     */\n    updateMfaAuthenticator<Preferences extends Models.Preferences = Models.DefaultPreferences>(type: AuthenticatorType, otp: string): Promise<Models.User<Preferences>> {\n        if (typeof type === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"type\"');\n        }\n        if (typeof otp === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"otp\"');\n        }\n        const apiPath = '/account/mfa/authenticators/{type}'.replace('{type}', type);\n        const payload: Payload = {};\n        if (typeof otp !== 'undefined') {\n            payload['otp'] = otp;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return this.client.call(\n            'put',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n\n    /**\n     * Delete an authenticator for a user by ID.\n     *\n     * @param {AuthenticatorType} type\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    deleteMfaAuthenticator(type: AuthenticatorType): Promise<{}> {\n        if (typeof type === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"type\"');\n        }\n        const apiPath = '/account/mfa/authenticators/{type}'.replace('{type}', type);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return this.client.call(\n            'delete',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n\n    /**\n     * Begin the process of MFA verification after sign-in. Finish the flow with [updateMfaChallenge](/docs/references/cloud/client-web/account#updateMfaChallenge) method.\n     *\n     * @param {AuthenticationFactor} factor\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.MfaChallenge>}\n     */\n    createMfaChallenge(factor: AuthenticationFactor): Promise<Models.MfaChallenge> {\n        if (typeof factor === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"factor\"');\n        }\n        const apiPath = '/account/mfa/challenge';\n        const payload: Payload = {};\n        if (typeof factor !== 'undefined') {\n            payload['factor'] = factor;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n\n    /**\n     * Complete the MFA challenge by providing the one-time password. Finish the process of MFA verification by providing the one-time password. To begin the flow, use [createMfaChallenge](/docs/references/cloud/client-web/account#createMfaChallenge) method.\n     *\n     * @param {string} challengeId\n     * @param {string} otp\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Session>}\n     */\n    updateMfaChallenge(challengeId: string, otp: string): Promise<Models.Session> {\n        if (typeof challengeId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"challengeId\"');\n        }\n        if (typeof otp === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"otp\"');\n        }\n        const apiPath = '/account/mfa/challenge';\n        const payload: Payload = {};\n        if (typeof challengeId !== 'undefined') {\n            payload['challengeId'] = challengeId;\n        }\n        if (typeof otp !== 'undefined') {\n            payload['otp'] = otp;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return this.client.call(\n            'put',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n\n    /**\n     * List the factors available on the account to be used as a MFA challange.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.MfaFactors>}\n     */\n    listMfaFactors(): Promise<Models.MfaFactors> {\n        const apiPath = '/account/mfa/factors';\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n        }\n\n        return this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n\n    /**\n     * Get recovery codes that can be used as backup for MFA flow. Before getting codes, they must be generated using [createMfaRecoveryCodes](/docs/references/cloud/client-web/account#createMfaRecoveryCodes) method. An OTP challenge is required to read recovery codes.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.MfaRecoveryCodes>}\n     */\n    getMfaRecoveryCodes(): Promise<Models.MfaRecoveryCodes> {\n        const apiPath = '/account/mfa/recovery-codes';\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n        }\n\n        return this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n\n    /**\n     * Generate recovery codes as backup for MFA flow. It&#039;s recommended to generate and show then immediately after user successfully adds their authehticator. Recovery codes can be used as a MFA verification type in [createMfaChallenge](/docs/references/cloud/client-web/account#createMfaChallenge) method.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.MfaRecoveryCodes>}\n     */\n    createMfaRecoveryCodes(): Promise<Models.MfaRecoveryCodes> {\n        const apiPath = '/account/mfa/recovery-codes';\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n\n    /**\n     * Regenerate recovery codes that can be used as backup for MFA flow. Before regenerating codes, they must be first generated using [createMfaRecoveryCodes](/docs/references/cloud/client-web/account#createMfaRecoveryCodes) method. An OTP challenge is required to regenreate recovery codes.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.MfaRecoveryCodes>}\n     */\n    updateMfaRecoveryCodes(): Promise<Models.MfaRecoveryCodes> {\n        const apiPath = '/account/mfa/recovery-codes';\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return this.client.call(\n            'patch',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n\n    /**\n     * Update currently logged in user account name.\n     *\n     * @param {string} name\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.User<Preferences>>}\n     */\n    updateName<Preferences extends Models.Preferences = Models.DefaultPreferences>(name: string): Promise<Models.User<Preferences>> {\n        if (typeof name === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"name\"');\n        }\n        const apiPath = '/account/name';\n        const payload: Payload = {};\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return this.client.call(\n            'patch',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n\n    /**\n     * Update currently logged in user password. For validation, user is required to pass in the new password, and the old password. For users created with OAuth, Team Invites and Magic URL, oldPassword is optional.\n     *\n     * @param {string} password\n     * @param {string} oldPassword\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.User<Preferences>>}\n     */\n    updatePassword<Preferences extends Models.Preferences = Models.DefaultPreferences>(password: string, oldPassword?: string): Promise<Models.User<Preferences>> {\n        if (typeof password === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"password\"');\n        }\n        const apiPath = '/account/password';\n        const payload: Payload = {};\n        if (typeof password !== 'undefined') {\n            payload['password'] = password;\n        }\n        if (typeof oldPassword !== 'undefined') {\n            payload['oldPassword'] = oldPassword;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return this.client.call(\n            'patch',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n\n    /**\n     * Update the currently logged in user&#039;s phone number. After updating the phone number, the phone verification status will be reset. A confirmation SMS is not sent automatically, however you can use the [POST /account/verification/phone](https://appwrite.io/docs/references/cloud/client-web/account#createPhoneVerification) endpoint to send a confirmation SMS.\n     *\n     * @param {string} phone\n     * @param {string} password\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.User<Preferences>>}\n     */\n    updatePhone<Preferences extends Models.Preferences = Models.DefaultPreferences>(phone: string, password: string): Promise<Models.User<Preferences>> {\n        if (typeof phone === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"phone\"');\n        }\n        if (typeof password === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"password\"');\n        }\n        const apiPath = '/account/phone';\n        const payload: Payload = {};\n        if (typeof phone !== 'undefined') {\n            payload['phone'] = phone;\n        }\n        if (typeof password !== 'undefined') {\n            payload['password'] = password;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return this.client.call(\n            'patch',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n\n    /**\n     * Get the preferences as a key-value object for the currently logged in user.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Preferences>}\n     */\n    getPrefs<Preferences extends Models.Preferences = Models.DefaultPreferences>(): Promise<Preferences> {\n        const apiPath = '/account/prefs';\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n        }\n\n        return this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n\n    /**\n     * Update currently logged in user account preferences. The object you pass is stored as is, and replaces any previous value. The maximum allowed prefs size is 64kB and throws error if exceeded.\n     *\n     * @param {Partial<Preferences>} prefs\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.User<Preferences>>}\n     */\n    updatePrefs<Preferences extends Models.Preferences = Models.DefaultPreferences>(prefs: Partial<Preferences>): Promise<Models.User<Preferences>> {\n        if (typeof prefs === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"prefs\"');\n        }\n        const apiPath = '/account/prefs';\n        const payload: Payload = {};\n        if (typeof prefs !== 'undefined') {\n            payload['prefs'] = prefs;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return this.client.call(\n            'patch',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n\n    /**\n     * Sends the user an email with a temporary secret key for password reset. When the user clicks the confirmation link he is redirected back to your app password reset URL with the secret key and email address values attached to the URL query string. Use the query string params to submit a request to the [PUT /account/recovery](https://appwrite.io/docs/references/cloud/client-web/account#updateRecovery) endpoint to complete the process. The verification link sent to the user&#039;s email address is valid for 1 hour.\n     *\n     * @param {string} email\n     * @param {string} url\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Token>}\n     */\n    createRecovery(email: string, url: string): Promise<Models.Token> {\n        if (typeof email === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"email\"');\n        }\n        if (typeof url === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"url\"');\n        }\n        const apiPath = '/account/recovery';\n        const payload: Payload = {};\n        if (typeof email !== 'undefined') {\n            payload['email'] = email;\n        }\n        if (typeof url !== 'undefined') {\n            payload['url'] = url;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n\n    /**\n     * Use this endpoint to complete the user account password reset. Both the **userId** and **secret** arguments will be passed as query parameters to the redirect URL you have provided when sending your request to the [POST /account/recovery](https://appwrite.io/docs/references/cloud/client-web/account#createRecovery) endpoint.\n     * \n     * Please note that in order to avoid a [Redirect Attack](https://github.com/OWASP/CheatSheetSeries/blob/master/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.md) the only valid redirect URLs are the ones from domains you have set when adding your platforms in the console interface.\n     *\n     * @param {string} userId\n     * @param {string} secret\n     * @param {string} password\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Token>}\n     */\n    updateRecovery(userId: string, secret: string, password: string): Promise<Models.Token> {\n        if (typeof userId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"userId\"');\n        }\n        if (typeof secret === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"secret\"');\n        }\n        if (typeof password === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"password\"');\n        }\n        const apiPath = '/account/recovery';\n        const payload: Payload = {};\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n        if (typeof secret !== 'undefined') {\n            payload['secret'] = secret;\n        }\n        if (typeof password !== 'undefined') {\n            payload['password'] = password;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return this.client.call(\n            'put',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n\n    /**\n     * Get the list of active sessions across different devices for the currently logged in user.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.SessionList>}\n     */\n    listSessions(): Promise<Models.SessionList> {\n        const apiPath = '/account/sessions';\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n        }\n\n        return this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n\n    /**\n     * Delete all sessions from the user account and remove any sessions cookies from the end client.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    deleteSessions(): Promise<{}> {\n        const apiPath = '/account/sessions';\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return this.client.call(\n            'delete',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n\n    /**\n     * Use this endpoint to allow a new user to register an anonymous account in your project. This route will also create a new session for the user. To allow the new user to convert an anonymous account to a normal account, you need to update its [email and password](https://appwrite.io/docs/references/cloud/client-web/account#updateEmail) or create an [OAuth2 session](https://appwrite.io/docs/references/cloud/client-web/account#CreateOAuth2Session).\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Session>}\n     */\n    createAnonymousSession(): Promise<Models.Session> {\n        const apiPath = '/account/sessions/anonymous';\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n\n    /**\n     * Allow the user to login into their account by providing a valid email and password combination. This route will create a new session for the user.\n     * \n     * A user is limited to 10 active sessions at a time by default. [Learn more about session limits](https://appwrite.io/docs/authentication-security#limits).\n     *\n     * @param {string} email\n     * @param {string} password\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Session>}\n     */\n    createEmailPasswordSession(email: string, password: string): Promise<Models.Session> {\n        if (typeof email === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"email\"');\n        }\n        if (typeof password === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"password\"');\n        }\n        const apiPath = '/account/sessions/email';\n        const payload: Payload = {};\n        if (typeof email !== 'undefined') {\n            payload['email'] = email;\n        }\n        if (typeof password !== 'undefined') {\n            payload['password'] = password;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n\n    /**\n     * Use this endpoint to create a session from token. Provide the **userId** and **secret** parameters from the successful response of authentication flows initiated by token creation. For example, magic URL and phone login.\n     *\n     * @param {string} userId\n     * @param {string} secret\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Session>}\n     */\n    updateMagicURLSession(userId: string, secret: string): Promise<Models.Session> {\n        if (typeof userId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"userId\"');\n        }\n        if (typeof secret === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"secret\"');\n        }\n        const apiPath = '/account/sessions/magic-url';\n        const payload: Payload = {};\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n        if (typeof secret !== 'undefined') {\n            payload['secret'] = secret;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return this.client.call(\n            'put',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n\n    /**\n     * Allow the user to login to their account using the OAuth2 provider of their choice. Each OAuth2 provider should be enabled from the Appwrite console first. Use the success and failure arguments to provide a redirect URL&#039;s back to your app when login is completed.\n     * \n     * If there is already an active session, the new session will be attached to the logged-in account. If there are no active sessions, the server will attempt to look for a user with the same email address as the email received from the OAuth2 provider and attach the new session to the existing user. If no matching user is found - the server will create a new user.\n     * \n     * A user is limited to 10 active sessions at a time by default. [Learn more about session limits](https://appwrite.io/docs/authentication-security#limits).\n     * \n     *\n     * @param {OAuthProvider} provider\n     * @param {string} success\n     * @param {string} failure\n     * @param {string[]} scopes\n     * @throws {AppwriteException}\n     * @returns {void | string}\n     */\n    createOAuth2Session(provider: OAuthProvider, success?: string, failure?: string, scopes?: string[]): void | string {\n        if (typeof provider === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"provider\"');\n        }\n        const apiPath = '/account/sessions/oauth2/{provider}'.replace('{provider}', provider);\n        const payload: Payload = {};\n        if (typeof success !== 'undefined') {\n            payload['success'] = success;\n        }\n        if (typeof failure !== 'undefined') {\n            payload['failure'] = failure;\n        }\n        if (typeof scopes !== 'undefined') {\n            payload['scopes'] = scopes;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n        }\n\n        payload['project'] = this.client.config.project;\n\n        for (const [key, value] of Object.entries(Service.flatten(payload))) {\n            uri.searchParams.append(key, value);\n        }\n        \n        if (typeof window !== 'undefined' && window?.location) {\n            window.location.href = uri.toString();\n            return;\n        } else {\n            return uri.toString();\n        }\n    }\n\n    /**\n     * Use this endpoint to create a session from token. Provide the **userId** and **secret** parameters from the successful response of authentication flows initiated by token creation. For example, magic URL and phone login.\n     *\n     * @param {string} userId\n     * @param {string} secret\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Session>}\n     */\n    updatePhoneSession(userId: string, secret: string): Promise<Models.Session> {\n        if (typeof userId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"userId\"');\n        }\n        if (typeof secret === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"secret\"');\n        }\n        const apiPath = '/account/sessions/phone';\n        const payload: Payload = {};\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n        if (typeof secret !== 'undefined') {\n            payload['secret'] = secret;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return this.client.call(\n            'put',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n\n    /**\n     * Use this endpoint to create a session from token. Provide the **userId** and **secret** parameters from the successful response of authentication flows initiated by token creation. For example, magic URL and phone login.\n     *\n     * @param {string} userId\n     * @param {string} secret\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Session>}\n     */\n    createSession(userId: string, secret: string): Promise<Models.Session> {\n        if (typeof userId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"userId\"');\n        }\n        if (typeof secret === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"secret\"');\n        }\n        const apiPath = '/account/sessions/token';\n        const payload: Payload = {};\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n        if (typeof secret !== 'undefined') {\n            payload['secret'] = secret;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n\n    /**\n     * Use this endpoint to get a logged in user&#039;s session using a Session ID. Inputting &#039;current&#039; will return the current session being used.\n     *\n     * @param {string} sessionId\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Session>}\n     */\n    getSession(sessionId: string): Promise<Models.Session> {\n        if (typeof sessionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"sessionId\"');\n        }\n        const apiPath = '/account/sessions/{sessionId}'.replace('{sessionId}', sessionId);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n        }\n\n        return this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n\n    /**\n     * Use this endpoint to extend a session&#039;s length. Extending a session is useful when session expiry is short. If the session was created using an OAuth provider, this endpoint refreshes the access token from the provider.\n     *\n     * @param {string} sessionId\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Session>}\n     */\n    updateSession(sessionId: string): Promise<Models.Session> {\n        if (typeof sessionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"sessionId\"');\n        }\n        const apiPath = '/account/sessions/{sessionId}'.replace('{sessionId}', sessionId);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return this.client.call(\n            'patch',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n\n    /**\n     * Logout the user. Use &#039;current&#039; as the session ID to logout on this device, use a session ID to logout on another device. If you&#039;re looking to logout the user on all devices, use [Delete Sessions](https://appwrite.io/docs/references/cloud/client-web/account#deleteSessions) instead.\n     *\n     * @param {string} sessionId\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    deleteSession(sessionId: string): Promise<{}> {\n        if (typeof sessionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"sessionId\"');\n        }\n        const apiPath = '/account/sessions/{sessionId}'.replace('{sessionId}', sessionId);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return this.client.call(\n            'delete',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n\n    /**\n     * Block the currently logged in user account. Behind the scene, the user record is not deleted but permanently blocked from any access. To completely delete a user, use the Users API instead.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.User<Preferences>>}\n     */\n    updateStatus<Preferences extends Models.Preferences = Models.DefaultPreferences>(): Promise<Models.User<Preferences>> {\n        const apiPath = '/account/status';\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return this.client.call(\n            'patch',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n\n    /**\n     * Use this endpoint to register a device for push notifications. Provide a target ID (custom or generated using ID.unique()), a device identifier (usually a device token), and optionally specify which provider should send notifications to this target. The target is automatically linked to the current session and includes device information like brand and model.\n     *\n     * @param {string} targetId\n     * @param {string} identifier\n     * @param {string} providerId\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Target>}\n     */\n    createPushTarget(targetId: string, identifier: string, providerId?: string): Promise<Models.Target> {\n        if (typeof targetId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"targetId\"');\n        }\n        if (typeof identifier === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"identifier\"');\n        }\n        const apiPath = '/account/targets/push';\n        const payload: Payload = {};\n        if (typeof targetId !== 'undefined') {\n            payload['targetId'] = targetId;\n        }\n        if (typeof identifier !== 'undefined') {\n            payload['identifier'] = identifier;\n        }\n        if (typeof providerId !== 'undefined') {\n            payload['providerId'] = providerId;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n\n    /**\n     * Update the currently logged in user&#039;s push notification target. You can modify the target&#039;s identifier (device token) and provider ID (token, email, phone etc.). The target must exist and belong to the current user. If you change the provider ID, notifications will be sent through the new messaging provider instead.\n     *\n     * @param {string} targetId\n     * @param {string} identifier\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Target>}\n     */\n    updatePushTarget(targetId: string, identifier: string): Promise<Models.Target> {\n        if (typeof targetId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"targetId\"');\n        }\n        if (typeof identifier === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"identifier\"');\n        }\n        const apiPath = '/account/targets/{targetId}/push'.replace('{targetId}', targetId);\n        const payload: Payload = {};\n        if (typeof identifier !== 'undefined') {\n            payload['identifier'] = identifier;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return this.client.call(\n            'put',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n\n    /**\n     * Delete a push notification target for the currently logged in user. After deletion, the device will no longer receive push notifications. The target must exist and belong to the current user.\n     *\n     * @param {string} targetId\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    deletePushTarget(targetId: string): Promise<{}> {\n        if (typeof targetId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"targetId\"');\n        }\n        const apiPath = '/account/targets/{targetId}/push'.replace('{targetId}', targetId);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return this.client.call(\n            'delete',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n\n    /**\n     * Sends the user an email with a secret key for creating a session. If the provided user ID has not be registered, a new user will be created. Use the returned user ID and secret and submit a request to the [POST /v1/account/sessions/token](https://appwrite.io/docs/references/cloud/client-web/account#createSession) endpoint to complete the login process. The secret sent to the user&#039;s email is valid for 15 minutes.\n     * \n     * A user is limited to 10 active sessions at a time by default. [Learn more about session limits](https://appwrite.io/docs/authentication-security#limits).\n     *\n     * @param {string} userId\n     * @param {string} email\n     * @param {boolean} phrase\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Token>}\n     */\n    createEmailToken(userId: string, email: string, phrase?: boolean): Promise<Models.Token> {\n        if (typeof userId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"userId\"');\n        }\n        if (typeof email === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"email\"');\n        }\n        const apiPath = '/account/tokens/email';\n        const payload: Payload = {};\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n        if (typeof email !== 'undefined') {\n            payload['email'] = email;\n        }\n        if (typeof phrase !== 'undefined') {\n            payload['phrase'] = phrase;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n\n    /**\n     * Sends the user an email with a secret key for creating a session. If the provided user ID has not been registered, a new user will be created. When the user clicks the link in the email, the user is redirected back to the URL you provided with the secret key and userId values attached to the URL query string. Use the query string parameters to submit a request to the [POST /v1/account/sessions/token](https://appwrite.io/docs/references/cloud/client-web/account#createSession) endpoint to complete the login process. The link sent to the user&#039;s email address is valid for 1 hour.\n     * \n     * A user is limited to 10 active sessions at a time by default. [Learn more about session limits](https://appwrite.io/docs/authentication-security#limits).\n     * \n     *\n     * @param {string} userId\n     * @param {string} email\n     * @param {string} url\n     * @param {boolean} phrase\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Token>}\n     */\n    createMagicURLToken(userId: string, email: string, url?: string, phrase?: boolean): Promise<Models.Token> {\n        if (typeof userId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"userId\"');\n        }\n        if (typeof email === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"email\"');\n        }\n        const apiPath = '/account/tokens/magic-url';\n        const payload: Payload = {};\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n        if (typeof email !== 'undefined') {\n            payload['email'] = email;\n        }\n        if (typeof url !== 'undefined') {\n            payload['url'] = url;\n        }\n        if (typeof phrase !== 'undefined') {\n            payload['phrase'] = phrase;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n\n    /**\n     * Allow the user to login to their account using the OAuth2 provider of their choice. Each OAuth2 provider should be enabled from the Appwrite console first. Use the success and failure arguments to provide a redirect URL&#039;s back to your app when login is completed. \n     * \n     * If authentication succeeds, `userId` and `secret` of a token will be appended to the success URL as query parameters. These can be used to create a new session using the [Create session](https://appwrite.io/docs/references/cloud/client-web/account#createSession) endpoint.\n     * \n     * A user is limited to 10 active sessions at a time by default. [Learn more about session limits](https://appwrite.io/docs/authentication-security#limits).\n     *\n     * @param {OAuthProvider} provider\n     * @param {string} success\n     * @param {string} failure\n     * @param {string[]} scopes\n     * @throws {AppwriteException}\n     * @returns {void | string}\n     */\n    createOAuth2Token(provider: OAuthProvider, success?: string, failure?: string, scopes?: string[]): void | string {\n        if (typeof provider === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"provider\"');\n        }\n        const apiPath = '/account/tokens/oauth2/{provider}'.replace('{provider}', provider);\n        const payload: Payload = {};\n        if (typeof success !== 'undefined') {\n            payload['success'] = success;\n        }\n        if (typeof failure !== 'undefined') {\n            payload['failure'] = failure;\n        }\n        if (typeof scopes !== 'undefined') {\n            payload['scopes'] = scopes;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n        }\n\n        payload['project'] = this.client.config.project;\n\n        for (const [key, value] of Object.entries(Service.flatten(payload))) {\n            uri.searchParams.append(key, value);\n        }\n        \n        if (typeof window !== 'undefined' && window?.location) {\n            window.location.href = uri.toString();\n            return;\n        } else {\n            return uri.toString();\n        }\n    }\n\n    /**\n     * Sends the user an SMS with a secret key for creating a session. If the provided user ID has not be registered, a new user will be created. Use the returned user ID and secret and submit a request to the [POST /v1/account/sessions/token](https://appwrite.io/docs/references/cloud/client-web/account#createSession) endpoint to complete the login process. The secret sent to the user&#039;s phone is valid for 15 minutes.\n     * \n     * A user is limited to 10 active sessions at a time by default. [Learn more about session limits](https://appwrite.io/docs/authentication-security#limits).\n     *\n     * @param {string} userId\n     * @param {string} phone\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Token>}\n     */\n    createPhoneToken(userId: string, phone: string): Promise<Models.Token> {\n        if (typeof userId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"userId\"');\n        }\n        if (typeof phone === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"phone\"');\n        }\n        const apiPath = '/account/tokens/phone';\n        const payload: Payload = {};\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n        if (typeof phone !== 'undefined') {\n            payload['phone'] = phone;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n\n    /**\n     * Use this endpoint to send a verification message to your user email address to confirm they are the valid owners of that address. Both the **userId** and **secret** arguments will be passed as query parameters to the URL you have provided to be attached to the verification email. The provided URL should redirect the user back to your app and allow you to complete the verification process by verifying both the **userId** and **secret** parameters. Learn more about how to [complete the verification process](https://appwrite.io/docs/references/cloud/client-web/account#updateVerification). The verification link sent to the user&#039;s email address is valid for 7 days.\n     * \n     * Please note that in order to avoid a [Redirect Attack](https://github.com/OWASP/CheatSheetSeries/blob/master/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.md), the only valid redirect URLs are the ones from domains you have set when adding your platforms in the console interface.\n     * \n     *\n     * @param {string} url\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Token>}\n     */\n    createVerification(url: string): Promise<Models.Token> {\n        if (typeof url === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"url\"');\n        }\n        const apiPath = '/account/verification';\n        const payload: Payload = {};\n        if (typeof url !== 'undefined') {\n            payload['url'] = url;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n\n    /**\n     * Use this endpoint to complete the user email verification process. Use both the **userId** and **secret** parameters that were attached to your app URL to verify the user email ownership. If confirmed this route will return a 200 status code.\n     *\n     * @param {string} userId\n     * @param {string} secret\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Token>}\n     */\n    updateVerification(userId: string, secret: string): Promise<Models.Token> {\n        if (typeof userId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"userId\"');\n        }\n        if (typeof secret === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"secret\"');\n        }\n        const apiPath = '/account/verification';\n        const payload: Payload = {};\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n        if (typeof secret !== 'undefined') {\n            payload['secret'] = secret;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return this.client.call(\n            'put',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n\n    /**\n     * Use this endpoint to send a verification SMS to the currently logged in user. This endpoint is meant for use after updating a user&#039;s phone number using the [accountUpdatePhone](https://appwrite.io/docs/references/cloud/client-web/account#updatePhone) endpoint. Learn more about how to [complete the verification process](https://appwrite.io/docs/references/cloud/client-web/account#updatePhoneVerification). The verification code sent to the user&#039;s phone number is valid for 15 minutes.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Token>}\n     */\n    createPhoneVerification(): Promise<Models.Token> {\n        const apiPath = '/account/verification/phone';\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n\n    /**\n     * Use this endpoint to complete the user phone verification process. Use the **userId** and **secret** that were sent to your user&#039;s phone number to verify the user email ownership. If confirmed this route will return a 200 status code.\n     *\n     * @param {string} userId\n     * @param {string} secret\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Token>}\n     */\n    updatePhoneVerification(userId: string, secret: string): Promise<Models.Token> {\n        if (typeof userId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"userId\"');\n        }\n        if (typeof secret === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"secret\"');\n        }\n        const apiPath = '/account/verification/phone';\n        const payload: Payload = {};\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n        if (typeof secret !== 'undefined') {\n            payload['secret'] = secret;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return this.client.call(\n            'put',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n}\n", "import { Service } from '../service';\nimport { AppwriteException, Client, type Payload, UploadProgress } from '../client';\nimport type { Models } from '../models';\nimport { Browser } from '../enums/browser';\nimport { CreditCard } from '../enums/credit-card';\nimport { Flag } from '../enums/flag';\n\nexport class Avatars {\n    client: Client;\n\n    constructor(client: Client) {\n        this.client = client;\n    }\n\n    /**\n     * You can use this endpoint to show different browser icons to your users. The code argument receives the browser code as it appears in your user [GET /account/sessions](https://appwrite.io/docs/references/cloud/client-web/account#getSessions) endpoint. Use width, height and quality arguments to change the output settings.\n     * \n     * When one dimension is specified and the other is 0, the image is scaled with preserved aspect ratio. If both dimensions are 0, the API provides an image at source quality. If dimensions are not specified, the default size of image returned is 100x100px.\n     *\n     * @param {Browser} code\n     * @param {number} width\n     * @param {number} height\n     * @param {number} quality\n     * @throws {AppwriteException}\n     * @returns {string}\n     */\n    getBrowser(code: Browser, width?: number, height?: number, quality?: number): string {\n        if (typeof code === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"code\"');\n        }\n        const apiPath = '/avatars/browsers/{code}'.replace('{code}', code);\n        const payload: Payload = {};\n        if (typeof width !== 'undefined') {\n            payload['width'] = width;\n        }\n        if (typeof height !== 'undefined') {\n            payload['height'] = height;\n        }\n        if (typeof quality !== 'undefined') {\n            payload['quality'] = quality;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n        }\n\n        payload['project'] = this.client.config.project;\n\n        for (const [key, value] of Object.entries(Service.flatten(payload))) {\n            uri.searchParams.append(key, value);\n        }\n        \n        return uri.toString();\n    }\n\n    /**\n     * The credit card endpoint will return you the icon of the credit card provider you need. Use width, height and quality arguments to change the output settings.\n     * \n     * When one dimension is specified and the other is 0, the image is scaled with preserved aspect ratio. If both dimensions are 0, the API provides an image at source quality. If dimensions are not specified, the default size of image returned is 100x100px.\n     * \n     *\n     * @param {CreditCard} code\n     * @param {number} width\n     * @param {number} height\n     * @param {number} quality\n     * @throws {AppwriteException}\n     * @returns {string}\n     */\n    getCreditCard(code: CreditCard, width?: number, height?: number, quality?: number): string {\n        if (typeof code === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"code\"');\n        }\n        const apiPath = '/avatars/credit-cards/{code}'.replace('{code}', code);\n        const payload: Payload = {};\n        if (typeof width !== 'undefined') {\n            payload['width'] = width;\n        }\n        if (typeof height !== 'undefined') {\n            payload['height'] = height;\n        }\n        if (typeof quality !== 'undefined') {\n            payload['quality'] = quality;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n        }\n\n        payload['project'] = this.client.config.project;\n\n        for (const [key, value] of Object.entries(Service.flatten(payload))) {\n            uri.searchParams.append(key, value);\n        }\n        \n        return uri.toString();\n    }\n\n    /**\n     * Use this endpoint to fetch the favorite icon (AKA favicon) of any remote website URL.\n     * \n     * This endpoint does not follow HTTP redirects.\n     *\n     * @param {string} url\n     * @throws {AppwriteException}\n     * @returns {string}\n     */\n    getFavicon(url: string): string {\n        if (typeof url === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"url\"');\n        }\n        const apiPath = '/avatars/favicon';\n        const payload: Payload = {};\n        if (typeof url !== 'undefined') {\n            payload['url'] = url;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n        }\n\n        payload['project'] = this.client.config.project;\n\n        for (const [key, value] of Object.entries(Service.flatten(payload))) {\n            uri.searchParams.append(key, value);\n        }\n        \n        return uri.toString();\n    }\n\n    /**\n     * You can use this endpoint to show different country flags icons to your users. The code argument receives the 2 letter country code. Use width, height and quality arguments to change the output settings. Country codes follow the [ISO 3166-1](https://en.wikipedia.org/wiki/ISO_3166-1) standard.\n     * \n     * When one dimension is specified and the other is 0, the image is scaled with preserved aspect ratio. If both dimensions are 0, the API provides an image at source quality. If dimensions are not specified, the default size of image returned is 100x100px.\n     * \n     *\n     * @param {Flag} code\n     * @param {number} width\n     * @param {number} height\n     * @param {number} quality\n     * @throws {AppwriteException}\n     * @returns {string}\n     */\n    getFlag(code: Flag, width?: number, height?: number, quality?: number): string {\n        if (typeof code === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"code\"');\n        }\n        const apiPath = '/avatars/flags/{code}'.replace('{code}', code);\n        const payload: Payload = {};\n        if (typeof width !== 'undefined') {\n            payload['width'] = width;\n        }\n        if (typeof height !== 'undefined') {\n            payload['height'] = height;\n        }\n        if (typeof quality !== 'undefined') {\n            payload['quality'] = quality;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n        }\n\n        payload['project'] = this.client.config.project;\n\n        for (const [key, value] of Object.entries(Service.flatten(payload))) {\n            uri.searchParams.append(key, value);\n        }\n        \n        return uri.toString();\n    }\n\n    /**\n     * Use this endpoint to fetch a remote image URL and crop it to any image size you want. This endpoint is very useful if you need to crop and display remote images in your app or in case you want to make sure a 3rd party image is properly served using a TLS protocol.\n     * \n     * When one dimension is specified and the other is 0, the image is scaled with preserved aspect ratio. If both dimensions are 0, the API provides an image at source quality. If dimensions are not specified, the default size of image returned is 400x400px.\n     * \n     * This endpoint does not follow HTTP redirects.\n     *\n     * @param {string} url\n     * @param {number} width\n     * @param {number} height\n     * @throws {AppwriteException}\n     * @returns {string}\n     */\n    getImage(url: string, width?: number, height?: number): string {\n        if (typeof url === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"url\"');\n        }\n        const apiPath = '/avatars/image';\n        const payload: Payload = {};\n        if (typeof url !== 'undefined') {\n            payload['url'] = url;\n        }\n        if (typeof width !== 'undefined') {\n            payload['width'] = width;\n        }\n        if (typeof height !== 'undefined') {\n            payload['height'] = height;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n        }\n\n        payload['project'] = this.client.config.project;\n\n        for (const [key, value] of Object.entries(Service.flatten(payload))) {\n            uri.searchParams.append(key, value);\n        }\n        \n        return uri.toString();\n    }\n\n    /**\n     * Use this endpoint to show your user initials avatar icon on your website or app. By default, this route will try to print your logged-in user name or email initials. You can also overwrite the user name if you pass the &#039;name&#039; parameter. If no name is given and no user is logged, an empty avatar will be returned.\n     * \n     * You can use the color and background params to change the avatar colors. By default, a random theme will be selected. The random theme will persist for the user&#039;s initials when reloading the same theme will always return for the same initials.\n     * \n     * When one dimension is specified and the other is 0, the image is scaled with preserved aspect ratio. If both dimensions are 0, the API provides an image at source quality. If dimensions are not specified, the default size of image returned is 100x100px.\n     * \n     *\n     * @param {string} name\n     * @param {number} width\n     * @param {number} height\n     * @param {string} background\n     * @throws {AppwriteException}\n     * @returns {string}\n     */\n    getInitials(name?: string, width?: number, height?: number, background?: string): string {\n        const apiPath = '/avatars/initials';\n        const payload: Payload = {};\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n        if (typeof width !== 'undefined') {\n            payload['width'] = width;\n        }\n        if (typeof height !== 'undefined') {\n            payload['height'] = height;\n        }\n        if (typeof background !== 'undefined') {\n            payload['background'] = background;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n        }\n\n        payload['project'] = this.client.config.project;\n\n        for (const [key, value] of Object.entries(Service.flatten(payload))) {\n            uri.searchParams.append(key, value);\n        }\n        \n        return uri.toString();\n    }\n\n    /**\n     * Converts a given plain text to a QR code image. You can use the query parameters to change the size and style of the resulting image.\n     * \n     *\n     * @param {string} text\n     * @param {number} size\n     * @param {number} margin\n     * @param {boolean} download\n     * @throws {AppwriteException}\n     * @returns {string}\n     */\n    getQR(text: string, size?: number, margin?: number, download?: boolean): string {\n        if (typeof text === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"text\"');\n        }\n        const apiPath = '/avatars/qr';\n        const payload: Payload = {};\n        if (typeof text !== 'undefined') {\n            payload['text'] = text;\n        }\n        if (typeof size !== 'undefined') {\n            payload['size'] = size;\n        }\n        if (typeof margin !== 'undefined') {\n            payload['margin'] = margin;\n        }\n        if (typeof download !== 'undefined') {\n            payload['download'] = download;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n        }\n\n        payload['project'] = this.client.config.project;\n\n        for (const [key, value] of Object.entries(Service.flatten(payload))) {\n            uri.searchParams.append(key, value);\n        }\n        \n        return uri.toString();\n    }\n}\n", "import { Service } from '../service';\nimport { AppwriteException, Client, type Payload, UploadProgress } from '../client';\nimport type { Models } from '../models';\n\nexport class Databases {\n    client: Client;\n\n    constructor(client: Client) {\n        this.client = client;\n    }\n\n    /**\n     * Get a list of all the user&#039;s documents in a given collection. You can use the query params to filter your results.\n     *\n     * @param {string} databaseId\n     * @param {string} collectionId\n     * @param {string[]} queries\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.DocumentList<Document>>}\n     */\n    listDocuments<Document extends Models.Document = Models.DefaultDocument>(databaseId: string, collectionId: string, queries?: string[]): Promise<Models.DocumentList<Document>> {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n        if (typeof collectionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"collectionId\"');\n        }\n        const apiPath = '/databases/{databaseId}/collections/{collectionId}/documents'.replace('{databaseId}', databaseId).replace('{collectionId}', collectionId);\n        const payload: Payload = {};\n        if (typeof queries !== 'undefined') {\n            payload['queries'] = queries;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n        }\n\n        return this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n\n    /**\n     * Create a new Document. Before using this route, you should create a new collection resource using either a [server integration](https://appwrite.io/docs/server/databases#databasesCreateCollection) API or directly from your database console.\n     *\n     * @param {string} databaseId\n     * @param {string} collectionId\n     * @param {string} documentId\n     * @param {Document extends Models.DefaultDocument ? Models.DataWithoutDocumentKeys : Omit<Document, keyof Models.Document>} data\n     * @param {string[]} permissions\n     * @throws {AppwriteException}\n     * @returns {Promise<Document>}\n     */\n    createDocument<Document extends Models.Document = Models.DefaultDocument>(databaseId: string, collectionId: string, documentId: string, data: Document extends Models.DefaultDocument ? Models.DataWithoutDocumentKeys : Omit<Document, keyof Models.Document>, permissions?: string[]): Promise<Document> {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n        if (typeof collectionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"collectionId\"');\n        }\n        if (typeof documentId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"documentId\"');\n        }\n        if (typeof data === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"data\"');\n        }\n        const apiPath = '/databases/{databaseId}/collections/{collectionId}/documents'.replace('{databaseId}', databaseId).replace('{collectionId}', collectionId);\n        const payload: Payload = {};\n        if (typeof documentId !== 'undefined') {\n            payload['documentId'] = documentId;\n        }\n        if (typeof data !== 'undefined') {\n            payload['data'] = data;\n        }\n        if (typeof permissions !== 'undefined') {\n            payload['permissions'] = permissions;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n\n    /**\n     * Get a document by its unique ID. This endpoint response returns a JSON object with the document data.\n     *\n     * @param {string} databaseId\n     * @param {string} collectionId\n     * @param {string} documentId\n     * @param {string[]} queries\n     * @throws {AppwriteException}\n     * @returns {Promise<Document>}\n     */\n    getDocument<Document extends Models.Document = Models.DefaultDocument>(databaseId: string, collectionId: string, documentId: string, queries?: string[]): Promise<Document> {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n        if (typeof collectionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"collectionId\"');\n        }\n        if (typeof documentId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"documentId\"');\n        }\n        const apiPath = '/databases/{databaseId}/collections/{collectionId}/documents/{documentId}'.replace('{databaseId}', databaseId).replace('{collectionId}', collectionId).replace('{documentId}', documentId);\n        const payload: Payload = {};\n        if (typeof queries !== 'undefined') {\n            payload['queries'] = queries;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n        }\n\n        return this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n\n    /**\n     * **WARNING: Experimental Feature** - This endpoint is experimental and not yet officially supported. It may be subject to breaking changes or removal in future versions.\n     * \n     * Create or update a Document. Before using this route, you should create a new collection resource using either a [server integration](https://appwrite.io/docs/server/databases#databasesCreateCollection) API or directly from your database console.\n     *\n     * @param {string} databaseId\n     * @param {string} collectionId\n     * @param {string} documentId\n     * @param {object} data\n     * @param {string[]} permissions\n     * @throws {AppwriteException}\n     * @returns {Promise<Document>}\n     */\n    upsertDocument<Document extends Models.Document = Models.DefaultDocument>(databaseId: string, collectionId: string, documentId: string, data: object, permissions?: string[]): Promise<Document> {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n        if (typeof collectionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"collectionId\"');\n        }\n        if (typeof documentId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"documentId\"');\n        }\n        if (typeof data === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"data\"');\n        }\n        const apiPath = '/databases/{databaseId}/collections/{collectionId}/documents/{documentId}'.replace('{databaseId}', databaseId).replace('{collectionId}', collectionId).replace('{documentId}', documentId);\n        const payload: Payload = {};\n        if (typeof data !== 'undefined') {\n            payload['data'] = data;\n        }\n        if (typeof permissions !== 'undefined') {\n            payload['permissions'] = permissions;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return this.client.call(\n            'put',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n\n    /**\n     * Update a document by its unique ID. Using the patch method you can pass only specific fields that will get updated.\n     *\n     * @param {string} databaseId\n     * @param {string} collectionId\n     * @param {string} documentId\n     * @param {Partial<Document extends Models.DefaultDocument ? Models.DataWithoutDocumentKeys : Omit<Document, keyof Models.Document>>} data\n     * @param {string[]} permissions\n     * @throws {AppwriteException}\n     * @returns {Promise<Document>}\n     */\n    updateDocument<Document extends Models.Document = Models.DefaultDocument>(databaseId: string, collectionId: string, documentId: string, data?: Partial<Document extends Models.DefaultDocument ? Models.DataWithoutDocumentKeys : Omit<Document, keyof Models.Document>>, permissions?: string[]): Promise<Document> {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n        if (typeof collectionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"collectionId\"');\n        }\n        if (typeof documentId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"documentId\"');\n        }\n        const apiPath = '/databases/{databaseId}/collections/{collectionId}/documents/{documentId}'.replace('{databaseId}', databaseId).replace('{collectionId}', collectionId).replace('{documentId}', documentId);\n        const payload: Payload = {};\n        if (typeof data !== 'undefined') {\n            payload['data'] = data;\n        }\n        if (typeof permissions !== 'undefined') {\n            payload['permissions'] = permissions;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return this.client.call(\n            'patch',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n\n    /**\n     * Delete a document by its unique ID.\n     *\n     * @param {string} databaseId\n     * @param {string} collectionId\n     * @param {string} documentId\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    deleteDocument(databaseId: string, collectionId: string, documentId: string): Promise<{}> {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n        if (typeof collectionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"collectionId\"');\n        }\n        if (typeof documentId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"documentId\"');\n        }\n        const apiPath = '/databases/{databaseId}/collections/{collectionId}/documents/{documentId}'.replace('{databaseId}', databaseId).replace('{collectionId}', collectionId).replace('{documentId}', documentId);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return this.client.call(\n            'delete',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n\n    /**\n     * Decrement a specific attribute of a document by a given value.\n     *\n     * @param {string} databaseId\n     * @param {string} collectionId\n     * @param {string} documentId\n     * @param {string} attribute\n     * @param {number} value\n     * @param {number} min\n     * @throws {AppwriteException}\n     * @returns {Promise<Document>}\n     */\n    decrementDocumentAttribute<Document extends Models.Document = Models.DefaultDocument>(databaseId: string, collectionId: string, documentId: string, attribute: string, value?: number, min?: number): Promise<Document> {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n        if (typeof collectionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"collectionId\"');\n        }\n        if (typeof documentId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"documentId\"');\n        }\n        if (typeof attribute === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"attribute\"');\n        }\n        const apiPath = '/databases/{databaseId}/collections/{collectionId}/documents/{documentId}/{attribute}/decrement'.replace('{databaseId}', databaseId).replace('{collectionId}', collectionId).replace('{documentId}', documentId).replace('{attribute}', attribute);\n        const payload: Payload = {};\n        if (typeof value !== 'undefined') {\n            payload['value'] = value;\n        }\n        if (typeof min !== 'undefined') {\n            payload['min'] = min;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return this.client.call(\n            'patch',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n\n    /**\n     * Increment a specific attribute of a document by a given value.\n     *\n     * @param {string} databaseId\n     * @param {string} collectionId\n     * @param {string} documentId\n     * @param {string} attribute\n     * @param {number} value\n     * @param {number} max\n     * @throws {AppwriteException}\n     * @returns {Promise<Document>}\n     */\n    incrementDocumentAttribute<Document extends Models.Document = Models.DefaultDocument>(databaseId: string, collectionId: string, documentId: string, attribute: string, value?: number, max?: number): Promise<Document> {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n        if (typeof collectionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"collectionId\"');\n        }\n        if (typeof documentId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"documentId\"');\n        }\n        if (typeof attribute === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"attribute\"');\n        }\n        const apiPath = '/databases/{databaseId}/collections/{collectionId}/documents/{documentId}/{attribute}/increment'.replace('{databaseId}', databaseId).replace('{collectionId}', collectionId).replace('{documentId}', documentId).replace('{attribute}', attribute);\n        const payload: Payload = {};\n        if (typeof value !== 'undefined') {\n            payload['value'] = value;\n        }\n        if (typeof max !== 'undefined') {\n            payload['max'] = max;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return this.client.call(\n            'patch',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n}\n", "import { Service } from '../service';\nimport { AppwriteException, Client, type Payload, UploadProgress } from '../client';\nimport type { Models } from '../models';\nimport { ExecutionMethod } from '../enums/execution-method';\n\nexport class Functions {\n    client: Client;\n\n    constructor(client: Client) {\n        this.client = client;\n    }\n\n    /**\n     * Get a list of all the current user function execution logs. You can use the query params to filter your results.\n     *\n     * @param {string} functionId\n     * @param {string[]} queries\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.ExecutionList>}\n     */\n    listExecutions(functionId: string, queries?: string[]): Promise<Models.ExecutionList> {\n        if (typeof functionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"functionId\"');\n        }\n        const apiPath = '/functions/{functionId}/executions'.replace('{functionId}', functionId);\n        const payload: Payload = {};\n        if (typeof queries !== 'undefined') {\n            payload['queries'] = queries;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n        }\n\n        return this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n\n    /**\n     * Trigger a function execution. The returned object will return you the current execution status. You can ping the `Get Execution` endpoint to get updates on the current execution status. Once this endpoint is called, your function execution process will start asynchronously.\n     *\n     * @param {string} functionId\n     * @param {string} body\n     * @param {boolean} async\n     * @param {string} xpath\n     * @param {ExecutionMethod} method\n     * @param {object} headers\n     * @param {string} scheduledAt\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Execution>}\n     */\n    createExecution(functionId: string, body?: string, async?: boolean, xpath?: string, method?: ExecutionMethod, headers?: object, scheduledAt?: string): Promise<Models.Execution> {\n        if (typeof functionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"functionId\"');\n        }\n        const apiPath = '/functions/{functionId}/executions'.replace('{functionId}', functionId);\n        const payload: Payload = {};\n        if (typeof body !== 'undefined') {\n            payload['body'] = body;\n        }\n        if (typeof async !== 'undefined') {\n            payload['async'] = async;\n        }\n        if (typeof xpath !== 'undefined') {\n            payload['path'] = xpath;\n        }\n        if (typeof method !== 'undefined') {\n            payload['method'] = method;\n        }\n        if (typeof headers !== 'undefined') {\n            payload['headers'] = headers;\n        }\n        if (typeof scheduledAt !== 'undefined') {\n            payload['scheduledAt'] = scheduledAt;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n\n    /**\n     * Get a function execution log by its unique ID.\n     *\n     * @param {string} functionId\n     * @param {string} executionId\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Execution>}\n     */\n    getExecution(functionId: string, executionId: string): Promise<Models.Execution> {\n        if (typeof functionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"functionId\"');\n        }\n        if (typeof executionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"executionId\"');\n        }\n        const apiPath = '/functions/{functionId}/executions/{executionId}'.replace('{functionId}', functionId).replace('{executionId}', executionId);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n        }\n\n        return this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n}\n", "import { Service } from '../service';\nimport { AppwriteException, Client, type Payload, UploadProgress } from '../client';\nimport type { Models } from '../models';\n\nexport class Graphql {\n    client: Client;\n\n    constructor(client: Client) {\n        this.client = client;\n    }\n\n    /**\n     * Execute a GraphQL mutation.\n     *\n     * @param {object} query\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    query(query: object): Promise<{}> {\n        if (typeof query === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"query\"');\n        }\n        const apiPath = '/graphql';\n        const payload: Payload = {};\n        if (typeof query !== 'undefined') {\n            payload['query'] = query;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'x-sdk-graphql': 'true',\n            'content-type': 'application/json',\n        }\n\n        return this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n\n    /**\n     * Execute a GraphQL mutation.\n     *\n     * @param {object} query\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    mutation(query: object): Promise<{}> {\n        if (typeof query === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"query\"');\n        }\n        const apiPath = '/graphql/mutation';\n        const payload: Payload = {};\n        if (typeof query !== 'undefined') {\n            payload['query'] = query;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'x-sdk-graphql': 'true',\n            'content-type': 'application/json',\n        }\n\n        return this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n}\n", "import { Service } from '../service';\nimport { AppwriteException, Client, type Payload, UploadProgress } from '../client';\nimport type { Models } from '../models';\n\nexport class Locale {\n    client: Client;\n\n    constructor(client: Client) {\n        this.client = client;\n    }\n\n    /**\n     * Get the current user location based on IP. Returns an object with user country code, country name, continent name, continent code, ip address and suggested currency. You can use the locale header to get the data in a supported language.\n     * \n     * ([IP Geolocation by DB-IP](https://db-ip.com))\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Locale>}\n     */\n    get(): Promise<Models.Locale> {\n        const apiPath = '/locale';\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n        }\n\n        return this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n\n    /**\n     * List of all locale codes in [ISO 639-1](https://en.wikipedia.org/wiki/List_of_ISO_639-1_codes).\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.LocaleCodeList>}\n     */\n    listCodes(): Promise<Models.LocaleCodeList> {\n        const apiPath = '/locale/codes';\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n        }\n\n        return this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n\n    /**\n     * List of all continents. You can use the locale header to get the data in a supported language.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.ContinentList>}\n     */\n    listContinents(): Promise<Models.ContinentList> {\n        const apiPath = '/locale/continents';\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n        }\n\n        return this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n\n    /**\n     * List of all countries. You can use the locale header to get the data in a supported language.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.CountryList>}\n     */\n    listCountries(): Promise<Models.CountryList> {\n        const apiPath = '/locale/countries';\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n        }\n\n        return this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n\n    /**\n     * List of all countries that are currently members of the EU. You can use the locale header to get the data in a supported language.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.CountryList>}\n     */\n    listCountriesEU(): Promise<Models.CountryList> {\n        const apiPath = '/locale/countries/eu';\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n        }\n\n        return this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n\n    /**\n     * List of all countries phone codes. You can use the locale header to get the data in a supported language.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.PhoneList>}\n     */\n    listCountriesPhones(): Promise<Models.PhoneList> {\n        const apiPath = '/locale/countries/phones';\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n        }\n\n        return this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n\n    /**\n     * List of all currencies, including currency symbol, name, plural, and decimal digits for all major and minor currencies. You can use the locale header to get the data in a supported language.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.CurrencyList>}\n     */\n    listCurrencies(): Promise<Models.CurrencyList> {\n        const apiPath = '/locale/currencies';\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n        }\n\n        return this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n\n    /**\n     * List of all languages classified by ISO 639-1 including 2-letter code, name in English, and name in the respective language.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.LanguageList>}\n     */\n    listLanguages(): Promise<Models.LanguageList> {\n        const apiPath = '/locale/languages';\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n        }\n\n        return this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n}\n", "import { Service } from '../service';\nimport { AppwriteException, Client, type Payload, UploadProgress } from '../client';\nimport type { Models } from '../models';\n\nexport class Messaging {\n    client: Client;\n\n    constructor(client: Client) {\n        this.client = client;\n    }\n\n    /**\n     * Create a new subscriber.\n     *\n     * @param {string} topicId\n     * @param {string} subscriberId\n     * @param {string} targetId\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Subscriber>}\n     */\n    createSubscriber(topicId: string, subscriberId: string, targetId: string): Promise<Models.Subscriber> {\n        if (typeof topicId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"topicId\"');\n        }\n        if (typeof subscriberId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"subscriberId\"');\n        }\n        if (typeof targetId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"targetId\"');\n        }\n        const apiPath = '/messaging/topics/{topicId}/subscribers'.replace('{topicId}', topicId);\n        const payload: Payload = {};\n        if (typeof subscriberId !== 'undefined') {\n            payload['subscriberId'] = subscriberId;\n        }\n        if (typeof targetId !== 'undefined') {\n            payload['targetId'] = targetId;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n\n    /**\n     * Delete a subscriber by its unique ID.\n     *\n     * @param {string} topicId\n     * @param {string} subscriberId\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    deleteSubscriber(topicId: string, subscriberId: string): Promise<{}> {\n        if (typeof topicId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"topicId\"');\n        }\n        if (typeof subscriberId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"subscriberId\"');\n        }\n        const apiPath = '/messaging/topics/{topicId}/subscribers/{subscriberId}'.replace('{topicId}', topicId).replace('{subscriberId}', subscriberId);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return this.client.call(\n            'delete',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n}\n", "import { Service } from '../service';\nimport { AppwriteException, Client, type Payload, UploadProgress } from '../client';\nimport type { Models } from '../models';\nimport { ImageGravity } from '../enums/image-gravity';\nimport { ImageFormat } from '../enums/image-format';\n\nexport class Storage {\n    client: Client;\n\n    constructor(client: Client) {\n        this.client = client;\n    }\n\n    /**\n     * Get a list of all the user files. You can use the query params to filter your results.\n     *\n     * @param {string} bucketId\n     * @param {string[]} queries\n     * @param {string} search\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.FileList>}\n     */\n    listFiles(bucketId: string, queries?: string[], search?: string): Promise<Models.FileList> {\n        if (typeof bucketId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"bucketId\"');\n        }\n        const apiPath = '/storage/buckets/{bucketId}/files'.replace('{bucketId}', bucketId);\n        const payload: Payload = {};\n        if (typeof queries !== 'undefined') {\n            payload['queries'] = queries;\n        }\n        if (typeof search !== 'undefined') {\n            payload['search'] = search;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n        }\n\n        return this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n\n    /**\n     * Create a new file. Before using this route, you should create a new bucket resource using either a [server integration](https://appwrite.io/docs/server/storage#storageCreateBucket) API or directly from your Appwrite console.\n     * \n     * Larger files should be uploaded using multiple requests with the [content-range](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Content-Range) header to send a partial request with a maximum supported chunk of `5MB`. The `content-range` header values should always be in bytes.\n     * \n     * When the first request is sent, the server will return the **File** object, and the subsequent part request must include the file&#039;s **id** in `x-appwrite-id` header to allow the server to know that the partial upload is for the existing file and not for a new one.\n     * \n     * If you&#039;re creating a new file using one of the Appwrite SDKs, all the chunking logic will be managed by the SDK internally.\n     * \n     *\n     * @param {string} bucketId\n     * @param {string} fileId\n     * @param {File} file\n     * @param {string[]} permissions\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.File>}\n     */\n    createFile(bucketId: string, fileId: string, file: File, permissions?: string[], onProgress = (progress: UploadProgress) => {}): Promise<Models.File> {\n        if (typeof bucketId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"bucketId\"');\n        }\n        if (typeof fileId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"fileId\"');\n        }\n        if (typeof file === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"file\"');\n        }\n        const apiPath = '/storage/buckets/{bucketId}/files'.replace('{bucketId}', bucketId);\n        const payload: Payload = {};\n        if (typeof fileId !== 'undefined') {\n            payload['fileId'] = fileId;\n        }\n        if (typeof file !== 'undefined') {\n            payload['file'] = file;\n        }\n        if (typeof permissions !== 'undefined') {\n            payload['permissions'] = permissions;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'multipart/form-data',\n        }\n\n        return this.client.chunkedUpload(\n            'post',\n            uri,\n            apiHeaders,\n            payload,\n            onProgress\n        );\n    }\n\n    /**\n     * Get a file by its unique ID. This endpoint response returns a JSON object with the file metadata.\n     *\n     * @param {string} bucketId\n     * @param {string} fileId\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.File>}\n     */\n    getFile(bucketId: string, fileId: string): Promise<Models.File> {\n        if (typeof bucketId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"bucketId\"');\n        }\n        if (typeof fileId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"fileId\"');\n        }\n        const apiPath = '/storage/buckets/{bucketId}/files/{fileId}'.replace('{bucketId}', bucketId).replace('{fileId}', fileId);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n        }\n\n        return this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n\n    /**\n     * Update a file by its unique ID. Only users with write permissions have access to update this resource.\n     *\n     * @param {string} bucketId\n     * @param {string} fileId\n     * @param {string} name\n     * @param {string[]} permissions\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.File>}\n     */\n    updateFile(bucketId: string, fileId: string, name?: string, permissions?: string[]): Promise<Models.File> {\n        if (typeof bucketId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"bucketId\"');\n        }\n        if (typeof fileId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"fileId\"');\n        }\n        const apiPath = '/storage/buckets/{bucketId}/files/{fileId}'.replace('{bucketId}', bucketId).replace('{fileId}', fileId);\n        const payload: Payload = {};\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n        if (typeof permissions !== 'undefined') {\n            payload['permissions'] = permissions;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return this.client.call(\n            'put',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n\n    /**\n     * Delete a file by its unique ID. Only users with write permissions have access to delete this resource.\n     *\n     * @param {string} bucketId\n     * @param {string} fileId\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    deleteFile(bucketId: string, fileId: string): Promise<{}> {\n        if (typeof bucketId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"bucketId\"');\n        }\n        if (typeof fileId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"fileId\"');\n        }\n        const apiPath = '/storage/buckets/{bucketId}/files/{fileId}'.replace('{bucketId}', bucketId).replace('{fileId}', fileId);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return this.client.call(\n            'delete',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n\n    /**\n     * Get a file content by its unique ID. The endpoint response return with a &#039;Content-Disposition: attachment&#039; header that tells the browser to start downloading the file to user downloads directory.\n     *\n     * @param {string} bucketId\n     * @param {string} fileId\n     * @param {string} token\n     * @throws {AppwriteException}\n     * @returns {string}\n     */\n    getFileDownload(bucketId: string, fileId: string, token?: string): string {\n        if (typeof bucketId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"bucketId\"');\n        }\n        if (typeof fileId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"fileId\"');\n        }\n        const apiPath = '/storage/buckets/{bucketId}/files/{fileId}/download'.replace('{bucketId}', bucketId).replace('{fileId}', fileId);\n        const payload: Payload = {};\n        if (typeof token !== 'undefined') {\n            payload['token'] = token;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n        }\n\n        payload['project'] = this.client.config.project;\n\n        for (const [key, value] of Object.entries(Service.flatten(payload))) {\n            uri.searchParams.append(key, value);\n        }\n        \n        return uri.toString();\n    }\n\n    /**\n     * Get a file preview image. Currently, this method supports preview for image files (jpg, png, and gif), other supported formats, like pdf, docs, slides, and spreadsheets, will return the file icon image. You can also pass query string arguments for cutting and resizing your preview image. Preview is supported only for image files smaller than 10MB.\n     *\n     * @param {string} bucketId\n     * @param {string} fileId\n     * @param {number} width\n     * @param {number} height\n     * @param {ImageGravity} gravity\n     * @param {number} quality\n     * @param {number} borderWidth\n     * @param {string} borderColor\n     * @param {number} borderRadius\n     * @param {number} opacity\n     * @param {number} rotation\n     * @param {string} background\n     * @param {ImageFormat} output\n     * @param {string} token\n     * @throws {AppwriteException}\n     * @returns {string}\n     */\n    getFilePreview(bucketId: string, fileId: string, width?: number, height?: number, gravity?: ImageGravity, quality?: number, borderWidth?: number, borderColor?: string, borderRadius?: number, opacity?: number, rotation?: number, background?: string, output?: ImageFormat, token?: string): string {\n        if (typeof bucketId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"bucketId\"');\n        }\n        if (typeof fileId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"fileId\"');\n        }\n        const apiPath = '/storage/buckets/{bucketId}/files/{fileId}/preview'.replace('{bucketId}', bucketId).replace('{fileId}', fileId);\n        const payload: Payload = {};\n        if (typeof width !== 'undefined') {\n            payload['width'] = width;\n        }\n        if (typeof height !== 'undefined') {\n            payload['height'] = height;\n        }\n        if (typeof gravity !== 'undefined') {\n            payload['gravity'] = gravity;\n        }\n        if (typeof quality !== 'undefined') {\n            payload['quality'] = quality;\n        }\n        if (typeof borderWidth !== 'undefined') {\n            payload['borderWidth'] = borderWidth;\n        }\n        if (typeof borderColor !== 'undefined') {\n            payload['borderColor'] = borderColor;\n        }\n        if (typeof borderRadius !== 'undefined') {\n            payload['borderRadius'] = borderRadius;\n        }\n        if (typeof opacity !== 'undefined') {\n            payload['opacity'] = opacity;\n        }\n        if (typeof rotation !== 'undefined') {\n            payload['rotation'] = rotation;\n        }\n        if (typeof background !== 'undefined') {\n            payload['background'] = background;\n        }\n        if (typeof output !== 'undefined') {\n            payload['output'] = output;\n        }\n        if (typeof token !== 'undefined') {\n            payload['token'] = token;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n        }\n\n        payload['project'] = this.client.config.project;\n\n        for (const [key, value] of Object.entries(Service.flatten(payload))) {\n            uri.searchParams.append(key, value);\n        }\n        \n        return uri.toString();\n    }\n\n    /**\n     * Get a file content by its unique ID. This endpoint is similar to the download method but returns with no  &#039;Content-Disposition: attachment&#039; header.\n     *\n     * @param {string} bucketId\n     * @param {string} fileId\n     * @param {string} token\n     * @throws {AppwriteException}\n     * @returns {string}\n     */\n    getFileView(bucketId: string, fileId: string, token?: string): string {\n        if (typeof bucketId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"bucketId\"');\n        }\n        if (typeof fileId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"fileId\"');\n        }\n        const apiPath = '/storage/buckets/{bucketId}/files/{fileId}/view'.replace('{bucketId}', bucketId).replace('{fileId}', fileId);\n        const payload: Payload = {};\n        if (typeof token !== 'undefined') {\n            payload['token'] = token;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n        }\n\n        payload['project'] = this.client.config.project;\n\n        for (const [key, value] of Object.entries(Service.flatten(payload))) {\n            uri.searchParams.append(key, value);\n        }\n        \n        return uri.toString();\n    }\n}\n", "import { Service } from '../service';\nimport { AppwriteException, Client, type Payload, UploadProgress } from '../client';\nimport type { Models } from '../models';\n\nexport class Teams {\n    client: Client;\n\n    constructor(client: Client) {\n        this.client = client;\n    }\n\n    /**\n     * Get a list of all the teams in which the current user is a member. You can use the parameters to filter your results.\n     *\n     * @param {string[]} queries\n     * @param {string} search\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.TeamList<Preferences>>}\n     */\n    list<Preferences extends Models.Preferences = Models.DefaultPreferences>(queries?: string[], search?: string): Promise<Models.TeamList<Preferences>> {\n        const apiPath = '/teams';\n        const payload: Payload = {};\n        if (typeof queries !== 'undefined') {\n            payload['queries'] = queries;\n        }\n        if (typeof search !== 'undefined') {\n            payload['search'] = search;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n        }\n\n        return this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n\n    /**\n     * Create a new team. The user who creates the team will automatically be assigned as the owner of the team. Only the users with the owner role can invite new members, add new owners and delete or update the team.\n     *\n     * @param {string} teamId\n     * @param {string} name\n     * @param {string[]} roles\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Team<Preferences>>}\n     */\n    create<Preferences extends Models.Preferences = Models.DefaultPreferences>(teamId: string, name: string, roles?: string[]): Promise<Models.Team<Preferences>> {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n        if (typeof name === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"name\"');\n        }\n        const apiPath = '/teams';\n        const payload: Payload = {};\n        if (typeof teamId !== 'undefined') {\n            payload['teamId'] = teamId;\n        }\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n        if (typeof roles !== 'undefined') {\n            payload['roles'] = roles;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n\n    /**\n     * Get a team by its ID. All team members have read access for this resource.\n     *\n     * @param {string} teamId\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Team<Preferences>>}\n     */\n    get<Preferences extends Models.Preferences = Models.DefaultPreferences>(teamId: string): Promise<Models.Team<Preferences>> {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n        const apiPath = '/teams/{teamId}'.replace('{teamId}', teamId);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n        }\n\n        return this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n\n    /**\n     * Update the team&#039;s name by its unique ID.\n     *\n     * @param {string} teamId\n     * @param {string} name\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Team<Preferences>>}\n     */\n    updateName<Preferences extends Models.Preferences = Models.DefaultPreferences>(teamId: string, name: string): Promise<Models.Team<Preferences>> {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n        if (typeof name === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"name\"');\n        }\n        const apiPath = '/teams/{teamId}'.replace('{teamId}', teamId);\n        const payload: Payload = {};\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return this.client.call(\n            'put',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n\n    /**\n     * Delete a team using its ID. Only team members with the owner role can delete the team.\n     *\n     * @param {string} teamId\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    delete(teamId: string): Promise<{}> {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n        const apiPath = '/teams/{teamId}'.replace('{teamId}', teamId);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return this.client.call(\n            'delete',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n\n    /**\n     * Use this endpoint to list a team&#039;s members using the team&#039;s ID. All team members have read access to this endpoint. Hide sensitive attributes from the response by toggling membership privacy in the Console.\n     *\n     * @param {string} teamId\n     * @param {string[]} queries\n     * @param {string} search\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.MembershipList>}\n     */\n    listMemberships(teamId: string, queries?: string[], search?: string): Promise<Models.MembershipList> {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n        const apiPath = '/teams/{teamId}/memberships'.replace('{teamId}', teamId);\n        const payload: Payload = {};\n        if (typeof queries !== 'undefined') {\n            payload['queries'] = queries;\n        }\n        if (typeof search !== 'undefined') {\n            payload['search'] = search;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n        }\n\n        return this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n\n    /**\n     * Invite a new member to join your team. Provide an ID for existing users, or invite unregistered users using an email or phone number. If initiated from a Client SDK, Appwrite will send an email or sms with a link to join the team to the invited user, and an account will be created for them if one doesn&#039;t exist. If initiated from a Server SDK, the new member will be added automatically to the team.\n     * \n     * You only need to provide one of a user ID, email, or phone number. Appwrite will prioritize accepting the user ID &gt; email &gt; phone number if you provide more than one of these parameters.\n     * \n     * Use the `url` parameter to redirect the user from the invitation email to your app. After the user is redirected, use the [Update Team Membership Status](https://appwrite.io/docs/references/cloud/client-web/teams#updateMembershipStatus) endpoint to allow the user to accept the invitation to the team. \n     * \n     * Please note that to avoid a [Redirect Attack](https://github.com/OWASP/CheatSheetSeries/blob/master/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.md) Appwrite will accept the only redirect URLs under the domains you have added as a platform on the Appwrite Console.\n     * \n     *\n     * @param {string} teamId\n     * @param {string[]} roles\n     * @param {string} email\n     * @param {string} userId\n     * @param {string} phone\n     * @param {string} url\n     * @param {string} name\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Membership>}\n     */\n    createMembership(teamId: string, roles: string[], email?: string, userId?: string, phone?: string, url?: string, name?: string): Promise<Models.Membership> {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n        if (typeof roles === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"roles\"');\n        }\n        const apiPath = '/teams/{teamId}/memberships'.replace('{teamId}', teamId);\n        const payload: Payload = {};\n        if (typeof email !== 'undefined') {\n            payload['email'] = email;\n        }\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n        if (typeof phone !== 'undefined') {\n            payload['phone'] = phone;\n        }\n        if (typeof roles !== 'undefined') {\n            payload['roles'] = roles;\n        }\n        if (typeof url !== 'undefined') {\n            payload['url'] = url;\n        }\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n\n    /**\n     * Get a team member by the membership unique id. All team members have read access for this resource. Hide sensitive attributes from the response by toggling membership privacy in the Console.\n     *\n     * @param {string} teamId\n     * @param {string} membershipId\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Membership>}\n     */\n    getMembership(teamId: string, membershipId: string): Promise<Models.Membership> {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n        if (typeof membershipId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"membershipId\"');\n        }\n        const apiPath = '/teams/{teamId}/memberships/{membershipId}'.replace('{teamId}', teamId).replace('{membershipId}', membershipId);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n        }\n\n        return this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n\n    /**\n     * Modify the roles of a team member. Only team members with the owner role have access to this endpoint. Learn more about [roles and permissions](https://appwrite.io/docs/permissions).\n     * \n     *\n     * @param {string} teamId\n     * @param {string} membershipId\n     * @param {string[]} roles\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Membership>}\n     */\n    updateMembership(teamId: string, membershipId: string, roles: string[]): Promise<Models.Membership> {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n        if (typeof membershipId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"membershipId\"');\n        }\n        if (typeof roles === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"roles\"');\n        }\n        const apiPath = '/teams/{teamId}/memberships/{membershipId}'.replace('{teamId}', teamId).replace('{membershipId}', membershipId);\n        const payload: Payload = {};\n        if (typeof roles !== 'undefined') {\n            payload['roles'] = roles;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return this.client.call(\n            'patch',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n\n    /**\n     * This endpoint allows a user to leave a team or for a team owner to delete the membership of any other team member. You can also use this endpoint to delete a user membership even if it is not accepted.\n     *\n     * @param {string} teamId\n     * @param {string} membershipId\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    deleteMembership(teamId: string, membershipId: string): Promise<{}> {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n        if (typeof membershipId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"membershipId\"');\n        }\n        const apiPath = '/teams/{teamId}/memberships/{membershipId}'.replace('{teamId}', teamId).replace('{membershipId}', membershipId);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return this.client.call(\n            'delete',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n\n    /**\n     * Use this endpoint to allow a user to accept an invitation to join a team after being redirected back to your app from the invitation email received by the user.\n     * \n     * If the request is successful, a session for the user is automatically created.\n     * \n     *\n     * @param {string} teamId\n     * @param {string} membershipId\n     * @param {string} userId\n     * @param {string} secret\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Membership>}\n     */\n    updateMembershipStatus(teamId: string, membershipId: string, userId: string, secret: string): Promise<Models.Membership> {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n        if (typeof membershipId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"membershipId\"');\n        }\n        if (typeof userId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"userId\"');\n        }\n        if (typeof secret === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"secret\"');\n        }\n        const apiPath = '/teams/{teamId}/memberships/{membershipId}/status'.replace('{teamId}', teamId).replace('{membershipId}', membershipId);\n        const payload: Payload = {};\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n        if (typeof secret !== 'undefined') {\n            payload['secret'] = secret;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return this.client.call(\n            'patch',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n\n    /**\n     * Get the team&#039;s shared preferences by its unique ID. If a preference doesn&#039;t need to be shared by all team members, prefer storing them in [user preferences](https://appwrite.io/docs/references/cloud/client-web/account#getPrefs).\n     *\n     * @param {string} teamId\n     * @throws {AppwriteException}\n     * @returns {Promise<Preferences>}\n     */\n    getPrefs<Preferences extends Models.Preferences = Models.DefaultPreferences>(teamId: string): Promise<Preferences> {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n        const apiPath = '/teams/{teamId}/prefs'.replace('{teamId}', teamId);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n        }\n\n        return this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n\n    /**\n     * Update the team&#039;s preferences by its unique ID. The object you pass is stored as is and replaces any previous value. The maximum allowed prefs size is 64kB and throws an error if exceeded.\n     *\n     * @param {string} teamId\n     * @param {object} prefs\n     * @throws {AppwriteException}\n     * @returns {Promise<Preferences>}\n     */\n    updatePrefs<Preferences extends Models.Preferences = Models.DefaultPreferences>(teamId: string, prefs: object): Promise<Preferences> {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n        if (typeof prefs === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"prefs\"');\n        }\n        const apiPath = '/teams/{teamId}/prefs'.replace('{teamId}', teamId);\n        const payload: Payload = {};\n        if (typeof prefs !== 'undefined') {\n            payload['prefs'] = prefs;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return this.client.call(\n            'put',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n}\n", "/**\n * Helper class to generate permission strings for resources.\n */\nexport class Permission {\n    /**\n     * Generate read permission string for the provided role.\n     *\n     * @param {string} role\n     * @returns {string}\n     */\n    static read = (role: string): string => {\n        return `read(\"${role}\")`;\n    }\n\n    /**\n     * Generate write permission string for the provided role.\n     *\n     * This is an alias of update, delete, and possibly create.\n     * Don't use write in combination with update, delete, or create.\n     *\n     * @param {string} role\n     * @returns {string}\n     */\n    static write = (role: string): string => {\n        return `write(\"${role}\")`;\n    }\n\n    /**\n     * Generate create permission string for the provided role.\n     *\n     * @param {string} role\n     * @returns {string}\n     */\n    static create = (role: string): string => {\n        return `create(\"${role}\")`;\n    }\n\n    /**\n     * Generate update permission string for the provided role.\n     *\n     * @param {string} role\n     * @returns {string}\n     */\n    static update = (role: string): string => {\n        return `update(\"${role}\")`;\n    }\n\n    /**\n     * Generate delete permission string for the provided role.\n     *\n     * @param {string} role\n     * @returns {string}\n     */\n    static delete = (role: string): string => {\n        return `delete(\"${role}\")`;\n    }\n}\n", "/**\n * Helper class to generate role strings for `Permission`.\n */\nexport class Role {\n\n    /**\n     * Grants access to anyone.\n     * \n     * This includes authenticated and unauthenticated users.\n     * \n     * @returns {string}\n     */\n    public static any(): string {\n        return 'any'\n    }\n\n    /**\n     * Grants access to a specific user by user ID.\n     * \n     * You can optionally pass verified or unverified for\n     * `status` to target specific types of users.\n     *\n     * @param {string} id \n     * @param {string} status \n     * @returns {string}\n     */\n    public static user(id: string, status: string = ''): string {\n        if (status === '') {\n            return `user:${id}`\n        }\n        return `user:${id}/${status}`\n    }\n\n    /**\n     * Grants access to any authenticated or anonymous user.\n     * \n     * You can optionally pass verified or unverified for\n     * `status` to target specific types of users.\n     * \n     * @param {string} status \n     * @returns {string}\n     */\n    public static users(status: string = ''): string {\n        if (status === '') {\n            return 'users'\n        }\n        return `users/${status}`\n    }\n\n    /**\n     * Grants access to any guest user without a session.\n     * \n     * Authenticated users don't have access to this role.\n     * \n     * @returns {string}\n     */\n    public static guests(): string {\n        return 'guests'\n    }\n\n    /**\n     * Grants access to a team by team ID.\n     * \n     * You can optionally pass a role for `role` to target\n     * team members with the specified role.\n     * \n     * @param {string} id \n     * @param {string} role \n     * @returns {string}\n     */\n    public static team(id: string, role: string = ''): string {\n        if (role === '') {\n            return `team:${id}`\n        }\n        return `team:${id}/${role}`\n    }\n\n    /**\n     * Grants access to a specific member of a team.\n     * \n     * When the member is removed from the team, they will\n     * no longer have access.\n     * \n     * @param {string} id \n     * @returns {string}\n     */\n    public static member(id: string): string {\n        return `member:${id}`\n    }\n\n    /**\n     * Grants access to a user with the specified label.\n     * \n     * @param {string} name \n     * @returns  {string}\n     */\n    public static label(name: string): string {\n        return `label:${name}`\n    }\n}", "/**\n * Helper class to generate ID strings for resources.\n */\nexport class ID {\n    /**\n     * Generate an hex ID based on timestamp.\n     * Recreated from https://www.php.net/manual/en/function.uniqid.php\n     *\n     * @returns {string}\n     */\n    static #hexTimestamp(): string {\n        const now = new Date();\n        const sec = Math.floor(now.getTime() / 1000);\n        const msec = now.getMilliseconds();\n\n        // Convert to hexadecimal\n        const hexTimestamp = sec.toString(16) + msec.toString(16).padStart(5, '0');\n        return hexTimestamp;\n    }\n\n    /**\n     * Uses the provided ID as the ID for the resource.\n     *\n     * @param {string} id\n     * @returns {string}\n     */\n    public static custom(id: string): string {\n        return id\n    }\n\n    /**\n     * Have Appwrite generate a unique ID for you.\n     * \n     * @param {number} padding. Default is 7.\n     * @returns {string}\n     */\n    public static unique(padding: number = 7): string {\n        // Generate a unique ID with padding to have a longer ID\n        const baseId = ID.#hexTimestamp();\n        let randomPadding = '';\n        for (let i = 0; i < padding; i++) {\n            const randomHexDigit = Math.floor(Math.random() * 16).toString(16);\n            randomPadding += randomHexDigit;\n        }\n        return baseId + randomPadding;\n    }\n}\n", "export enum AuthenticatorType {\n    Totp = 'totp',\n}", "export enum AuthenticationFactor {\n    Email = 'email',\n    Phone = 'phone',\n    Totp = 'totp',\n    Recoverycode = 'recoverycode',\n}", "export enum OAuthProvider {\n    Amazon = 'amazon',\n    Apple = 'apple',\n    Auth0 = 'auth0',\n    Authentik = 'authentik',\n    Autodesk = 'autodesk',\n    Bitbucket = 'bitbucket',\n    Bitly = 'bitly',\n    Box = 'box',\n    Dailymotion = 'dailymotion',\n    Discord = 'discord',\n    Disqus = 'disqus',\n    Dropbox = 'dropbox',\n    Etsy = 'etsy',\n    Facebook = 'facebook',\n    Figma = 'figma',\n    Github = 'github',\n    Gitlab = 'gitlab',\n    Google = 'google',\n    Linkedin = 'linkedin',\n    Microsoft = 'microsoft',\n    Notion = 'notion',\n    Oidc = 'oidc',\n    Okta = 'okta',\n    Paypal = 'paypal',\n    PaypalSandbox = 'paypalSandbox',\n    Podio = 'podio',\n    Salesforce = 'salesforce',\n    Slack = 'slack',\n    Spotify = 'spotify',\n    Stripe = 'stripe',\n    Tradeshift = 'tradeshift',\n    TradeshiftBox = 'tradeshiftBox',\n    Twitch = 'twitch',\n    Wordpress = 'wordpress',\n    Yahoo = 'yahoo',\n    Yammer = 'yammer',\n    Yandex = 'yandex',\n    Zoho = 'zoho',\n    Zoom = 'zoom',\n    Mock = 'mock',\n}", "export enum Browser {\n    AvantBrowser = 'aa',\n    AndroidWebViewBeta = 'an',\n    GoogleChrome = 'ch',\n    GoogleChromeIOS = 'ci',\n    GoogleChromeMobile = 'cm',\n    Chromium = 'cr',\n    MozillaFirefox = 'ff',\n    Safari = 'sf',\n    MobileSafari = 'mf',\n    MicrosoftEdge = 'ps',\n    MicrosoftEdgeIOS = 'oi',\n    OperaMini = 'om',\n    Opera = 'op',\n    OperaNext = 'on',\n}", "export enum CreditCard {\n    AmericanExpress = 'amex',\n    Argencard = 'argencard',\n    Cabal = 'cabal',\n    Cencosud = 'cencosud',\n    DinersClub = 'diners',\n    Discover = 'discover',\n    Elo = 'elo',\n    Hipercard = 'hipercard',\n    JCB = 'jcb',\n    Mastercard = 'mastercard',\n    Naranja = 'naranja',\n    TarjetaShopping = 'targeta-shopping',\n    UnionChinaPay = 'union-china-pay',\n    Visa = 'visa',\n    MIR = 'mir',\n    Mae<PERSON> = 'maestro',\n    Rupay = 'rupay',\n}", "export enum Flag {\n    Afghanistan = 'af',\n    Angola = 'ao',\n    Albania = 'al',\n    Andorra = 'ad',\n    UnitedArabEmirates = 'ae',\n    Argentina = 'ar',\n    Armenia = 'am',\n    AntiguaAndBarbuda = 'ag',\n    Australia = 'au',\n    Austria = 'at',\n    Azerbaijan = 'az',\n    Burundi = 'bi',\n    Belgium = 'be',\n    Benin = 'bj',\n    BurkinaFaso = 'bf',\n    Bangladesh = 'bd',\n    Bulgaria = 'bg',\n    Bahrain = 'bh',\n    Bahamas = 'bs',\n    BosniaAndHerzegovina = 'ba',\n    Belarus = 'by',\n    Belize = 'bz',\n    Bolivia = 'bo',\n    Brazil = 'br',\n    Barbados = 'bb',\n    BruneiDarussalam = 'bn',\n    Bhutan = 'bt',\n    Botswana = 'bw',\n    CentralAfricanRepublic = 'cf',\n    Canada = 'ca',\n    Switzerland = 'ch',\n    Chile = 'cl',\n    China = 'cn',\n    CoteDIvoire = 'ci',\n    Cameroon = 'cm',\n    DemocraticRepublicOfTheCongo = 'cd',\n    RepublicOfTheCongo = 'cg',\n    Colombia = 'co',\n    Comoros = 'km',\n    CapeVerde = 'cv',\n    CostaRica = 'cr',\n    Cuba = 'cu',\n    Cyprus = 'cy',\n    CzechRepublic = 'cz',\n    Germany = 'de',\n    Djibouti = 'dj',\n    Dominica = 'dm',\n    Denmark = 'dk',\n    DominicanRepublic = 'do',\n    Algeria = 'dz',\n    Ecuador = 'ec',\n    Egypt = 'eg',\n    Eritrea = 'er',\n    Spain = 'es',\n    Estonia = 'ee',\n    Ethiopia = 'et',\n    Finland = 'fi',\n    Fiji = 'fj',\n    France = 'fr',\n    MicronesiaFederatedStatesOf = 'fm',\n    Gabon = 'ga',\n    UnitedKingdom = 'gb',\n    Georgia = 'ge',\n    Ghana = 'gh',\n    Guinea = 'gn',\n    Gambia = 'gm',\n    GuineaBissau = 'gw',\n    EquatorialGuinea = 'gq',\n    Greece = 'gr',\n    Grenada = 'gd',\n    Guatemala = 'gt',\n    Guyana = 'gy',\n    Honduras = 'hn',\n    Croatia = 'hr',\n    Haiti = 'ht',\n    Hungary = 'hu',\n    Indonesia = 'id',\n    India = 'in',\n    Ireland = 'ie',\n    IranIslamicRepublicOf = 'ir',\n    Iraq = 'iq',\n    Iceland = 'is',\n    Israel = 'il',\n    Italy = 'it',\n    Jamaica = 'jm',\n    Jordan = 'jo',\n    Japan = 'jp',\n    Kazakhstan = 'kz',\n    Kenya = 'ke',\n    Kyrgyzstan = 'kg',\n    Cambodia = 'kh',\n    Kiribati = 'ki',\n    SaintKittsAndNevis = 'kn',\n    SouthKorea = 'kr',\n    Kuwait = 'kw',\n    LaoPeopleSDemocraticRepublic = 'la',\n    Lebanon = 'lb',\n    Liberia = 'lr',\n    Libya = 'ly',\n    SaintLucia = 'lc',\n    Liechtenstein = 'li',\n    SriLanka = 'lk',\n    Lesotho = 'ls',\n    Lithuania = 'lt',\n    Luxembourg = 'lu',\n    Latvia = 'lv',\n    Morocco = 'ma',\n    Monaco = 'mc',\n    Moldova = 'md',\n    Madagascar = 'mg',\n    Maldives = 'mv',\n    Mexico = 'mx',\n    MarshallIslands = 'mh',\n    NorthMacedonia = 'mk',\n    Mali = 'ml',\n    Malta = 'mt',\n    Myanmar = 'mm',\n    Montenegro = 'me',\n    Mongolia = 'mn',\n    Mozambique = 'mz',\n    Mauritania = 'mr',\n    Mauritius = 'mu',\n    Malawi = 'mw',\n    Malaysia = 'my',\n    Namibia = 'na',\n    Niger = 'ne',\n    Nigeria = 'ng',\n    Nicaragua = 'ni',\n    Netherlands = 'nl',\n    Norway = 'no',\n    Nepal = 'np',\n    Nauru = 'nr',\n    NewZealand = 'nz',\n    Oman = 'om',\n    Pakistan = 'pk',\n    Panama = 'pa',\n    Peru = 'pe',\n    Philippines = 'ph',\n    Palau = 'pw',\n    PapuaNewGuinea = 'pg',\n    Poland = 'pl',\n    FrenchPolynesia = 'pf',\n    NorthKorea = 'kp',\n    Portugal = 'pt',\n    Paraguay = 'py',\n    Qatar = 'qa',\n    Romania = 'ro',\n    Russia = 'ru',\n    Rwanda = 'rw',\n    SaudiArabia = 'sa',\n    Sudan = 'sd',\n    Senegal = 'sn',\n    Singapore = 'sg',\n    SolomonIslands = 'sb',\n    SierraLeone = 'sl',\n    ElSalvador = 'sv',\n    SanMarino = 'sm',\n    Somalia = 'so',\n    Serbia = 'rs',\n    SouthSudan = 'ss',\n    SaoTomeAndPrincipe = 'st',\n    Suriname = 'sr',\n    Slovakia = 'sk',\n    Slovenia = 'si',\n    Sweden = 'se',\n    Eswatini = 'sz',\n    Seychelles = 'sc',\n    Syria = 'sy',\n    Chad = 'td',\n    Togo = 'tg',\n    Thailand = 'th',\n    Tajikistan = 'tj',\n    Turkmenistan = 'tm',\n    TimorLeste = 'tl',\n    Tonga = 'to',\n    TrinidadAndTobago = 'tt',\n    Tunisia = 'tn',\n    Turkey = 'tr',\n    Tuvalu = 'tv',\n    Tanzania = 'tz',\n    Uganda = 'ug',\n    Ukraine = 'ua',\n    Uruguay = 'uy',\n    UnitedStates = 'us',\n    Uzbekistan = 'uz',\n    VaticanCity = 'va',\n    SaintVincentAndTheGrenadines = 'vc',\n    Venezuela = 've',\n    Vietnam = 'vn',\n    Vanuatu = 'vu',\n    Samoa = 'ws',\n    Yemen = 'ye',\n    SouthAfrica = 'za',\n    Zambia = 'zm',\n    Zimbabwe = 'zw',\n}", "export enum ExecutionMethod {\n    GET = 'GET',\n    POST = 'POST',\n    PUT = 'PUT',\n    PATCH = 'PATCH',\n    DELETE = 'DELETE',\n    OPTIONS = 'OPTIONS',\n}", "export enum ImageGravity {\n    Center = 'center',\n    Topleft = 'top-left',\n    Top = 'top',\n    Topright = 'top-right',\n    Left = 'left',\n    Right = 'right',\n    Bottomleft = 'bottom-left',\n    Bottom = 'bottom',\n    Bottomright = 'bottom-right',\n}", "export enum ImageFormat {\n    Jpg = 'jpg',\n    Jpeg = 'jpeg',\n    Png = 'png',\n    Webp = 'webp',\n    Heic = 'heic',\n    Avif = 'avif',\n    Gif = 'gif',\n}"], "names": ["AuthenticatorType", "AuthenticationFactor", "OAuth<PERSON><PERSON><PERSON>", "Browser", "CreditCard", "Flag", "ExecutionMethod", "ImageGravity", "ImageFormat"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAuDA;AACO,SAAS,SAAS,CAAC,OAAO,EAAE,UAAU,EAAE,CAAC,EAAE,SAAS,EAAE;AAC7D,IAAI,SAAS,KAAK,CAAC,KAAK,EAAE,EAAE,OAAO,KAAK,YAAY,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,CAAC,UAAU,OAAO,EAAE,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;AAChH,IAAI,OAAO,KAAK,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC,EAAE,UAAU,OAAO,EAAE,MAAM,EAAE;AAC/D,QAAQ,SAAS,SAAS,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;AACnG,QAAQ,SAAS,QAAQ,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;AACtG,QAAQ,SAAS,IAAI,CAAC,MAAM,EAAE,EAAE,MAAM,CAAC,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC,EAAE;AACtH,QAAQ,IAAI,CAAC,CAAC,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC,OAAO,EAAE,UAAU,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;AAC9E,KAAK,CAAC,CAAC;AACP,CAAC;AAyJD;AACO,SAAS,sBAAsB,CAAC,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,EAAE;AACjE,IAAI,IAAI,IAAI,KAAK,GAAG,IAAI,CAAC,CAAC,EAAE,MAAM,IAAI,SAAS,CAAC,+CAA+C,CAAC,CAAC;AACjG,IAAI,IAAI,OAAO,KAAK,KAAK,UAAU,GAAG,QAAQ,KAAK,KAAK,IAAI,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,MAAM,IAAI,SAAS,CAAC,0EAA0E,CAAC,CAAC;AACvL,IAAI,OAAO,IAAI,KAAK,GAAG,GAAG,CAAC,GAAG,IAAI,KAAK,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;AAClG;;ACtOA;;AAEG;MACU,KAAK,CAAA;AAKhB;;;;;;AAMG;AACH,IAAA,WAAA,CACE,MAAc,EACd,SAA2B,EAC3B,MAAmB,EAAA;AAEnB,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACrB,QAAA,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAE3B,IAAI,MAAM,KAAK,SAAS,EAAE;AACxB,YAAA,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;AACzB,gBAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACtB,aAAA;AAAM,iBAAA;AACL,gBAAA,IAAI,CAAC,MAAM,GAAG,CAAC,MAAM,CAAmB,CAAC;AAC1C,aAAA;AACF,SAAA;KACF;AAED;;;;AAIG;IACH,QAAQ,GAAA;QACN,OAAO,IAAI,CAAC,SAAS,CAAC;YACpB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,MAAM,EAAE,IAAI,CAAC,MAAM;AACpB,SAAA,CAAC,CAAC;KACJ;;AAED;;;;;;AAMG;AACI,KAAK,CAAA,KAAA,GAAG,CAAC,SAAiB,EAAE,KAAiB,KAClD,IAAI,KAAK,CAAC,OAAO,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC,QAAQ,EAAE,CAAC;AAElD;;;;;;AAMG;AACI,KAAQ,CAAA,QAAA,GAAG,CAAC,SAAiB,EAAE,KAAiB,KACrD,IAAI,KAAK,CAAC,UAAU,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC,QAAQ,EAAE,CAAC;AAErD;;;;;;AAMG;AACI,KAAQ,CAAA,QAAA,GAAG,CAAC,SAAiB,EAAE,KAAiB,KACrD,IAAI,KAAK,CAAC,UAAU,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC,QAAQ,EAAE,CAAC;AAErD;;;;;;AAMG;AACI,KAAa,CAAA,aAAA,GAAG,CAAC,SAAiB,EAAE,KAAiB,KAC1D,IAAI,KAAK,CAAC,eAAe,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC,QAAQ,EAAE,CAAC;AAE1D;;;;;;AAMG;AACI,KAAW,CAAA,WAAA,GAAG,CAAC,SAAiB,EAAE,KAAiB,KACxD,IAAI,KAAK,CAAC,aAAa,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC,QAAQ,EAAE,CAAC;AAExD;;;;;;AAMG;AACI,KAAgB,CAAA,gBAAA,GAAG,CAAC,SAAiB,EAAE,KAAiB,KAC7D,IAAI,KAAK,CAAC,kBAAkB,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC,QAAQ,EAAE,CAAC;AAE7D;;;;;AAKG;AACI,KAAA,CAAA,MAAM,GAAG,CAAC,SAAiB,KAChC,IAAI,KAAK,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC,QAAQ,EAAE,CAAC;AAE5C;;;;;AAKG;AACI,KAAA,CAAA,SAAS,GAAG,CAAC,SAAiB,KACnC,IAAI,KAAK,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC,QAAQ,EAAE,CAAC;AAE/C;;;;;;;AAOG;AACI,KAAO,CAAA,OAAA,GAAG,CAAC,SAAiB,EAAE,KAAsB,EAAE,GAAoB,KAC/E,IAAI,KAAK,CAAC,SAAS,EAAE,SAAS,EAAE,CAAC,KAAK,EAAE,GAAG,CAAmB,CAAC,CAAC,QAAQ,EAAE,CAAC;AAE7E;;;;;;AAMG;AACI,KAAU,CAAA,UAAA,GAAG,CAAC,SAAiB,EAAE,KAAa,KACnD,IAAI,KAAK,CAAC,YAAY,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC,QAAQ,EAAE,CAAC;AAEvD;;;;;;AAMG;AACI,KAAQ,CAAA,QAAA,GAAG,CAAC,SAAiB,EAAE,KAAa,KACjD,IAAI,KAAK,CAAC,UAAU,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC,QAAQ,EAAE,CAAC;AAErD;;;;;AAKG;AACI,KAAA,CAAA,MAAM,GAAG,CAAC,UAAoB,KACnC,IAAI,KAAK,CAAC,QAAQ,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC,QAAQ,EAAE,CAAC;AAExD;;;;;;;AAOG;AACI,KAAM,CAAA,MAAA,GAAG,CAAC,SAAiB,EAAE,KAAa,KAC/C,IAAI,KAAK,CAAC,QAAQ,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC,QAAQ,EAAE,CAAC;AAEnD;;;;;AAKG;AACI,KAAA,CAAA,SAAS,GAAG,CAAC,SAAiB,KACnC,IAAI,KAAK,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC,QAAQ,EAAE,CAAC;AAE/C;;;;;AAKG;AACI,KAAA,CAAA,QAAQ,GAAG,CAAC,SAAiB,KAClC,IAAI,KAAK,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC,QAAQ,EAAE,CAAC;AAE9C;;;;;AAKG;AACI,KAAA,CAAA,WAAW,GAAG,CAAC,UAAkB,KACtC,IAAI,KAAK,CAAC,aAAa,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC,QAAQ,EAAE,CAAC;AAE7D;;;;;AAKG;AACI,KAAA,CAAA,YAAY,GAAG,CAAC,UAAkB,KACvC,IAAI,KAAK,CAAC,cAAc,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC,QAAQ,EAAE,CAAC;AAE9D;;;;;AAKG;AACI,KAAA,CAAA,KAAK,GAAG,CAAC,KAAa,KAC3B,IAAI,KAAK,CAAC,OAAO,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC,QAAQ,EAAE,CAAC;AAElD;;;;;AAKG;AACI,KAAA,CAAA,MAAM,GAAG,CAAC,MAAc,KAC7B,IAAI,KAAK,CAAC,QAAQ,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC,QAAQ,EAAE,CAAC;AAEpD;;;;;;AAMG;AACI,KAAQ,CAAA,QAAA,GAAG,CAAC,SAAiB,EAAE,KAAwB,KAC5D,IAAI,KAAK,CAAC,UAAU,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC,QAAQ,EAAE,CAAC;AAErD;;;;;AAKG;AACI,KAAA,CAAA,EAAE,GAAG,CAAC,OAAiB,KAC5B,IAAI,KAAK,CAAC,IAAI,EAAE,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;AAEnF;;;;;AAKG;AACI,KAAA,CAAA,GAAG,GAAG,CAAC,OAAiB,KAC7B,IAAI,KAAK,CAAC,KAAK,EAAE,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE;;ACJrF;;AAEG;AACH,MAAM,iBAAkB,SAAQ,KAAK,CAAA;AAiBjC;;;;;;;AAOG;IACH,WAAY,CAAA,OAAe,EAAE,IAAe,GAAA,CAAC,EAAE,IAAe,GAAA,EAAE,EAAE,QAAA,GAAmB,EAAE,EAAA;QACnF,KAAK,CAAC,OAAO,CAAC,CAAC;AACf,QAAA,IAAI,CAAC,IAAI,GAAG,mBAAmB,CAAC;AAChC,QAAA,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;AACvB,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACjB,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACjB,QAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;KAC5B;AACJ,CAAA;AAED;;AAEG;AACH,MAAM,MAAM,CAAA;AAAZ,IAAA,WAAA,GAAA;AAGI;;AAEG;AACH,QAAA,IAAA,CAAA,MAAM,GAAG;AACL,YAAA,QAAQ,EAAE,8BAA8B;AACxC,YAAA,gBAAgB,EAAE,EAAE;AACpB,YAAA,OAAO,EAAE,EAAE;AACX,YAAA,GAAG,EAAE,EAAE;AACP,YAAA,MAAM,EAAE,EAAE;AACV,YAAA,OAAO,EAAE,EAAE;AACX,YAAA,MAAM,EAAE,EAAE;SACb,CAAC;AACF;;AAEG;AACH,QAAA,IAAA,CAAA,OAAO,GAAY;AACf,YAAA,YAAY,EAAE,KAAK;AACnB,YAAA,gBAAgB,EAAE,QAAQ;AAC1B,YAAA,gBAAgB,EAAE,KAAK;AACvB,YAAA,eAAe,EAAE,QAAQ;AACzB,YAAA,4BAA4B,EAAE,OAAO;SACxC,CAAC;AA2GM,QAAA,IAAA,CAAA,QAAQ,GAAa;AACzB,YAAA,MAAM,EAAE,SAAS;AACjB,YAAA,OAAO,EAAE,SAAS;AAClB,YAAA,SAAS,EAAE,SAAS;AACpB,YAAA,GAAG,EAAE,EAAE;YACP,QAAQ,EAAE,IAAI,GAAG,EAAE;YACnB,aAAa,EAAE,IAAI,GAAG,EAAE;AACxB,YAAA,oBAAoB,EAAE,CAAC;AACvB,YAAA,SAAS,EAAE,IAAI;AACf,YAAA,iBAAiB,EAAE,CAAC;AACpB,YAAA,WAAW,EAAE,SAAS;YACtB,OAAO,EAAE,MAAK;AACV,gBAAA,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;AACpC,gBAAA,IAAI,CAAC,QAAQ,CAAC,OAAO,GAAG,MAAM,KAAA,IAAA,IAAN,MAAM,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAN,MAAM,CAAE,UAAU,CAAC,MAAK;AAC5C,oBAAA,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE,CAAC;iBAChC,EAAE,EAAE,CAAC,CAAC;aACV;YACD,UAAU,EAAE,MAAK;AACb,gBAAA,QAAQ,IAAI;AACR,oBAAA,KAAK,IAAI,CAAC,QAAQ,CAAC,iBAAiB,GAAG,CAAC;AACpC,wBAAA,OAAO,IAAI,CAAC;AAChB,oBAAA,KAAK,IAAI,CAAC,QAAQ,CAAC,iBAAiB,GAAG,EAAE;AACrC,wBAAA,OAAO,IAAI,CAAC;AAChB,oBAAA,KAAK,IAAI,CAAC,QAAQ,CAAC,iBAAiB,GAAG,GAAG;AACtC,wBAAA,OAAO,KAAM,CAAC;AAClB,oBAAA;AACI,wBAAA,OAAO,KAAM,CAAC;AACrB,iBAAA;aACJ;YACD,eAAe,EAAE,MAAK;AAClB,gBAAA,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE;AACzB,oBAAA,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;AACzC,iBAAA;AAED,gBAAA,IAAI,CAAC,QAAQ,CAAC,SAAS,GAAG,MAAM,KAAA,IAAA,IAAN,MAAM,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAN,MAAM,CAAE,WAAW,CAAC,MAAK;;oBAC/C,CAAA,EAAA,GAAA,IAAI,CAAC,QAAQ,CAAC,MAAM,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC;AACtC,wBAAA,IAAI,EAAE,MAAM;AACf,qBAAA,CAAC,CAAC,CAAC;iBACP,EAAE,KAAM,CAAC,CAAC;aACd;YACD,YAAY,EAAE,MAAK;;gBACf,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,GAAG,CAAC,EAAE;AACjC,oBAAA,IAAI,CAAC,QAAQ,CAAC,SAAS,GAAG,KAAK,CAAC;oBAChC,CAAA,EAAA,GAAA,IAAI,CAAC,QAAQ,CAAC,MAAM,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAK,EAAE,CAAC;oBAC9B,OAAO;AACV,iBAAA;AAED,gBAAA,MAAM,QAAQ,GAAG,IAAI,eAAe,EAAE,CAAC;gBACvC,QAAQ,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;gBAC7C,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,IAAG;AACrC,oBAAA,QAAQ,CAAC,MAAM,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;AAC3C,iBAAC,CAAC,CAAC;AAEH,gBAAA,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,GAAG,YAAY,GAAG,QAAQ,CAAC,QAAQ,EAAE,CAAC;gBAE9E,IACI,GAAG,KAAK,IAAI,CAAC,QAAQ,CAAC,GAAG;AACzB,oBAAA,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM;AACrB,oBAAA,CAAA,CAAA,EAAA,GAAA,IAAI,CAAC,QAAQ,CAAC,MAAM,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,UAAU,IAAG,SAAS,CAAC,IAAI;AACnD,kBAAA;AACE,oBAAA,IACI,IAAI,CAAC,QAAQ,CAAC,MAAM;AACpB,wBAAA,CAAA,CAAA,EAAA,GAAA,IAAI,CAAC,QAAQ,CAAC,MAAM,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,UAAU,IAAG,SAAS,CAAC,OAAO;AACtD,sBAAA;AACE,wBAAA,IAAI,CAAC,QAAQ,CAAC,SAAS,GAAG,KAAK,CAAC;AAChC,wBAAA,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;AAChC,qBAAA;AAED,oBAAA,IAAI,CAAC,QAAQ,CAAC,GAAG,GAAG,GAAG,CAAC;oBACxB,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,IAAI,SAAS,CAAC,GAAG,CAAC,CAAC;AAC1C,oBAAA,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;oBAC1E,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,gBAAgB,CAAC,MAAM,EAAE,MAAM,IAAG;AACnD,wBAAA,IAAI,CAAC,QAAQ,CAAC,iBAAiB,GAAG,CAAC,CAAC;AACpC,wBAAA,IAAI,CAAC,QAAQ,CAAC,eAAe,EAAE,CAAC;AACpC,qBAAC,CAAC,CAAC;oBACH,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,KAAK,IAAG;;AACnD,wBAAA,IACI,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS;AACxB,6BACI,CAAA,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,IAAI,CAAC,QAAQ,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,WAAW,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAI,MAAK,OAAO;AAC5C,gCAAA,CAAwB,CAAA,EAAA,GAAA,IAAI,CAAC,QAAQ,0CAAE,WAAW,CAAC,IAAK,EAAC,IAAI,KAAK,IAAI;6BACzE,EACH;AACE,4BAAA,IAAI,CAAC,QAAQ,CAAC,SAAS,GAAG,IAAI,CAAC;4BAC/B,OAAO;AACV,yBAAA;wBAED,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC;AAC3C,wBAAA,OAAO,CAAC,KAAK,CAAC,CAAA,0DAAA,EAA6D,OAAO,GAAG,IAAI,CAAA,SAAA,CAAW,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;wBAEpH,UAAU,CAAC,MAAK;AACZ,4BAAA,IAAI,CAAC,QAAQ,CAAC,iBAAiB,EAAE,CAAC;AAClC,4BAAA,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE,CAAC;yBAChC,EAAE,OAAO,CAAC,CAAC;AAChB,qBAAC,CAAC,CAAA;AACL,iBAAA;aACJ;AACD,YAAA,SAAS,EAAE,CAAC,KAAK,KAAI;;gBACjB,IAAI;oBACA,MAAM,OAAO,GAAqB,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AACzD,oBAAA,IAAI,CAAC,QAAQ,CAAC,WAAW,GAAG,OAAO,CAAC;oBACpC,QAAQ,OAAO,CAAC,IAAI;AAChB,wBAAA,KAAK,WAAW;AACZ,4BAAA,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAAA,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,gBAAgB,CAAC,MAAI,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,IAAI,CAAC,CAAC;AACjF,4BAAA,MAAM,OAAO,GAAG,MAAM,KAAN,IAAA,IAAA,MAAM,uBAAN,MAAM,CAAG,CAAa,UAAA,EAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAA,CAAE,CAAC,CAAC;AAC7D,4BAAA,MAAM,WAAW,GAA8B,OAAO,CAAC,IAAI,CAAC;AAE5D,4BAAA,IAAI,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE;gCAC9B,CAAA,EAAA,GAAA,IAAI,CAAC,QAAQ,CAAC,MAAM,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAI,CAAC,IAAI,CAAC,SAAS,CAAkB;AACvD,oCAAA,IAAI,EAAE,gBAAgB;AACtB,oCAAA,IAAI,EAAE;wCACF,OAAO;AACV,qCAAA;AACJ,iCAAA,CAAC,CAAC,CAAC;AACP,6BAAA;4BACD,MAAM;AACV,wBAAA,KAAK,OAAO;AACR,4BAAA,IAAI,IAAI,GAAmC,OAAO,CAAC,IAAI,CAAC;AACxD,4BAAA,IAAI,IAAI,KAAJ,IAAA,IAAA,IAAI,uBAAJ,IAAI,CAAE,QAAQ,EAAE;gCAChB,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC;AACxF,gCAAA,IAAI,CAAC,YAAY;oCAAE,OAAO;gCAC1B,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,YAAY,IAAG;AAC/C,oCAAA,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,IAAI,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE;wCACxE,UAAU,CAAC,MAAM,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;AACjD,qCAAA;AACL,iCAAC,CAAC,CAAA;AACL,6BAAA;4BACD,MAAM;AACV,wBAAA,KAAK,MAAM;AACP,4BAAA,MAAM;AACV,wBAAA,KAAK,OAAO;4BACR,MAAM,OAAO,CAAC,IAAI,CAAC;AACvB,wBAAA;4BACI,MAAM;AACb,qBAAA;AACJ,iBAAA;AAAC,gBAAA,OAAO,CAAC,EAAE;AACR,oBAAA,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACpB,iBAAA;aACJ;YACD,OAAO,EAAE,QAAQ,IAAG;gBAChB,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,IAAG;AACrC,oBAAA,IAAI,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE;wBAC5B,IAAI,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,YAAY,CAAC,KAAI;4BAC9E,OAAO,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;AACnD,yBAAC,CAAC,CAAA;wBAEF,IAAI,CAAC,KAAK,EAAE;4BACR,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AAC1C,yBAAA;AACJ,qBAAA;AACL,iBAAC,CAAC,CAAA;aACL;SACJ,CAAA;KA2NJ;AA5dG;;;;;;;;AAQG;AACH,IAAA,WAAW,CAAC,QAAgB,EAAA;AACxB,QAAA,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE;AACrE,YAAA,MAAM,IAAI,iBAAiB,CAAC,wBAAwB,GAAG,QAAQ,CAAC,CAAC;AACpE,SAAA;AAED,QAAA,IAAI,CAAC,MAAM,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAChC,IAAI,CAAC,MAAM,CAAC,gBAAgB,GAAG,QAAQ,CAAC,OAAO,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC,OAAO,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;AAElG,QAAA,OAAO,IAAI,CAAC;KACf;AAED;;;;;;AAMG;AACH,IAAA,mBAAmB,CAAC,gBAAwB,EAAA;AACxC,QAAA,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE;AACjF,YAAA,MAAM,IAAI,iBAAiB,CAAC,iCAAiC,GAAG,gBAAgB,CAAC,CAAC;AACrF,SAAA;AAED,QAAA,IAAI,CAAC,MAAM,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;AAChD,QAAA,OAAO,IAAI,CAAC;KACf;AAED;;;;;;;;AAQG;AACH,IAAA,UAAU,CAAC,KAAa,EAAA;AACpB,QAAA,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,GAAG,KAAK,CAAC;AAC3C,QAAA,IAAI,CAAC,MAAM,CAAC,OAAO,GAAG,KAAK,CAAC;AAC5B,QAAA,OAAO,IAAI,CAAC;KACf;AACD;;;;;;;;AAQG;AACH,IAAA,MAAM,CAAC,KAAa,EAAA;AAChB,QAAA,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,GAAG,KAAK,CAAC;AACvC,QAAA,IAAI,CAAC,MAAM,CAAC,GAAG,GAAG,KAAK,CAAC;AACxB,QAAA,OAAO,IAAI,CAAC;KACf;AACD;;;;;;AAMG;AACH,IAAA,SAAS,CAAC,KAAa,EAAA;AACnB,QAAA,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,GAAG,KAAK,CAAC;AAC1C,QAAA,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,KAAK,CAAC;AAC3B,QAAA,OAAO,IAAI,CAAC;KACf;AACD;;;;;;;;AAQG;AACH,IAAA,UAAU,CAAC,KAAa,EAAA;AACpB,QAAA,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,GAAG,KAAK,CAAC;AAC3C,QAAA,IAAI,CAAC,MAAM,CAAC,OAAO,GAAG,KAAK,CAAC;AAC5B,QAAA,OAAO,IAAI,CAAC;KACf;AACD;;;;;;;;AAQG;AACH,IAAA,SAAS,CAAC,KAAa,EAAA;AACnB,QAAA,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,GAAG,KAAK,CAAC;AAC3C,QAAA,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,KAAK,CAAC;AAC3B,QAAA,OAAO,IAAI,CAAC;KACf;AA4JD;;;;;;;;;;;;;;;;;;;;;;;;AAwBG;IACH,SAAS,CAAoB,QAA2B,EAAE,QAAqD,EAAA;AAC3G,QAAA,IAAI,YAAY,GAAG,OAAO,QAAQ,KAAK,QAAQ,GAAG,CAAC,QAAQ,CAAC,GAAG,QAAQ,CAAC;AACxE,QAAA,YAAY,CAAC,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC;QAErE,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,oBAAoB,EAAE,CAAC;QACrD,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,GAAG,CAAC,OAAO,EAAE;AACrC,YAAA,QAAQ,EAAE,YAAY;YACtB,QAAQ;AACX,SAAA,CAAC,CAAC;AAEH,QAAA,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;AAExB,QAAA,OAAO,MAAK;YACR,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AAC5C,YAAA,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;AACpC,YAAA,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;AAC5B,SAAC,CAAA;KACJ;IAED,cAAc,CAAC,MAAc,EAAE,GAAQ,EAAE,OAAmB,GAAA,EAAE,EAAE,MAAA,GAAkB,EAAE,EAAA;AAChF,QAAA,MAAM,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC;AAE9B,QAAA,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QAEnD,IAAI,OAAO,MAAM,KAAK,WAAW,IAAI,MAAM,CAAC,YAAY,EAAE;YACtD,MAAM,cAAc,GAAG,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;AACrE,YAAA,IAAI,cAAc,EAAE;AAChB,gBAAA,OAAO,CAAC,oBAAoB,CAAC,GAAG,cAAc,CAAC;AAClD,aAAA;AACJ,SAAA;AAED,QAAA,IAAI,OAAO,GAAgB;YACvB,MAAM;YACN,OAAO;SACV,CAAC;AAEF,QAAA,IAAI,OAAO,CAAC,oBAAoB,CAAC,KAAK,SAAS,EAAE;AAC7C,YAAA,OAAO,CAAC,WAAW,GAAG,SAAS,CAAC;AACnC,SAAA;QAED,IAAI,MAAM,KAAK,KAAK,EAAE;AAClB,YAAA,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE;gBAC/D,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;AACvC,aAAA;AACJ,SAAA;AAAM,aAAA;AACH,YAAA,QAAQ,OAAO,CAAC,cAAc,CAAC;AAC3B,gBAAA,KAAK,kBAAkB;oBACnB,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;oBACtC,MAAM;AAEV,gBAAA,KAAK,qBAAqB;AACtB,oBAAA,MAAM,QAAQ,GAAG,IAAI,QAAQ,EAAE,CAAC;AAEhC,oBAAA,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;wBAC/C,IAAI,KAAK,YAAY,IAAI,EAAE;4BACvB,QAAQ,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;AAC3C,yBAAA;AAAM,6BAAA,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;AAC7B,4BAAA,KAAK,MAAM,WAAW,IAAI,KAAK,EAAE;gCAC7B,QAAQ,CAAC,MAAM,CAAC,CAAA,EAAG,GAAG,CAAI,EAAA,CAAA,EAAE,WAAW,CAAC,CAAC;AAC5C,6BAAA;AACJ,yBAAA;AAAM,6BAAA;AACH,4BAAA,QAAQ,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;AAC/B,yBAAA;AACJ,qBAAA;AAED,oBAAA,OAAO,CAAC,IAAI,GAAG,QAAQ,CAAC;AACxB,oBAAA,OAAO,OAAO,CAAC,cAAc,CAAC,CAAC;oBAC/B,MAAM;AACb,aAAA;AACJ,SAAA;QAED,OAAO,EAAE,GAAG,EAAE,GAAG,CAAC,QAAQ,EAAE,EAAE,OAAO,EAAE,CAAC;KAC3C;IAEK,aAAa,CAAC,MAAc,EAAE,GAAQ,EAAE,OAAmB,GAAA,EAAE,EAAE,eAAA,GAA2B,EAAE,EAAE,UAA8C,EAAA;;;AAC9I,YAAA,MAAM,CAAC,SAAS,EAAE,IAAI,CAAC,GAAG,CAAA,EAAA,GAAA,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,KAAK,KAAK,YAAY,IAAI,CAAC,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAI,EAAE,CAAC;AAE5G,YAAA,IAAI,CAAC,IAAI,IAAI,CAAC,SAAS,EAAE;AACrB,gBAAA,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;AAChD,aAAA;AAED,YAAA,IAAI,IAAI,CAAC,IAAI,IAAI,MAAM,CAAC,UAAU,EAAE;AAChC,gBAAA,OAAO,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,OAAO,EAAE,eAAe,CAAC,CAAC;AACjE,aAAA;YAED,IAAI,KAAK,GAAG,CAAC,CAAC;YACd,IAAI,QAAQ,GAAG,IAAI,CAAC;AAEpB,YAAA,OAAO,KAAK,GAAG,IAAI,CAAC,IAAI,EAAE;gBACtB,IAAI,GAAG,GAAG,KAAK,GAAG,MAAM,CAAC,UAAU,CAAC;AACpC,gBAAA,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE;AAClB,oBAAA,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC;AACnB,iBAAA;AAED,gBAAA,OAAO,CAAC,eAAe,CAAC,GAAG,SAAS,KAAK,CAAA,CAAA,EAAI,GAAG,GAAC,CAAC,CAAI,CAAA,EAAA,IAAI,CAAC,IAAI,EAAE,CAAC;gBAClE,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;AAErC,gBAAA,IAAI,OAAO,GAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAQ,eAAe,CAAE,CAAC;AACrC,gBAAA,OAAO,CAAC,SAAS,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;AAElD,gBAAA,QAAQ,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;AAE1D,gBAAA,IAAI,UAAU,IAAI,OAAO,UAAU,KAAK,UAAU,EAAE;AAChD,oBAAA,UAAU,CAAC;wBACP,GAAG,EAAE,QAAQ,CAAC,GAAG;AACjB,wBAAA,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,IAAI,GAAG,CAAC;AAC7C,wBAAA,YAAY,EAAE,GAAG;AACjB,wBAAA,WAAW,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC,UAAU,CAAC;wBACrD,cAAc,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,MAAM,CAAC,UAAU,CAAC;AACrD,qBAAA,CAAC,CAAC;AACN,iBAAA;AAED,gBAAA,IAAI,QAAQ,IAAI,QAAQ,CAAC,GAAG,EAAE;AAC1B,oBAAA,OAAO,CAAC,eAAe,CAAC,GAAG,QAAQ,CAAC,GAAG,CAAC;AAC3C,iBAAA;gBAED,KAAK,GAAG,GAAG,CAAC;AACf,aAAA;AAED,YAAA,OAAO,QAAQ,CAAC;;AACnB,KAAA;IAEK,IAAI,GAAA;;AACN,YAAA,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC,CAAC;SACpE,CAAA,CAAA;AAAA,KAAA;AAEK,IAAA,IAAI,CAAC,MAAc,EAAE,GAAQ,EAAE,OAAA,GAAmB,EAAE,EAAE,MAAkB,GAAA,EAAE,EAAE,YAAY,GAAG,MAAM,EAAA;;;AACnG,YAAA,MAAM,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,GAAG,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;YAE3E,IAAI,IAAI,GAAQ,IAAI,CAAC;YAErB,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;;AAG3C,YAAA,IAAI,QAAQ,CAAC,IAAI,KAAK,QAAQ,EAAE;AAC5B,gBAAA,MAAM,IAAI,iBAAiB,CACvB,CAA6C,0CAAA,EAAA,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAA,yDAAA,CAA2D,EAC5H,GAAG,EACH,WAAW,EACX,EAAE,CACL,CAAC;AACL,aAAA;YAED,MAAM,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC;AAC5D,YAAA,IAAI,QAAQ,EAAE;gBACV,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,OAAe,KAAK,OAAO,CAAC,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,CAAC,CAAC;AACzF,aAAA;AAED,YAAA,IAAI,CAAA,EAAA,GAAA,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,QAAQ,CAAC,kBAAkB,CAAC,EAAE;AACpE,gBAAA,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;AAChC,aAAA;iBAAM,IAAI,YAAY,KAAK,aAAa,EAAE;AACvC,gBAAA,IAAI,GAAG,MAAM,QAAQ,CAAC,WAAW,EAAE,CAAC;AACvC,aAAA;AAAM,iBAAA;AACH,gBAAA,IAAI,GAAG;AACH,oBAAA,OAAO,EAAE,MAAM,QAAQ,CAAC,IAAI,EAAE;iBACjC,CAAC;AACL,aAAA;AAED,YAAA,IAAI,GAAG,IAAI,QAAQ,CAAC,MAAM,EAAE;gBACxB,IAAI,YAAY,GAAG,EAAE,CAAC;AACtB,gBAAA,IAAI,CAAA,CAAA,EAAA,GAAA,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,QAAQ,CAAC,kBAAkB,CAAC,KAAI,YAAY,KAAK,aAAa,EAAE;AACtG,oBAAA,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;AACvC,iBAAA;AAAM,qBAAA;oBACH,YAAY,GAAG,IAAI,KAAJ,IAAA,IAAA,IAAI,uBAAJ,IAAI,CAAE,OAAO,CAAC;AAChC,iBAAA;gBACD,MAAM,IAAI,iBAAiB,CAAC,IAAI,KAAA,IAAA,IAAJ,IAAI,KAAJ,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,IAAI,CAAE,OAAO,EAAE,QAAQ,CAAC,MAAM,EAAE,IAAI,KAAA,IAAA,IAAJ,IAAI,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAJ,IAAI,CAAE,IAAI,EAAE,YAAY,CAAC,CAAC;AACzF,aAAA;YAED,MAAM,cAAc,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC;YAElE,IAAI,OAAO,MAAM,KAAK,WAAW,IAAI,MAAM,CAAC,YAAY,IAAI,cAAc,EAAE;AACxE,gBAAA,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,+HAA+H,CAAC,CAAC;gBACrJ,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,gBAAgB,EAAE,cAAc,CAAC,CAAC;AACjE,aAAA;AAED,YAAA,OAAO,IAAI,CAAC;;AACf,KAAA;AAED,IAAA,OAAO,OAAO,CAAC,IAAa,EAAE,MAAM,GAAG,EAAE,EAAA;QACrC,IAAI,MAAM,GAAY,EAAE,CAAC;AAEzB,QAAA,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;AAC7C,YAAA,IAAI,QAAQ,GAAG,MAAM,GAAG,MAAM,GAAG,GAAG,GAAG,GAAG,GAAE,GAAG,GAAG,GAAG,CAAC;AACtD,YAAA,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;AACtB,gBAAA,MAAM,GAAQ,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,MAAM,CAAK,EAAA,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAE,CAAC;AAC9D,aAAA;AAAM,iBAAA;AACH,gBAAA,MAAM,CAAC,QAAQ,CAAC,GAAG,KAAK,CAAC;AAC5B,aAAA;AACJ,SAAA;AAED,QAAA,OAAO,MAAM,CAAC;KACjB;;AApfM,MAAA,CAAA,UAAU,GAAG,IAAI,GAAG,IAAI,GAAG,CAAC;;MCtS1B,OAAO,CAAA;AAQhB,IAAA,WAAA,CAAY,MAAc,EAAA;AACtB,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;KACxB;AAED,IAAA,OAAO,OAAO,CAAC,IAAa,EAAE,MAAM,GAAG,EAAE,EAAA;QACrC,IAAI,MAAM,GAAY,EAAE,CAAC;AAEzB,QAAA,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;AAC7C,YAAA,IAAI,QAAQ,GAAG,MAAM,GAAG,MAAM,GAAG,GAAG,GAAG,GAAG,GAAE,GAAG,GAAG,GAAG,CAAC;AACtD,YAAA,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;AACtB,gBAAA,MAAM,GAAQ,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,MAAM,CAAK,EAAA,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAE,CAAC;AAC/D,aAAA;AAAM,iBAAA;AACH,gBAAA,MAAM,CAAC,QAAQ,CAAC,GAAG,KAAK,CAAC;AAC5B,aAAA;AACJ,SAAA;AAED,QAAA,OAAO,MAAM,CAAC;KACjB;;AAxBD;;AAEG;AACI,OAAU,CAAA,UAAA,GAAG,CAAC,GAAC,IAAI,GAAC,IAAI,CAAC;;MCAvB,OAAO,CAAA;AAGhB,IAAA,WAAA,CAAY,MAAc,EAAA;AACtB,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;KACxB;AAED;;;;;AAKG;IACH,GAAG,GAAA;QACC,MAAM,OAAO,GAAG,UAAU,CAAC;QAC3B,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;QAE3D,MAAM,UAAU,GAAiC,EAChD,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,KAAK,EACL,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AAED;;;;;;;;;AASG;AACH,IAAA,MAAM,CAAqE,MAAc,EAAE,KAAa,EAAE,QAAgB,EAAE,IAAa,EAAA;AACrI,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,YAAA,MAAM,IAAI,iBAAiB,CAAC,sCAAsC,CAAC,CAAC;AACvE,SAAA;AACD,QAAA,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE;AAC9B,YAAA,MAAM,IAAI,iBAAiB,CAAC,qCAAqC,CAAC,CAAC;AACtE,SAAA;AACD,QAAA,IAAI,OAAO,QAAQ,KAAK,WAAW,EAAE;AACjC,YAAA,MAAM,IAAI,iBAAiB,CAAC,wCAAwC,CAAC,CAAC;AACzE,SAAA;QACD,MAAM,OAAO,GAAG,UAAU,CAAC;QAC3B,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,YAAA,OAAO,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC;AAC9B,SAAA;AACD,QAAA,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE;AAC9B,YAAA,OAAO,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC;AAC5B,SAAA;AACD,QAAA,IAAI,OAAO,QAAQ,KAAK,WAAW,EAAE;AACjC,YAAA,OAAO,CAAC,UAAU,CAAC,GAAG,QAAQ,CAAC;AAClC,SAAA;AACD,QAAA,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;AAC7B,YAAA,OAAO,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;AAC1B,SAAA;AACD,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;AAE3D,QAAA,MAAM,UAAU,GAAiC;AAC7C,YAAA,cAAc,EAAE,kBAAkB;SACrC,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,MAAM,EACN,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AAED;;;;;;;;;AASG;IACH,WAAW,CAAqE,KAAa,EAAE,QAAgB,EAAA;AAC3G,QAAA,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE;AAC9B,YAAA,MAAM,IAAI,iBAAiB,CAAC,qCAAqC,CAAC,CAAC;AACtE,SAAA;AACD,QAAA,IAAI,OAAO,QAAQ,KAAK,WAAW,EAAE;AACjC,YAAA,MAAM,IAAI,iBAAiB,CAAC,wCAAwC,CAAC,CAAC;AACzE,SAAA;QACD,MAAM,OAAO,GAAG,gBAAgB,CAAC;QACjC,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE;AAC9B,YAAA,OAAO,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC;AAC5B,SAAA;AACD,QAAA,IAAI,OAAO,QAAQ,KAAK,WAAW,EAAE;AACjC,YAAA,OAAO,CAAC,UAAU,CAAC,GAAG,QAAQ,CAAC;AAClC,SAAA;AACD,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;AAE3D,QAAA,MAAM,UAAU,GAAiC;AAC7C,YAAA,cAAc,EAAE,kBAAkB;SACrC,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,OAAO,EACP,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AAED;;;;;;AAMG;AACH,IAAA,cAAc,CAAC,OAAkB,EAAA;QAC7B,MAAM,OAAO,GAAG,qBAAqB,CAAC;QACtC,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,IAAI,OAAO,OAAO,KAAK,WAAW,EAAE;AAChC,YAAA,OAAO,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC;AAChC,SAAA;AACD,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;QAE3D,MAAM,UAAU,GAAiC,EAChD,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,KAAK,EACL,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AAED;;;;;;AAMG;AACH,IAAA,cAAc,CAAC,UAAkB,EAAA;AAC7B,QAAA,IAAI,OAAO,UAAU,KAAK,WAAW,EAAE;AACnC,YAAA,MAAM,IAAI,iBAAiB,CAAC,0CAA0C,CAAC,CAAC;AAC3E,SAAA;QACD,MAAM,OAAO,GAAG,kCAAkC,CAAC,OAAO,CAAC,cAAc,EAAE,UAAU,CAAC,CAAC;QACvF,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;AAE3D,QAAA,MAAM,UAAU,GAAiC;AAC7C,YAAA,cAAc,EAAE,kBAAkB;SACrC,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,QAAQ,EACR,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AAED;;;;;AAKG;IACH,SAAS,GAAA;QACL,MAAM,OAAO,GAAG,eAAe,CAAC;QAChC,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;AAE3D,QAAA,MAAM,UAAU,GAAiC;AAC7C,YAAA,cAAc,EAAE,kBAAkB;SACrC,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,MAAM,EACN,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AAED;;;;;;AAMG;AACH,IAAA,QAAQ,CAAC,OAAkB,EAAA;QACvB,MAAM,OAAO,GAAG,eAAe,CAAC;QAChC,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,IAAI,OAAO,OAAO,KAAK,WAAW,EAAE;AAChC,YAAA,OAAO,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC;AAChC,SAAA;AACD,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;QAE3D,MAAM,UAAU,GAAiC,EAChD,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,KAAK,EACL,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AAED;;;;;;AAMG;AACH,IAAA,SAAS,CAAqE,GAAY,EAAA;AACtF,QAAA,IAAI,OAAO,GAAG,KAAK,WAAW,EAAE;AAC5B,YAAA,MAAM,IAAI,iBAAiB,CAAC,mCAAmC,CAAC,CAAC;AACpE,SAAA;QACD,MAAM,OAAO,GAAG,cAAc,CAAC;QAC/B,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,IAAI,OAAO,GAAG,KAAK,WAAW,EAAE;AAC5B,YAAA,OAAO,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC;AACxB,SAAA;AACD,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;AAE3D,QAAA,MAAM,UAAU,GAAiC;AAC7C,YAAA,cAAc,EAAE,kBAAkB;SACrC,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,OAAO,EACP,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AAED;;;;;;AAMG;AACH,IAAA,sBAAsB,CAAC,IAAuB,EAAA;AAC1C,QAAA,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;AAC7B,YAAA,MAAM,IAAI,iBAAiB,CAAC,oCAAoC,CAAC,CAAC;AACrE,SAAA;QACD,MAAM,OAAO,GAAG,oCAAoC,CAAC,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;QAC7E,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;AAE3D,QAAA,MAAM,UAAU,GAAiC;AAC7C,YAAA,cAAc,EAAE,kBAAkB;SACrC,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,MAAM,EACN,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AAED;;;;;;;AAOG;IACH,sBAAsB,CAAqE,IAAuB,EAAE,GAAW,EAAA;AAC3H,QAAA,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;AAC7B,YAAA,MAAM,IAAI,iBAAiB,CAAC,oCAAoC,CAAC,CAAC;AACrE,SAAA;AACD,QAAA,IAAI,OAAO,GAAG,KAAK,WAAW,EAAE;AAC5B,YAAA,MAAM,IAAI,iBAAiB,CAAC,mCAAmC,CAAC,CAAC;AACpE,SAAA;QACD,MAAM,OAAO,GAAG,oCAAoC,CAAC,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;QAC7E,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,IAAI,OAAO,GAAG,KAAK,WAAW,EAAE;AAC5B,YAAA,OAAO,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC;AACxB,SAAA;AACD,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;AAE3D,QAAA,MAAM,UAAU,GAAiC;AAC7C,YAAA,cAAc,EAAE,kBAAkB;SACrC,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,KAAK,EACL,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AAED;;;;;;AAMG;AACH,IAAA,sBAAsB,CAAC,IAAuB,EAAA;AAC1C,QAAA,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;AAC7B,YAAA,MAAM,IAAI,iBAAiB,CAAC,oCAAoC,CAAC,CAAC;AACrE,SAAA;QACD,MAAM,OAAO,GAAG,oCAAoC,CAAC,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;QAC7E,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;AAE3D,QAAA,MAAM,UAAU,GAAiC;AAC7C,YAAA,cAAc,EAAE,kBAAkB;SACrC,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,QAAQ,EACR,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AAED;;;;;;AAMG;AACH,IAAA,kBAAkB,CAAC,MAA4B,EAAA;AAC3C,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,YAAA,MAAM,IAAI,iBAAiB,CAAC,sCAAsC,CAAC,CAAC;AACvE,SAAA;QACD,MAAM,OAAO,GAAG,wBAAwB,CAAC;QACzC,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,YAAA,OAAO,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC;AAC9B,SAAA;AACD,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;AAE3D,QAAA,MAAM,UAAU,GAAiC;AAC7C,YAAA,cAAc,EAAE,kBAAkB;SACrC,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,MAAM,EACN,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AAED;;;;;;;AAOG;IACH,kBAAkB,CAAC,WAAmB,EAAE,GAAW,EAAA;AAC/C,QAAA,IAAI,OAAO,WAAW,KAAK,WAAW,EAAE;AACpC,YAAA,MAAM,IAAI,iBAAiB,CAAC,2CAA2C,CAAC,CAAC;AAC5E,SAAA;AACD,QAAA,IAAI,OAAO,GAAG,KAAK,WAAW,EAAE;AAC5B,YAAA,MAAM,IAAI,iBAAiB,CAAC,mCAAmC,CAAC,CAAC;AACpE,SAAA;QACD,MAAM,OAAO,GAAG,wBAAwB,CAAC;QACzC,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,IAAI,OAAO,WAAW,KAAK,WAAW,EAAE;AACpC,YAAA,OAAO,CAAC,aAAa,CAAC,GAAG,WAAW,CAAC;AACxC,SAAA;AACD,QAAA,IAAI,OAAO,GAAG,KAAK,WAAW,EAAE;AAC5B,YAAA,OAAO,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC;AACxB,SAAA;AACD,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;AAE3D,QAAA,MAAM,UAAU,GAAiC;AAC7C,YAAA,cAAc,EAAE,kBAAkB;SACrC,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,KAAK,EACL,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AAED;;;;;AAKG;IACH,cAAc,GAAA;QACV,MAAM,OAAO,GAAG,sBAAsB,CAAC;QACvC,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;QAE3D,MAAM,UAAU,GAAiC,EAChD,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,KAAK,EACL,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AAED;;;;;AAKG;IACH,mBAAmB,GAAA;QACf,MAAM,OAAO,GAAG,6BAA6B,CAAC;QAC9C,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;QAE3D,MAAM,UAAU,GAAiC,EAChD,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,KAAK,EACL,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AAED;;;;;AAKG;IACH,sBAAsB,GAAA;QAClB,MAAM,OAAO,GAAG,6BAA6B,CAAC;QAC9C,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;AAE3D,QAAA,MAAM,UAAU,GAAiC;AAC7C,YAAA,cAAc,EAAE,kBAAkB;SACrC,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,MAAM,EACN,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AAED;;;;;AAKG;IACH,sBAAsB,GAAA;QAClB,MAAM,OAAO,GAAG,6BAA6B,CAAC;QAC9C,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;AAE3D,QAAA,MAAM,UAAU,GAAiC;AAC7C,YAAA,cAAc,EAAE,kBAAkB;SACrC,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,OAAO,EACP,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AAED;;;;;;AAMG;AACH,IAAA,UAAU,CAAqE,IAAY,EAAA;AACvF,QAAA,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;AAC7B,YAAA,MAAM,IAAI,iBAAiB,CAAC,oCAAoC,CAAC,CAAC;AACrE,SAAA;QACD,MAAM,OAAO,GAAG,eAAe,CAAC;QAChC,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;AAC7B,YAAA,OAAO,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;AAC1B,SAAA;AACD,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;AAE3D,QAAA,MAAM,UAAU,GAAiC;AAC7C,YAAA,cAAc,EAAE,kBAAkB;SACrC,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,OAAO,EACP,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AAED;;;;;;;AAOG;IACH,cAAc,CAAqE,QAAgB,EAAE,WAAoB,EAAA;AACrH,QAAA,IAAI,OAAO,QAAQ,KAAK,WAAW,EAAE;AACjC,YAAA,MAAM,IAAI,iBAAiB,CAAC,wCAAwC,CAAC,CAAC;AACzE,SAAA;QACD,MAAM,OAAO,GAAG,mBAAmB,CAAC;QACpC,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,IAAI,OAAO,QAAQ,KAAK,WAAW,EAAE;AACjC,YAAA,OAAO,CAAC,UAAU,CAAC,GAAG,QAAQ,CAAC;AAClC,SAAA;AACD,QAAA,IAAI,OAAO,WAAW,KAAK,WAAW,EAAE;AACpC,YAAA,OAAO,CAAC,aAAa,CAAC,GAAG,WAAW,CAAC;AACxC,SAAA;AACD,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;AAE3D,QAAA,MAAM,UAAU,GAAiC;AAC7C,YAAA,cAAc,EAAE,kBAAkB;SACrC,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,OAAO,EACP,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AAED;;;;;;;AAOG;IACH,WAAW,CAAqE,KAAa,EAAE,QAAgB,EAAA;AAC3G,QAAA,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE;AAC9B,YAAA,MAAM,IAAI,iBAAiB,CAAC,qCAAqC,CAAC,CAAC;AACtE,SAAA;AACD,QAAA,IAAI,OAAO,QAAQ,KAAK,WAAW,EAAE;AACjC,YAAA,MAAM,IAAI,iBAAiB,CAAC,wCAAwC,CAAC,CAAC;AACzE,SAAA;QACD,MAAM,OAAO,GAAG,gBAAgB,CAAC;QACjC,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE;AAC9B,YAAA,OAAO,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC;AAC5B,SAAA;AACD,QAAA,IAAI,OAAO,QAAQ,KAAK,WAAW,EAAE;AACjC,YAAA,OAAO,CAAC,UAAU,CAAC,GAAG,QAAQ,CAAC;AAClC,SAAA;AACD,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;AAE3D,QAAA,MAAM,UAAU,GAAiC;AAC7C,YAAA,cAAc,EAAE,kBAAkB;SACrC,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,OAAO,EACP,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AAED;;;;;AAKG;IACH,QAAQ,GAAA;QACJ,MAAM,OAAO,GAAG,gBAAgB,CAAC;QACjC,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;QAE3D,MAAM,UAAU,GAAiC,EAChD,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,KAAK,EACL,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AAED;;;;;;AAMG;AACH,IAAA,WAAW,CAAqE,KAA2B,EAAA;AACvG,QAAA,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE;AAC9B,YAAA,MAAM,IAAI,iBAAiB,CAAC,qCAAqC,CAAC,CAAC;AACtE,SAAA;QACD,MAAM,OAAO,GAAG,gBAAgB,CAAC;QACjC,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE;AAC9B,YAAA,OAAO,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC;AAC5B,SAAA;AACD,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;AAE3D,QAAA,MAAM,UAAU,GAAiC;AAC7C,YAAA,cAAc,EAAE,kBAAkB;SACrC,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,OAAO,EACP,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AAED;;;;;;;AAOG;IACH,cAAc,CAAC,KAAa,EAAE,GAAW,EAAA;AACrC,QAAA,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE;AAC9B,YAAA,MAAM,IAAI,iBAAiB,CAAC,qCAAqC,CAAC,CAAC;AACtE,SAAA;AACD,QAAA,IAAI,OAAO,GAAG,KAAK,WAAW,EAAE;AAC5B,YAAA,MAAM,IAAI,iBAAiB,CAAC,mCAAmC,CAAC,CAAC;AACpE,SAAA;QACD,MAAM,OAAO,GAAG,mBAAmB,CAAC;QACpC,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE;AAC9B,YAAA,OAAO,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC;AAC5B,SAAA;AACD,QAAA,IAAI,OAAO,GAAG,KAAK,WAAW,EAAE;AAC5B,YAAA,OAAO,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC;AACxB,SAAA;AACD,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;AAE3D,QAAA,MAAM,UAAU,GAAiC;AAC7C,YAAA,cAAc,EAAE,kBAAkB;SACrC,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,MAAM,EACN,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AAED;;;;;;;;;;AAUG;AACH,IAAA,cAAc,CAAC,MAAc,EAAE,MAAc,EAAE,QAAgB,EAAA;AAC3D,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,YAAA,MAAM,IAAI,iBAAiB,CAAC,sCAAsC,CAAC,CAAC;AACvE,SAAA;AACD,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,YAAA,MAAM,IAAI,iBAAiB,CAAC,sCAAsC,CAAC,CAAC;AACvE,SAAA;AACD,QAAA,IAAI,OAAO,QAAQ,KAAK,WAAW,EAAE;AACjC,YAAA,MAAM,IAAI,iBAAiB,CAAC,wCAAwC,CAAC,CAAC;AACzE,SAAA;QACD,MAAM,OAAO,GAAG,mBAAmB,CAAC;QACpC,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,YAAA,OAAO,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC;AAC9B,SAAA;AACD,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,YAAA,OAAO,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC;AAC9B,SAAA;AACD,QAAA,IAAI,OAAO,QAAQ,KAAK,WAAW,EAAE;AACjC,YAAA,OAAO,CAAC,UAAU,CAAC,GAAG,QAAQ,CAAC;AAClC,SAAA;AACD,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;AAE3D,QAAA,MAAM,UAAU,GAAiC;AAC7C,YAAA,cAAc,EAAE,kBAAkB;SACrC,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,KAAK,EACL,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AAED;;;;;AAKG;IACH,YAAY,GAAA;QACR,MAAM,OAAO,GAAG,mBAAmB,CAAC;QACpC,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;QAE3D,MAAM,UAAU,GAAiC,EAChD,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,KAAK,EACL,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AAED;;;;;AAKG;IACH,cAAc,GAAA;QACV,MAAM,OAAO,GAAG,mBAAmB,CAAC;QACpC,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;AAE3D,QAAA,MAAM,UAAU,GAAiC;AAC7C,YAAA,cAAc,EAAE,kBAAkB;SACrC,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,QAAQ,EACR,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AAED;;;;;AAKG;IACH,sBAAsB,GAAA;QAClB,MAAM,OAAO,GAAG,6BAA6B,CAAC;QAC9C,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;AAE3D,QAAA,MAAM,UAAU,GAAiC;AAC7C,YAAA,cAAc,EAAE,kBAAkB;SACrC,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,MAAM,EACN,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AAED;;;;;;;;;AASG;IACH,0BAA0B,CAAC,KAAa,EAAE,QAAgB,EAAA;AACtD,QAAA,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE;AAC9B,YAAA,MAAM,IAAI,iBAAiB,CAAC,qCAAqC,CAAC,CAAC;AACtE,SAAA;AACD,QAAA,IAAI,OAAO,QAAQ,KAAK,WAAW,EAAE;AACjC,YAAA,MAAM,IAAI,iBAAiB,CAAC,wCAAwC,CAAC,CAAC;AACzE,SAAA;QACD,MAAM,OAAO,GAAG,yBAAyB,CAAC;QAC1C,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE;AAC9B,YAAA,OAAO,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC;AAC5B,SAAA;AACD,QAAA,IAAI,OAAO,QAAQ,KAAK,WAAW,EAAE;AACjC,YAAA,OAAO,CAAC,UAAU,CAAC,GAAG,QAAQ,CAAC;AAClC,SAAA;AACD,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;AAE3D,QAAA,MAAM,UAAU,GAAiC;AAC7C,YAAA,cAAc,EAAE,kBAAkB;SACrC,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,MAAM,EACN,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AAED;;;;;;;AAOG;IACH,qBAAqB,CAAC,MAAc,EAAE,MAAc,EAAA;AAChD,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,YAAA,MAAM,IAAI,iBAAiB,CAAC,sCAAsC,CAAC,CAAC;AACvE,SAAA;AACD,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,YAAA,MAAM,IAAI,iBAAiB,CAAC,sCAAsC,CAAC,CAAC;AACvE,SAAA;QACD,MAAM,OAAO,GAAG,6BAA6B,CAAC;QAC9C,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,YAAA,OAAO,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC;AAC9B,SAAA;AACD,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,YAAA,OAAO,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC;AAC9B,SAAA;AACD,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;AAE3D,QAAA,MAAM,UAAU,GAAiC;AAC7C,YAAA,cAAc,EAAE,kBAAkB;SACrC,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,KAAK,EACL,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AAED;;;;;;;;;;;;;;AAcG;AACH,IAAA,mBAAmB,CAAC,QAAuB,EAAE,OAAgB,EAAE,OAAgB,EAAE,MAAiB,EAAA;AAC9F,QAAA,IAAI,OAAO,QAAQ,KAAK,WAAW,EAAE;AACjC,YAAA,MAAM,IAAI,iBAAiB,CAAC,wCAAwC,CAAC,CAAC;AACzE,SAAA;QACD,MAAM,OAAO,GAAG,qCAAqC,CAAC,OAAO,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;QACtF,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,IAAI,OAAO,OAAO,KAAK,WAAW,EAAE;AAChC,YAAA,OAAO,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC;AAChC,SAAA;AACD,QAAA,IAAI,OAAO,OAAO,KAAK,WAAW,EAAE;AAChC,YAAA,OAAO,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC;AAChC,SAAA;AACD,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,YAAA,OAAO,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC;AAC9B,SAAA;AACD,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;QAK3D,OAAO,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC;AAEhD,QAAA,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,EAAE;YACjE,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;AACvC,SAAA;AAED,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,KAAI,MAAM,KAAN,IAAA,IAAA,MAAM,KAAN,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,MAAM,CAAE,QAAQ,CAAA,EAAE;YACnD,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,GAAG,CAAC,QAAQ,EAAE,CAAC;YACtC,OAAO;AACV,SAAA;AAAM,aAAA;AACH,YAAA,OAAO,GAAG,CAAC,QAAQ,EAAE,CAAC;AACzB,SAAA;KACJ;AAED;;;;;;;AAOG;IACH,kBAAkB,CAAC,MAAc,EAAE,MAAc,EAAA;AAC7C,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,YAAA,MAAM,IAAI,iBAAiB,CAAC,sCAAsC,CAAC,CAAC;AACvE,SAAA;AACD,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,YAAA,MAAM,IAAI,iBAAiB,CAAC,sCAAsC,CAAC,CAAC;AACvE,SAAA;QACD,MAAM,OAAO,GAAG,yBAAyB,CAAC;QAC1C,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,YAAA,OAAO,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC;AAC9B,SAAA;AACD,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,YAAA,OAAO,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC;AAC9B,SAAA;AACD,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;AAE3D,QAAA,MAAM,UAAU,GAAiC;AAC7C,YAAA,cAAc,EAAE,kBAAkB;SACrC,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,KAAK,EACL,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AAED;;;;;;;AAOG;IACH,aAAa,CAAC,MAAc,EAAE,MAAc,EAAA;AACxC,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,YAAA,MAAM,IAAI,iBAAiB,CAAC,sCAAsC,CAAC,CAAC;AACvE,SAAA;AACD,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,YAAA,MAAM,IAAI,iBAAiB,CAAC,sCAAsC,CAAC,CAAC;AACvE,SAAA;QACD,MAAM,OAAO,GAAG,yBAAyB,CAAC;QAC1C,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,YAAA,OAAO,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC;AAC9B,SAAA;AACD,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,YAAA,OAAO,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC;AAC9B,SAAA;AACD,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;AAE3D,QAAA,MAAM,UAAU,GAAiC;AAC7C,YAAA,cAAc,EAAE,kBAAkB;SACrC,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,MAAM,EACN,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AAED;;;;;;AAMG;AACH,IAAA,UAAU,CAAC,SAAiB,EAAA;AACxB,QAAA,IAAI,OAAO,SAAS,KAAK,WAAW,EAAE;AAClC,YAAA,MAAM,IAAI,iBAAiB,CAAC,yCAAyC,CAAC,CAAC;AAC1E,SAAA;QACD,MAAM,OAAO,GAAG,+BAA+B,CAAC,OAAO,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;QAClF,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;QAE3D,MAAM,UAAU,GAAiC,EAChD,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,KAAK,EACL,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AAED;;;;;;AAMG;AACH,IAAA,aAAa,CAAC,SAAiB,EAAA;AAC3B,QAAA,IAAI,OAAO,SAAS,KAAK,WAAW,EAAE;AAClC,YAAA,MAAM,IAAI,iBAAiB,CAAC,yCAAyC,CAAC,CAAC;AAC1E,SAAA;QACD,MAAM,OAAO,GAAG,+BAA+B,CAAC,OAAO,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;QAClF,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;AAE3D,QAAA,MAAM,UAAU,GAAiC;AAC7C,YAAA,cAAc,EAAE,kBAAkB;SACrC,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,OAAO,EACP,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AAED;;;;;;AAMG;AACH,IAAA,aAAa,CAAC,SAAiB,EAAA;AAC3B,QAAA,IAAI,OAAO,SAAS,KAAK,WAAW,EAAE;AAClC,YAAA,MAAM,IAAI,iBAAiB,CAAC,yCAAyC,CAAC,CAAC;AAC1E,SAAA;QACD,MAAM,OAAO,GAAG,+BAA+B,CAAC,OAAO,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;QAClF,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;AAE3D,QAAA,MAAM,UAAU,GAAiC;AAC7C,YAAA,cAAc,EAAE,kBAAkB;SACrC,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,QAAQ,EACR,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AAED;;;;;AAKG;IACH,YAAY,GAAA;QACR,MAAM,OAAO,GAAG,iBAAiB,CAAC;QAClC,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;AAE3D,QAAA,MAAM,UAAU,GAAiC;AAC7C,YAAA,cAAc,EAAE,kBAAkB;SACrC,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,OAAO,EACP,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AAED;;;;;;;;AAQG;AACH,IAAA,gBAAgB,CAAC,QAAgB,EAAE,UAAkB,EAAE,UAAmB,EAAA;AACtE,QAAA,IAAI,OAAO,QAAQ,KAAK,WAAW,EAAE;AACjC,YAAA,MAAM,IAAI,iBAAiB,CAAC,wCAAwC,CAAC,CAAC;AACzE,SAAA;AACD,QAAA,IAAI,OAAO,UAAU,KAAK,WAAW,EAAE;AACnC,YAAA,MAAM,IAAI,iBAAiB,CAAC,0CAA0C,CAAC,CAAC;AAC3E,SAAA;QACD,MAAM,OAAO,GAAG,uBAAuB,CAAC;QACxC,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,IAAI,OAAO,QAAQ,KAAK,WAAW,EAAE;AACjC,YAAA,OAAO,CAAC,UAAU,CAAC,GAAG,QAAQ,CAAC;AAClC,SAAA;AACD,QAAA,IAAI,OAAO,UAAU,KAAK,WAAW,EAAE;AACnC,YAAA,OAAO,CAAC,YAAY,CAAC,GAAG,UAAU,CAAC;AACtC,SAAA;AACD,QAAA,IAAI,OAAO,UAAU,KAAK,WAAW,EAAE;AACnC,YAAA,OAAO,CAAC,YAAY,CAAC,GAAG,UAAU,CAAC;AACtC,SAAA;AACD,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;AAE3D,QAAA,MAAM,UAAU,GAAiC;AAC7C,YAAA,cAAc,EAAE,kBAAkB;SACrC,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,MAAM,EACN,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AAED;;;;;;;AAOG;IACH,gBAAgB,CAAC,QAAgB,EAAE,UAAkB,EAAA;AACjD,QAAA,IAAI,OAAO,QAAQ,KAAK,WAAW,EAAE;AACjC,YAAA,MAAM,IAAI,iBAAiB,CAAC,wCAAwC,CAAC,CAAC;AACzE,SAAA;AACD,QAAA,IAAI,OAAO,UAAU,KAAK,WAAW,EAAE;AACnC,YAAA,MAAM,IAAI,iBAAiB,CAAC,0CAA0C,CAAC,CAAC;AAC3E,SAAA;QACD,MAAM,OAAO,GAAG,kCAAkC,CAAC,OAAO,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;QACnF,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,IAAI,OAAO,UAAU,KAAK,WAAW,EAAE;AACnC,YAAA,OAAO,CAAC,YAAY,CAAC,GAAG,UAAU,CAAC;AACtC,SAAA;AACD,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;AAE3D,QAAA,MAAM,UAAU,GAAiC;AAC7C,YAAA,cAAc,EAAE,kBAAkB;SACrC,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,KAAK,EACL,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AAED;;;;;;AAMG;AACH,IAAA,gBAAgB,CAAC,QAAgB,EAAA;AAC7B,QAAA,IAAI,OAAO,QAAQ,KAAK,WAAW,EAAE;AACjC,YAAA,MAAM,IAAI,iBAAiB,CAAC,wCAAwC,CAAC,CAAC;AACzE,SAAA;QACD,MAAM,OAAO,GAAG,kCAAkC,CAAC,OAAO,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;QACnF,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;AAE3D,QAAA,MAAM,UAAU,GAAiC;AAC7C,YAAA,cAAc,EAAE,kBAAkB;SACrC,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,QAAQ,EACR,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AAED;;;;;;;;;;AAUG;AACH,IAAA,gBAAgB,CAAC,MAAc,EAAE,KAAa,EAAE,MAAgB,EAAA;AAC5D,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,YAAA,MAAM,IAAI,iBAAiB,CAAC,sCAAsC,CAAC,CAAC;AACvE,SAAA;AACD,QAAA,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE;AAC9B,YAAA,MAAM,IAAI,iBAAiB,CAAC,qCAAqC,CAAC,CAAC;AACtE,SAAA;QACD,MAAM,OAAO,GAAG,uBAAuB,CAAC;QACxC,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,YAAA,OAAO,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC;AAC9B,SAAA;AACD,QAAA,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE;AAC9B,YAAA,OAAO,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC;AAC5B,SAAA;AACD,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,YAAA,OAAO,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC;AAC9B,SAAA;AACD,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;AAE3D,QAAA,MAAM,UAAU,GAAiC;AAC7C,YAAA,cAAc,EAAE,kBAAkB;SACrC,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,MAAM,EACN,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AAED;;;;;;;;;;;;AAYG;AACH,IAAA,mBAAmB,CAAC,MAAc,EAAE,KAAa,EAAE,GAAY,EAAE,MAAgB,EAAA;AAC7E,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,YAAA,MAAM,IAAI,iBAAiB,CAAC,sCAAsC,CAAC,CAAC;AACvE,SAAA;AACD,QAAA,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE;AAC9B,YAAA,MAAM,IAAI,iBAAiB,CAAC,qCAAqC,CAAC,CAAC;AACtE,SAAA;QACD,MAAM,OAAO,GAAG,2BAA2B,CAAC;QAC5C,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,YAAA,OAAO,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC;AAC9B,SAAA;AACD,QAAA,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE;AAC9B,YAAA,OAAO,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC;AAC5B,SAAA;AACD,QAAA,IAAI,OAAO,GAAG,KAAK,WAAW,EAAE;AAC5B,YAAA,OAAO,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC;AACxB,SAAA;AACD,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,YAAA,OAAO,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC;AAC9B,SAAA;AACD,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;AAE3D,QAAA,MAAM,UAAU,GAAiC;AAC7C,YAAA,cAAc,EAAE,kBAAkB;SACrC,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,MAAM,EACN,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AAED;;;;;;;;;;;;;AAaG;AACH,IAAA,iBAAiB,CAAC,QAAuB,EAAE,OAAgB,EAAE,OAAgB,EAAE,MAAiB,EAAA;AAC5F,QAAA,IAAI,OAAO,QAAQ,KAAK,WAAW,EAAE;AACjC,YAAA,MAAM,IAAI,iBAAiB,CAAC,wCAAwC,CAAC,CAAC;AACzE,SAAA;QACD,MAAM,OAAO,GAAG,mCAAmC,CAAC,OAAO,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;QACpF,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,IAAI,OAAO,OAAO,KAAK,WAAW,EAAE;AAChC,YAAA,OAAO,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC;AAChC,SAAA;AACD,QAAA,IAAI,OAAO,OAAO,KAAK,WAAW,EAAE;AAChC,YAAA,OAAO,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC;AAChC,SAAA;AACD,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,YAAA,OAAO,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC;AAC9B,SAAA;AACD,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;QAK3D,OAAO,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC;AAEhD,QAAA,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,EAAE;YACjE,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;AACvC,SAAA;AAED,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,KAAI,MAAM,KAAN,IAAA,IAAA,MAAM,KAAN,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,MAAM,CAAE,QAAQ,CAAA,EAAE;YACnD,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,GAAG,CAAC,QAAQ,EAAE,CAAC;YACtC,OAAO;AACV,SAAA;AAAM,aAAA;AACH,YAAA,OAAO,GAAG,CAAC,QAAQ,EAAE,CAAC;AACzB,SAAA;KACJ;AAED;;;;;;;;;AASG;IACH,gBAAgB,CAAC,MAAc,EAAE,KAAa,EAAA;AAC1C,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,YAAA,MAAM,IAAI,iBAAiB,CAAC,sCAAsC,CAAC,CAAC;AACvE,SAAA;AACD,QAAA,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE;AAC9B,YAAA,MAAM,IAAI,iBAAiB,CAAC,qCAAqC,CAAC,CAAC;AACtE,SAAA;QACD,MAAM,OAAO,GAAG,uBAAuB,CAAC;QACxC,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,YAAA,OAAO,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC;AAC9B,SAAA;AACD,QAAA,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE;AAC9B,YAAA,OAAO,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC;AAC5B,SAAA;AACD,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;AAE3D,QAAA,MAAM,UAAU,GAAiC;AAC7C,YAAA,cAAc,EAAE,kBAAkB;SACrC,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,MAAM,EACN,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AAED;;;;;;;;;AASG;AACH,IAAA,kBAAkB,CAAC,GAAW,EAAA;AAC1B,QAAA,IAAI,OAAO,GAAG,KAAK,WAAW,EAAE;AAC5B,YAAA,MAAM,IAAI,iBAAiB,CAAC,mCAAmC,CAAC,CAAC;AACpE,SAAA;QACD,MAAM,OAAO,GAAG,uBAAuB,CAAC;QACxC,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,IAAI,OAAO,GAAG,KAAK,WAAW,EAAE;AAC5B,YAAA,OAAO,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC;AACxB,SAAA;AACD,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;AAE3D,QAAA,MAAM,UAAU,GAAiC;AAC7C,YAAA,cAAc,EAAE,kBAAkB;SACrC,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,MAAM,EACN,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AAED;;;;;;;AAOG;IACH,kBAAkB,CAAC,MAAc,EAAE,MAAc,EAAA;AAC7C,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,YAAA,MAAM,IAAI,iBAAiB,CAAC,sCAAsC,CAAC,CAAC;AACvE,SAAA;AACD,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,YAAA,MAAM,IAAI,iBAAiB,CAAC,sCAAsC,CAAC,CAAC;AACvE,SAAA;QACD,MAAM,OAAO,GAAG,uBAAuB,CAAC;QACxC,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,YAAA,OAAO,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC;AAC9B,SAAA;AACD,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,YAAA,OAAO,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC;AAC9B,SAAA;AACD,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;AAE3D,QAAA,MAAM,UAAU,GAAiC;AAC7C,YAAA,cAAc,EAAE,kBAAkB;SACrC,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,KAAK,EACL,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AAED;;;;;AAKG;IACH,uBAAuB,GAAA;QACnB,MAAM,OAAO,GAAG,6BAA6B,CAAC;QAC9C,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;AAE3D,QAAA,MAAM,UAAU,GAAiC;AAC7C,YAAA,cAAc,EAAE,kBAAkB;SACrC,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,MAAM,EACN,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AAED;;;;;;;AAOG;IACH,uBAAuB,CAAC,MAAc,EAAE,MAAc,EAAA;AAClD,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,YAAA,MAAM,IAAI,iBAAiB,CAAC,sCAAsC,CAAC,CAAC;AACvE,SAAA;AACD,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,YAAA,MAAM,IAAI,iBAAiB,CAAC,sCAAsC,CAAC,CAAC;AACvE,SAAA;QACD,MAAM,OAAO,GAAG,6BAA6B,CAAC;QAC9C,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,YAAA,OAAO,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC;AAC9B,SAAA;AACD,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,YAAA,OAAO,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC;AAC9B,SAAA;AACD,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;AAE3D,QAAA,MAAM,UAAU,GAAiC;AAC7C,YAAA,cAAc,EAAE,kBAAkB;SACrC,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,KAAK,EACL,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AACJ;;MCp+CY,OAAO,CAAA;AAGhB,IAAA,WAAA,CAAY,MAAc,EAAA;AACtB,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;KACxB;AAED;;;;;;;;;;;AAWG;AACH,IAAA,UAAU,CAAC,IAAa,EAAE,KAAc,EAAE,MAAe,EAAE,OAAgB,EAAA;AACvE,QAAA,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;AAC7B,YAAA,MAAM,IAAI,iBAAiB,CAAC,oCAAoC,CAAC,CAAC;AACrE,SAAA;QACD,MAAM,OAAO,GAAG,0BAA0B,CAAC,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;QACnE,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE;AAC9B,YAAA,OAAO,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC;AAC5B,SAAA;AACD,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,YAAA,OAAO,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC;AAC9B,SAAA;AACD,QAAA,IAAI,OAAO,OAAO,KAAK,WAAW,EAAE;AAChC,YAAA,OAAO,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC;AAChC,SAAA;AACD,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;QAK3D,OAAO,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC;AAEhD,QAAA,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,EAAE;YACjE,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;AACvC,SAAA;AAED,QAAA,OAAO,GAAG,CAAC,QAAQ,EAAE,CAAC;KACzB;AAED;;;;;;;;;;;;AAYG;AACH,IAAA,aAAa,CAAC,IAAgB,EAAE,KAAc,EAAE,MAAe,EAAE,OAAgB,EAAA;AAC7E,QAAA,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;AAC7B,YAAA,MAAM,IAAI,iBAAiB,CAAC,oCAAoC,CAAC,CAAC;AACrE,SAAA;QACD,MAAM,OAAO,GAAG,8BAA8B,CAAC,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;QACvE,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE;AAC9B,YAAA,OAAO,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC;AAC5B,SAAA;AACD,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,YAAA,OAAO,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC;AAC9B,SAAA;AACD,QAAA,IAAI,OAAO,OAAO,KAAK,WAAW,EAAE;AAChC,YAAA,OAAO,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC;AAChC,SAAA;AACD,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;QAK3D,OAAO,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC;AAEhD,QAAA,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,EAAE;YACjE,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;AACvC,SAAA;AAED,QAAA,OAAO,GAAG,CAAC,QAAQ,EAAE,CAAC;KACzB;AAED;;;;;;;;AAQG;AACH,IAAA,UAAU,CAAC,GAAW,EAAA;AAClB,QAAA,IAAI,OAAO,GAAG,KAAK,WAAW,EAAE;AAC5B,YAAA,MAAM,IAAI,iBAAiB,CAAC,mCAAmC,CAAC,CAAC;AACpE,SAAA;QACD,MAAM,OAAO,GAAG,kBAAkB,CAAC;QACnC,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,IAAI,OAAO,GAAG,KAAK,WAAW,EAAE;AAC5B,YAAA,OAAO,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC;AACxB,SAAA;AACD,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;QAK3D,OAAO,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC;AAEhD,QAAA,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,EAAE;YACjE,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;AACvC,SAAA;AAED,QAAA,OAAO,GAAG,CAAC,QAAQ,EAAE,CAAC;KACzB;AAED;;;;;;;;;;;;AAYG;AACH,IAAA,OAAO,CAAC,IAAU,EAAE,KAAc,EAAE,MAAe,EAAE,OAAgB,EAAA;AACjE,QAAA,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;AAC7B,YAAA,MAAM,IAAI,iBAAiB,CAAC,oCAAoC,CAAC,CAAC;AACrE,SAAA;QACD,MAAM,OAAO,GAAG,uBAAuB,CAAC,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;QAChE,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE;AAC9B,YAAA,OAAO,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC;AAC5B,SAAA;AACD,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,YAAA,OAAO,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC;AAC9B,SAAA;AACD,QAAA,IAAI,OAAO,OAAO,KAAK,WAAW,EAAE;AAChC,YAAA,OAAO,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC;AAChC,SAAA;AACD,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;QAK3D,OAAO,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC;AAEhD,QAAA,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,EAAE;YACjE,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;AACvC,SAAA;AAED,QAAA,OAAO,GAAG,CAAC,QAAQ,EAAE,CAAC;KACzB;AAED;;;;;;;;;;;;AAYG;AACH,IAAA,QAAQ,CAAC,GAAW,EAAE,KAAc,EAAE,MAAe,EAAA;AACjD,QAAA,IAAI,OAAO,GAAG,KAAK,WAAW,EAAE;AAC5B,YAAA,MAAM,IAAI,iBAAiB,CAAC,mCAAmC,CAAC,CAAC;AACpE,SAAA;QACD,MAAM,OAAO,GAAG,gBAAgB,CAAC;QACjC,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,IAAI,OAAO,GAAG,KAAK,WAAW,EAAE;AAC5B,YAAA,OAAO,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC;AACxB,SAAA;AACD,QAAA,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE;AAC9B,YAAA,OAAO,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC;AAC5B,SAAA;AACD,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,YAAA,OAAO,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC;AAC9B,SAAA;AACD,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;QAK3D,OAAO,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC;AAEhD,QAAA,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,EAAE;YACjE,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;AACvC,SAAA;AAED,QAAA,OAAO,GAAG,CAAC,QAAQ,EAAE,CAAC;KACzB;AAED;;;;;;;;;;;;;;AAcG;AACH,IAAA,WAAW,CAAC,IAAa,EAAE,KAAc,EAAE,MAAe,EAAE,UAAmB,EAAA;QAC3E,MAAM,OAAO,GAAG,mBAAmB,CAAC;QACpC,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;AAC7B,YAAA,OAAO,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;AAC1B,SAAA;AACD,QAAA,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE;AAC9B,YAAA,OAAO,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC;AAC5B,SAAA;AACD,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,YAAA,OAAO,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC;AAC9B,SAAA;AACD,QAAA,IAAI,OAAO,UAAU,KAAK,WAAW,EAAE;AACnC,YAAA,OAAO,CAAC,YAAY,CAAC,GAAG,UAAU,CAAC;AACtC,SAAA;AACD,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;QAK3D,OAAO,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC;AAEhD,QAAA,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,EAAE;YACjE,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;AACvC,SAAA;AAED,QAAA,OAAO,GAAG,CAAC,QAAQ,EAAE,CAAC;KACzB;AAED;;;;;;;;;;AAUG;AACH,IAAA,KAAK,CAAC,IAAY,EAAE,IAAa,EAAE,MAAe,EAAE,QAAkB,EAAA;AAClE,QAAA,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;AAC7B,YAAA,MAAM,IAAI,iBAAiB,CAAC,oCAAoC,CAAC,CAAC;AACrE,SAAA;QACD,MAAM,OAAO,GAAG,aAAa,CAAC;QAC9B,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;AAC7B,YAAA,OAAO,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;AAC1B,SAAA;AACD,QAAA,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;AAC7B,YAAA,OAAO,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;AAC1B,SAAA;AACD,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,YAAA,OAAO,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC;AAC9B,SAAA;AACD,QAAA,IAAI,OAAO,QAAQ,KAAK,WAAW,EAAE;AACjC,YAAA,OAAO,CAAC,UAAU,CAAC,GAAG,QAAQ,CAAC;AAClC,SAAA;AACD,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;QAK3D,OAAO,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC;AAEhD,QAAA,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,EAAE;YACjE,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;AACvC,SAAA;AAED,QAAA,OAAO,GAAG,CAAC,QAAQ,EAAE,CAAC;KACzB;AACJ;;MCvSY,SAAS,CAAA;AAGlB,IAAA,WAAA,CAAY,MAAc,EAAA;AACtB,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;KACxB;AAED;;;;;;;;AAQG;AACH,IAAA,aAAa,CAA4D,UAAkB,EAAE,YAAoB,EAAE,OAAkB,EAAA;AACjI,QAAA,IAAI,OAAO,UAAU,KAAK,WAAW,EAAE;AACnC,YAAA,MAAM,IAAI,iBAAiB,CAAC,0CAA0C,CAAC,CAAC;AAC3E,SAAA;AACD,QAAA,IAAI,OAAO,YAAY,KAAK,WAAW,EAAE;AACrC,YAAA,MAAM,IAAI,iBAAiB,CAAC,4CAA4C,CAAC,CAAC;AAC7E,SAAA;AACD,QAAA,MAAM,OAAO,GAAG,8DAA8D,CAAC,OAAO,CAAC,cAAc,EAAE,UAAU,CAAC,CAAC,OAAO,CAAC,gBAAgB,EAAE,YAAY,CAAC,CAAC;QAC3J,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,IAAI,OAAO,OAAO,KAAK,WAAW,EAAE;AAChC,YAAA,OAAO,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC;AAChC,SAAA;AACD,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;QAE3D,MAAM,UAAU,GAAiC,EAChD,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,KAAK,EACL,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AAED;;;;;;;;;;AAUG;IACH,cAAc,CAA4D,UAAkB,EAAE,YAAoB,EAAE,UAAkB,EAAE,IAAsH,EAAE,WAAsB,EAAA;AAClR,QAAA,IAAI,OAAO,UAAU,KAAK,WAAW,EAAE;AACnC,YAAA,MAAM,IAAI,iBAAiB,CAAC,0CAA0C,CAAC,CAAC;AAC3E,SAAA;AACD,QAAA,IAAI,OAAO,YAAY,KAAK,WAAW,EAAE;AACrC,YAAA,MAAM,IAAI,iBAAiB,CAAC,4CAA4C,CAAC,CAAC;AAC7E,SAAA;AACD,QAAA,IAAI,OAAO,UAAU,KAAK,WAAW,EAAE;AACnC,YAAA,MAAM,IAAI,iBAAiB,CAAC,0CAA0C,CAAC,CAAC;AAC3E,SAAA;AACD,QAAA,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;AAC7B,YAAA,MAAM,IAAI,iBAAiB,CAAC,oCAAoC,CAAC,CAAC;AACrE,SAAA;AACD,QAAA,MAAM,OAAO,GAAG,8DAA8D,CAAC,OAAO,CAAC,cAAc,EAAE,UAAU,CAAC,CAAC,OAAO,CAAC,gBAAgB,EAAE,YAAY,CAAC,CAAC;QAC3J,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,IAAI,OAAO,UAAU,KAAK,WAAW,EAAE;AACnC,YAAA,OAAO,CAAC,YAAY,CAAC,GAAG,UAAU,CAAC;AACtC,SAAA;AACD,QAAA,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;AAC7B,YAAA,OAAO,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;AAC1B,SAAA;AACD,QAAA,IAAI,OAAO,WAAW,KAAK,WAAW,EAAE;AACpC,YAAA,OAAO,CAAC,aAAa,CAAC,GAAG,WAAW,CAAC;AACxC,SAAA;AACD,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;AAE3D,QAAA,MAAM,UAAU,GAAiC;AAC7C,YAAA,cAAc,EAAE,kBAAkB;SACrC,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,MAAM,EACN,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AAED;;;;;;;;;AASG;AACH,IAAA,WAAW,CAA4D,UAAkB,EAAE,YAAoB,EAAE,UAAkB,EAAE,OAAkB,EAAA;AACnJ,QAAA,IAAI,OAAO,UAAU,KAAK,WAAW,EAAE;AACnC,YAAA,MAAM,IAAI,iBAAiB,CAAC,0CAA0C,CAAC,CAAC;AAC3E,SAAA;AACD,QAAA,IAAI,OAAO,YAAY,KAAK,WAAW,EAAE;AACrC,YAAA,MAAM,IAAI,iBAAiB,CAAC,4CAA4C,CAAC,CAAC;AAC7E,SAAA;AACD,QAAA,IAAI,OAAO,UAAU,KAAK,WAAW,EAAE;AACnC,YAAA,MAAM,IAAI,iBAAiB,CAAC,0CAA0C,CAAC,CAAC;AAC3E,SAAA;QACD,MAAM,OAAO,GAAG,2EAA2E,CAAC,OAAO,CAAC,cAAc,EAAE,UAAU,CAAC,CAAC,OAAO,CAAC,gBAAgB,EAAE,YAAY,CAAC,CAAC,OAAO,CAAC,cAAc,EAAE,UAAU,CAAC,CAAC;QAC5M,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,IAAI,OAAO,OAAO,KAAK,WAAW,EAAE;AAChC,YAAA,OAAO,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC;AAChC,SAAA;AACD,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;QAE3D,MAAM,UAAU,GAAiC,EAChD,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,KAAK,EACL,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AAED;;;;;;;;;;;;AAYG;IACH,cAAc,CAA4D,UAAkB,EAAE,YAAoB,EAAE,UAAkB,EAAE,IAAY,EAAE,WAAsB,EAAA;AACxK,QAAA,IAAI,OAAO,UAAU,KAAK,WAAW,EAAE;AACnC,YAAA,MAAM,IAAI,iBAAiB,CAAC,0CAA0C,CAAC,CAAC;AAC3E,SAAA;AACD,QAAA,IAAI,OAAO,YAAY,KAAK,WAAW,EAAE;AACrC,YAAA,MAAM,IAAI,iBAAiB,CAAC,4CAA4C,CAAC,CAAC;AAC7E,SAAA;AACD,QAAA,IAAI,OAAO,UAAU,KAAK,WAAW,EAAE;AACnC,YAAA,MAAM,IAAI,iBAAiB,CAAC,0CAA0C,CAAC,CAAC;AAC3E,SAAA;AACD,QAAA,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;AAC7B,YAAA,MAAM,IAAI,iBAAiB,CAAC,oCAAoC,CAAC,CAAC;AACrE,SAAA;QACD,MAAM,OAAO,GAAG,2EAA2E,CAAC,OAAO,CAAC,cAAc,EAAE,UAAU,CAAC,CAAC,OAAO,CAAC,gBAAgB,EAAE,YAAY,CAAC,CAAC,OAAO,CAAC,cAAc,EAAE,UAAU,CAAC,CAAC;QAC5M,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;AAC7B,YAAA,OAAO,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;AAC1B,SAAA;AACD,QAAA,IAAI,OAAO,WAAW,KAAK,WAAW,EAAE;AACpC,YAAA,OAAO,CAAC,aAAa,CAAC,GAAG,WAAW,CAAC;AACxC,SAAA;AACD,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;AAE3D,QAAA,MAAM,UAAU,GAAiC;AAC7C,YAAA,cAAc,EAAE,kBAAkB;SACrC,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,KAAK,EACL,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AAED;;;;;;;;;;AAUG;IACH,cAAc,CAA4D,UAAkB,EAAE,YAAoB,EAAE,UAAkB,EAAE,IAAgI,EAAE,WAAsB,EAAA;AAC5R,QAAA,IAAI,OAAO,UAAU,KAAK,WAAW,EAAE;AACnC,YAAA,MAAM,IAAI,iBAAiB,CAAC,0CAA0C,CAAC,CAAC;AAC3E,SAAA;AACD,QAAA,IAAI,OAAO,YAAY,KAAK,WAAW,EAAE;AACrC,YAAA,MAAM,IAAI,iBAAiB,CAAC,4CAA4C,CAAC,CAAC;AAC7E,SAAA;AACD,QAAA,IAAI,OAAO,UAAU,KAAK,WAAW,EAAE;AACnC,YAAA,MAAM,IAAI,iBAAiB,CAAC,0CAA0C,CAAC,CAAC;AAC3E,SAAA;QACD,MAAM,OAAO,GAAG,2EAA2E,CAAC,OAAO,CAAC,cAAc,EAAE,UAAU,CAAC,CAAC,OAAO,CAAC,gBAAgB,EAAE,YAAY,CAAC,CAAC,OAAO,CAAC,cAAc,EAAE,UAAU,CAAC,CAAC;QAC5M,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;AAC7B,YAAA,OAAO,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;AAC1B,SAAA;AACD,QAAA,IAAI,OAAO,WAAW,KAAK,WAAW,EAAE;AACpC,YAAA,OAAO,CAAC,aAAa,CAAC,GAAG,WAAW,CAAC;AACxC,SAAA;AACD,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;AAE3D,QAAA,MAAM,UAAU,GAAiC;AAC7C,YAAA,cAAc,EAAE,kBAAkB;SACrC,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,OAAO,EACP,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AAED;;;;;;;;AAQG;AACH,IAAA,cAAc,CAAC,UAAkB,EAAE,YAAoB,EAAE,UAAkB,EAAA;AACvE,QAAA,IAAI,OAAO,UAAU,KAAK,WAAW,EAAE;AACnC,YAAA,MAAM,IAAI,iBAAiB,CAAC,0CAA0C,CAAC,CAAC;AAC3E,SAAA;AACD,QAAA,IAAI,OAAO,YAAY,KAAK,WAAW,EAAE;AACrC,YAAA,MAAM,IAAI,iBAAiB,CAAC,4CAA4C,CAAC,CAAC;AAC7E,SAAA;AACD,QAAA,IAAI,OAAO,UAAU,KAAK,WAAW,EAAE;AACnC,YAAA,MAAM,IAAI,iBAAiB,CAAC,0CAA0C,CAAC,CAAC;AAC3E,SAAA;QACD,MAAM,OAAO,GAAG,2EAA2E,CAAC,OAAO,CAAC,cAAc,EAAE,UAAU,CAAC,CAAC,OAAO,CAAC,gBAAgB,EAAE,YAAY,CAAC,CAAC,OAAO,CAAC,cAAc,EAAE,UAAU,CAAC,CAAC;QAC5M,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;AAE3D,QAAA,MAAM,UAAU,GAAiC;AAC7C,YAAA,cAAc,EAAE,kBAAkB;SACrC,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,QAAQ,EACR,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AAED;;;;;;;;;;;AAWG;IACH,0BAA0B,CAA4D,UAAkB,EAAE,YAAoB,EAAE,UAAkB,EAAE,SAAiB,EAAE,KAAc,EAAE,GAAY,EAAA;AAC/L,QAAA,IAAI,OAAO,UAAU,KAAK,WAAW,EAAE;AACnC,YAAA,MAAM,IAAI,iBAAiB,CAAC,0CAA0C,CAAC,CAAC;AAC3E,SAAA;AACD,QAAA,IAAI,OAAO,YAAY,KAAK,WAAW,EAAE;AACrC,YAAA,MAAM,IAAI,iBAAiB,CAAC,4CAA4C,CAAC,CAAC;AAC7E,SAAA;AACD,QAAA,IAAI,OAAO,UAAU,KAAK,WAAW,EAAE;AACnC,YAAA,MAAM,IAAI,iBAAiB,CAAC,0CAA0C,CAAC,CAAC;AAC3E,SAAA;AACD,QAAA,IAAI,OAAO,SAAS,KAAK,WAAW,EAAE;AAClC,YAAA,MAAM,IAAI,iBAAiB,CAAC,yCAAyC,CAAC,CAAC;AAC1E,SAAA;AACD,QAAA,MAAM,OAAO,GAAG,iGAAiG,CAAC,OAAO,CAAC,cAAc,EAAE,UAAU,CAAC,CAAC,OAAO,CAAC,gBAAgB,EAAE,YAAY,CAAC,CAAC,OAAO,CAAC,cAAc,EAAE,UAAU,CAAC,CAAC,OAAO,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;QACpQ,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE;AAC9B,YAAA,OAAO,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC;AAC5B,SAAA;AACD,QAAA,IAAI,OAAO,GAAG,KAAK,WAAW,EAAE;AAC5B,YAAA,OAAO,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC;AACxB,SAAA;AACD,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;AAE3D,QAAA,MAAM,UAAU,GAAiC;AAC7C,YAAA,cAAc,EAAE,kBAAkB;SACrC,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,OAAO,EACP,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AAED;;;;;;;;;;;AAWG;IACH,0BAA0B,CAA4D,UAAkB,EAAE,YAAoB,EAAE,UAAkB,EAAE,SAAiB,EAAE,KAAc,EAAE,GAAY,EAAA;AAC/L,QAAA,IAAI,OAAO,UAAU,KAAK,WAAW,EAAE;AACnC,YAAA,MAAM,IAAI,iBAAiB,CAAC,0CAA0C,CAAC,CAAC;AAC3E,SAAA;AACD,QAAA,IAAI,OAAO,YAAY,KAAK,WAAW,EAAE;AACrC,YAAA,MAAM,IAAI,iBAAiB,CAAC,4CAA4C,CAAC,CAAC;AAC7E,SAAA;AACD,QAAA,IAAI,OAAO,UAAU,KAAK,WAAW,EAAE;AACnC,YAAA,MAAM,IAAI,iBAAiB,CAAC,0CAA0C,CAAC,CAAC;AAC3E,SAAA;AACD,QAAA,IAAI,OAAO,SAAS,KAAK,WAAW,EAAE;AAClC,YAAA,MAAM,IAAI,iBAAiB,CAAC,yCAAyC,CAAC,CAAC;AAC1E,SAAA;AACD,QAAA,MAAM,OAAO,GAAG,iGAAiG,CAAC,OAAO,CAAC,cAAc,EAAE,UAAU,CAAC,CAAC,OAAO,CAAC,gBAAgB,EAAE,YAAY,CAAC,CAAC,OAAO,CAAC,cAAc,EAAE,UAAU,CAAC,CAAC,OAAO,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;QACpQ,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE;AAC9B,YAAA,OAAO,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC;AAC5B,SAAA;AACD,QAAA,IAAI,OAAO,GAAG,KAAK,WAAW,EAAE;AAC5B,YAAA,OAAO,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC;AACxB,SAAA;AACD,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;AAE3D,QAAA,MAAM,UAAU,GAAiC;AAC7C,YAAA,cAAc,EAAE,kBAAkB;SACrC,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,OAAO,EACP,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AACJ;;MC1VY,SAAS,CAAA;AAGlB,IAAA,WAAA,CAAY,MAAc,EAAA;AACtB,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;KACxB;AAED;;;;;;;AAOG;IACH,cAAc,CAAC,UAAkB,EAAE,OAAkB,EAAA;AACjD,QAAA,IAAI,OAAO,UAAU,KAAK,WAAW,EAAE;AACnC,YAAA,MAAM,IAAI,iBAAiB,CAAC,0CAA0C,CAAC,CAAC;AAC3E,SAAA;QACD,MAAM,OAAO,GAAG,oCAAoC,CAAC,OAAO,CAAC,cAAc,EAAE,UAAU,CAAC,CAAC;QACzF,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,IAAI,OAAO,OAAO,KAAK,WAAW,EAAE;AAChC,YAAA,OAAO,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC;AAChC,SAAA;AACD,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;QAE3D,MAAM,UAAU,GAAiC,EAChD,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,KAAK,EACL,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AAED;;;;;;;;;;;;AAYG;AACH,IAAA,eAAe,CAAC,UAAkB,EAAE,IAAa,EAAE,KAAe,EAAE,KAAc,EAAE,MAAwB,EAAE,OAAgB,EAAE,WAAoB,EAAA;AAChJ,QAAA,IAAI,OAAO,UAAU,KAAK,WAAW,EAAE;AACnC,YAAA,MAAM,IAAI,iBAAiB,CAAC,0CAA0C,CAAC,CAAC;AAC3E,SAAA;QACD,MAAM,OAAO,GAAG,oCAAoC,CAAC,OAAO,CAAC,cAAc,EAAE,UAAU,CAAC,CAAC;QACzF,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;AAC7B,YAAA,OAAO,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;AAC1B,SAAA;AACD,QAAA,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE;AAC9B,YAAA,OAAO,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC;AAC5B,SAAA;AACD,QAAA,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE;AAC9B,YAAA,OAAO,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC;AAC3B,SAAA;AACD,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,YAAA,OAAO,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC;AAC9B,SAAA;AACD,QAAA,IAAI,OAAO,OAAO,KAAK,WAAW,EAAE;AAChC,YAAA,OAAO,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC;AAChC,SAAA;AACD,QAAA,IAAI,OAAO,WAAW,KAAK,WAAW,EAAE;AACpC,YAAA,OAAO,CAAC,aAAa,CAAC,GAAG,WAAW,CAAC;AACxC,SAAA;AACD,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;AAE3D,QAAA,MAAM,UAAU,GAAiC;AAC7C,YAAA,cAAc,EAAE,kBAAkB;SACrC,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,MAAM,EACN,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AAED;;;;;;;AAOG;IACH,YAAY,CAAC,UAAkB,EAAE,WAAmB,EAAA;AAChD,QAAA,IAAI,OAAO,UAAU,KAAK,WAAW,EAAE;AACnC,YAAA,MAAM,IAAI,iBAAiB,CAAC,0CAA0C,CAAC,CAAC;AAC3E,SAAA;AACD,QAAA,IAAI,OAAO,WAAW,KAAK,WAAW,EAAE;AACpC,YAAA,MAAM,IAAI,iBAAiB,CAAC,2CAA2C,CAAC,CAAC;AAC5E,SAAA;AACD,QAAA,MAAM,OAAO,GAAG,kDAAkD,CAAC,OAAO,CAAC,cAAc,EAAE,UAAU,CAAC,CAAC,OAAO,CAAC,eAAe,EAAE,WAAW,CAAC,CAAC;QAC7I,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;QAE3D,MAAM,UAAU,GAAiC,EAChD,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,KAAK,EACL,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AACJ;;MCtHY,OAAO,CAAA;AAGhB,IAAA,WAAA,CAAY,MAAc,EAAA;AACtB,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;KACxB;AAED;;;;;;AAMG;AACH,IAAA,KAAK,CAAC,KAAa,EAAA;AACf,QAAA,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE;AAC9B,YAAA,MAAM,IAAI,iBAAiB,CAAC,qCAAqC,CAAC,CAAC;AACtE,SAAA;QACD,MAAM,OAAO,GAAG,UAAU,CAAC;QAC3B,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE;AAC9B,YAAA,OAAO,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC;AAC5B,SAAA;AACD,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;AAE3D,QAAA,MAAM,UAAU,GAAiC;AAC7C,YAAA,eAAe,EAAE,MAAM;AACvB,YAAA,cAAc,EAAE,kBAAkB;SACrC,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,MAAM,EACN,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AAED;;;;;;AAMG;AACH,IAAA,QAAQ,CAAC,KAAa,EAAA;AAClB,QAAA,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE;AAC9B,YAAA,MAAM,IAAI,iBAAiB,CAAC,qCAAqC,CAAC,CAAC;AACtE,SAAA;QACD,MAAM,OAAO,GAAG,mBAAmB,CAAC;QACpC,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE;AAC9B,YAAA,OAAO,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC;AAC5B,SAAA;AACD,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;AAE3D,QAAA,MAAM,UAAU,GAAiC;AAC7C,YAAA,eAAe,EAAE,MAAM;AACvB,YAAA,cAAc,EAAE,kBAAkB;SACrC,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,MAAM,EACN,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AACJ;;MCpEY,MAAM,CAAA;AAGf,IAAA,WAAA,CAAY,MAAc,EAAA;AACtB,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;KACxB;AAED;;;;;;;AAOG;IACH,GAAG,GAAA;QACC,MAAM,OAAO,GAAG,SAAS,CAAC;QAC1B,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;QAE3D,MAAM,UAAU,GAAiC,EAChD,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,KAAK,EACL,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AAED;;;;;AAKG;IACH,SAAS,GAAA;QACL,MAAM,OAAO,GAAG,eAAe,CAAC;QAChC,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;QAE3D,MAAM,UAAU,GAAiC,EAChD,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,KAAK,EACL,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AAED;;;;;AAKG;IACH,cAAc,GAAA;QACV,MAAM,OAAO,GAAG,oBAAoB,CAAC;QACrC,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;QAE3D,MAAM,UAAU,GAAiC,EAChD,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,KAAK,EACL,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AAED;;;;;AAKG;IACH,aAAa,GAAA;QACT,MAAM,OAAO,GAAG,mBAAmB,CAAC;QACpC,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;QAE3D,MAAM,UAAU,GAAiC,EAChD,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,KAAK,EACL,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AAED;;;;;AAKG;IACH,eAAe,GAAA;QACX,MAAM,OAAO,GAAG,sBAAsB,CAAC;QACvC,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;QAE3D,MAAM,UAAU,GAAiC,EAChD,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,KAAK,EACL,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AAED;;;;;AAKG;IACH,mBAAmB,GAAA;QACf,MAAM,OAAO,GAAG,0BAA0B,CAAC;QAC3C,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;QAE3D,MAAM,UAAU,GAAiC,EAChD,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,KAAK,EACL,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AAED;;;;;AAKG;IACH,cAAc,GAAA;QACV,MAAM,OAAO,GAAG,oBAAoB,CAAC;QACrC,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;QAE3D,MAAM,UAAU,GAAiC,EAChD,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,KAAK,EACL,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AAED;;;;;AAKG;IACH,aAAa,GAAA;QACT,MAAM,OAAO,GAAG,mBAAmB,CAAC;QACpC,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;QAE3D,MAAM,UAAU,GAAiC,EAChD,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,KAAK,EACL,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AACJ;;MCxLY,SAAS,CAAA;AAGlB,IAAA,WAAA,CAAY,MAAc,EAAA;AACtB,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;KACxB;AAED;;;;;;;;AAQG;AACH,IAAA,gBAAgB,CAAC,OAAe,EAAE,YAAoB,EAAE,QAAgB,EAAA;AACpE,QAAA,IAAI,OAAO,OAAO,KAAK,WAAW,EAAE;AAChC,YAAA,MAAM,IAAI,iBAAiB,CAAC,uCAAuC,CAAC,CAAC;AACxE,SAAA;AACD,QAAA,IAAI,OAAO,YAAY,KAAK,WAAW,EAAE;AACrC,YAAA,MAAM,IAAI,iBAAiB,CAAC,4CAA4C,CAAC,CAAC;AAC7E,SAAA;AACD,QAAA,IAAI,OAAO,QAAQ,KAAK,WAAW,EAAE;AACjC,YAAA,MAAM,IAAI,iBAAiB,CAAC,wCAAwC,CAAC,CAAC;AACzE,SAAA;QACD,MAAM,OAAO,GAAG,yCAAyC,CAAC,OAAO,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;QACxF,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,IAAI,OAAO,YAAY,KAAK,WAAW,EAAE;AACrC,YAAA,OAAO,CAAC,cAAc,CAAC,GAAG,YAAY,CAAC;AAC1C,SAAA;AACD,QAAA,IAAI,OAAO,QAAQ,KAAK,WAAW,EAAE;AACjC,YAAA,OAAO,CAAC,UAAU,CAAC,GAAG,QAAQ,CAAC;AAClC,SAAA;AACD,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;AAE3D,QAAA,MAAM,UAAU,GAAiC;AAC7C,YAAA,cAAc,EAAE,kBAAkB;SACrC,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,MAAM,EACN,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AAED;;;;;;;AAOG;IACH,gBAAgB,CAAC,OAAe,EAAE,YAAoB,EAAA;AAClD,QAAA,IAAI,OAAO,OAAO,KAAK,WAAW,EAAE;AAChC,YAAA,MAAM,IAAI,iBAAiB,CAAC,uCAAuC,CAAC,CAAC;AACxE,SAAA;AACD,QAAA,IAAI,OAAO,YAAY,KAAK,WAAW,EAAE;AACrC,YAAA,MAAM,IAAI,iBAAiB,CAAC,4CAA4C,CAAC,CAAC;AAC7E,SAAA;AACD,QAAA,MAAM,OAAO,GAAG,wDAAwD,CAAC,OAAO,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC,OAAO,CAAC,gBAAgB,EAAE,YAAY,CAAC,CAAC;QAC/I,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;AAE3D,QAAA,MAAM,UAAU,GAAiC;AAC7C,YAAA,cAAc,EAAE,kBAAkB;SACrC,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,QAAQ,EACR,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AACJ;;MC5EY,OAAO,CAAA;AAGhB,IAAA,WAAA,CAAY,MAAc,EAAA;AACtB,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;KACxB;AAED;;;;;;;;AAQG;AACH,IAAA,SAAS,CAAC,QAAgB,EAAE,OAAkB,EAAE,MAAe,EAAA;AAC3D,QAAA,IAAI,OAAO,QAAQ,KAAK,WAAW,EAAE;AACjC,YAAA,MAAM,IAAI,iBAAiB,CAAC,wCAAwC,CAAC,CAAC;AACzE,SAAA;QACD,MAAM,OAAO,GAAG,mCAAmC,CAAC,OAAO,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;QACpF,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,IAAI,OAAO,OAAO,KAAK,WAAW,EAAE;AAChC,YAAA,OAAO,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC;AAChC,SAAA;AACD,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,YAAA,OAAO,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC;AAC9B,SAAA;AACD,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;QAE3D,MAAM,UAAU,GAAiC,EAChD,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,KAAK,EACL,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AAED;;;;;;;;;;;;;;;;AAgBG;AACH,IAAA,UAAU,CAAC,QAAgB,EAAE,MAAc,EAAE,IAAU,EAAE,WAAsB,EAAE,aAAa,CAAC,QAAwB,QAAO,EAAA;AAC1H,QAAA,IAAI,OAAO,QAAQ,KAAK,WAAW,EAAE;AACjC,YAAA,MAAM,IAAI,iBAAiB,CAAC,wCAAwC,CAAC,CAAC;AACzE,SAAA;AACD,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,YAAA,MAAM,IAAI,iBAAiB,CAAC,sCAAsC,CAAC,CAAC;AACvE,SAAA;AACD,QAAA,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;AAC7B,YAAA,MAAM,IAAI,iBAAiB,CAAC,oCAAoC,CAAC,CAAC;AACrE,SAAA;QACD,MAAM,OAAO,GAAG,mCAAmC,CAAC,OAAO,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;QACpF,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,YAAA,OAAO,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC;AAC9B,SAAA;AACD,QAAA,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;AAC7B,YAAA,OAAO,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;AAC1B,SAAA;AACD,QAAA,IAAI,OAAO,WAAW,KAAK,WAAW,EAAE;AACpC,YAAA,OAAO,CAAC,aAAa,CAAC,GAAG,WAAW,CAAC;AACxC,SAAA;AACD,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;AAE3D,QAAA,MAAM,UAAU,GAAiC;AAC7C,YAAA,cAAc,EAAE,qBAAqB;SACxC,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,aAAa,CAC5B,MAAM,EACN,GAAG,EACH,UAAU,EACV,OAAO,EACP,UAAU,CACb,CAAC;KACL;AAED;;;;;;;AAOG;IACH,OAAO,CAAC,QAAgB,EAAE,MAAc,EAAA;AACpC,QAAA,IAAI,OAAO,QAAQ,KAAK,WAAW,EAAE;AACjC,YAAA,MAAM,IAAI,iBAAiB,CAAC,wCAAwC,CAAC,CAAC;AACzE,SAAA;AACD,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,YAAA,MAAM,IAAI,iBAAiB,CAAC,sCAAsC,CAAC,CAAC;AACvE,SAAA;AACD,QAAA,MAAM,OAAO,GAAG,4CAA4C,CAAC,OAAO,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC,OAAO,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;QACzH,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;QAE3D,MAAM,UAAU,GAAiC,EAChD,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,KAAK,EACL,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AAED;;;;;;;;;AASG;AACH,IAAA,UAAU,CAAC,QAAgB,EAAE,MAAc,EAAE,IAAa,EAAE,WAAsB,EAAA;AAC9E,QAAA,IAAI,OAAO,QAAQ,KAAK,WAAW,EAAE;AACjC,YAAA,MAAM,IAAI,iBAAiB,CAAC,wCAAwC,CAAC,CAAC;AACzE,SAAA;AACD,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,YAAA,MAAM,IAAI,iBAAiB,CAAC,sCAAsC,CAAC,CAAC;AACvE,SAAA;AACD,QAAA,MAAM,OAAO,GAAG,4CAA4C,CAAC,OAAO,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC,OAAO,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;QACzH,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;AAC7B,YAAA,OAAO,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;AAC1B,SAAA;AACD,QAAA,IAAI,OAAO,WAAW,KAAK,WAAW,EAAE;AACpC,YAAA,OAAO,CAAC,aAAa,CAAC,GAAG,WAAW,CAAC;AACxC,SAAA;AACD,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;AAE3D,QAAA,MAAM,UAAU,GAAiC;AAC7C,YAAA,cAAc,EAAE,kBAAkB;SACrC,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,KAAK,EACL,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AAED;;;;;;;AAOG;IACH,UAAU,CAAC,QAAgB,EAAE,MAAc,EAAA;AACvC,QAAA,IAAI,OAAO,QAAQ,KAAK,WAAW,EAAE;AACjC,YAAA,MAAM,IAAI,iBAAiB,CAAC,wCAAwC,CAAC,CAAC;AACzE,SAAA;AACD,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,YAAA,MAAM,IAAI,iBAAiB,CAAC,sCAAsC,CAAC,CAAC;AACvE,SAAA;AACD,QAAA,MAAM,OAAO,GAAG,4CAA4C,CAAC,OAAO,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC,OAAO,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;QACzH,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;AAE3D,QAAA,MAAM,UAAU,GAAiC;AAC7C,YAAA,cAAc,EAAE,kBAAkB;SACrC,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,QAAQ,EACR,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AAED;;;;;;;;AAQG;AACH,IAAA,eAAe,CAAC,QAAgB,EAAE,MAAc,EAAE,KAAc,EAAA;AAC5D,QAAA,IAAI,OAAO,QAAQ,KAAK,WAAW,EAAE;AACjC,YAAA,MAAM,IAAI,iBAAiB,CAAC,wCAAwC,CAAC,CAAC;AACzE,SAAA;AACD,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,YAAA,MAAM,IAAI,iBAAiB,CAAC,sCAAsC,CAAC,CAAC;AACvE,SAAA;AACD,QAAA,MAAM,OAAO,GAAG,qDAAqD,CAAC,OAAO,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC,OAAO,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;QAClI,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE;AAC9B,YAAA,OAAO,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC;AAC5B,SAAA;AACD,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;QAK3D,OAAO,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC;AAEhD,QAAA,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,EAAE;YACjE,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;AACvC,SAAA;AAED,QAAA,OAAO,GAAG,CAAC,QAAQ,EAAE,CAAC;KACzB;AAED;;;;;;;;;;;;;;;;;;;AAmBG;IACH,cAAc,CAAC,QAAgB,EAAE,MAAc,EAAE,KAAc,EAAE,MAAe,EAAE,OAAsB,EAAE,OAAgB,EAAE,WAAoB,EAAE,WAAoB,EAAE,YAAqB,EAAE,OAAgB,EAAE,QAAiB,EAAE,UAAmB,EAAE,MAAoB,EAAE,KAAc,EAAA;AACzR,QAAA,IAAI,OAAO,QAAQ,KAAK,WAAW,EAAE;AACjC,YAAA,MAAM,IAAI,iBAAiB,CAAC,wCAAwC,CAAC,CAAC;AACzE,SAAA;AACD,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,YAAA,MAAM,IAAI,iBAAiB,CAAC,sCAAsC,CAAC,CAAC;AACvE,SAAA;AACD,QAAA,MAAM,OAAO,GAAG,oDAAoD,CAAC,OAAO,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC,OAAO,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;QACjI,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE;AAC9B,YAAA,OAAO,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC;AAC5B,SAAA;AACD,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,YAAA,OAAO,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC;AAC9B,SAAA;AACD,QAAA,IAAI,OAAO,OAAO,KAAK,WAAW,EAAE;AAChC,YAAA,OAAO,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC;AAChC,SAAA;AACD,QAAA,IAAI,OAAO,OAAO,KAAK,WAAW,EAAE;AAChC,YAAA,OAAO,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC;AAChC,SAAA;AACD,QAAA,IAAI,OAAO,WAAW,KAAK,WAAW,EAAE;AACpC,YAAA,OAAO,CAAC,aAAa,CAAC,GAAG,WAAW,CAAC;AACxC,SAAA;AACD,QAAA,IAAI,OAAO,WAAW,KAAK,WAAW,EAAE;AACpC,YAAA,OAAO,CAAC,aAAa,CAAC,GAAG,WAAW,CAAC;AACxC,SAAA;AACD,QAAA,IAAI,OAAO,YAAY,KAAK,WAAW,EAAE;AACrC,YAAA,OAAO,CAAC,cAAc,CAAC,GAAG,YAAY,CAAC;AAC1C,SAAA;AACD,QAAA,IAAI,OAAO,OAAO,KAAK,WAAW,EAAE;AAChC,YAAA,OAAO,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC;AAChC,SAAA;AACD,QAAA,IAAI,OAAO,QAAQ,KAAK,WAAW,EAAE;AACjC,YAAA,OAAO,CAAC,UAAU,CAAC,GAAG,QAAQ,CAAC;AAClC,SAAA;AACD,QAAA,IAAI,OAAO,UAAU,KAAK,WAAW,EAAE;AACnC,YAAA,OAAO,CAAC,YAAY,CAAC,GAAG,UAAU,CAAC;AACtC,SAAA;AACD,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,YAAA,OAAO,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC;AAC9B,SAAA;AACD,QAAA,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE;AAC9B,YAAA,OAAO,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC;AAC5B,SAAA;AACD,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;QAK3D,OAAO,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC;AAEhD,QAAA,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,EAAE;YACjE,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;AACvC,SAAA;AAED,QAAA,OAAO,GAAG,CAAC,QAAQ,EAAE,CAAC;KACzB;AAED;;;;;;;;AAQG;AACH,IAAA,WAAW,CAAC,QAAgB,EAAE,MAAc,EAAE,KAAc,EAAA;AACxD,QAAA,IAAI,OAAO,QAAQ,KAAK,WAAW,EAAE;AACjC,YAAA,MAAM,IAAI,iBAAiB,CAAC,wCAAwC,CAAC,CAAC;AACzE,SAAA;AACD,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,YAAA,MAAM,IAAI,iBAAiB,CAAC,sCAAsC,CAAC,CAAC;AACvE,SAAA;AACD,QAAA,MAAM,OAAO,GAAG,iDAAiD,CAAC,OAAO,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC,OAAO,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;QAC9H,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE;AAC9B,YAAA,OAAO,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC;AAC5B,SAAA;AACD,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;QAK3D,OAAO,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC;AAEhD,QAAA,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,EAAE;YACjE,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;AACvC,SAAA;AAED,QAAA,OAAO,GAAG,CAAC,QAAQ,EAAE,CAAC;KACzB;AACJ;;MCxVY,KAAK,CAAA;AAGd,IAAA,WAAA,CAAY,MAAc,EAAA;AACtB,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;KACxB;AAED;;;;;;;AAOG;IACH,IAAI,CAAqE,OAAkB,EAAE,MAAe,EAAA;QACxG,MAAM,OAAO,GAAG,QAAQ,CAAC;QACzB,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,IAAI,OAAO,OAAO,KAAK,WAAW,EAAE;AAChC,YAAA,OAAO,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC;AAChC,SAAA;AACD,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,YAAA,OAAO,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC;AAC9B,SAAA;AACD,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;QAE3D,MAAM,UAAU,GAAiC,EAChD,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,KAAK,EACL,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AAED;;;;;;;;AAQG;AACH,IAAA,MAAM,CAAqE,MAAc,EAAE,IAAY,EAAE,KAAgB,EAAA;AACrH,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,YAAA,MAAM,IAAI,iBAAiB,CAAC,sCAAsC,CAAC,CAAC;AACvE,SAAA;AACD,QAAA,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;AAC7B,YAAA,MAAM,IAAI,iBAAiB,CAAC,oCAAoC,CAAC,CAAC;AACrE,SAAA;QACD,MAAM,OAAO,GAAG,QAAQ,CAAC;QACzB,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,YAAA,OAAO,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC;AAC9B,SAAA;AACD,QAAA,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;AAC7B,YAAA,OAAO,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;AAC1B,SAAA;AACD,QAAA,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE;AAC9B,YAAA,OAAO,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC;AAC5B,SAAA;AACD,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;AAE3D,QAAA,MAAM,UAAU,GAAiC;AAC7C,YAAA,cAAc,EAAE,kBAAkB;SACrC,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,MAAM,EACN,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AAED;;;;;;AAMG;AACH,IAAA,GAAG,CAAqE,MAAc,EAAA;AAClF,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,YAAA,MAAM,IAAI,iBAAiB,CAAC,sCAAsC,CAAC,CAAC;AACvE,SAAA;QACD,MAAM,OAAO,GAAG,iBAAiB,CAAC,OAAO,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;QAC9D,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;QAE3D,MAAM,UAAU,GAAiC,EAChD,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,KAAK,EACL,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AAED;;;;;;;AAOG;IACH,UAAU,CAAqE,MAAc,EAAE,IAAY,EAAA;AACvG,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,YAAA,MAAM,IAAI,iBAAiB,CAAC,sCAAsC,CAAC,CAAC;AACvE,SAAA;AACD,QAAA,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;AAC7B,YAAA,MAAM,IAAI,iBAAiB,CAAC,oCAAoC,CAAC,CAAC;AACrE,SAAA;QACD,MAAM,OAAO,GAAG,iBAAiB,CAAC,OAAO,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;QAC9D,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;AAC7B,YAAA,OAAO,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;AAC1B,SAAA;AACD,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;AAE3D,QAAA,MAAM,UAAU,GAAiC;AAC7C,YAAA,cAAc,EAAE,kBAAkB;SACrC,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,KAAK,EACL,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AAED;;;;;;AAMG;AACH,IAAA,MAAM,CAAC,MAAc,EAAA;AACjB,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,YAAA,MAAM,IAAI,iBAAiB,CAAC,sCAAsC,CAAC,CAAC;AACvE,SAAA;QACD,MAAM,OAAO,GAAG,iBAAiB,CAAC,OAAO,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;QAC9D,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;AAE3D,QAAA,MAAM,UAAU,GAAiC;AAC7C,YAAA,cAAc,EAAE,kBAAkB;SACrC,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,QAAQ,EACR,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AAED;;;;;;;;AAQG;AACH,IAAA,eAAe,CAAC,MAAc,EAAE,OAAkB,EAAE,MAAe,EAAA;AAC/D,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,YAAA,MAAM,IAAI,iBAAiB,CAAC,sCAAsC,CAAC,CAAC;AACvE,SAAA;QACD,MAAM,OAAO,GAAG,6BAA6B,CAAC,OAAO,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;QAC1E,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,IAAI,OAAO,OAAO,KAAK,WAAW,EAAE;AAChC,YAAA,OAAO,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC;AAChC,SAAA;AACD,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,YAAA,OAAO,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC;AAC9B,SAAA;AACD,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;QAE3D,MAAM,UAAU,GAAiC,EAChD,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,KAAK,EACL,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AAED;;;;;;;;;;;;;;;;;;;AAmBG;AACH,IAAA,gBAAgB,CAAC,MAAc,EAAE,KAAe,EAAE,KAAc,EAAE,MAAe,EAAE,KAAc,EAAE,GAAY,EAAE,IAAa,EAAA;AAC1H,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,YAAA,MAAM,IAAI,iBAAiB,CAAC,sCAAsC,CAAC,CAAC;AACvE,SAAA;AACD,QAAA,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE;AAC9B,YAAA,MAAM,IAAI,iBAAiB,CAAC,qCAAqC,CAAC,CAAC;AACtE,SAAA;QACD,MAAM,OAAO,GAAG,6BAA6B,CAAC,OAAO,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;QAC1E,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE;AAC9B,YAAA,OAAO,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC;AAC5B,SAAA;AACD,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,YAAA,OAAO,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC;AAC9B,SAAA;AACD,QAAA,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE;AAC9B,YAAA,OAAO,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC;AAC5B,SAAA;AACD,QAAA,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE;AAC9B,YAAA,OAAO,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC;AAC5B,SAAA;AACD,QAAA,IAAI,OAAO,GAAG,KAAK,WAAW,EAAE;AAC5B,YAAA,OAAO,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC;AACxB,SAAA;AACD,QAAA,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;AAC7B,YAAA,OAAO,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;AAC1B,SAAA;AACD,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;AAE3D,QAAA,MAAM,UAAU,GAAiC;AAC7C,YAAA,cAAc,EAAE,kBAAkB;SACrC,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,MAAM,EACN,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AAED;;;;;;;AAOG;IACH,aAAa,CAAC,MAAc,EAAE,YAAoB,EAAA;AAC9C,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,YAAA,MAAM,IAAI,iBAAiB,CAAC,sCAAsC,CAAC,CAAC;AACvE,SAAA;AACD,QAAA,IAAI,OAAO,YAAY,KAAK,WAAW,EAAE;AACrC,YAAA,MAAM,IAAI,iBAAiB,CAAC,4CAA4C,CAAC,CAAC;AAC7E,SAAA;AACD,QAAA,MAAM,OAAO,GAAG,4CAA4C,CAAC,OAAO,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC,OAAO,CAAC,gBAAgB,EAAE,YAAY,CAAC,CAAC;QACjI,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;QAE3D,MAAM,UAAU,GAAiC,EAChD,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,KAAK,EACL,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AAED;;;;;;;;;AASG;AACH,IAAA,gBAAgB,CAAC,MAAc,EAAE,YAAoB,EAAE,KAAe,EAAA;AAClE,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,YAAA,MAAM,IAAI,iBAAiB,CAAC,sCAAsC,CAAC,CAAC;AACvE,SAAA;AACD,QAAA,IAAI,OAAO,YAAY,KAAK,WAAW,EAAE;AACrC,YAAA,MAAM,IAAI,iBAAiB,CAAC,4CAA4C,CAAC,CAAC;AAC7E,SAAA;AACD,QAAA,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE;AAC9B,YAAA,MAAM,IAAI,iBAAiB,CAAC,qCAAqC,CAAC,CAAC;AACtE,SAAA;AACD,QAAA,MAAM,OAAO,GAAG,4CAA4C,CAAC,OAAO,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC,OAAO,CAAC,gBAAgB,EAAE,YAAY,CAAC,CAAC;QACjI,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE;AAC9B,YAAA,OAAO,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC;AAC5B,SAAA;AACD,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;AAE3D,QAAA,MAAM,UAAU,GAAiC;AAC7C,YAAA,cAAc,EAAE,kBAAkB;SACrC,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,OAAO,EACP,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AAED;;;;;;;AAOG;IACH,gBAAgB,CAAC,MAAc,EAAE,YAAoB,EAAA;AACjD,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,YAAA,MAAM,IAAI,iBAAiB,CAAC,sCAAsC,CAAC,CAAC;AACvE,SAAA;AACD,QAAA,IAAI,OAAO,YAAY,KAAK,WAAW,EAAE;AACrC,YAAA,MAAM,IAAI,iBAAiB,CAAC,4CAA4C,CAAC,CAAC;AAC7E,SAAA;AACD,QAAA,MAAM,OAAO,GAAG,4CAA4C,CAAC,OAAO,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC,OAAO,CAAC,gBAAgB,EAAE,YAAY,CAAC,CAAC;QACjI,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;AAE3D,QAAA,MAAM,UAAU,GAAiC;AAC7C,YAAA,cAAc,EAAE,kBAAkB;SACrC,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,QAAQ,EACR,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AAED;;;;;;;;;;;;AAYG;AACH,IAAA,sBAAsB,CAAC,MAAc,EAAE,YAAoB,EAAE,MAAc,EAAE,MAAc,EAAA;AACvF,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,YAAA,MAAM,IAAI,iBAAiB,CAAC,sCAAsC,CAAC,CAAC;AACvE,SAAA;AACD,QAAA,IAAI,OAAO,YAAY,KAAK,WAAW,EAAE;AACrC,YAAA,MAAM,IAAI,iBAAiB,CAAC,4CAA4C,CAAC,CAAC;AAC7E,SAAA;AACD,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,YAAA,MAAM,IAAI,iBAAiB,CAAC,sCAAsC,CAAC,CAAC;AACvE,SAAA;AACD,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,YAAA,MAAM,IAAI,iBAAiB,CAAC,sCAAsC,CAAC,CAAC;AACvE,SAAA;AACD,QAAA,MAAM,OAAO,GAAG,mDAAmD,CAAC,OAAO,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC,OAAO,CAAC,gBAAgB,EAAE,YAAY,CAAC,CAAC;QACxI,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,YAAA,OAAO,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC;AAC9B,SAAA;AACD,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,YAAA,OAAO,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC;AAC9B,SAAA;AACD,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;AAE3D,QAAA,MAAM,UAAU,GAAiC;AAC7C,YAAA,cAAc,EAAE,kBAAkB;SACrC,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,OAAO,EACP,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AAED;;;;;;AAMG;AACH,IAAA,QAAQ,CAAqE,MAAc,EAAA;AACvF,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,YAAA,MAAM,IAAI,iBAAiB,CAAC,sCAAsC,CAAC,CAAC;AACvE,SAAA;QACD,MAAM,OAAO,GAAG,uBAAuB,CAAC,OAAO,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;QACpE,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;QAE3D,MAAM,UAAU,GAAiC,EAChD,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,KAAK,EACL,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AAED;;;;;;;AAOG;IACH,WAAW,CAAqE,MAAc,EAAE,KAAa,EAAA;AACzG,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,YAAA,MAAM,IAAI,iBAAiB,CAAC,sCAAsC,CAAC,CAAC;AACvE,SAAA;AACD,QAAA,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE;AAC9B,YAAA,MAAM,IAAI,iBAAiB,CAAC,qCAAqC,CAAC,CAAC;AACtE,SAAA;QACD,MAAM,OAAO,GAAG,uBAAuB,CAAC,OAAO,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;QACpE,MAAM,OAAO,GAAY,EAAE,CAAC;AAC5B,QAAA,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE;AAC9B,YAAA,OAAO,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC;AAC5B,SAAA;AACD,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;AAE3D,QAAA,MAAM,UAAU,GAAiC;AAC7C,YAAA,cAAc,EAAE,kBAAkB;SACrC,CAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB,KAAK,EACL,GAAG,EACH,UAAU,EACV,OAAO,CACV,CAAC;KACL;AACJ;;ACvdD;;AAEG;MACU,UAAU,CAAA;;AACnB;;;;;AAKG;AACI,UAAA,CAAA,IAAI,GAAG,CAAC,IAAY,KAAY;IACnC,OAAO,CAAA,MAAA,EAAS,IAAI,CAAA,EAAA,CAAI,CAAC;AAC7B,CAAC,CAAA;AAED;;;;;;;;AAQG;AACI,UAAA,CAAA,KAAK,GAAG,CAAC,IAAY,KAAY;IACpC,OAAO,CAAA,OAAA,EAAU,IAAI,CAAA,EAAA,CAAI,CAAC;AAC9B,CAAC,CAAA;AAED;;;;;AAKG;AACI,UAAA,CAAA,MAAM,GAAG,CAAC,IAAY,KAAY;IACrC,OAAO,CAAA,QAAA,EAAW,IAAI,CAAA,EAAA,CAAI,CAAC;AAC/B,CAAC,CAAA;AAED;;;;;AAKG;AACI,UAAA,CAAA,MAAM,GAAG,CAAC,IAAY,KAAY;IACrC,OAAO,CAAA,QAAA,EAAW,IAAI,CAAA,EAAA,CAAI,CAAC;AAC/B,CAAC,CAAA;AAED;;;;;AAKG;AACI,UAAA,CAAA,MAAM,GAAG,CAAC,IAAY,KAAY;IACrC,OAAO,CAAA,QAAA,EAAW,IAAI,CAAA,EAAA,CAAI,CAAC;AAC/B,CAAC;;ACvDL;;AAEG;MACU,IAAI,CAAA;AAEb;;;;;;AAMG;AACI,IAAA,OAAO,GAAG,GAAA;AACb,QAAA,OAAO,KAAK,CAAA;KACf;AAED;;;;;;;;;AASG;AACI,IAAA,OAAO,IAAI,CAAC,EAAU,EAAE,SAAiB,EAAE,EAAA;QAC9C,IAAI,MAAM,KAAK,EAAE,EAAE;YACf,OAAO,CAAA,KAAA,EAAQ,EAAE,CAAA,CAAE,CAAA;AACtB,SAAA;AACD,QAAA,OAAO,CAAQ,KAAA,EAAA,EAAE,CAAI,CAAA,EAAA,MAAM,EAAE,CAAA;KAChC;AAED;;;;;;;;AAQG;AACI,IAAA,OAAO,KAAK,CAAC,MAAA,GAAiB,EAAE,EAAA;QACnC,IAAI,MAAM,KAAK,EAAE,EAAE;AACf,YAAA,OAAO,OAAO,CAAA;AACjB,SAAA;QACD,OAAO,CAAA,MAAA,EAAS,MAAM,CAAA,CAAE,CAAA;KAC3B;AAED;;;;;;AAMG;AACI,IAAA,OAAO,MAAM,GAAA;AAChB,QAAA,OAAO,QAAQ,CAAA;KAClB;AAED;;;;;;;;;AASG;AACI,IAAA,OAAO,IAAI,CAAC,EAAU,EAAE,OAAe,EAAE,EAAA;QAC5C,IAAI,IAAI,KAAK,EAAE,EAAE;YACb,OAAO,CAAA,KAAA,EAAQ,EAAE,CAAA,CAAE,CAAA;AACtB,SAAA;AACD,QAAA,OAAO,CAAQ,KAAA,EAAA,EAAE,CAAI,CAAA,EAAA,IAAI,EAAE,CAAA;KAC9B;AAED;;;;;;;;AAQG;IACI,OAAO,MAAM,CAAC,EAAU,EAAA;QAC3B,OAAO,CAAA,OAAA,EAAU,EAAE,CAAA,CAAE,CAAA;KACxB;AAED;;;;;AAKG;IACI,OAAO,KAAK,CAAC,IAAY,EAAA;QAC5B,OAAO,CAAA,MAAA,EAAS,IAAI,CAAA,CAAE,CAAA;KACzB;AACJ;;;ACnGD;;AAEG;MACU,EAAE,CAAA;AAiBX;;;;;AAKG;IACI,OAAO,MAAM,CAAC,EAAU,EAAA;AAC3B,QAAA,OAAO,EAAE,CAAA;KACZ;AAED;;;;;AAKG;AACI,IAAA,OAAO,MAAM,CAAC,OAAA,GAAkB,CAAC,EAAA;;QAEpC,MAAM,MAAM,GAAG,sBAAA,CAAA,EAAE,4BAAc,CAAhB,IAAA,CAAA,EAAE,CAAgB,CAAC;QAClC,IAAI,aAAa,GAAG,EAAE,CAAC;QACvB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,EAAE,CAAC,EAAE,EAAE;AAC9B,YAAA,MAAM,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YACnE,aAAa,IAAI,cAAc,CAAC;AACnC,SAAA;QACD,OAAO,MAAM,GAAG,aAAa,CAAC;KACjC;AACJ,CAAA;;AAnCO,IAAA,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;AACvB,IAAA,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC;AAC7C,IAAA,MAAM,IAAI,GAAG,GAAG,CAAC,eAAe,EAAE,CAAC;;IAGnC,MAAM,YAAY,GAAG,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;AAC3E,IAAA,OAAO,YAAY,CAAC;AACxB,CAAC;;AClBOA,mCAEX;AAFD,CAAA,UAAY,iBAAiB,EAAA;AACzB,IAAA,iBAAA,CAAA,MAAA,CAAA,GAAA,MAAa,CAAA;AACjB,CAAC,EAFWA,yBAAiB,KAAjBA,yBAAiB,GAE5B,EAAA,CAAA,CAAA;;ACFWC,sCAKX;AALD,CAAA,UAAY,oBAAoB,EAAA;AAC5B,IAAA,oBAAA,CAAA,OAAA,CAAA,GAAA,OAAe,CAAA;AACf,IAAA,oBAAA,CAAA,OAAA,CAAA,GAAA,OAAe,CAAA;AACf,IAAA,oBAAA,CAAA,MAAA,CAAA,GAAA,MAAa,CAAA;AACb,IAAA,oBAAA,CAAA,cAAA,CAAA,GAAA,cAA6B,CAAA;AACjC,CAAC,EALWA,4BAAoB,KAApBA,4BAAoB,GAK/B,EAAA,CAAA,CAAA;;ACLWC,+BAyCX;AAzCD,CAAA,UAAY,aAAa,EAAA;AACrB,IAAA,aAAA,CAAA,QAAA,CAAA,GAAA,QAAiB,CAAA;AACjB,IAAA,aAAA,CAAA,OAAA,CAAA,GAAA,OAAe,CAAA;AACf,IAAA,aAAA,CAAA,OAAA,CAAA,GAAA,OAAe,CAAA;AACf,IAAA,aAAA,CAAA,WAAA,CAAA,GAAA,WAAuB,CAAA;AACvB,IAAA,aAAA,CAAA,UAAA,CAAA,GAAA,UAAqB,CAAA;AACrB,IAAA,aAAA,CAAA,WAAA,CAAA,GAAA,WAAuB,CAAA;AACvB,IAAA,aAAA,CAAA,OAAA,CAAA,GAAA,OAAe,CAAA;AACf,IAAA,aAAA,CAAA,KAAA,CAAA,GAAA,KAAW,CAAA;AACX,IAAA,aAAA,CAAA,aAAA,CAAA,GAAA,aAA2B,CAAA;AAC3B,IAAA,aAAA,CAAA,SAAA,CAAA,GAAA,SAAmB,CAAA;AACnB,IAAA,aAAA,CAAA,QAAA,CAAA,GAAA,QAAiB,CAAA;AACjB,IAAA,aAAA,CAAA,SAAA,CAAA,GAAA,SAAmB,CAAA;AACnB,IAAA,aAAA,CAAA,MAAA,CAAA,GAAA,MAAa,CAAA;AACb,IAAA,aAAA,CAAA,UAAA,CAAA,GAAA,UAAqB,CAAA;AACrB,IAAA,aAAA,CAAA,OAAA,CAAA,GAAA,OAAe,CAAA;AACf,IAAA,aAAA,CAAA,QAAA,CAAA,GAAA,QAAiB,CAAA;AACjB,IAAA,aAAA,CAAA,QAAA,CAAA,GAAA,QAAiB,CAAA;AACjB,IAAA,aAAA,CAAA,QAAA,CAAA,GAAA,QAAiB,CAAA;AACjB,IAAA,aAAA,CAAA,UAAA,CAAA,GAAA,UAAqB,CAAA;AACrB,IAAA,aAAA,CAAA,WAAA,CAAA,GAAA,WAAuB,CAAA;AACvB,IAAA,aAAA,CAAA,QAAA,CAAA,GAAA,QAAiB,CAAA;AACjB,IAAA,aAAA,CAAA,MAAA,CAAA,GAAA,MAAa,CAAA;AACb,IAAA,aAAA,CAAA,MAAA,CAAA,GAAA,MAAa,CAAA;AACb,IAAA,aAAA,CAAA,QAAA,CAAA,GAAA,QAAiB,CAAA;AACjB,IAAA,aAAA,CAAA,eAAA,CAAA,GAAA,eAA+B,CAAA;AAC/B,IAAA,aAAA,CAAA,OAAA,CAAA,GAAA,OAAe,CAAA;AACf,IAAA,aAAA,CAAA,YAAA,CAAA,GAAA,YAAyB,CAAA;AACzB,IAAA,aAAA,CAAA,OAAA,CAAA,GAAA,OAAe,CAAA;AACf,IAAA,aAAA,CAAA,SAAA,CAAA,GAAA,SAAmB,CAAA;AACnB,IAAA,aAAA,CAAA,QAAA,CAAA,GAAA,QAAiB,CAAA;AACjB,IAAA,aAAA,CAAA,YAAA,CAAA,GAAA,YAAyB,CAAA;AACzB,IAAA,aAAA,CAAA,eAAA,CAAA,GAAA,eAA+B,CAAA;AAC/B,IAAA,aAAA,CAAA,QAAA,CAAA,GAAA,QAAiB,CAAA;AACjB,IAAA,aAAA,CAAA,WAAA,CAAA,GAAA,WAAuB,CAAA;AACvB,IAAA,aAAA,CAAA,OAAA,CAAA,GAAA,OAAe,CAAA;AACf,IAAA,aAAA,CAAA,QAAA,CAAA,GAAA,QAAiB,CAAA;AACjB,IAAA,aAAA,CAAA,QAAA,CAAA,GAAA,QAAiB,CAAA;AACjB,IAAA,aAAA,CAAA,MAAA,CAAA,GAAA,MAAa,CAAA;AACb,IAAA,aAAA,CAAA,MAAA,CAAA,GAAA,MAAa,CAAA;AACb,IAAA,aAAA,CAAA,MAAA,CAAA,GAAA,MAAa,CAAA;AACjB,CAAC,EAzCWA,qBAAa,KAAbA,qBAAa,GAyCxB,EAAA,CAAA,CAAA;;ACzCWC,yBAeX;AAfD,CAAA,UAAY,OAAO,EAAA;AACf,IAAA,OAAA,CAAA,cAAA,CAAA,GAAA,IAAmB,CAAA;AACnB,IAAA,OAAA,CAAA,oBAAA,CAAA,GAAA,IAAyB,CAAA;AACzB,IAAA,OAAA,CAAA,cAAA,CAAA,GAAA,IAAmB,CAAA;AACnB,IAAA,OAAA,CAAA,iBAAA,CAAA,GAAA,IAAsB,CAAA;AACtB,IAAA,OAAA,CAAA,oBAAA,CAAA,GAAA,IAAyB,CAAA;AACzB,IAAA,OAAA,CAAA,UAAA,CAAA,GAAA,IAAe,CAAA;AACf,IAAA,OAAA,CAAA,gBAAA,CAAA,GAAA,IAAqB,CAAA;AACrB,IAAA,OAAA,CAAA,QAAA,CAAA,GAAA,IAAa,CAAA;AACb,IAAA,OAAA,CAAA,cAAA,CAAA,GAAA,IAAmB,CAAA;AACnB,IAAA,OAAA,CAAA,eAAA,CAAA,GAAA,IAAoB,CAAA;AACpB,IAAA,OAAA,CAAA,kBAAA,CAAA,GAAA,IAAuB,CAAA;AACvB,IAAA,OAAA,CAAA,WAAA,CAAA,GAAA,IAAgB,CAAA;AAChB,IAAA,OAAA,CAAA,OAAA,CAAA,GAAA,IAAY,CAAA;AACZ,IAAA,OAAA,CAAA,WAAA,CAAA,GAAA,IAAgB,CAAA;AACpB,CAAC,EAfWA,eAAO,KAAPA,eAAO,GAelB,EAAA,CAAA,CAAA;;ACfWC,4BAkBX;AAlBD,CAAA,UAAY,UAAU,EAAA;AAClB,IAAA,UAAA,CAAA,iBAAA,CAAA,GAAA,MAAwB,CAAA;AACxB,IAAA,UAAA,CAAA,WAAA,CAAA,GAAA,WAAuB,CAAA;AACvB,IAAA,UAAA,CAAA,OAAA,CAAA,GAAA,OAAe,CAAA;AACf,IAAA,UAAA,CAAA,UAAA,CAAA,GAAA,UAAqB,CAAA;AACrB,IAAA,UAAA,CAAA,YAAA,CAAA,GAAA,QAAqB,CAAA;AACrB,IAAA,UAAA,CAAA,UAAA,CAAA,GAAA,UAAqB,CAAA;AACrB,IAAA,UAAA,CAAA,KAAA,CAAA,GAAA,KAAW,CAAA;AACX,IAAA,UAAA,CAAA,WAAA,CAAA,GAAA,WAAuB,CAAA;AACvB,IAAA,UAAA,CAAA,KAAA,CAAA,GAAA,KAAW,CAAA;AACX,IAAA,UAAA,CAAA,YAAA,CAAA,GAAA,YAAyB,CAAA;AACzB,IAAA,UAAA,CAAA,SAAA,CAAA,GAAA,SAAmB,CAAA;AACnB,IAAA,UAAA,CAAA,iBAAA,CAAA,GAAA,kBAAoC,CAAA;AACpC,IAAA,UAAA,CAAA,eAAA,CAAA,GAAA,iBAAiC,CAAA;AACjC,IAAA,UAAA,CAAA,MAAA,CAAA,GAAA,MAAa,CAAA;AACb,IAAA,UAAA,CAAA,KAAA,CAAA,GAAA,KAAW,CAAA;AACX,IAAA,UAAA,CAAA,SAAA,CAAA,GAAA,SAAmB,CAAA;AACnB,IAAA,UAAA,CAAA,OAAA,CAAA,GAAA,OAAe,CAAA;AACnB,CAAC,EAlBWA,kBAAU,KAAVA,kBAAU,GAkBrB,EAAA,CAAA,CAAA;;AClBWC,sBAoMX;AApMD,CAAA,UAAY,IAAI,EAAA;AACZ,IAAA,IAAA,CAAA,aAAA,CAAA,GAAA,IAAkB,CAAA;AAClB,IAAA,IAAA,CAAA,QAAA,CAAA,GAAA,IAAa,CAAA;AACb,IAAA,IAAA,CAAA,SAAA,CAAA,GAAA,IAAc,CAAA;AACd,IAAA,IAAA,CAAA,SAAA,CAAA,GAAA,IAAc,CAAA;AACd,IAAA,IAAA,CAAA,oBAAA,CAAA,GAAA,IAAyB,CAAA;AACzB,IAAA,IAAA,CAAA,WAAA,CAAA,GAAA,IAAgB,CAAA;AAChB,IAAA,IAAA,CAAA,SAAA,CAAA,GAAA,IAAc,CAAA;AACd,IAAA,IAAA,CAAA,mBAAA,CAAA,GAAA,IAAwB,CAAA;AACxB,IAAA,IAAA,CAAA,WAAA,CAAA,GAAA,IAAgB,CAAA;AAChB,IAAA,IAAA,CAAA,SAAA,CAAA,GAAA,IAAc,CAAA;AACd,IAAA,IAAA,CAAA,YAAA,CAAA,GAAA,IAAiB,CAAA;AACjB,IAAA,IAAA,CAAA,SAAA,CAAA,GAAA,IAAc,CAAA;AACd,IAAA,IAAA,CAAA,SAAA,CAAA,GAAA,IAAc,CAAA;AACd,IAAA,IAAA,CAAA,OAAA,CAAA,GAAA,IAAY,CAAA;AACZ,IAAA,IAAA,CAAA,aAAA,CAAA,GAAA,IAAkB,CAAA;AAClB,IAAA,IAAA,CAAA,YAAA,CAAA,GAAA,IAAiB,CAAA;AACjB,IAAA,IAAA,CAAA,UAAA,CAAA,GAAA,IAAe,CAAA;AACf,IAAA,IAAA,CAAA,SAAA,CAAA,GAAA,IAAc,CAAA;AACd,IAAA,IAAA,CAAA,SAAA,CAAA,GAAA,IAAc,CAAA;AACd,IAAA,IAAA,CAAA,sBAAA,CAAA,GAAA,IAA2B,CAAA;AAC3B,IAAA,IAAA,CAAA,SAAA,CAAA,GAAA,IAAc,CAAA;AACd,IAAA,IAAA,CAAA,QAAA,CAAA,GAAA,IAAa,CAAA;AACb,IAAA,IAAA,CAAA,SAAA,CAAA,GAAA,IAAc,CAAA;AACd,IAAA,IAAA,CAAA,QAAA,CAAA,GAAA,IAAa,CAAA;AACb,IAAA,IAAA,CAAA,UAAA,CAAA,GAAA,IAAe,CAAA;AACf,IAAA,IAAA,CAAA,kBAAA,CAAA,GAAA,IAAuB,CAAA;AACvB,IAAA,IAAA,CAAA,QAAA,CAAA,GAAA,IAAa,CAAA;AACb,IAAA,IAAA,CAAA,UAAA,CAAA,GAAA,IAAe,CAAA;AACf,IAAA,IAAA,CAAA,wBAAA,CAAA,GAAA,IAA6B,CAAA;AAC7B,IAAA,IAAA,CAAA,QAAA,CAAA,GAAA,IAAa,CAAA;AACb,IAAA,IAAA,CAAA,aAAA,CAAA,GAAA,IAAkB,CAAA;AAClB,IAAA,IAAA,CAAA,OAAA,CAAA,GAAA,IAAY,CAAA;AACZ,IAAA,IAAA,CAAA,OAAA,CAAA,GAAA,IAAY,CAAA;AACZ,IAAA,IAAA,CAAA,aAAA,CAAA,GAAA,IAAkB,CAAA;AAClB,IAAA,IAAA,CAAA,UAAA,CAAA,GAAA,IAAe,CAAA;AACf,IAAA,IAAA,CAAA,8BAAA,CAAA,GAAA,IAAmC,CAAA;AACnC,IAAA,IAAA,CAAA,oBAAA,CAAA,GAAA,IAAyB,CAAA;AACzB,IAAA,IAAA,CAAA,UAAA,CAAA,GAAA,IAAe,CAAA;AACf,IAAA,IAAA,CAAA,SAAA,CAAA,GAAA,IAAc,CAAA;AACd,IAAA,IAAA,CAAA,WAAA,CAAA,GAAA,IAAgB,CAAA;AAChB,IAAA,IAAA,CAAA,WAAA,CAAA,GAAA,IAAgB,CAAA;AAChB,IAAA,IAAA,CAAA,MAAA,CAAA,GAAA,IAAW,CAAA;AACX,IAAA,IAAA,CAAA,QAAA,CAAA,GAAA,IAAa,CAAA;AACb,IAAA,IAAA,CAAA,eAAA,CAAA,GAAA,IAAoB,CAAA;AACpB,IAAA,IAAA,CAAA,SAAA,CAAA,GAAA,IAAc,CAAA;AACd,IAAA,IAAA,CAAA,UAAA,CAAA,GAAA,IAAe,CAAA;AACf,IAAA,IAAA,CAAA,UAAA,CAAA,GAAA,IAAe,CAAA;AACf,IAAA,IAAA,CAAA,SAAA,CAAA,GAAA,IAAc,CAAA;AACd,IAAA,IAAA,CAAA,mBAAA,CAAA,GAAA,IAAwB,CAAA;AACxB,IAAA,IAAA,CAAA,SAAA,CAAA,GAAA,IAAc,CAAA;AACd,IAAA,IAAA,CAAA,SAAA,CAAA,GAAA,IAAc,CAAA;AACd,IAAA,IAAA,CAAA,OAAA,CAAA,GAAA,IAAY,CAAA;AACZ,IAAA,IAAA,CAAA,SAAA,CAAA,GAAA,IAAc,CAAA;AACd,IAAA,IAAA,CAAA,OAAA,CAAA,GAAA,IAAY,CAAA;AACZ,IAAA,IAAA,CAAA,SAAA,CAAA,GAAA,IAAc,CAAA;AACd,IAAA,IAAA,CAAA,UAAA,CAAA,GAAA,IAAe,CAAA;AACf,IAAA,IAAA,CAAA,SAAA,CAAA,GAAA,IAAc,CAAA;AACd,IAAA,IAAA,CAAA,MAAA,CAAA,GAAA,IAAW,CAAA;AACX,IAAA,IAAA,CAAA,QAAA,CAAA,GAAA,IAAa,CAAA;AACb,IAAA,IAAA,CAAA,6BAAA,CAAA,GAAA,IAAkC,CAAA;AAClC,IAAA,IAAA,CAAA,OAAA,CAAA,GAAA,IAAY,CAAA;AACZ,IAAA,IAAA,CAAA,eAAA,CAAA,GAAA,IAAoB,CAAA;AACpB,IAAA,IAAA,CAAA,SAAA,CAAA,GAAA,IAAc,CAAA;AACd,IAAA,IAAA,CAAA,OAAA,CAAA,GAAA,IAAY,CAAA;AACZ,IAAA,IAAA,CAAA,QAAA,CAAA,GAAA,IAAa,CAAA;AACb,IAAA,IAAA,CAAA,QAAA,CAAA,GAAA,IAAa,CAAA;AACb,IAAA,IAAA,CAAA,cAAA,CAAA,GAAA,IAAmB,CAAA;AACnB,IAAA,IAAA,CAAA,kBAAA,CAAA,GAAA,IAAuB,CAAA;AACvB,IAAA,IAAA,CAAA,QAAA,CAAA,GAAA,IAAa,CAAA;AACb,IAAA,IAAA,CAAA,SAAA,CAAA,GAAA,IAAc,CAAA;AACd,IAAA,IAAA,CAAA,WAAA,CAAA,GAAA,IAAgB,CAAA;AAChB,IAAA,IAAA,CAAA,QAAA,CAAA,GAAA,IAAa,CAAA;AACb,IAAA,IAAA,CAAA,UAAA,CAAA,GAAA,IAAe,CAAA;AACf,IAAA,IAAA,CAAA,SAAA,CAAA,GAAA,IAAc,CAAA;AACd,IAAA,IAAA,CAAA,OAAA,CAAA,GAAA,IAAY,CAAA;AACZ,IAAA,IAAA,CAAA,SAAA,CAAA,GAAA,IAAc,CAAA;AACd,IAAA,IAAA,CAAA,WAAA,CAAA,GAAA,IAAgB,CAAA;AAChB,IAAA,IAAA,CAAA,OAAA,CAAA,GAAA,IAAY,CAAA;AACZ,IAAA,IAAA,CAAA,SAAA,CAAA,GAAA,IAAc,CAAA;AACd,IAAA,IAAA,CAAA,uBAAA,CAAA,GAAA,IAA4B,CAAA;AAC5B,IAAA,IAAA,CAAA,MAAA,CAAA,GAAA,IAAW,CAAA;AACX,IAAA,IAAA,CAAA,SAAA,CAAA,GAAA,IAAc,CAAA;AACd,IAAA,IAAA,CAAA,QAAA,CAAA,GAAA,IAAa,CAAA;AACb,IAAA,IAAA,CAAA,OAAA,CAAA,GAAA,IAAY,CAAA;AACZ,IAAA,IAAA,CAAA,SAAA,CAAA,GAAA,IAAc,CAAA;AACd,IAAA,IAAA,CAAA,QAAA,CAAA,GAAA,IAAa,CAAA;AACb,IAAA,IAAA,CAAA,OAAA,CAAA,GAAA,IAAY,CAAA;AACZ,IAAA,IAAA,CAAA,YAAA,CAAA,GAAA,IAAiB,CAAA;AACjB,IAAA,IAAA,CAAA,OAAA,CAAA,GAAA,IAAY,CAAA;AACZ,IAAA,IAAA,CAAA,YAAA,CAAA,GAAA,IAAiB,CAAA;AACjB,IAAA,IAAA,CAAA,UAAA,CAAA,GAAA,IAAe,CAAA;AACf,IAAA,IAAA,CAAA,UAAA,CAAA,GAAA,IAAe,CAAA;AACf,IAAA,IAAA,CAAA,oBAAA,CAAA,GAAA,IAAyB,CAAA;AACzB,IAAA,IAAA,CAAA,YAAA,CAAA,GAAA,IAAiB,CAAA;AACjB,IAAA,IAAA,CAAA,QAAA,CAAA,GAAA,IAAa,CAAA;AACb,IAAA,IAAA,CAAA,8BAAA,CAAA,GAAA,IAAmC,CAAA;AACnC,IAAA,IAAA,CAAA,SAAA,CAAA,GAAA,IAAc,CAAA;AACd,IAAA,IAAA,CAAA,SAAA,CAAA,GAAA,IAAc,CAAA;AACd,IAAA,IAAA,CAAA,OAAA,CAAA,GAAA,IAAY,CAAA;AACZ,IAAA,IAAA,CAAA,YAAA,CAAA,GAAA,IAAiB,CAAA;AACjB,IAAA,IAAA,CAAA,eAAA,CAAA,GAAA,IAAoB,CAAA;AACpB,IAAA,IAAA,CAAA,UAAA,CAAA,GAAA,IAAe,CAAA;AACf,IAAA,IAAA,CAAA,SAAA,CAAA,GAAA,IAAc,CAAA;AACd,IAAA,IAAA,CAAA,WAAA,CAAA,GAAA,IAAgB,CAAA;AAChB,IAAA,IAAA,CAAA,YAAA,CAAA,GAAA,IAAiB,CAAA;AACjB,IAAA,IAAA,CAAA,QAAA,CAAA,GAAA,IAAa,CAAA;AACb,IAAA,IAAA,CAAA,SAAA,CAAA,GAAA,IAAc,CAAA;AACd,IAAA,IAAA,CAAA,QAAA,CAAA,GAAA,IAAa,CAAA;AACb,IAAA,IAAA,CAAA,SAAA,CAAA,GAAA,IAAc,CAAA;AACd,IAAA,IAAA,CAAA,YAAA,CAAA,GAAA,IAAiB,CAAA;AACjB,IAAA,IAAA,CAAA,UAAA,CAAA,GAAA,IAAe,CAAA;AACf,IAAA,IAAA,CAAA,QAAA,CAAA,GAAA,IAAa,CAAA;AACb,IAAA,IAAA,CAAA,iBAAA,CAAA,GAAA,IAAsB,CAAA;AACtB,IAAA,IAAA,CAAA,gBAAA,CAAA,GAAA,IAAqB,CAAA;AACrB,IAAA,IAAA,CAAA,MAAA,CAAA,GAAA,IAAW,CAAA;AACX,IAAA,IAAA,CAAA,OAAA,CAAA,GAAA,IAAY,CAAA;AACZ,IAAA,IAAA,CAAA,SAAA,CAAA,GAAA,IAAc,CAAA;AACd,IAAA,IAAA,CAAA,YAAA,CAAA,GAAA,IAAiB,CAAA;AACjB,IAAA,IAAA,CAAA,UAAA,CAAA,GAAA,IAAe,CAAA;AACf,IAAA,IAAA,CAAA,YAAA,CAAA,GAAA,IAAiB,CAAA;AACjB,IAAA,IAAA,CAAA,YAAA,CAAA,GAAA,IAAiB,CAAA;AACjB,IAAA,IAAA,CAAA,WAAA,CAAA,GAAA,IAAgB,CAAA;AAChB,IAAA,IAAA,CAAA,QAAA,CAAA,GAAA,IAAa,CAAA;AACb,IAAA,IAAA,CAAA,UAAA,CAAA,GAAA,IAAe,CAAA;AACf,IAAA,IAAA,CAAA,SAAA,CAAA,GAAA,IAAc,CAAA;AACd,IAAA,IAAA,CAAA,OAAA,CAAA,GAAA,IAAY,CAAA;AACZ,IAAA,IAAA,CAAA,SAAA,CAAA,GAAA,IAAc,CAAA;AACd,IAAA,IAAA,CAAA,WAAA,CAAA,GAAA,IAAgB,CAAA;AAChB,IAAA,IAAA,CAAA,aAAA,CAAA,GAAA,IAAkB,CAAA;AAClB,IAAA,IAAA,CAAA,QAAA,CAAA,GAAA,IAAa,CAAA;AACb,IAAA,IAAA,CAAA,OAAA,CAAA,GAAA,IAAY,CAAA;AACZ,IAAA,IAAA,CAAA,OAAA,CAAA,GAAA,IAAY,CAAA;AACZ,IAAA,IAAA,CAAA,YAAA,CAAA,GAAA,IAAiB,CAAA;AACjB,IAAA,IAAA,CAAA,MAAA,CAAA,GAAA,IAAW,CAAA;AACX,IAAA,IAAA,CAAA,UAAA,CAAA,GAAA,IAAe,CAAA;AACf,IAAA,IAAA,CAAA,QAAA,CAAA,GAAA,IAAa,CAAA;AACb,IAAA,IAAA,CAAA,MAAA,CAAA,GAAA,IAAW,CAAA;AACX,IAAA,IAAA,CAAA,aAAA,CAAA,GAAA,IAAkB,CAAA;AAClB,IAAA,IAAA,CAAA,OAAA,CAAA,GAAA,IAAY,CAAA;AACZ,IAAA,IAAA,CAAA,gBAAA,CAAA,GAAA,IAAqB,CAAA;AACrB,IAAA,IAAA,CAAA,QAAA,CAAA,GAAA,IAAa,CAAA;AACb,IAAA,IAAA,CAAA,iBAAA,CAAA,GAAA,IAAsB,CAAA;AACtB,IAAA,IAAA,CAAA,YAAA,CAAA,GAAA,IAAiB,CAAA;AACjB,IAAA,IAAA,CAAA,UAAA,CAAA,GAAA,IAAe,CAAA;AACf,IAAA,IAAA,CAAA,UAAA,CAAA,GAAA,IAAe,CAAA;AACf,IAAA,IAAA,CAAA,OAAA,CAAA,GAAA,IAAY,CAAA;AACZ,IAAA,IAAA,CAAA,SAAA,CAAA,GAAA,IAAc,CAAA;AACd,IAAA,IAAA,CAAA,QAAA,CAAA,GAAA,IAAa,CAAA;AACb,IAAA,IAAA,CAAA,QAAA,CAAA,GAAA,IAAa,CAAA;AACb,IAAA,IAAA,CAAA,aAAA,CAAA,GAAA,IAAkB,CAAA;AAClB,IAAA,IAAA,CAAA,OAAA,CAAA,GAAA,IAAY,CAAA;AACZ,IAAA,IAAA,CAAA,SAAA,CAAA,GAAA,IAAc,CAAA;AACd,IAAA,IAAA,CAAA,WAAA,CAAA,GAAA,IAAgB,CAAA;AAChB,IAAA,IAAA,CAAA,gBAAA,CAAA,GAAA,IAAqB,CAAA;AACrB,IAAA,IAAA,CAAA,aAAA,CAAA,GAAA,IAAkB,CAAA;AAClB,IAAA,IAAA,CAAA,YAAA,CAAA,GAAA,IAAiB,CAAA;AACjB,IAAA,IAAA,CAAA,WAAA,CAAA,GAAA,IAAgB,CAAA;AAChB,IAAA,IAAA,CAAA,SAAA,CAAA,GAAA,IAAc,CAAA;AACd,IAAA,IAAA,CAAA,QAAA,CAAA,GAAA,IAAa,CAAA;AACb,IAAA,IAAA,CAAA,YAAA,CAAA,GAAA,IAAiB,CAAA;AACjB,IAAA,IAAA,CAAA,oBAAA,CAAA,GAAA,IAAyB,CAAA;AACzB,IAAA,IAAA,CAAA,UAAA,CAAA,GAAA,IAAe,CAAA;AACf,IAAA,IAAA,CAAA,UAAA,CAAA,GAAA,IAAe,CAAA;AACf,IAAA,IAAA,CAAA,UAAA,CAAA,GAAA,IAAe,CAAA;AACf,IAAA,IAAA,CAAA,QAAA,CAAA,GAAA,IAAa,CAAA;AACb,IAAA,IAAA,CAAA,UAAA,CAAA,GAAA,IAAe,CAAA;AACf,IAAA,IAAA,CAAA,YAAA,CAAA,GAAA,IAAiB,CAAA;AACjB,IAAA,IAAA,CAAA,OAAA,CAAA,GAAA,IAAY,CAAA;AACZ,IAAA,IAAA,CAAA,MAAA,CAAA,GAAA,IAAW,CAAA;AACX,IAAA,IAAA,CAAA,MAAA,CAAA,GAAA,IAAW,CAAA;AACX,IAAA,IAAA,CAAA,UAAA,CAAA,GAAA,IAAe,CAAA;AACf,IAAA,IAAA,CAAA,YAAA,CAAA,GAAA,IAAiB,CAAA;AACjB,IAAA,IAAA,CAAA,cAAA,CAAA,GAAA,IAAmB,CAAA;AACnB,IAAA,IAAA,CAAA,YAAA,CAAA,GAAA,IAAiB,CAAA;AACjB,IAAA,IAAA,CAAA,OAAA,CAAA,GAAA,IAAY,CAAA;AACZ,IAAA,IAAA,CAAA,mBAAA,CAAA,GAAA,IAAwB,CAAA;AACxB,IAAA,IAAA,CAAA,SAAA,CAAA,GAAA,IAAc,CAAA;AACd,IAAA,IAAA,CAAA,QAAA,CAAA,GAAA,IAAa,CAAA;AACb,IAAA,IAAA,CAAA,QAAA,CAAA,GAAA,IAAa,CAAA;AACb,IAAA,IAAA,CAAA,UAAA,CAAA,GAAA,IAAe,CAAA;AACf,IAAA,IAAA,CAAA,QAAA,CAAA,GAAA,IAAa,CAAA;AACb,IAAA,IAAA,CAAA,SAAA,CAAA,GAAA,IAAc,CAAA;AACd,IAAA,IAAA,CAAA,SAAA,CAAA,GAAA,IAAc,CAAA;AACd,IAAA,IAAA,CAAA,cAAA,CAAA,GAAA,IAAmB,CAAA;AACnB,IAAA,IAAA,CAAA,YAAA,CAAA,GAAA,IAAiB,CAAA;AACjB,IAAA,IAAA,CAAA,aAAA,CAAA,GAAA,IAAkB,CAAA;AAClB,IAAA,IAAA,CAAA,8BAAA,CAAA,GAAA,IAAmC,CAAA;AACnC,IAAA,IAAA,CAAA,WAAA,CAAA,GAAA,IAAgB,CAAA;AAChB,IAAA,IAAA,CAAA,SAAA,CAAA,GAAA,IAAc,CAAA;AACd,IAAA,IAAA,CAAA,SAAA,CAAA,GAAA,IAAc,CAAA;AACd,IAAA,IAAA,CAAA,OAAA,CAAA,GAAA,IAAY,CAAA;AACZ,IAAA,IAAA,CAAA,OAAA,CAAA,GAAA,IAAY,CAAA;AACZ,IAAA,IAAA,CAAA,aAAA,CAAA,GAAA,IAAkB,CAAA;AAClB,IAAA,IAAA,CAAA,QAAA,CAAA,GAAA,IAAa,CAAA;AACb,IAAA,IAAA,CAAA,UAAA,CAAA,GAAA,IAAe,CAAA;AACnB,CAAC,EApMWA,YAAI,KAAJA,YAAI,GAoMf,EAAA,CAAA,CAAA;;ACpMWC,iCAOX;AAPD,CAAA,UAAY,eAAe,EAAA;AACvB,IAAA,eAAA,CAAA,KAAA,CAAA,GAAA,KAAW,CAAA;AACX,IAAA,eAAA,CAAA,MAAA,CAAA,GAAA,MAAa,CAAA;AACb,IAAA,eAAA,CAAA,KAAA,CAAA,GAAA,KAAW,CAAA;AACX,IAAA,eAAA,CAAA,OAAA,CAAA,GAAA,OAAe,CAAA;AACf,IAAA,eAAA,CAAA,QAAA,CAAA,GAAA,QAAiB,CAAA;AACjB,IAAA,eAAA,CAAA,SAAA,CAAA,GAAA,SAAmB,CAAA;AACvB,CAAC,EAPWA,uBAAe,KAAfA,uBAAe,GAO1B,EAAA,CAAA,CAAA;;ACPWC,8BAUX;AAVD,CAAA,UAAY,YAAY,EAAA;AACpB,IAAA,YAAA,CAAA,QAAA,CAAA,GAAA,QAAiB,CAAA;AACjB,IAAA,YAAA,CAAA,SAAA,CAAA,GAAA,UAAoB,CAAA;AACpB,IAAA,YAAA,CAAA,KAAA,CAAA,GAAA,KAAW,CAAA;AACX,IAAA,YAAA,CAAA,UAAA,CAAA,GAAA,WAAsB,CAAA;AACtB,IAAA,YAAA,CAAA,MAAA,CAAA,GAAA,MAAa,CAAA;AACb,IAAA,YAAA,CAAA,OAAA,CAAA,GAAA,OAAe,CAAA;AACf,IAAA,YAAA,CAAA,YAAA,CAAA,GAAA,aAA0B,CAAA;AAC1B,IAAA,YAAA,CAAA,QAAA,CAAA,GAAA,QAAiB,CAAA;AACjB,IAAA,YAAA,CAAA,aAAA,CAAA,GAAA,cAA4B,CAAA;AAChC,CAAC,EAVWA,oBAAY,KAAZA,oBAAY,GAUvB,EAAA,CAAA,CAAA;;ACVWC,6BAQX;AARD,CAAA,UAAY,WAAW,EAAA;AACnB,IAAA,WAAA,CAAA,KAAA,CAAA,GAAA,KAAW,CAAA;AACX,IAAA,WAAA,CAAA,MAAA,CAAA,GAAA,MAAa,CAAA;AACb,IAAA,WAAA,CAAA,KAAA,CAAA,GAAA,KAAW,CAAA;AACX,IAAA,WAAA,CAAA,MAAA,CAAA,GAAA,MAAa,CAAA;AACb,IAAA,WAAA,CAAA,MAAA,CAAA,GAAA,MAAa,CAAA;AACb,IAAA,WAAA,CAAA,MAAA,CAAA,GAAA,MAAa,CAAA;AACb,IAAA,WAAA,CAAA,KAAA,CAAA,GAAA,KAAW,CAAA;AACf,CAAC,EARWA,mBAAW,KAAXA,mBAAW,GAQtB,EAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;"}